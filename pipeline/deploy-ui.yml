---
- name: Deploy to "{{target}}" Env
  hosts: "{{target}}"
  vars:
    fin_stack_dir: "finnate-docker-stack"
  tasks:
    - name: Environment
      debug:
        msg: "Hi, {{ ansible_hostname }} - {{ ansible_distribution }} {{ ansible_distribution_version }}"

    - name: deploy service to finnate
      shell: task finnate -- {{ service }} {{ tag }}
      args:
        chdir: ~/saas-stack/
      environment:
        ENV_VAULT_PASSWORD: "{{ lookup('env', 'DEV_VAULT_PASS') }}"
      register: finnate_output

    - name: output finnate
      debug:
        msg: "{{ finnate_output.stdout }}"
