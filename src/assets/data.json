{"label.header.uniqueKey": "Unique ID", "_comment_applicationSummary_approval": "applicationSummary_approval", "label.title.approvalInformation": "Approval Information", "label.title.submittedTaskForApproval": "has submitted the task for A<PERSON><PERSON>al", "label.title.status": "Status", "label.title.approvalsRequired": "No of approvals required", "label.button.approve": "Approve", "label.button.reject": "Reject", "label.field.rejectionReason": "Rejection reason", "label.header.selectRejectionReason": "Select Reason for Rejection", "label.button.approvedStatus": "Approved", "label.button.rejectedStatus": "Rejected", "label.button.onHold": "ON HOLD", "_comment_applicationSummary_dealDetails": "applicationSummary_dealDetails", "label.button.reOpen": "Reopen", "label.button.changeStage": "Change stage", "label.button.nextStage": "Next Stage", "label.button.previousStage": "Previous Stage", "label.button.holdStage": "Hold Stage", "label.button.monitorInvestment": "MONITOR INVESTMENT", "label.button.shareStage": "Share", "label.button.draftSave": "Draft Save", "label.button.executeRules": "Execute Rules", "label.title.noDataFound": "No matching data found.", "label.title.noDataAvailable": "No data available.", "lable.title.noDataHere": "No Data Here!", "label.title.ModuleConfiguration": "Module configuration", "label.title.ApplicationConfiguration": "Application configuration", "label.title.EntityConfiguration": "Default Entity configuration", "_comment_applicationSummary_dealDetails_score": "applicationSummary_dealDetails_score", "label.button.sendToReview": "Send to review", "label.button.sendToTeam": "Send to team", "label.button.save": "Save", "label.button.saveAndExit": "Save And Exit", "label.title.dealScore": "Score", "label.title.stageSelect": "Select A Stage", "label.title.score": "Score", "label.title.version": "Version", "label.title.compare": "Compare", "label.button.rescore": "RESCORE", "label.title.teamAverage": "Team Average", "label.title.outOf5": "out of 5", "label.title.membersScored": "members scored the Deal", "label.title.partnersScored": "partners scored the Deal", "label.title.teamScores": "Team Scores", "_comment_applicationSummary_dealTask": "applicationSummary_dealTask", "label.title.searchTask": "Search Task", "label.button.create": "Create", "label.button.rule": "Rules", "label.title.tasks": "Tasks", "label.table.title.taskSummary": "Task Summary", "label.table.title.assignedTo": "Assigned to", "label.table.title.taskDueDate": "Task due date", "label.table.title.taskOwner": "Task owner", "label.table.title.taskReminder": "Task reminder", "label.table.title.taskPriority": "Task priority", "label.table.title.assigneeType": "Assignee type", "label.table.title.taskStatus": "Task status", "label.table.title.taskCreationDate": "Task creation date", "label.table.title.action": "Action", "label.title.noRecordsFound": "No records found", "label.literals.noHistoryRecords": "No history records found", "label.literals.sortName": "Name", "label.literals.sortDate": "Date", "label.title.history": "History", "label.title.advanceHistory": "Advance History", "_comment_applicationSummary_dialogs_updateDocument": "dialog_updateDocument", "label.title.editDocument": "Edit Document", "label.field.documentTypeEdit": "Document Type", "label.select.documentType": "Select Document Type", "label.workflowRule": "Workflow Rule", "label.select.workflow": "Select Workflow", "label.add.documentType": "Please add document type in document section", "label.field.tag": "Tags", "label.button.updateDocument": "UPDATE DOCUMENT", "_comment_applicationSummary_dialogs_requestDetailsDialog": "dialog_requestDetails", "label.title.documentRequestDetails": "Request Document Details", "label.title.shareOnLinkRequestDetails": "Shared Deal Status", "label.noRecords": " No records found", "label.no.notification.yet": "No Notifications Yet", "lable.notifications.inform": "We'll Keep You Posted When There's Something New.", "label.see.more": "See more", "label.mark.all.as.read": "<PERSON> as <PERSON>", "_applicationSummary_dialogs_requestDetailsDialog": "dialog_stage_movement_remarks", "label.header.stageMovementRemarks": "Add Remarks", "label.title.stageMovementRemarks": "Remarks", "label.error.stageMovementRemarksError": "Remarks are required.", "_comment_applicationSummary_dialogs_uploadDocument": "dialog_uploadDocument", "label.title.uploadDocument": "Upload Document", "label.field.documentType": "Document Type", "label.field.tags": "Tags", "label.button.chooseFile": "Select file", "label.dropFile": " or Drop it here!", "label.documentFormat": " Note : Select document in .doc,.docx, .ppt, .xls, .pdf,.pptx,.jpeg,.jpg,.xlsx ,.HEIC,.MOV  format upto 100 MB of size.", "label.Docsize": "Note : Select document in .doc, .pdf,.pptx,.jpeg,.jpg,.xlsx ,.HEIC,.MOV format upto 10 MB of size.", "label.fileSize": " File Size Should be less than", "label.size": " 100 MB", "label.Uploadsize": "10 MB", "label.specificDocumentTags": " Note : Add specific document tags. Tags once added cannot be removed.", "label.button.uploadDocumentDialog": "UPLOAD", "_comment_applicationSummary_dialogs_generateDocument": "dialog_generateDocument", "label.title.generateDocument": "Generate Document", "label.field.selectTemplate": "Select Template", "label.field.downloadAs": "Download As", "label.button.generateDocumentDialog": "GENERATE DOCUMENT", "_comment_applicationSummary_dialogs_requestDocument": "dialog_requestDocument", "label.title.createDocumentRequest": "Create Document Request", "label.title.selectDocuments": "Select Documents", "label.field.contact": "Contact person / email", "label.field.secureLink": "Secure link is valid for", "label.field.note": "Note", "_comment_applicationSummary_documentsDetail": "applicationSummary_documentsDetail", "label.button.requestDocument": "REQUEST DOCUMENT", "label.button.generateDocument": "GENERATE DOCUMENT", "label.button.uploadDocument": "UPLOAD DOCUMENT", "label.title.documents": "Documents", "label.title.tags": "TAGS", "label.table.title.name": "Name", "label.table.title.contact": "Contact", "label.table.title.createdDate": "Created Date ", "label.table.title.status": "Status ", "label.table.title.documentType": "Document Type", "label.table.title.size": "Size", "label.table.title.updated": "Updated", "label.title.noMatchingData": "No data matching the filter", "_comment_applicationSummary": "applicationSummary", "label.button.back": "BACK", "_comment_entity_companyDetails": "entity_companyDetails", "label.searchCompany": "Search Company", "label.title.company": "Company", "_comment_entity_companyDetails_createCompany": "entity_companyDetails_createCompany", "label.title.createCompany": "Create Company", "label.field.companyName": "Company Name", "label.field.companyType": "Company Type", "label.field.companyInformation": "Add Detailed Company Information", "label.validation.companyName": "Company Name is required.", "label.validation.companyType": "Extension is required.", "_comment_entity_companyDetails_viewCompany_detail": "entity_companyDetails_viewCompany_detail", "label.title.companyInformation": "Company Information", "label.title.deals": "Deals", "label.title.persons": "Persons", "_comment_entity_companyDetails_viewCompany_detail_editDetail": "entity_companyDetails_viewCompany_detail__editDetail", "label.title.editItem": "<PERSON>em", "_comment_entity_personDetails": "entity_personDetails", "label.searchPerson": "Search Person", "label.title.person": "Person", "_comment_entity_personDetails_createPerson": "entity_personDetails_createPerson", "label.title.createPerson": "Create Person", "label.field.name": "Name", "label.field.personType": "Person Type", "label.field.company": "Company", "label.field.personInformation": "Add Detailed Person Information", "label.validation.personName": " Person Name is required.", "label.validation.personType": " Person type is required.", "_comment_entity_personDetails_viewPerson_personDetail": "entity_personDetails_viewPerson_personDetail", "label.title.personInformation": "Person Information", "label.title.personInformationDeals": "Deals", "label.noMatchingData": "No data matching the filter", "label.noRecordsFound": "No records found", "_comment_dashboard_dashboardHeader": "dashboard_dashboardHeader", "label.title.originate": "Originate", "label.title.monitor": "Monitor", "label.editProfile": " Edit profile", "label.changePassword": " Change password", "label.theme": " Theme", "label.logout": " Logout", "label.company": " Company", "label.person": " Person", "label.assets": " Data Models", "label.entity": " Entity", "label.entityDefinition": " Entity Definition", "label.businessProcess": " Business process", "label.utilities": " Utilities", "label.title.sidebarLiterals": "Set Sidebar Literals", "label.title.businessTitle": "Configure Title", "_comment_home": "home", "label.title.dealToScoreHold": "To score - Hold", "label.title.dealToScoreBIR": "To score - BIR", "label.title.dealToScoreAIR": "To score - AIR", "label.title.pitchDeckPending": "Pitch Deck Pending", "label.title.tasksHome": "Tasks", "label.noDealsPresent": " No deals are present for the specified range.", "label.noRecordFound": " No records found", "label.title.quickActions": "Quick Actions", "label.title.createDeal": "Create Deal", "label.captureDealDetails": "Capture deal details with QDE", "label.title.createTask": "Create task", "label.createTasksAndAssignToTeam": "Create tasks and assign to team", "label.title.icMeeting": "IC Meeting", "label.planMeeting": "Plan your next meeting for deal", "label.title.uploadDocuments": "Upload documents", "label.oneStepDocumentManagement": "One Step document management", "label.title.cloneAssetType": "Clone Data Model Type", "label.customize": "Customize ", "_comment_login": "login", "label.validation.enterUsernameAndPassword": "Please enter valid username and password.", "label.password": "Password", "_comment_dialogs_deleteConfirmationDialog": "dialogs_deleteConfirmationDialog", "label.button.delete": "Delete", "label.button.cancel": "Cancel", "_comment_dialogs_editDealDialog": "dialogs_editDealDialog", "label.title.editDeal": "Edit Deal", "label.field.labels": "Labels", "_comment_dialogs_emailDialog": "dialogs_emailDialog", "label.field.deals": "Deals", "_comment_dialogs_filePreview": "dialogs_filePreview", "label.button.close": "Close", "_comment_dialogs_conceptualAddTask": "dialogs_conceptualAddTask", "label.relatedTo": "Related to ", "_comment_dialogs_changePasswordDialog": "dialogs_changePasswordDialog", "label.title.changePassword": "Change Password", "label.validation.passwordValidation": "password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter and 1 number", "label.button.change": "CHANGE", "_comment_dialogs_editTaskDialog": "dialogs_editTaskDialog", "label.relatedToeditTaskDialog": "Related to ", "_comment_dialogs_cancelConfirmationDialog": "dialogs_cancelConfirmationDialog", "label.title.uploadAsset": "Upload Data Model", "label.Assetsize": "1 MB", "label.AssetDocumentformat": " Note : Only .xlsx file supported upto 1 MB.", "label.title.cloneBusinessProcess": "Clone Business Process", "label.title.cloneDashboard": "Clone Dashboard", "label.button.cloneBusinessProcess": "<PERSON><PERSON>", "label.button.more": "More", "label.button.history": "History", "label.button.stageHistory": "Stage History", "label.button.assetHistory": "Asset History", "label.button.upgradeAsset": "Update Data Model", "label.button.upgradeDataModel": "Upgrade Data Model", "label.button.upgradeRecords": "Upgrade Records", "label.button.viewUpgradeDetails": "View Upgrade Details", "label.button.addDataModel": "Upgrade Data Model", "label.button.rules": "Rules", "_comment_settings_application_labels": "application labels", "label.field.labelName": "Label Name", "label.field.searchlabel": "Search Label", "label.theader.labelName": "Label Name", "label.theader.createdDate": "Created Date", "label.theader.updatedDate": "Updated Date", "label.theader.label": "Label", "label.tdatacell.noData": "No data matching the filter", "label.card.noRecord": "No records found", "label.spinner.noRecord": "No records found", "_comment_settings_application_labels_createLabel": "create_label", "label.title.createLable": "Create Label", "label.error.newLabel": "Please enter valid label name", "label.error.labelName": "Label Name is required.", "_comment_settings_application_labels_editLabel": "edit_label", "label.title.updateLable": "Update Label", "_comment_settings_assets": "Data Models", "label.header.assetConfig": "Data Model Configuration", "label.field.searchAsset": "Search Data Model", "label.header.assets": "Data Models", "label.theader.asset": "Data Model", "label.theader.description": "Description", "label.theader.version": "Version", "label.button.configure": "CONFIGURE", "label.button.clone": "<PERSON><PERSON>", "label.card.noRecordsFound": "No records found", "label.suggestions": "Suggestions", "label.search": "Search", "label.error.noMatchInput": "No matching record found. Please verify your input and try again.", "_comment_settings_assets_asset_clone": "Data Models", "label.field.assetName": "Data Model Name", "label.field.description": "Description", "_comment_settings_assets_create_asset": "create Data Models", "label.header.createAssetType": "Create Data Model Type", "_comment_settings_assets_edit_asset": "edit Data Models", "label.editName": "Modifying JSON Node Name", "label.editName.additionalMessage": "You are about to modify the name of a JSON node. This operation is not recommended unless absolutely necessary.", "label.header.editAsset": "Edit Data Model Type", "label.error.nameRequired": "Data Model Name is required.", "label.error.notValid": "Data Model Name is not valid.", "_comment_settings_assets_view_asset": "view Data Models", "label.header.description": "Description", "label.field.entityTypes": "Entity Type", "label.header.dataItems": "Data Items", "label.field.searchItem": "Search Item", "label.theader.name": "Name", "label.theader.type": "Type", "label.theader.displayColumns": "Select Columns to Display in List View", "_comment_settings_assets_BP_config_asset_item_dialog": "Data Model-items-dialog", "label.title.panel.itemConfig": "Item Configuration", "label.field.searchItems": "Search Item", "label.theader.order": "Order", "label.theader.access": "Access", "label.theader.mask": "Mask", "label.theader.encrypt": "Encrypt", "label.tdatacel.All": "All", "label.toggle.ApplytoAllStages": "Apply To All Stages", "label.card.noConfiguredItems": "Configure at least one field from manage items.", "_comment_settings_BP_config_manage_items": "manage items", "label.theader.mandatory": "Mandatory", "label.theader.readOnly": "Read Only", "label.header.hideRule": "Hide Rule", "label.theader.disableRule": "Read Only (Disable) Rule", "label.theader.validateRule": "Validate Rule", "label.theader.valueRule": "Value Rule", "label.theader.defaultValueRule": "Default Value Rule", "label.card.noRecordFound": "No records found", "label.tooltip.rulesConfiguration": "Rules Configuration", "label.title.FErulesConfiguration": "Event Rule Configuration", "label.tooltip.rulesConfigurationNew": "Experience the New Front-End Rules UI!", "label.tooltip.rulesConfigurationNewSub": "Explore an enhanced interface designed for seamless front-end rule configuration.", "_comment_settings_assets__add_edit_item_dialog": "add and edit item dialog", "label.header.addItem": "Add item", "label.header.addItemDetails": "Set item details", "label.header.executeRuleOnSelect": "Rule to execute on select event", "label.header.editItem": "Edit item", "label.field.names": "Name", "label.field.descriptions": "Description", "label.field.dataType": "Data Type", "label.field.showSerialNumber": "Show Serial Number Column", "label.field.defaultValue": "Default Value", "label.field.options": "Options", "label.field.workflowValue": "Workflow Name", "label.field.formlyJson": "<PERSON><PERSON>", "label.field.selectCurrencyTypes": "Select Currency types", "label.header.columnName": "Name", "label.header.datatype": "Data type", "label.button.add": "Add", "label.tdatacell.nodata": "No data matching the filter", "label.field.validationRegularExpression": "Validation Regular Expression", "label.button.update": "Update", "label.error.message": "The requested resource is Not Found, Please contact the Support Team.", "label.error.enterItem": "Enter item name.", "label.error.editNodeName": "Spaces and special characters are not acceptable.", "label.error.enterDescription": "Enter item description", "label.error.dataTyprReq": "Data Type is required.", "label.error.nameReq": " Name is required.", "label.validation.12HrFormat": "Invalid 12-hour time format. Use hh:mm AM/PM", "label.validation.24HrFormat": "Invalid 24-hour time format. Use HH:mm", "_comment_settings_assets_business_process_config": "create and edit business process", "label.header.createBP": "Create Business Process", "label.header.editBP": "Edit Business Process", "label.field.BPName": "Business Process Name", "label.field.aliasName": "<PERSON>as Name", "label.field.assetType": "Data Model Type", "labe.field.primaryActor": "Primary Actor", "label.field.recordName": "Record Name Options", "label.field.desc": "Description", "label.error.BPrequired": "Business Process Name is required.", "label.error.cloneBPrequired": "Please select business process to clone", "label.error.cloneDMrequired": "Please select data model to clone", "label.error.clonePrimaryActorRequired": "Please select primary actor to clone", "label.error.Aliserequired": "Alise Name is required.", "label.error.assetTyperequired": "Data Model Type is required.", "label.error.primaryactorrequired": "Primary actor is required.", "label.error.numberNotAllowed": "Numbers not allowed.", "_comment_settings_dashboard_config": "create and edit dashboard configuration", "label.field.dashboardName": "Dashboard Name", "label.field.businessProcess": "Select Business Process", "label.field.dashboardType": "Dashboard Type", "label.header.businessProcess": "Business Process", "label.header.createDashboard": "Create Dashboard Configuration", "label.header.editDashboardName": "Edit Dashboard Name", "label.error.dashboardSave": "Please add atleast one chart to save.", "label.error.DashboardName.required": "Dashboard Name is required", "label.error.BpRequired": "Business Process is required.", "label.error.DashboardTypeRequired": "Dashboard Type is required.", "label.error.GraphTitleRequired": "Graph title is required.", "comment_settings_assets_BP_config_create_edit_stage": "create and edit stage", "label.header.addStage": "Add Stage", "label.header.editstage": "Edit Stage", "label.header.editstageName": "Edit Stage Name", "label.field.stageName": "Stage Name", "label.field.staus": "Status", "label.field.section": "Section", "label.error.stagerequired": "Stage Name is required.", "label.error.stagename": "Enter Valid Stage Name.", "label.error.selectStatus": "Please select status.", "label.tooltip.addSubsection": "Add Subsection", "label.tooltip.editSection": "Edit Section", "label.tooltip.deleteSection": "Delete Section", "label.tooltip.editSubsection": "Edit Subsection", "label.tooltip.deleteSubsection": "Delete Subsection", "label.message.deleteStage": "Are you sure you want to delete this Stage ?", "label.message.deleteSection": "Are you sure you want to delete this section?", "label.message.deleteSubsection": "Are you sure you want to delete this subsection?", "comment_settings_assets_BP_config_create_edit_rejectionType": "create and edit rejection type", "label.header.addRejectiontype": "Add Rejection Type", "label.header.editRejectiontype": "Edit Rejection Type", "label.field.rejectionType": "Rejection Type", "label.error.rejectionTypeRequired": "Rejection Type is required.", "label.error.enterValidRejectionType": "Enter valid Rejection Type.", "label.button.updateRejection": "Update", "comment_settings_assets_BP_config_set_rules": "set rules", "label.header.assignRules": "Assign Workflows", "label.container.noRules": "No workflow found.", "label.theader.ruleName": "Workflow", "label.theader.eventRule": "<PERSON><PERSON>", "label.field.selectEvent": "Select Event", "label.button.apply": "Apply", "comment_settings_assets_BP_config_set_team": "set team", "label.header.selectTeam": "Select Team", "label.field.discription": "description", "label.field.Select": "Select", "comment_settings_assets_BP_config_stage_upgrade": "stage upgrade record", "label.chip.recordsProcessed": "Records Processed", "label.chip.sucess": "Success", "label.chip.failed": "Failed", "label.div.upgradeEvent": "Upgarde Event: Upgrade Records", "label.chip.asserVersion": "Data Model Version", "label.chip.BP-Version": "Business Process Version", "label.chip.updatedOn": "Updated On", "label.theader.Description": "Description", "label.theader.createdBy": "Created By", "label.theader.createDate": "Created Date", "label.theader.Status": "Status", "label.theader.exceptions": "Exceptions", "label.button.closeRecord": "Close", "comment_settings_entity_upgrade_view_details": "Upgrade and view details", "label.theader.upgradeEvents": "Upgrade Event", "label.anchor.upgradeRecord": "Upgrade Records", "label.theader.entityVersion": "Entity Version", "label.theader.totalRecord": "Total Records", "label.theader.suces": "Success", "label.theader.fail": "Failed", "label.theader.updatedOn": "Updated On", "label.theader.updatedBy": "Updated By", "label.theader.status": "Status", "comment_settings_assets_view_records_upgrade": "stage view record", "comment_settings_assets_BP_config_upgrade_stage": "upgrade stage", "label.anchor.upgradeRecords": "Upgrade Records", "label.theader.upgradeEvent": "Upgrade Event", "label.theader.assetVersion": "Data Model Version", "label.theader.totalRecords": "Total Records", "label.theader.sucess": "Success", "label.theader.failed": "Failed", "label.theader.createdOn": "Created On", "comment_settings_assets_BP_config_stage": "stage", "label.header.assetType": "Data Model Type", "label.header.descriptions": "Description", "label.header.primaryActor": "Primary Actor", "label.header.version": "Version", "label.header.recordName": "Record Name", "label.button.addStage": "Add", "label.button.addRules": "Event Rules", "label.button.displayColumns": "Display Columns", "label.button.addRejectionType": "Add", "label.header.AppStages": "Stages", "label.theader.stageOrder": "Order", "label.theader.stageName": "Stage Name", "label.theader.lastUpdated": "Last Updated", "label.theader.pipelineDisplay": "Status", "label.button.security": "Security", "label.theader.Action": "Action", "label.button.managerItems": "MANAGE ITEMS", "label.theader.shareOnLink": "Share", "label.button.team": "Team", "label.header.rejectionTypes": "Types", "label.theader.rejectionType": "Rejection Type", "label.card.noRecords": "No records found", "label.header.appStages": "Application Stages", "label.button.manage": "MANAGE", "label.header.AdvanceRules": "Advance Rules", "comment_settings_assets_BP_config": "Business process configuration", "label.header.BPconfig": "Business Process Configuration", "label.field.searchBP": "Search Business process", "label.header.BP": "Business Processes", "label.theader.BPname": "Business process Name", "label.theader.createddate": "Created Date", "label.theader.BPversion": "Version", "label.theader.BPdesc": "Description", "label.theader.assetType": "Data Model Type", "label.card.norecord": "No records found", "comment_settings_assets_doc_temp_config": "Document template configuration", "label.field.selectBP": "Select business process", "label.field.selectStage": "Select Stage", "label.option.all": "All", "label.field.docList": "Add Document List/Choose from below", "label.field.docName": "Type", "label.field.mandatorystage": "Mandatory Stage", "label.span.upload": "Upload", "label.span.request": "Request", "label.header.processDocs": "Process Documents", "label.theader.Stage": "Stage", "label.theader.BP": "Business Process", "label.theader.CreatedDate": "Created Date", "label.tdatacell.noMatchData": "No data matching the filter", "label.tdatacell.noRecords": "No records found", "label.header.templates": "Templates", "label.theader.docTempName": "Name", "comment_settings_assets_additional_details_config": "Additonal details configuration", "label.header.stageRemarks": "Stage Movement Remarks", "label.placeholder.searchStage": "Search Stage...", "label.hint.movementDirection": "*Prev/Next - Previous/Next stage movement remarks, *Approve - Approval remarks", "label.button.selectAll": "Select All", "label.button.clearAll": "Clear All", "label.header.staticColumn": "Deal List Static Column Set Up", "label.subHeader.entity": "Entity", "label.subHeader.status": "Status", "label.header.collapsibleSubsection": "Collapsible Subsection", "label.subHeader.expandeSubsections": "Expand Subsections", "comment_settings_assets_add_doc_list": "Add document list", "label.header.addList": "Add Document Type", "comment_add_button_rules": "Add button rules dialog", "label.header.setBtnRules": "Set Rules Json", "label.button.saveRule": " Save Rule", "comment_settings_assets_create_templete": "Create templete", "label.header.createTemplete": "Create Template", "label.field.selectBPtemplete": "Select business process", "label.button.choodeFile": "Select file", "label.span.dropIt": "or Drop it here!", "label.hint.note": "Note : Upload sample template in .docx format upto", "label.hint.fileSize": "File Size Should be less than", "comment_settings_assets_edit_templete": "<PERSON> templete", "label.header.editList": "Edit Document Type", "comment_settings_assets_view_templete": "View templete", "label.header.viewList": "View List", "comment_settings_entity_add_item": "entity add item", "label.header.addItems": "Add Item", "label.field.itemName": "Name", "label.error.enterName": "Enter Item name.", "label.field.Description": "Description", "label.error.enterDesc": "Enter Item description.", "label.field.dataTypes": "Data Type", "label.error.dataTypeRequired": "Data Type is required.", "label.field.defaultValues": "Default Value", "label.field.timeFormat": "Time Format", "label.field.fetchOptions": "Fetch Options using rule", "label.field.chooseValue": "Choose a Defualt value", "label.option.today": "Today", "label.option.none": "None", "label.field.formlyJsonEntity": "<PERSON><PERSON>", "label.field.selectCurrency": "Select currency", "label.theader.columnName": "Column name", "label.theader.dataType": "Data type", "label.theader.defaultValues": "Default values", "label.tdatacell.nomatchData": "No data matching the filter", "label.error.nameRequire": "Name is required.", "label.field.validation": "Validation Regular Expression", "label.field.mandatory": "Mandatory", "label.radiobutton.yes": "Yes", "label.radiobutton.no": "No", "comment_settings_entity_edit_item": "entity edit item", "label.header.editItems": "<PERSON>em", "comment_settings_entity": "entity component", "label.header.entityTemplate": "Entity Template", "labe.theader.type": "Type", "label.theader.ShowonList": "Show on List", "label.header.ShowonForm": "Show on Form", "label.label.Extension": "Extension", "label.field.SearchExtension": "Search Extension", "comment_settings_entity_create_extension": "create extension", "label.header.createExtension": "Create Extension", "label.field.descr": "Description", "label.error.extensionName": "Enter Extension name.", "label.error.extensionDescr": "Enter Extension description.", "label.field.entityType": "Entity Type", "label.error.entityType": "Entity Type is required.", "label.field.cloneInformation": "Clone Entity information", "label.error.selectstatus": "Please select status.", "label.field.statu": "Status", "label.option.status": "-Select Status-", "comment_settings_entity_edit_extension": "Edit Extension", "label.header.editExtension": "Edit Extension", "label.button.saveChangesExtension": "Save Changes", "comment_settings_pageLayout_": "session and sidebar configuration", "label.button.editSession": "Edit", "comment_settings_pocComponent": "POC component", "label.field.Fillformfield": "Fill form field", "label.field.ChooseDate": "Choose a date", "label.field.Longtext": "Long text", "label.field.Enteryouremail": "Enter your email", "comment_settings_dialog_create_user": "create user", "label.header.addUser": "Add user", "label.field.Firstname": "First name", "label.field.Lastname": "Last name", "label.error.charLong": "Must be atleast 2 characters long.", "label.field.Mailid": "Mail id", "label.error.validEmail": " Please enter a valid email address", "label.field.Roles": "Roles", "label.field.Phonenumber": "Phone number", "label.error.validPhNum": "Please give valid phone number.", "label.field.Username": "Username", "label.note": "Note", "comment_settings_dialog_update_user": "update user", "label.header.updateUser": "Update user", "comment_settings_users": "users components", "label.header.Users": "Users", "label.theader.userName": "Name", "label.tdatacell.noMatch": "No data matching the filter", "label.card.noRecFound": "No records found", "comment_settings_utility": "utility components", "label.header.Utilities": "Utilities", "label.header.defineProcess": "Define business process essentials.", "comment_settings_sign-up": "sign_up components", "label.welcome": "Welcome to", "label.finnate": "Finnate platform", "label.Username": "Username*", "label.NewPassword": "New Password*", "label.pswdHint": "(Password should contain minimum 8 digits,1 uppercase, 1 lowercase, and 1 special characters for security purpose)", "label.Re-EnterewPassword": "Re-Enter New Password*", "label.button.submit": "Submit", "label.button.goBack": "Back", "comment_shared_module_add__edit_item_dataTable": "add and edit item in dataTable", "label.header.addItemTable": "Add", "label.noDataMessage": "No Data available", "label.header.updateItemTable": "Update", "comment_shared_module_formly_custom_upload": "formly custom upload", "label.field.prooftype": "Proof type", "comment_shared_module_formly_nominee_repeatField_details": "nominee details and repeat field", "label.card.Nominee": "<PERSON><PERSON><PERSON>", "comment_shared_module_formly_repeat_section": "repeat secction", "label.card.country": "Country", "comment_shared_module_formly_upload_cheque": "upload cheque", "label.header.uploadFile": "Upload file", "label.header.dropFile": "Drag and drop file here", "label.header.dropFileOr": "Drag and drop files here or ", "label.header.or": "or", "label.button.browseFile": "Browse for file", "label.header.pleaseUpload": "Please upload cancelled cheque", "comment_shared_module_formly_edit_view_note": "view or edit note", "label.button.closeNote": "CLOSE", "comment_shared_module_formly_note": "notes component", "label.header.notes": "Notes", "label.theader.Date": "Date", "label.theader.Author": "Author", "label.theader.Content": "Content", "label.tdatacell.noDataMatch": "No data matching the filter", "label.tdatacell.noRecord": "No records found", "comment.billdeskType_formly": "formly billdesk type", "label.button.payment": "Payment", "comment_formly_custom_dob_stepper": "formly custom stepper", "label.button.Back": "BACK", "label.button.draft": "DRAFT", "label.button.next": "NEXT", "label.button.submitstep": "Submit", "label.button.saveandnext": "SAVE & NEXT", "comment_formly_custom_upload_file_upload": "formly custom and file upload", "label.field.Prooftype": "Proof type", "label.subtitle.fileUpload": "File Upload", "comment_formly_kyc_verify_otp": "kyc verify otp", "label.header.verifyOTP": "Verify OTP", "label.parah.hint": "Please enter the OTP that has been sent to your registered mobile number", "label.field.enterOTP": "Enter full OTP", "label.button.verify": "Verify", "label.button.VERIFY": "VERIFY", "label.button.Close": "Close", "label.button.resendOTP": "Resend OTP", "comment_formly_ckyc_details": "ckyc details", "label.hint.OTPresent": "OTP can be resent in the next ", "label.hint.seconds": "seconds.", "comment_shared_module_formly_payNow_drop": "pay now button", "label.header.uhoh!": "UH OH !", "label.penny.verificationFailed": "It seems Pennydrop verification has failed", "label.penny.verificationRetry": "It seems Pennydrop verification has failed again", "label.penny.tryagain": "Please try again .", "label.button.retry": "RETRY", "label.button.pennyDropVerify": "PENNY DROP VERIFICATION", "label.penny.retryagain": "Please upload cancelled cheque to proceed .", "label.button.upload": "UPLOAD", "comment_formly_image_upload": "formly image upload", "label.header.cancelCheque": "Cancelled cheque uploaded successfully !", "label.button.proceed": "Proceed", "label.button.proceed.anyway": "Proceed Anyway", "comment_formly_table_type": "formly table type", "label.theader.num": "No.", "label.theader.securityName": "Security name", "label.theader.ISIN": "ISIN", "label.theader.quantity": "Quantity", "label.theader.unitPrice": "Unit price", "label.theader.totalValue": "Total value", "label.theader.pledge": "<PERSON><PERSON> (yes/No)", "label.theader.pledgeQty": "<PERSON><PERSON>", "comment_formly_upload_dob_type": "formly upload dob type", "label.cardtitle.Photograph": "Photograph", "label.parah.size": "Photograph should be in JPEG format & file size should be between 2kb and 3mb.", "label.parah.passportSize": "Passport size photograph should be uploaded.", "label.parah.prefer": "Preferably 300x300 dpi photograph image to be uploaded", "comment_comments-component": "comments_component", "label.materror.commentDesc": "Description is required", "label.title.Comment": "Comment", "label.title.Comments": "Comments", "label.title.noComments": " There are no comments yet", "label.title.editComment": "Edit Comment", "component_report_configuration": "report configuration", "label.label.businessProcessList": "Business process list", "label.label.entityList": "Entity list", "label.header.reportName": "Report name", "label.header.automatedReportName": "Report Name", "label.header.reportType": "Report type", "label.header.bp/entity": "Business Process / Entity", "label.header.action": "Action", "label.header.noDataAvailable": "No records found", "label.header.reports": "Reports", "label.header.automated": "Query Reports", "label.succes.queryReport": "Report Generated Succesfully!", "label.button.Save": "Save", "label.button.Selectcolumns": "Select columns", "label.label.searchReport": "search report", "label.label.businessProcess": "Business process", "label.label.Entity": "Entity", "label.label.SearchEntity": "Search Entity", "label.entity.version": "Version", "label.entity.isDefault": "<PERSON><PERSON><PERSON>", "label.header.reportConfig": "Report Configuration", "label.header.addReport": "Add Report", "label.header.editReport": "Edit Report", "label.field.selectType": "Select type", "label.field.typeList": "Type list", "label.success.addReport": "Report Added Succesfully", "label.success.updateReport": "Report updated succesfully", "label.success.rulesUpadteReport": "Rules updated succesfully", "label.success.deleteReport": "Report deleted Succesfully", "comment_Materror": "Mat-error", "label.materror.updateQDE": "Document type is required", "label.materror.BP": "Business process name is required", "label.materror.Aliasname": "<PERSON><PERSON> name is required", "label.materror.Searchname": "Search name is required", "label.materror.Searchfield": "Select field is required", "label.materror.Editdeal": "Deal name is required", "label.materror.email": " Email is required", "label.materror.Investment": "Investment name is required", "label.materror.deal": " Deal is required", "label.materror.Contact": "Contact person / email is required ", "label.materror.rejection": "Please enter a reason", "label.materror.Dashboard": "Dashboard name is required", "label.materror.Daterange": "date range is required", "label.materror.Tenant": " Please enter Tenant", "label.materror.Username": " Please enter Username", "label.materror.Password": " Please enter Password", "label.materror.Agenda": "Agenda is required", "label.materror.Recordname": " Record name options is required", "label.materror.Teammembers": "Team members are required", "label.materror.Notedescription": "Note description is required", "label.materror.Taskname": " Task name is required", "label.materror.Taskduedate": "Task due date is required", "label.materror.Assigneetype": "Assignee type is required", "label.materror.Assigneename": "Assignee name is required", "label.materror.Taskstatus": "Task status is required", "label.materror.Entity": "Enter Entity name - atleast 3 characters.", "label.materror.Configuration": "Please enter a value", "label.materror.Node": "Only alphanumeric allowed", "label.materror.selectTemplate": "Template is required", "label.materror.NewPassword": "Please enter newPassword", "label.materror.pattern": " Please enter valid Password", "label.materror.ReEnterPassword": " Please ReEnterPassword", "_comment_success_message": "_comment_success", "label.success.Createdeal": "Deal created Successfully.", "label.success.eventRule": "Event rule deleted successfully.", "label.success.eventRuleAdded": "Rule created Successfully.", "label.success.eventRuleUpdated": "Rule updated Successfully.", "label.success.CreatedealOnDocUpload": "Application created successfully. Documents added, if any, will be uploaded.", "label.success.Create": "created Successfully.", "label.success.Delete": "Deleted successfully.", "label.success.Update": "updated successfully.", "label.success.entityDelink": "Entity is delinked from deal.", "label.success.entitylink": "Entity is linked to the Deal.", "label.success.score": "Score updated successfully", "label.success.StageScore": "Stage scores are reset, you can rescore now.", "label.success.DealDetails": "Details saved successfully.", "label.success.stageMovement": "Stage changed successfully.", "label.success.DealUpdate": "has been updated successfully.", "label.success.DraftDealUpdate": "Draft saved successfully.", "label.success.DealLead": "lead has been changed successfully.", "label.success.DealStatus": "status has been updated successfully.", "label.success.UploadDocument": "Document uploaded successfully.", "label.success.DeleteDocument": "Document Deleted successfully", "label.success.DownloadDocument": "Document downloaded successfully", "label.success.PreviewDocumentNotAvailable": "File you are trying to view is not available.", "label.success.UpdateTeam": "Team list updated successfully.", "label.success.DeleteTask": "Task deleted successfully.", "label.success.UpdateTask": "Task updated successfully.", "label.success.Request": "Request submitted successfully.", "label.success.UploadAsset": "Data Model uploaded successfully.", "label.success.GenerateDocument": "Document generated successfully.", "label.success.RequestSent": "Document request sent successfully.", "label.success.SharedStage": "Shared stage has been recalled successfully.", "label.success.ShareOnLink": "Success! Stage has been shared.", "label.success.ShareOnLinkSection": "Success! Section has been shared.", "label.success.ShareLink": "Secure link has sent successfully.", "label.success.configuration": "'Financial configuration downloaded successfully'", "label.success.DealChange": "lead has been changed successfully.", "label.success.SaveQuery": "Query have been saved successfully", "label.success.DeleteQuery": "Query deleted successfully", "label.success.SaveSearch": "Please save the search to proceed", "label.success.BulkMovement": "Bulk movement for deals request has been submitted successfully", "label.success.SubmitApplication": "Applications submitted for Loan account creation", "label.success.Password": "Password changed successfully", "label.success.UpdateDetails": "Details updated Successfully", "label.success.Extension": "Selected Rules have been assigned to the Extension", "label.success.UpdateExtension": "Extension Details Updated successfully", "label.success.Entity": "Selected Rules have been assigned to the Entity", "label.success.Company": "Company Added Succesfully", "label.success.CompanyDetails": "Company details Updated successfully.", "label.success.AddPerson": " Added Succesfully", "label.success.PersonDetails": "Person details updated successfully.", "label.success.Dashboard": " Dashboard Saved successfully", "label.success.DeleteDeal": " Deal Deleted successfully", "label.success.CreateMeeting": "Meeting created successfully.", "label.success.DownloadFile": "File downloaded successfully.", "label.success.DealAnalysis": "Deal analysis is successfully downloaded as PDF.", "label.success.UpdateAnalysis": "Deal Analysis updated successfully", "label.success.UpdateLabel": " Label Details Updated Successfully.", "label.success.DeleteLabel": "Label Details Deleted successfully", "label.success.UpdateItem": "Item Updated Successfully", "label.success.AddItem": "Item Added Successfully", "label.success.DeleteItem": "Item Deleted Successfully", "label.success.AssetClone": " Data Model cloned Successfully", "label.success.AssetTemplate": "Data Model template downloaded successfully.", "label.success.ItemDetails": "Section item details saved successfully.", "label.success.ItemSave": " Stage item details saved successfully.", "label.success.Rules": " Selected Rules have been assigned to the stage", "label.success.UpdateBP": "Request submitted to delete Business process and associated links.", "label.success.BPversion": " Latest Data Model Version has been Added To Business process", "label.success.DeleteBP": " Business Process Deleted Successfully", "label.success.CloneBP": "  Business Process cloned Successfully", "label.success.UploadTemplate": "Template uploaded successfully.", "label.success.UpdateDocument": " Document List updated successfully.", "label.success.DeleteTemplate": "Template deleted successfully.", "label.success.deleteDocument": "Document List deleted successfully.", "label.success.DownloadTemplate": "Template downloaded successfully.", "label.success.UpdateEntity": "Entity Details Updated successfully.", "label.success.DeleteExtension": "Extension Details Deleted successfully", "label.success.UploadItems": "Item Details Updated Successfully", "label.success.UpgradeDataModel": "Data Model Upgraded for the extension", "label.success.AddDataModel": "Data Model added for the extension", "label.success.DataModelEntity": "Data Model Upgraded for the entity", "label.success.AddDataModelEntity": "Data Model added for the entity", "label.success.UpgradeRecords": "Records upgraded with latest extension definition.", "label.success.UpgradeRecordsEntity": "Records upgraded with latest entity  definition.", "label.success.Sidebar": "Personalised configuration is assigned to your process.", "label.success.AddUser": "User added successfully.", "label.success.UpdateUser": "User updated successfully.", "label.success.DeleteUser": "User deleted successfully.", "label.success.CreateRecord": "Record created Successfully.", "label.success.UpdateRecord": "Record updated Successfully.", "label.success.Payment": "Payment was succesful", "label.success.UpdateNote": "Note updated successfully", "label.success.DeleteNote": "Note Deleted successfully", "label.success.AddNote": "Note added successfully", "label.success.CreateTask": "Task created successfully.", "label.success.AddDocument": "Document Added successfully.", "label.success.DeleteEntity": "Request submitted to delete entity and associated links.", "label.success.UpdateRecords": "Request submitted to update records with latest version of entity.", "label.success.Configuration": "Configuration is updated for tenant.", "label.success.DealScore": "Deal Score updated successfully", "label.success.addMenu": "<PERSON><PERSON> Added Succesfully", "label.success.menuQuery": "Query added successfully", "label.success.menuEditQuery": "Query updated successfully", "label.success.deleteMenu": "<PERSON><PERSON> deleted successfully", "label.success.deleteSubMenu": "Sub Menu deleted successfully", "label.success.addSubMenu": "Sub Menu added successfully", "label.success.asset.name": "Data Model name updated successfully", "label.success.entity.name": "Entity name updated successfully", "label.success.dashboardName": "Dashboard Name updated successfully.", "label.button.manageItemIntable": "Manage Item", "label.header.addMenu": "Add Navigation", "label.field.menuName": "<PERSON>u Name", "label.menu.addQuery": "Edit pad to add Filter Query", "label.header.addSubMenu": "Add Sub Menu", "label.success.menuName": "Menu name updated successfully", "label.success.menuConfiguration": "Top Menu Count updated successfully", "label.success.subMenuName": "Sub Menu name updated successfully", "label.success.tenantAdded": "Module configuration added successfully", "label.success.tenantupdated": "Module configuration updated successfully", "label.success.applicationAdded": "Application configuration added successfully", "label.success.applicationupdated": "Application configuration updated successfully", "label.success.envPropertiesUpdated": "Environment properties updated successfully", "label.success.envPropertiesDeleted": "Environment properties deleted successfully", "label.success.addNewEnv": "New environment added successfully", "label.error.validURL": "Please enter a valid website URL", "label.field.HideRule": "Hide Rule", "label.field.ReadOnly": "Read Only (Disable) Rule", "label.field.ValidateRule": "Validate Rule", "label.field.ValueRule": "Value Rule", "label.field.selectStageclone": "Select Stage to clone", "label.theader.length": "Masking Length", "label.theader.direction": "Masking Direction", "label.title.panel.shareStage": "Share On Link", "label.toggle.ShareSteage": "Share Stage", "label.success.CloneDashboard": "Request submitted for cloning the dashboard", "label.materror.user": "Please select user", "label.header.communication": "Communication", "label.option.todaywithTime": "Today With Current Time", "label.field.SectionLabel": "Sections", "label.field.ShareEmail": "Email", "label.field.searchSectionLabel": "Search Tab for Section", "label.filed.Presetvalue": "This is a Preconfigured value.", "label.field.searchRule": "Search Rule", "label.error.configTemplate": "Please configure template to generate document", "label.warning.template": "Template has been modified. Do you wish to regenerate the document?", "label.warning.dataModelLinkedBP": "This data model is linked to a business process. Deleting it may impact business process configurations or workflow rules.", "label.warning.impacts": "Please assess these impacts carefully before deleting.", "label.warning.delete.confirmation": "Are you sure you want to delete this item?", "label.error.document": "File you are trying to download is not available.", "label.field.addressUrl": "Address Url", "label.error.validUrl": "Please enter valid url", "label.field.externalApiURL": "External Api URL", "label.field.fetchDataRule": "Fetch data rule", "label.field.mapDataRule": "Map data rule", "label.field.showInGroupOfFields": "Map data as group of fields", "label.field.configurationType": "Configuration Type", "label.field.sourceAreaName": "Source Area Name", "label.field.sourceTenantName": "Source Tenant Name", "label.field.tenantName": "Tenant Name", "label.field.businessProcessName": "Source Business Process Name", "label.field.businessProcessNameUpdate": "Business Process Name", "label.field.entityName": "Source Entity Name", "label.field.entityNameUpdate": "Entity Name", "label.button.saveAndProceed": "Save And Proceed", "label.label.createRule": "Add Workflow?", "label.label.createDataModel": "Add Data Model?", "label.label.updateRule": "Update Workflow?", "label.label.updateDataModel": "Update Data Model?", "error.message.unexpected": "An unexpected error occured! Please try again.", "dialog.addConfiguration.heading": "Add Configuration", "dialog.addConfiguration.subheading": "What would you like to do?", "text.noResultsFound": "No matching results found", "label.field.addRulesList": "Add Workflows", "label.field.updateRulesList": "Update Workflows", "dialog.selectedRulesList.title": "Workflow List", "dialog.selectedRulesList.subheading": "Below are the Rules selected for your Configuration Setup", "error.500.default.message": "Server Error. Please try again later. If the issue persists, contact your administrator or support team.", "error.400.default.message": "There seems to be an issue with the information provided. Please review and try again.", "error.403.default.message": "Access is restricted. Ensure you have valid credentials or permissions. If you're unable to proceed, contact your administrator or support team.", "error.404.default.message": "Error 404: Not Found!", "error.502.default.message": "The service is temporarily unavailable. Please try again later.", "error.unknown.default.message": "An Unexpected error occurred!", "hint.message.noRulesAvailable": "No Workflows available.", "dialog.addConfigurationConfirmation.mode.duplicate": "The configuration replication request will use the following values.", "dialog.addConfigurationConfirmation.mode.update": "The configuration update request will use the following values.", "dialog.addConfigurationConfirmation.mode.rules": "The workflow configuration update request will use the following values .", "dialog.cancelConfigurationConfirmation": "This will permanently erase the request you were creating. Are you sure you want to proceed ?", "stepper.previousStepConfirmation": "Going back and making changes in a previous stage will overwrite the data in your current stage. This action cannot be undone. Are you sure you want to proceed ?", "dialog.deleteEnvironmentConfirmation": "This action will permanently delete your request. Are you sure you want to proceed ?", "label.change.stage": "Change Stage?", "label.cancel.request": "Cancel Request?", "label.delete.environment": "Delete Environment?", "label.field.targetBusinessProcessName": "Target Business Process", "label.field.targetEntityName": "Target Entity", "label.tooltip.addRules": "These workflows in source are available for replication to target", "label.tooltip.updateRules": "Lists replicated workflows in the target for update", "label.title.updateRuleList": "Update Workflow List", "label.title.addRuleList": "Workflows to Add to Target Area", "label.subTitle.updateRuleList": "Displays the workflows selected for updates, allowing you to review and confirm", "label.subTitle.addRuleList": "Review the selected workflows to be added to the target area.", "dialog.title.workflowUpdate": "Workflow Update", "dialog.title.updateConfiguration": "Update Configuration", "dialog.title.createConfiguration": "Replicate Configuration", "label.title.envDetails": "Environment Details", "label.title.addEnvironment": "Add Environment", "label.field.environmentName": "Environment Name", "label.field.keycloakHost": "Keycloak Host", "label.field.provisionerUrl": "Provisioner URL", "label.field.workflowUrl": "Workflow URL", "label.field.zcpUrl": "ZCP URL", "label.title.urlDetails": "<PERSON>rl <PERSON>", "label.button.WFE": "Workflows", "label.theader.SyncWFE": "Sync", "label.header.dealIdentifierDisplay": "Deal Identifier Display Name", "label.subHeader.displayName": "Display Name", "label.reports.reportQuery": "Report Query", "label.title.statusConfiguration": "Status Filter Configuration", "label.title.statusConfigurationList": "Deal List Status Labels", "label.title.statusConfigurationCTA": "Approve & Reject CTA Labels", "label.title.add.query": "Add Query", "label.title.edit.query": "Edit Query", "label.field.queryName": "Query Name", "label.add.query": "Query Added Succesfully!", "label.update.query": "Query Updated Succesfully!", "label.delete.query": "Query Deleted Succesfully!", "label.warning.queryLinked": "This query may be referenced in one or more API configurations. Deleting it could disrupt the functionality of associated APIs.", "label.warning.query.impacts": "Please ensure it is not in use before proceeding.", "label.warning.delete.query": "Are you sure you want to delete it?", "---------------------------------------------Common literals---------------------------------------------------": "", "label.materror.nameValidation": "Only alphabets, numbers, spaces, hyphens, and underscores are allowed.", "label.notification.error.invalidForm": "Please fill in all the required fields with valid data.", "hint.currencyDecimalDisabled": "Decimal values are not allowed.", "label.updateDataModel": "Update Data Model?", "dialog.updateDataModelConfirmation": "Are You Sure You Want To Proceed With This Change?", "dialog.updateDataModelConfirmationMessage": "This Action May Affect Existing Configuration And Form Behaviour.", "label.datamodel.warning": "You're About To Update The Data Model Used In This Business Process.", "label.button.switchToOldConfiguration": "Switch to old configuration", "error.file.upload": "File Upload Failed! Please try again.", "label.file.delete.error": "Failed to delete the file, Please try again.", "error.message.userGuide.incorrectFileFormat": "Only PDF documents are allowed.", "label.field.selectCurrency.decimalPlaces": "Decimal Places", "label.selectCurrency.allowDecimalPlaces": "Allow Decimal Places?", "utilities.message.switchToNewUI": "Turn this toggle on to Switch to new Utilities UI for a better experience.", "utilities.message.switchToOldUI": "Turn this toggle off   to Switch to the old Utilities UI.", "utilities.tooltip.brandIdentity": "This section allows you to define the visual identity of your workspace. Select a logo and customize your title as they'll appear across the platform.", "utilities.tooltip.applicationDetails": "This section lets you configure global application settings, including environment URLs, display formats, and helpful documentation — ensuring consistency across your platform.", "utilities.tooltip.entityDetails": "Set default company and person entities used across forms, processes, and approvals.", "utilities.tooltip.sidebarLiterals": "Customize the labels that appear in your application's sidebar for different roles. These changes help tailor navigation to suit user-specific terminology or workflows.", "utilities.tooltip.topBarNavigation": "Define the menu items that will appear in the top navigation of your app.", "utilities.tooltip.label": "Configure labels and literals used throughout the application to align with your organization's terminology and improve user comprehension.", "utilities.tooltip.newUI": "Prefer the Classic View?", "utilities.tooltip.newUISub": "You can switch to the classic Utilities UI if required. We recommend continuing with the new UI for the better experience.", "utilities.tooltip.toggle.selectRoles": "Manage sidebar menu display by selecting or deselecting roles", "utilities.tooltip.toggle.hideRuleEnabled": "Hide rule applied - Sidebar display is controlled by rule configuration.", "utilities.tooltip.toggle.hideRuleDisabled": "No hide rule applied - Manage sidebar menu display through role configuration.", "utilities.tooltip.toggle.applicatioNavigationIcon": "Switch the toggle on to display the navigation icon on top bar.", "utilities.tooltip.toggle.testEnvIndicator": "Switch the toggle on to display the environment indicator.", "utilities.hint.testEnvIndicator": "Enter the Environment Name", "utilities.tooltip.searchBasedQueries": "Queries are processed in real-time to perform data based operations on your input.", "label.workspace": "Workspace", "label.workspaceConfiguration": "Workspace Configuration", "label.noItemsFound": "No items found.", "label.newCategoryName": "New Work Group", "label.categoryOverview": "Work Group Overview", "label.workspace.subTitle.assignItems": "Assign at least one Work Item from below to proceed.", "label.recentlyViewed": "Recently Viewed", "label.workGroupName": "Work Group Name", "label.workGroupDescription": "Work Group Description", "label.selectRoles": "Select Roles", "label.uncategorized": "Uncategorized", "label.message.noItemsFound": "No Items found"}