textarea {
  resize: both;
}

textarea.vertical {
  resize: vertical;
}

textarea.horizontal {
  resize: vertical;
}

textarea.noresize {
  resize: none;
}

.full-input input,
.full-input select,
.full-input textarea {
  width: 100%;
}

.normal input,
.normal select,
.normal textarea,
.full-input [type="checkbox"],
.full-input [type="radio"] {
  width: auto;
}

.mat-tab-body-content {
  margin-bottom: 20px;
}

.custom-mat-input-style {

  .mat-mdc-text-field-wrapper:not(.mdc-text-field--disabled) {
    mat-label {
      color: var(--input-text-text-color);
    }

    border: 1.5px solid var(--input-border-color);

    &:hover {
      background-color: var(--input-hover-background-color);
      border: 1.5px solid var(--input-hover-background-color);

      mat-label,
      mat-icon,
      .mat-mdc-form-field-icon-prefix,
      .mdc-text-field__input,
      .mat-mdc-select-arrow,
      .mat-mdc-select-value-text,
      .mat-mdc-icon-button,
      .mdc-form-field > label,
      .custom-input-info-text {
        color: var(--input-hover-text-color);
      }

      .mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: var(--input-hover-text-color);
      }
    }

    &:has(input:focus) {
      background-color: var(--input-hover-background-color);
      border: 1.5px solid var(--input-active-border-color);

      mat-icon,
      .mat-mdc-form-field-icon-prefix,
      .mdc-text-field__input,
      .mat-mdc-select-arrow,
      .mat-mdc-select-value-text,
      .mat-mdc-icon-button,
      .mdc-form-field > label,
      .custom-input-info-text {
        color: var(--input-hover-text-color);
      }

      mat-label {
        color: var(--input-hover-text-color);
      }

      .mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: var(--input-hover-text-color);
      }
    }
  }

  .mat-mdc-text-field-wrapper.mdc-text-field--invalid:not(.mdc-text-field--disabled) {
    border: 1.5px solid var(--input-invalid-border-color);
    background: var(--input-invalid-background-color);

    mat-label,
    .mdc-text-field__input,
    .mat-mdc-select-arrow,
    .mat-mdc-select-value-text,
    .mat-mdc-icon-button,
    .mat-mdc-form-field-icon-prefix {
      color: var(--input-invalid-border-color);
    }
  }

  .mdc-text-field--filled.mdc-text-field--disabled {
    background: var(--input-disabled-background-color);

    mat-label {
      color: var(--input-disabled-text-color);
    }
  }

  .mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused {
    border: 1.5px solid var(--input-active-border-color);
  }

  .mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,
  .mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above {
    color: var(--input-text-text-color);
  }

  .mat-mdc-form-field-error {
    color: var(--input-invalid-border-color);
  }
}
