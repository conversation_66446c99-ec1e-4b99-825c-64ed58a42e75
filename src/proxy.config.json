{"/identity": {"target": "https://devtest.dev.originate.centelon.com/", "secure": false, "pathRewrite": {"^/identity": "/identity"}, "changeOrigin": true, "logLevel": "debug"}, "/originate": {"target": "https://devtest.dev.originate.centelon.com/", "secure": false, "pathRewrite": {"^/originate": "/originate"}, "changeOrigin": true, "logLevel": "debug"}, "/dms": {"target": "https://devtest.dev.originate.centelon.com/", "secure": false, "pathRewrite": {"^/dms": "/dms"}, "changeOrigin": true, "logLevel": "debug"}, "/notification": {"target": "https://devtest.dev.originate.centelon.com/", "secure": false, "pathRewrite": {"^/notification": "/notification"}, "changeOrigin": true, "logLevel": "debug"}, "/workflow": {"target": "https://devtest.dev.originate.centelon.com/:2098/originate/v1/", "secure": false, "pathRewrite": {"^/workflow": "/workflow"}, "changeOrigin": true, "logLevel": "debug"}, "/assettypedefinition": {"target": "https://devtest.dev.originate.centelon.com/:2098/originate/v1/asset", "secure": false, "pathRewrite": {"^/assettypedefinition": "/assettypedefinition"}, "changeOrigin": true, "logLevel": "debug"}, "/integration": {"target": "https://dev-digionboard.centelon.com/", "secure": false, "pathRewrite": {"^/integration": "/integration"}, "changeOrigin": true, "logLevel": "debug"}, "/auth": {"target": "https://auth.dev.centelon.com/", "secure": false, "pathRewrite": {"^/auth": "/auth"}, "changeOrigin": true, "logLevel": "debug"}, "/index.php": {"target": "https://matomo.dev.centelon.com/", "secure": false, "pathRewrite": {"^/index.php": "/index.php"}, "changeOrigin": true, "logLevel": "debug"}, "/addresses": {"target": "http://addresser.centelon.com:8080/", "secure": false, "pathRewrite": {"^/addresses": "/addresses"}, "changeOrigin": true, "logLevel": "debug"}}