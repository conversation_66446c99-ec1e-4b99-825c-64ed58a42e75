
export const applicationSummaryTabs = [
   { index: 0, id: 1, label: 'Details', icon: 'analytics', isDisable: false, link: '/application-summary/details'},
   // { index: 1, id: 2, label: 'Associated Entities', icon: 'assignment', isDisable: false, link: '/application-summary/associated-entities' },
   // { index: 2, id: 3, label: 'Financials', icon: 'assignment', isDisable: false, link: '/application-summary/financials' },
   { index: 3, id: 4, label: 'Score', icon: 'readiness_score', isDisable: false, link: '/application-summary/dealAnalysis' },
   { index: 4, id: 5, label: 'Documents', icon: 'description', isDisable: false, link: '/application-summary/documents'},
   // { index: 5, id: 6, label: 'Communications', icon: 'chat', isDisable: true, link: '/application-summary/communication' },
  // { index: 6, id: 7, label: 'Tasks', icon: 'task_alt', isDisable: false, link: '/application-summary/task'},
   // { index: 7, id: 8, label: 'Approval', icon: 'assignment', isDisable: false, link: '/application-summary/approval' },
   { index: 8, id: 9, label: 'Stage History', icon: 'history', isDisable: false, link: '/application-summary/stage-history' },


]

export const companyDetailsTabs = [

   { index: 0, id: 1, label: 'Details', icon: 'graphic_eq', link: 'entity/viewcompany/detail/' },
   { index: 1, id: 2, label: 'Deal', icon: 'graphic_eq', link: 'entity/viewcompany/associated-deals/' },
   { index: 2, id: 3, label: 'Persons', icon: 'graphic_eq', link: 'entity/viewcompany/associated-persons/' },

]

export const personDetailsTabs = [

   { index: 0, id: 1, label: 'Details', icon: 'graphic_eq', link: 'entity/viewperson/detail/' },
   { index: 1, id: 2, label: 'Deal', icon: 'graphic_eq', link: 'entity/viewperson/associated-deals/' },


]

export const listOfDocumentTypes = [

"Other",

"Pitch Deck",

"Term sheet from another investor",

"Existing share subscription Agreement",

"Existing shareholder Agreement",

"Share holding pattern on company letter head",

"Stock options details",

"Company’s charter documents – Articles",

"Company’s charter documents – Memo",

"Founder’s PAN",

"Founder’s DIN",

"Financial Projections – Cashflow",

"Financial Projections – all",

"Operational metrics",

"Existing Cap table",

"Company’s calculation of Market size",

"Company’s view on competitive landscape",

"Historical Financial statements (audited)",

"Certification of Incorporation",

"GST Certificate",

"Company PAN",

"Patents",

"Financial Due Diligence Report",

"Techincal Due Diligence Report",

"Legal Due Diligence Report" ,

"TAX Due Diligence Report"  ,

"Investment Committee Report",

"Minutes of Meeting",

"Share subscription Agreement",

"Shareholder Agreement",

"Reserved Matters",
"Annual return with ROC copy" ,

"Legal Documents",

"All loan sanction letters",

"Auditors Report if PVT LTD",

"Directors Report if PVT LTD",

"Balance sheet",

"Business credit score",

"Company profile"  ,

"Details of board of directors",

"Details of transfer of ownership (sale/ transfer of stock options)",

"Disclosure of other debt",

"KYC of the firm (Gumastha Licensce/Registration certificate)",

"KYC of the Proprietor",

"Latest Income Tax Return /Personal and business tax returns",

"Latest Profession Tax Return",

"MOA/AOA",

"MSME - Provisional",

"MSME - Final",

"One year bank statement of Director",

"One year bank statement of Partner",

"One year bank statement of Proprietor" ,

"Ownership and affiliations",

"Past and future projects",

"Project Photograph",

"Processing fee cheque",

"Project report",

"Property related documents",

"Real estate purchase agreement" ,

"Trust Deed"

]

export const listOfDocumentTypesForRequest = [
   { "documentTypeName": "Pitch Deck" },
   { "documentTypeName": "Term sheet from another investor" },

   { "documentTypeName": "Existing Share Subscription Agreements and Shareholder’s Agreements" },

   { "documentTypeName": "Company’s charter documents(Articles of Association, Memorandum of Association)" },

   { "documentTypeName": "Founders’ PAN / DIN details" },

   { "documentTypeName": "Financial projections" },

   { "documentTypeName": "Operational metrics" },

   { "documentTypeName": "Company’s calculation of market size" },

   { "documentTypeName": "Company’s view on competitive landscape" },

   { "documentTypeName": "Historical financial statements(audited)" },

   { "documentTypeName": "Existing cap table" },

   { "documentTypeName": "Certificate of Incorporation, GST / PAN details" },

   { "documentTypeName": "Patents(if any)" },
   { "documentTypeName": "Term Sheet" },
   { "documentTypeName": "Financial DD Report" },
   { "documentTypeName": "Legal DD Report" },
   { "documentTypeName": "Tech DD Report" },
   { "documentTypeName": "Tax DD Report" },
   { "documentTypeName": "IC Report" },
   { "documentTypeName": "SSA" },
   { "documentTypeName": "SHA" },
   { "documentTypeName": "Reserved Matters" },
   { "documentTypeName": "Tax Documents" },
   { "documentTypeName": "Legal Documents" },
   { "documentTypeName": "Other" },
]




