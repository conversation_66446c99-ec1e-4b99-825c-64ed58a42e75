@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
    $color-config: mat.get-color-config($theme);
    $background-palette: map.get($color-config, 'background');
    $warn-palette: map.get($color-config, 'warn');


    .asset-list-containter{
        th{
          background-color: var(--container-color);
        }
       .delete-icon:hover{
          color: mat.get-color-from-palette($warn-palette, 400) !important;
        }
    }


}


@mixin typography($theme) {
    $typography-config: mat.get-typography-config($theme);
  }

@mixin theme($theme) {
    $color-config: mat.get-color-config($theme);
    @if $color-config != null {
      @include color($theme);
    }
  
    $typography-config: mat.get-typography-config($theme);
    @if $typography-config != null {
      @include typography($theme);
    }
  }