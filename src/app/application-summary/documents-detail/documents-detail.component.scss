.actionBtns {
  margin: 0 1% 0 0;
  float: right;
}
.actionBtnsContainer {
  margin: 1% 0;
  // ::ng-deep .mat-form-field-wrapper {
  //   padding-bottom: 0 !important;
  //   background-color: white !important;
  // }

  // .mat-form-field-appearance-outline .mat-form-field-wrapper {
  //   margin: 0 !important;
  // }
}

.mb-5{
 margin-bottom: 5%
}

.labelChipsInTable {
  width: 50% !important;
  padding-right: 50px !important;
  display: inline-flex;
  .mat-mdc-standard-chip {
    padding: 2px 5px !important;
    min-height: 22px !important;
    height: auto;
  }
}

.activeFilter{
  background: lightgray;
  border-bottom: 3px solid rgb(0, 0, 255);

}

.documentsTitle {
  margin-bottom: 0.5%;
  display: inline-flex; 
  width: 100%;
}

.selectdocumentTypeList {
  font-size: 14px;
  font-weight: 500 !important;
  /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
  ::ng-deep .mat-select-value {
    max-width: fit-content;
    width: fit-content;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 1% !important;
  }
}

.selectDocumentListInput {
  font-size: 14px;
  font-weight: 500;
}

.w-17 {
  width: 17%;
}

.w-9 {
  width: 9%;
}
.w-12 {
  width: 12%;
}

.w-22 {
  width: 22%;
}

.w-25 {
  width: 25%;
}
.w-29 {
  width: 29%;
}
.w-15{
  width: 15%;
}

.w-18{
  width: 18%;
}

/* TODO(mdc-migration): The following rule targets internal classes of paginator that may no longer apply for the MDC version.*/
.mat-paginator-center {
  display: flex;
  justify-content: center;
}

// .subTitle {
//   margin: 0;
//   font-size: 20px;
//   font-weight: 500;
// }

// {
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
::ng-deep .searchInput .mat-form-field-infix {
  // padding: .5em 0;
  border-top: 0.4375em solid transparent !important;
  // }
}



.documentNames th, td {
  overflow: hidden;
  width:auto;
  text-overflow: ellipsis;
  white-space: nowrap;
}

td > span.ellipsis {
  text-overflow: ellipsis; 
  overflow: hidden; 
  white-space: nowrap;
}

.docDetailNavBarSearchField{
  font-weight: 500;
  width: 100%;
}

.docDetailNavBarSearchIcon{
  margin-bottom: -10%;
}

.docDetailTagsText{
  margin: 0.2% 2%; 
  display: flex;
  align-items: baseline;
  overflow:auto;
  width:100%;
}

.docDetailsMatChipList{
  font-size: 12px;
  margin: 1% 0;
}

.docDetailsMatChipListText{
  font-size: 12px;
  text-transform: uppercase;
  cursor: pointer;
}

.docDetailsTableRowText{
  text-align: inherit !important;
}

.docDetailsLoader{
  margin : 5% 0;
}
.mt-1{
  margin-top: 1%;
  height: 150px !important;
}
.ml-13{
  margin-left: 12% !important; 
}

.iconPosition{
  position: relative;
  top: 1vh;
}


//New UI

.document-details-container{
  .asset-actions-containter{
    padding: 15px 10px ;
    .search-field{
      mat-form-field{
        width: 600px;
      }
    }
    .search-field-doc{
      margin-top:1%;
      mat-form-field{
        width: 600px;
      }
    }
  }

  .asset-list-containter{
    margin-bottom: 15px;
      .center{
        text-align: center !important;
      }
      .table-spinner{
        margin-top:5%;
        display: flex;
        justify-content: center;
      }
    }
  .actionBtnsContainer {
    margin: 4% 0;
   
  }
 
  .action-buttons{
    padding: 15px;
  }
  .docDetailNavBarSearchField{
    font-weight: 500;
    width: 100%;
  }
}