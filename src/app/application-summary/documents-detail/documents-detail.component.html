<ng-container *ngIf="!useNewThemeUI">

  <div fxLayout="row wrap" fxLayoutGap="4px" class="actionBtnsContainer">

    <div fxFlex="29%" fxFlex.md="20%" fxFlex.xs="40%" fxFlex.sm="48%">
      <mat-form-field class="searchInput docDetailNavBarSearchField">
        <mat-icon matSuffix class="docDetailNavBarSearchIcon">search</mat-icon>
        <input matInput (keyup)="applyFilter($event)" [(ngModel)]="searchKey"
          placeholder="Search Document" #input>
      </mat-form-field>
    </div>

    <div fxFlex="35%" fxFlex.md="76%" fxFlex.xs="57%" fxFlex.sm="48%" fxFlex.lg="68%"
      fxFlex.xl="69%">
      <button mat-raised-button class="actionBtns green" [disabled]="disableWhenReject"
        (click)="openRequestDialog(type)"
        *ifHasPermission="DEAL_RESOURCE.Request_Doc; scope:'CHANGE'">
        {{"label.button.requestDocument"|literal}}
      </button>
      <button mat-raised-button class="actionBtns green" [disabled]="disableWhenReject"
        (click)="openGenrateDocumentDialog(type)"
        *ifHasPermission="DEAL_RESOURCE.Generate_Doc; scope:'CHANGE'">
        {{"label.button.generateDocument"|literal}}
      </button>
      <button mat-raised-button class="actionBtns green" [disabled]="disableWhenReject"
        (click)="openUploadDocumentDialog(type)"
        *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
        {{"label.button.uploadDocument"|literal}}
      </button>

    </div>





  </div>
  <mat-card appearance="outlined" class="mat-card-top-border mb-5">
    <mat-card-content class="documentsTitle">


      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="50%" fxFlex.md="76%" fxFlex.xs="57%" fxFlex.sm="48%" fxFlex.lg="68%"
          fxFlex.xl="69%">
          <h2 class="table-header ">{{"label.title.documents"|literal}}</h2>
        </div>
      </div>

      <div fxFlex="50%" class="docDetailTagsText ml-13 ">
        <h3 class="table-header">{{"label.title.tags"|literal}}: </h3>

        <ng-container *ngFor="let tag of  tags">
          <div class="example-button-row">

            <ng-container>
              <a class="filterBtns" mat-flat-button [class.activeFilter]="isSelectedTag(tag)"
                (click)="tagWiseFilter(tag)">{{tag}}</a>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </mat-card-content>
    <mat-divider class="dividerMargin"></mat-divider>
    <mat-card-content>
      <div class="selectdocumentTypeList">
        <mat-form-field class="selectDocumentListInput">

          <mat-select [(ngModel)]="selectedDocumentType"
            (selectionChange)="onSelectDocumentType($event.value); pageIndex = 0"
            [ngModelOptions]="{standalone: true}">
            <mat-option *ngFor="let list of documentTypeList" [value]="list.value">
              {{list.viewValue}}
            </mat-option>
          </mat-select>

        </mat-form-field>
      </div>
      <div
        class="task-table-container mat-elevation-z0 mat-table-width task-table-container">

        <table mat-table [dataSource]="dataSource " matSort
          class="  mat-elevation-z0 mat-table-width">



          <!--  Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef class="  w-22">
              {{"label.table.title.name"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row"> <span
                [matTooltip]="row.fileName">{{stringWithEllipsis(row.fileName)}}</span>
              <span class="labelChipsInTable">
                <mat-chip-listbox aria-orientation="vertical" *ngIf="row && row.tags?.length !== 0"
                  #chipList class="docDetailsMatChipList">
                  <mat-chip-option class="docDetailsMatChipListText"
                    *ngFor="let tag of row.tags; let i = index">
                    {{tag}}
                  </mat-chip-option>
                </mat-chip-listbox>
              </span>

            </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="dealRequestDocumentList">
            <th mat-header-cell *matHeaderCellDef class="  w-29"> Name </th>
            <td mat-cell class=" " *matCellDef="let row"> <span class=""
                [matTooltip]="getDocumentNamesOnHover(row.docType)">{{getDocumentTypes(row.docType)}}</span>
            </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="contactName">
            <th mat-header-cell *matHeaderCellDef class="  w-18">
              {{"label.table.title.contact"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row">
              <span *ngIf="this.selectedDocumentType !== 'REQUEST'">{{row.contactName}}</span>
              <span *ngIf="this.selectedDocumentType === 'REQUEST'">{{row.contactName ?
                row.contactName : row.email}}</span>
            </td>

          </ng-container>



          <!--  Column -->
          <ng-container matColumnDef="createdDate">
            <th mat-header-cell *matHeaderCellDef class="  w-22">
              {{"label.table.title.createdDate"|literal}}</th>
            <td mat-cell class=" " *matCellDef="let row"> {{row.createdDate | date}} </td>
          </ng-container>


          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef class="  w-15">
              {{"label.table.title.status"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row">

              <span *ngIf="row.status === 'LINK_EXPIRED'">Secure Link Expired</span>


              <span *ngIf="row.status === 'REQUEST_WIP'">Secure Link Sent</span>


              <span *ngIf="row.status === 'REQUEST_COMPLETED'"> Documents Uploaded </span>
            </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="documentTitle">
            <th mat-header-cell *matHeaderCellDef class="  w-22">
              {{"label.table.title.documentType"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row"> <span
                [matTooltip]="row.documentTitle">{{stringWithEllipsis(row.documentTitle)}}</span>
            </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="size">
            <th mat-header-cell *matHeaderCellDef class="  w-9">
              {{"label.table.title.size"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row"> {{row.size }} </td>
          </ng-container>


          <!--  Column -->
          <ng-container matColumnDef="updated">
            <th mat-header-cell *matHeaderCellDef class="  w-17">
              {{"label.table.title.updated"|literal}} </th>
            <td mat-cell class=" " *matCellDef="let row"><span
                *ngIf="row.modifiedDate">{{row.modifiedDate | date}} by
                {{row.modifiedBy}}</span>
              <span *ngIf="!(row.modifiedDate)"> {{row.createdDate | date}} </span>
            </td>

          </ng-container>

          <!-- Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef class="w-12  "> </th>
            <td class=" " mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">

              <span *ifHasPermission="DEAL_RESOURCE.Preview_Doc; scope:'READ'">
                <button
                  *ngIf="element.type === 'UPLOAD' && checkPreview(element) || element.type === 'GENERATED' && checkPreview(element) "
                  mat-icon-button class="mat-icon-buttons-in-action-column green"
                  (click)="previewFile(element.fileName , element.randomSrlNum , element.type)">
                  <mat-icon class="pointer icon-white">
                    remove_red_eye
                  </mat-icon>
                </button>
              </span>

              <span *ifHasPermission="DEAL_RESOURCE.Download_Doc; scope:'READ'">
                <button mat-icon-button class="green" *ngIf="element.type === 'UPLOAD'"
                  (click)="downloadFile(element.fileName , element.randomSrlNum , element.type)">
                  <mat-icon class="pointer icon-white">
                    get_app
                  </mat-icon>
                </button>
              </span>

              <span *ifHasPermission="DEAL_RESOURCE.Generate_Doc; scope:'READ'">
                <button mat-icon-button class="green" *ngIf="element.type === 'GENERATED'"
                  (click)="downloadFile(element.fileName , element.randomSrlNum , element.type)">
                  <mat-icon class="pointer icon-white">
                    get_app
                  </mat-icon>
                </button>
              </span>

              <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
                <button mat-icon-button *ngIf="element.type === 'UPLOAD'" class="blue"
                  [ngClass]="disableWhenReject ? 'gray' :'blue'" [disabled]="disableWhenReject"
                  (click)="openUploadDocumentDialog(element.documentTitle)">
                  <mat-icon class="pointer icon-white">
                    publish
                  </mat-icon>
                </button>
              </span>

              <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
                <button *ngIf="element.type === 'UPLOAD'" mat-icon-button class="blue"
                  [ngClass]="disableWhenReject ? 'gray' :'blue'" [disabled]="disableWhenReject"
                  (click)="openEditDialog(element)">
                  <mat-icon aria-label="Edit">
                    edit
                  </mat-icon>
                </button>
              </span>

              <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'DELETE'">
                <button *ngIf="element.type === 'UPLOAD'" mat-icon-button class="red"
                  [ngClass]="disableWhenReject ? 'gray' :'red'" [disabled]="disableWhenReject"
                  (click)="openDeleteDialog(element)">
                  <mat-icon aria-label="Delete">
                    delete
                  </mat-icon>
                </button>
              </span>

              <mat-icon *ngIf="this.selectedDocumentType === 'REQUEST'" class="pointer iconPosition"
                (click)="requestDetails(element.requestId)"
                matTooltip="Request Details">info_outline</mat-icon>
              <span *ifHasPermission="DEAL_RESOURCE.Request_Doc_Resend; scope:'CHANGE'">
                <button *ngIf="this.selectedDocumentType === 'REQUEST'" mat-raised-button
                  class="green" (click)="resendLink(element)"
                  matTooltip="Resend Link to {{element.email}}"
                  [disabled]="element.status ===  'REQUEST_COMPLETED'">
                  RESEND
                </button>
              </span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns;" class="docDetailsTableRowText">
          </tr>
          <tr mat-row class="pointer docDetailsTableRowText"
            *matRowDef="let row; columns: displayedColumns; let i = index; "
            [class.task-row__alternate]="i % 2"></tr>
          <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="4">{{"label.title.noMatchingData"|literal}}</td>
            </tr>
          </div>
        </table>



        <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
          <mat-paginator class="" [pageIndex]="pageIndex" [pageSizeOptions]="[8, 25,50, 100]"
            (page)="handlePage($event)"></mat-paginator>
        </div>
        <div *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner"
          class="mt-1 no-records-found">
          <!-- <mat-card appearance="outlined" class="no-record-card mat-elevation-z0"> {{"label.title.noRecordsFound"|literal}} </mat-card> -->
        </div>
        <div *ngIf="showLoaderSpinner">
          <mat-spinner class="no-record-card"> {{"label.title.noRecordsFound"|literal}}
          </mat-spinner>
        </div>
      </div>

    </mat-card-content>


  </mat-card>
</ng-container>


<div *ngIf="useNewThemeUI" class="document-details-container">

  <div class="asset-actions-containter" fxLayout="row" fxLayoutAlign="space-between">

    <div fxLayoutAlign="start center" class="search-field">
      <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
        <mat-select [(ngModel)]="selectedDocumentType"
          (selectionChange)="onSelectDocumentType($event.value); pageIndex = 0"
          [ngModelOptions]="{standalone: true}">
          <mat-option *ngFor="let list of documentTypeList" [value]="list.value">
            {{list.viewValue}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div *ngIf="!this.searchIconFlag" fxLayoutAlign="end center" class="search-field">
      <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
        <mat-icon matIconPrefix>search</mat-icon>
        <input aria-label="search-document" matInput #input [(ngModel)]="searchKey"
          (keyup)="applyFilter($event,true)" [placeholder]=" 'Search'">
      </mat-form-field>
    </div>

    <div *ngIf="this.searchIconFlag" fxLayoutAlign="end center" fxLayoutGap="10">

      <span>
        <button aria-label="search-btn" mat-icon-button type="button"
          class="colored-icon-button large-icon-button" (click)="searchIcon()"
          matTooltipPosition="above" matTooltipClass="accent-tooltip" matTooltip="Search Document">
          <span class="material-symbols-outlined"> search </span>
        </button>
      </span>

      <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
        <button aria-label="upload-btn" mat-icon-button type="button"
          class="colored-icon-button large-icon-button" [disabled]="disableWhenReject"
          (click)="openUploadDocumentDialog(type)" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Upload Document">
          <span class="material-symbols-outlined"> upload </span>
        </button>
      </span>

      <span *ifHasPermission="DEAL_RESOURCE.Request_Doc; scope:'CHANGE'">
        <button aria-label="approval-delegation-btn" mat-icon-button type="button"
          class="colored-icon-button large-icon-button" [disabled]="disableWhenReject"
          (click)="openRequestDialog(type)" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Request Document">
          <span class="material-symbols-outlined"> approval_delegation </span>
        </button>
      </span>

      <span *ifHasPermission="DEAL_RESOURCE.Generate_Doc; scope:'CHANGE'">
        <button aria-label="add-box-btn" mat-icon-button type="button"
          class="colored-icon-button large-icon-button" [disabled]="disableWhenReject"
          (click)="openGenrateDocumentDialog(type)" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Generate Document">
          <span class="material-symbols-outlined">upload_file</span>
        </button>
      </span>

    </div>

  </div>

  <mat-divider class="dividerMargin"></mat-divider>

  <div class="asset-list-containter">


    <table mat-table [dataSource]="dataSource " matSort
      class="  mat-elevation-z0 mat-table-width">



      <!--  Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef class="  w-22"> {{"label.table.title.name"|literal}}
        </th>
        <td mat-cell class=" " *matCellDef="let row"> <span
            [matTooltip]="row.fileName">{{stringWithEllipsis(row.fileName)}}</span>
          <span class="labelChipsInTable">
            <mat-chip-listbox aria-orientation="vertical" *ngIf="row && row.tags?.length !== 0"
              #chipList class="docDetailsMatChipList">
              <mat-chip-option class="docDetailsMatChipListText"
                *ngFor="let tag of row.tags; let i = index">
                {{tag}}
              </mat-chip-option>
            </mat-chip-listbox>
          </span>

        </td>
      </ng-container>

      <!--  Column -->
      <ng-container matColumnDef="dealRequestDocumentList">
        <th mat-header-cell *matHeaderCellDef class="  w-29"> Name </th>
        <td mat-cell class=" " *matCellDef="let row"> <span class=""
            [matTooltip]="getDocumentNamesOnHover(row.docType)">{{getDocumentTypes(row.docType)}}</span>
        </td>
      </ng-container>

      <!--  Column -->
      <ng-container matColumnDef="contactName">
        <th mat-header-cell *matHeaderCellDef class="  w-18">
          {{"label.table.title.contact"|literal}} </th>
        <td mat-cell class=" " *matCellDef="let row">
          <span *ngIf="this.selectedDocumentType !== 'REQUEST'">{{row.contactName}}</span>
          <span *ngIf="this.selectedDocumentType === 'REQUEST'">{{row.contactName ? row.contactName
            : row.email}}</span>
        </td>

      </ng-container>



      <!--  Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef class="  w-22">
          {{"label.table.title.createdDate"|literal}}</th>
        <td mat-cell class=" " *matCellDef="let row"> {{row.createdDate | date}} </td>
      </ng-container>


      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef class="  w-15"> {{"label.table.title.status"|literal}}
        </th>
        <td mat-cell class=" " *matCellDef="let row">

          <span *ngIf="row.status === 'LINK_EXPIRED'">Secure Link Expired</span>


          <span *ngIf="row.status === 'REQUEST_WIP'">Secure Link Sent</span>


          <span *ngIf="row.status === 'REQUEST_COMPLETED'"> Documents Uploaded </span>
        </td>
      </ng-container>

      <!--  Column -->
      <ng-container matColumnDef="documentTitle">
        <th mat-header-cell *matHeaderCellDef class="  w-22">
          {{"label.table.title.documentType"|literal}} </th>
        <td mat-cell class=" " *matCellDef="let row"> <span
            [matTooltip]="row.documentTitle">{{stringWithEllipsis(row.documentTitle)}}</span> </td>
      </ng-container>

      <!--  Column -->
      <ng-container matColumnDef="size">
        <th mat-header-cell *matHeaderCellDef class="  w-9"> {{"label.table.title.size"|literal}}
        </th>
        <td mat-cell class=" " *matCellDef="let row"> {{row.size }} </td>
      </ng-container>


      <!--  Column -->
      <ng-container matColumnDef="updated">
        <th mat-header-cell *matHeaderCellDef class="  w-17">
          {{"label.table.title.updated"|literal}} </th>
        <td mat-cell class=" " *matCellDef="let row"><span
            *ngIf="row.modifiedDate">{{row.modifiedDate | date}} by
            {{row.modifiedBy}}</span>
          <span *ngIf="!(row.modifiedDate)"> {{row.createdDate | date}} </span>
        </td>

      </ng-container>

      <!-- Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef class="w-12  "> </th>
        <td class=" " mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">

          <span *ifHasPermission="DEAL_RESOURCE.Preview_Doc; scope:'READ'">
            <button
              *ngIf="element.type === 'UPLOAD' && checkPreview(element) || element.type === 'GENERATED' && checkPreview(element) "
              mat-icon-button class="mat-icon-buttons-in-action-column gray"
              (click)="previewFile(element.fileName , element.randomSrlNum , element.type)">
              <span class="material-symbols-outlined">
                remove_red_eye
              </span>
            </button>
          </span>

          <span *ifHasPermission="DEAL_RESOURCE.Download_Doc; scope:'READ'">
            <button mat-icon-button class="green" *ngIf="element.type === 'UPLOAD'"
              (click)="downloadFile(element.fileName , element.randomSrlNum , element.type)">
              <span class="material-symbols-outlined">
                get_app
              </span>
            </button>
          </span>

          <span *ifHasPermission="DEAL_RESOURCE.Generate_Doc; scope:'READ'">
            <button mat-icon-button class="green" *ngIf="element.type === 'GENERATED'"
              (click)="downloadFile(element.fileName , element.randomSrlNum , element.type)">
              <span class="material-symbols-outlined">
                get_app
              </span>
            </button>
          </span>

          <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
            <button mat-icon-button *ngIf="element.type === 'UPLOAD'" class="blue"
              [ngClass]="disableWhenReject ? 'gray' :'blue'" [disabled]="disableWhenReject"
              (click)="openUploadDocumentDialog(element.documentTitle)">
              <span class="material-symbols-outlined">publish</span>
            </button>
          </span>

          <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'CHANGE'">
            <button *ngIf="element.type === 'UPLOAD'" mat-icon-button class="blue"
              [ngClass]="disableWhenReject ? 'gray' :'blue'" [disabled]="disableWhenReject"
              (click)="openEditDialog(element)">
              <span class="material-symbols-outlined">edit</span>
            </button>
          </span>

          <span *ifHasPermission="DEAL_RESOURCE.Upload_Doc; scope:'DELETE'">
            <button *ngIf="element.type === 'UPLOAD'" mat-icon-button class="red"
              [ngClass]="disableWhenReject ? 'gray' :'red'" [disabled]="disableWhenReject"
              (click)="openDeleteDialog(element)" class="delete-icon">
              <span class="material-symbols-outlined">delete</span>
            </button>
          </span>

          <mat-icon *ngIf="this.selectedDocumentType === 'REQUEST'" class="pointer iconPosition"
            (click)="requestDetails(element.requestId)"
            matTooltip="Request Details">info_outline</mat-icon>
          <span *ifHasPermission="DEAL_RESOURCE.Request_Doc_Resend; scope:'CHANGE'">
             <button *ngIf="this.selectedDocumentType === 'REQUEST'" mat-raised-button
                  class="outlined-button" (click)="resendLink(element)"
                  matTooltip="Resend Link to {{element.email}}"
                  [disabled]="element.status ===  'REQUEST_COMPLETED'">
                  RESEND
                </button>
          </span>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns;" class="docDetailsTableRowText"></tr>
      <tr mat-row class="pointer docDetailsTableRowText"
        *matRowDef="let row; columns: displayedColumns; let i = index; "
        [class.task-row__alternate]="i % 2"></tr>
      <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="4">{{"label.title.noMatchingData"|literal}}</td>
        </tr>
      </div>
    </table>



    <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
      <mat-paginator class="" [pageIndex]="pageIndex" [pageSizeOptions]="[8, 25,50, 100]"
        (page)="handlePage($event)"></mat-paginator>
    </div>
    <div *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner" class="mt-1 no-records-found">
    </div>
    <div *ngIf="showLoaderSpinner" class="table-spinner">
      <mat-spinner></mat-spinner>
    </div>

  </div>

</div>
