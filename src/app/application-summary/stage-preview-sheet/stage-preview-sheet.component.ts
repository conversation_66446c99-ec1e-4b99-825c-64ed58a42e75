import { Component, Inject } from '@angular/core';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';

@Component({
  selector: 'app-stage-preview-sheet',
  templateUrl: './stage-preview-sheet.component.html',
  styleUrls: ['./stage-preview-sheet.component.scss']
})
export class StagePreviewSheetComponent {
  stageDetails : stageDetails;
  constructor(
    @Inject(MAT_BOTTOM_SHEET_DATA) data : {selectedStageDetails : stageDetails},
    public bottomsheetRef: MatBottomSheetRef<StagePreviewSheetComponent>
  ){
    this.stageDetails = data.selectedStageDetails;
  }
}


interface stageDetails {
  name:string,
  index:number,
  totalStages:number
}
