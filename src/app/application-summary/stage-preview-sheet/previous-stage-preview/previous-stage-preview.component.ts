import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { DealDetailsComponent } from "../../deal-details/deal-details.component";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { FormBuilder } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { MatSnackBar } from "@angular/material/snack-bar";
import { Router, ActivatedRoute } from "@angular/router";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ToasterService } from "src/app/common/toaster.service";
import { CommentsService } from "src/app/shared-module/comments/comments.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DealService } from "src/app/shared-service/deal.service";
import { DownloadFileService } from "src/app/shared-service/download-file.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { ActiveSectionService } from "../../section-preview-dialog/active-section.service";

@Component({
  selector: "app-previous-stage-preview",
  templateUrl: "../../deal-details/deal-details.component.html",
  styleUrls: ["../../deal-details/deal-details.component.scss"],
})
export class PreviousStagePreviewComponent
  extends DealDetailsComponent
  implements OnInit
{
  @Input() stageDetails: stageDetails;
  constructor(
    errorService: ErrorService,
    notificationMessage: ToasterService,
    businessProcessService: BusinessProcessService,
    router: Router,
    dataSharingService: DataSharingService,
    fb: FormBuilder,
    dealService: DealService,
    dialog: MatDialog,
    errorMessageService: ValidationErrorMessageService,
    currencyPipe: CurrencyPipe,
    identityService: IdentityService,
    activeRoute: ActivatedRoute,
    entityService: EntityService,
    matDialog: MatDialog,
    bottomsheet: MatBottomSheet,
    downloadFileService: DownloadFileService,
    currencyFormatService: CurrencyFormatService,
    snackbar: MatSnackBar,
    commentService: CommentsService,
    datepipe: DatePipe,
    themeService: ThemeService,
    cdr: ChangeDetectorRef,
    unsavedChangesHandler: UnsavedChangesHandlerService,
    dataTypesUtils: DataTypesUtilsService,
    activeSectionService: ActiveSectionService
  ) {
    super(
      errorService,
      notificationMessage,
      businessProcessService,
      router,
      dataSharingService,
      fb,
      dealService,
      dialog,
      errorMessageService,
      currencyPipe,
      identityService,
      activeRoute,
      entityService,
      matDialog,
      bottomsheet,
      downloadFileService,
      currencyFormatService,
      commentService,
      datepipe,
      themeService,
      cdr,
      unsavedChangesHandler,
      dataTypesUtils,
      activeSectionService
    );
  }

  ngOnInit(): void {
    this.previewReadOnlyStage = true;
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.dataSharingService.backButton = false;
    this.handlePreviousStageDetails(this.stageDetails.name);

    this.commentService.unAddedComment = "";
    this.dataSharingService.updatebuton = true;
    this.currentStage = this.selectedApplicationsData?.currentStageName;
    this.dataSharingService.hightlightFields = false;
    this.buttonRules = this.dataSharingService.FEeventRules;
    this.getconfigurableList();
  }
}

interface stageDetails {
  name: string;
  index: number;
  totalStages: number;
}
