@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue)
  ;
.stage-preview-containter{
    .stage-preview-header{
        .stage-info{
            .active-stage{
                color: $primary-color;
            }
        }
      
    }
   
}

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .stage-preview-containter{
    .stage-preview-header{
        .stage-info{
            @include mat.typography-level($typography-config, 'headline-5');
        }
      
    }
  }

}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}