<div class="stage-preview-containter">
    <div class="stage-preview-header" fxLayout="row" fxLayoutAlign="space-between center">
        <span class="stage-info">
            {{stageDetails.name}} 
            <span class="active-stage">{{stageDetails.index}} </span>  
            <span class="total-stages">/ {{stageDetails.totalStages}}</span>
        </span>
        <span>
            <button mat-icon-button (click)="bottomsheetRef.dismiss()">
                <mat-icon>close</mat-icon>
            </button>
        </span>
    </div>

    <div class="tab-select">

    </div>

    <div class="stage-preview" >
        <app-previous-stage-preview [stageDetails]="stageDetails"></app-previous-stage-preview>
    </div>

</div>