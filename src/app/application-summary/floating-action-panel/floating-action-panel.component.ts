import { trigger, transition, style, animate, state } from '@angular/animations';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-floating-action-panel',
  templateUrl: './floating-action-panel.component.html',
  styleUrls: ['./floating-action-panel.component.scss'],
    animations: [
      trigger('slideIn', [
        // State for the 'horizontal' orientation
        state('horizontal', style({ transform: 'translate(-50%,0)' })),
        transition('void => horizontal', [
          style({ transform: 'translateY(100%)' }),
          animate('500ms ease-in-out')
        ]),
        transition('horizontal => void', [
          animate('500ms ease-in-out', style({ transform: 'translateY(100%)' }))
        ]),
  
        // State for the 'vertical' orientation
        state('vertical', style({ transform: 'translateX(0)' })),
        transition('void => vertical', [
          style({ transform: 'translateX(100%)' }),
          animate('500ms ease-in-out')
        ]),
        transition('vertical => void', [
          animate('500ms ease-in-out', style({ transform: 'translateX(100%)' }))
        ]),
      ])
  
    ]
})
export class FloatingActionPanelComponent {
  @Input() vertical:boolean;
}
