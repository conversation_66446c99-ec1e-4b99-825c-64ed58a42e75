.action-panel-container.horizontal {
    position: fixed;
    bottom: 5vh;
    left: 52vw;
    z-index: 1000;
    display: flex;
    justify-content: center;
    background: var(--container-color);
    border-radius:10px;
    border: solid 1px var(--primary-color);
    box-shadow: 0px 4px 17.9px 0px rgba(0, 0, 0, 0.25);
  }
  
  .carousel-wrapper.horizontal {
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .carousel-content.horizontal {
    display: flex;
    overflow: hidden;
    scroll-behavior: smooth;
    transition: all 0.5s ease-in-out;
  }
  
  .action-item.horizontal {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    transition: transform 0.3s ease-in-out;
  }
  
  
 
  .action-panel-container.vertical {
      position: absolute;
      right: 1.3vw;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: var(--container-color);
      border-radius:10px;
      box-shadow: 0px 4px 17.9px 0px rgba(0, 0, 0, 0.25);
      z-index:100;
    }
    
    .carousel-wrapper.vertical {
      display: flex;
      flex-direction: column;
      align-items: center;
      max-height: 90vh;
      overflow: hidden;
    }
    
    .carousel-content.vertical {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      max-height: 300px;
      overflow-y: auto;
      transition: transform 0.3s ease-in-out;
    }

      
    
    
  
  
  