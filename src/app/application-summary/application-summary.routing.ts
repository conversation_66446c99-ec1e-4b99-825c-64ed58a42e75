import { Routes, RouterModule } from "@angular/router";
import { ApplicationSummaryComponent } from "./application-summary.component";
import { DocumentsDetailComponent } from "./documents-detail/documents-detail.component";
import { DealDetailsComponent } from "./deal-details/deal-details.component";
import { DealTaskComponent } from "./dealTask/dealTask.component";
import { ScoreComponent } from "./deal-details/score/score.component";

import { StageMovementHistoryComponent } from "../history-record/stage-movement-history/stage-movement-history.component";
import { unsavedChangesGuard } from "../guard/unsaved-changes.guard";

const routes: Routes = [
  {
    path: "",
    component: ApplicationSummaryComponent,
    children: [
      { path: "", redirectTo: "details", pathMatch: "full" },
      {
        path: "documents/:Id",
        component: DocumentsDetailComponent,
      },
      {
        path: "details/:Id",
        component: DealDetailsComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: "details/:Id/:sectionId?",
        component: DealDetailsComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      { path: "task/:Id", component: DealTaskComponent },
      { path: "dealAnalysis/:Id", component: ScoreComponent },
      { path: "stage-history/:Id", component: StageMovementHistoryComponent },
    ],
  },
];
export const ApplicationSummaryRoutes = RouterModule.forChild(routes);
