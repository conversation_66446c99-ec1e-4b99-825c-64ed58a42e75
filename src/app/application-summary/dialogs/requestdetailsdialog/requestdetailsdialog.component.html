
<div class="row">

  <div  fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" fxLayoutAlign="end" class="floatRight">
    <mat-icon (click)="onClose()" class="pointer">close</mat-icon>
  </div>
  
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="85%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
      <h2 class="subTitle">{{"label.title.documentRequestDetails"|literal}}</h2>
    </div>
  </div>

</div>

<div class="row">

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="height-auto">
    <table mat-table [dataSource]="dataSource" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mat-elevation-z0  mat-table-width tableSpacing">
      <ng-container matColumnDef="Checkbox"> 
        <th mat-header-cell *matHeaderCellDef>  </th>
        <td mat-cell *matCellDef="let row"><mat-checkbox class="example-margin"></mat-checkbox> </td>
      </ng-container>
      
      <ng-container matColumnDef="dealRequestDocumentList">
        <th mat-header-cell *matHeaderCellDef> Document </th>
        <td mat-cell *matCellDef="let element"> {{element.docType}} </td>
      </ng-container>
    
      <!-- Name Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef> Requested On </th>
        <td mat-cell *matCellDef="let element"> {{element.createdOn | date}} </td>
      </ng-container>
    
      <!-- Weight Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element"> 
          <span *ngIf="element.docStatus == 'NOT_UPLOADED'">Document Pending</span>
          <span *ngIf="element.docStatus == 'UPLOADED'">Document Uploaded</span>
          <span *ngIf="element.docStatus == 'DELETED'">Document Deleted</span>
  
        </td>
      </ng-container>
    
      <!-- Symbol Column -->
      <ng-container matColumnDef="fileName">
        <th mat-header-cell *matHeaderCellDef> File </th>
        <td mat-cell *matCellDef="let element"> {{element.fileName}} </td>
      </ng-container>
    
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
      >
    </tr>
    </table>
    <div *ngIf="showLoaderSpinner">
      <mat-spinner  class="ShowLoader"> {{"label.noRecords"|literal}} </mat-spinner>
    </div>
  </div>

</div>
    