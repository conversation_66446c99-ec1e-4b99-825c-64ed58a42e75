
import { DealService } from 'src/app/shared-service/deal.service';
import { Component, Inject, OnInit } from '@angular/core';
import {
  MatBottomSheetRef,
  MAT_BOTTOM_SHEET_DATA,
} from "@angular/material/bottom-sheet";
import { MatTableDataSource } from '@angular/material/table';
import { ToasterService } from 'src/app/common/toaster.service';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { listOfDocumentTypes, listOfDocumentTypesForRequest } from '../../static-data';
import { SelectionModel } from '@angular/cdk/collections';



@Component({
  selector: 'app-requestdetailsdialog',
  templateUrl: './requestdetailsdialog.component.html',
  styleUrls: ['./requestdetailsdialog.component.scss']
})
export class RequestdetailsdialogComponent implements OnInit {
  fileData: any = new FormData();

  displayedColumns: string[] = ['dealRequestDocumentList', 'createdDate', 'status', 'fileName'];
   dataSource = new MatTableDataSource<any>();
   //dataSource = ELEMENT_DATA;


  showNoDataMessage: any = false;
  noDataMessage: string;
  requestId:any;
  dealRequestDocumentList:any;
  createdDate:any;
  status:any;
  docType:any;
  table: any;
  selectedApplicationsData: any;
  showNoRecordsAvailbleMessage: boolean = false;
  showLoaderSpinner: boolean = false;
  refreshDataTable: any;
  element : any;
  constructor(public matDialog: MatDialog,
    public notificationMessage: ToasterService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public data,
    private bottomSheet: MatBottomSheetRef<RequestdetailsdialogComponent>,
    public dealService: DealService) { }

  ngOnInit(){
    this.requestId = this.data.requestId;
    this.getData()
  }
  onClose() {

    this.bottomSheet.dismiss();

}
getData(){

  this.dealService.getrequestDetails(this.requestId).subscribe((res: any) => {


    this.dealRequestDocumentList = res.docType;
    this.createdDate = res.createdDate;
    this.dataSource = res.docType;
    this.status = res.status;

  }, (error) => {

    this.showLoaderSpinner = false;
    this.showNoRecordsAvailbleMessage = true;
  })
}
// resendRequest(element) {
//   // element.docTypeList = [element.documentType]
//   this.dealService.resendRequest(element, element.id).subscribe(res => {
//     this.notificationMessage.success("Secure link has sent successfully.");
//     this.displayedColumns = ["dealRequestDocumentList", "contactName", "createdDate", "status", "action"];
//     this.getAllTheRequests(this.selectedApplicationsData.id)
//   })
// }
// getAllTheRequests(id) {
//   this.dealService.getAllRequests(id).subscribe(res => {
//     this.showLoaderSpinner = false
//     this.refreshDataTable(res, 'REQUEST')
//   }, (error) => {
//     this.refreshDataTable([], 'REQUEST')
//     this.showLoaderSpinner = false;
//     this.showNoRecordsAvailbleMessage = true;
//   })
// }
}
