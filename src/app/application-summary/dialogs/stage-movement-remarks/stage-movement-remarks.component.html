<mat-dialog-content>

  <div class="dialog-header-container">
    <h2 class="dialog-header">{{"label.header.stageMovementRemarks"|literal}}</h2>
  </div>

  <mat-form-field class="full-width custom-mat-input-style">
    <mat-label>{{"label.title.stageMovementRemarks"|literal}}</mat-label>
    <textarea aria-label="select-reason-input-field" class="full-width"
      [formControl]="stageMovementRemarksControl" matInput></textarea>
    <mat-error *ngIf="stageMovementRemarksControl.errors?.required">
      {{"label.error.stageMovementRemarksError"|literal}}
    </mat-error>
  </mat-form-field>

</mat-dialog-content>
<mat-card-footer>
  <div class="dialog-button">
    <button aria-label="cancel-button" mat-raised-button [mat-dialog-close]="false"
      class="outlined-button" type="button">
      {{"label.button.cancel"|literal}}
    </button>
    <button aria-label="move-stage-button" mat-raised-button (click)="changeStage()" color="primary"
      type="button" *ngIf="approvalRemarks;else changeStageButton">
      {{"label.button.approve"|literal}}
    </button>
    <ng-template #changeStageButton>
      <button aria-label="move-stage-button" mat-raised-button (click)="changeStage()"
        color="primary" type="button">
        {{"label.button.changeStage"|literal}}
      </button>
    </ng-template>
  </div>
</mat-card-footer>
