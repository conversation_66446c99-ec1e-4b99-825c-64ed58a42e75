import { Component, Input } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-stage-movement-remarks',
  templateUrl: './stage-movement-remarks.component.html',
  styleUrls: ['./stage-movement-remarks.component.scss']
})
export class StageMovementRemarksComponent {
  @Input() approvalRemarks:boolean; 

  stageMovementRemarksControl: FormControl = new FormControl('',[Validators.required]);
constructor(
  private dialog: MatDialogRef<StageMovementRemarksComponent>
){}
  changeStage(){
    this.stageMovementRemarksControl.markAsTouched();
    if(this.stageMovementRemarksControl.invalid) return;
    this.dialog.close(this.stageMovementRemarksControl.value);
  }
}
