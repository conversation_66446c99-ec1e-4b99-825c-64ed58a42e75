import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Component, Inject, OnInit, Optional } from '@angular/core';
import { ToasterService } from '../../../common/toaster.service';
import { UntypedFormControl, Validators } from '@angular/forms';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import JsonData from 'src/assets/data.json'
import { DealService } from 'src/app/shared-service/deal.service';
import { ThemeService } from 'src/app/theme.service';
@Component({
  selector: 'app-add-rejection',
  templateUrl: './add-rejection.component.html',
  styleUrls: ['./add-rejection.component.scss']
})
export class AddRejectionComponent implements OnInit {
  selectedReason : UntypedFormControl = new UntypedFormControl('' , [ Validators.required,])
  customReasonDetail: UntypedFormControl = new UntypedFormControl("");
  rejectionSelected: any;
  customReason = "";
  rejectionTypes = [];
  JsonData = JsonData;
  dealId: any;
  loading =  false;
  defaultValues:any;

  constructor(public dialogRef: MatDialogRef<AddRejectionComponent>,
    public dataSharingService: DataSharingService,
    private dealService: DealService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public toasterService : ToasterService,
    public themeService : ThemeService
  ) {}

  ngOnInit() {
    this.dealId = this.dataSharingService.selectedApplicationData.id;
    this.getRejectionTypeById();
  }

  getRejectionTypeById(){
    this.loading = true;

    this.dealService.getRejectionDetailsById(this.dealId).subscribe((data:any) => {
        if(data?.rejectionDetails.length){
          this.rejectionTypes = data?.rejectionDetails;
          data.rejectionDetails.forEach((item)=>{
            item?.stages.forEach((value)=>{
              if(value.isDefault == true){
                this.defaultValues =item?.reason
              }

            })
              })
              this.rejectionTypes.forEach((item)=>{
                if(item.reason == this.defaultValues){
              this.selectedReason.setValue(item);
              this.rejectionSelected = item

                }
              })
        }
        else{
          this.selectedReason.setValue("Other");
          this.rejectionSelected = "Other";
        }
        this.loading = false;

      },
      () => {
        this.loading = false;
      }
      )
  }

  saveRejection() {
    if(!this.rejectionSelected){
      this.selectedReason.markAsTouched()
      this.toasterService.error("Please fill in all the required fields with valid data.")
      return

    }
    let reason;
    if (this.rejectionSelected == "Other") {
      if (this.customReasonDetail.invalid) {
        this.customReasonDetail.markAsTouched();

        this.toasterService.error(
          "Please fill in all the required fields with valid data."
        );
        return;
      }
      reason =
        this.customReason.length > 0
          ? this.customReason
          : this.rejectionSelected;
      this.dialogRef.close(reason);
    } else {
      this.dialogRef.close(this.rejectionSelected);
    }

  }

  closeDialog() {
    this.dialogRef.close();
  }

}
