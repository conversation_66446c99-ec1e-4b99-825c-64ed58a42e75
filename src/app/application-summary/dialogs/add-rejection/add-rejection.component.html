<ng-container *ngIf="!themeService.useNewTheme">
  <div fxLayout="column" class="create-asset-dialog padding-24 mb-2">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
        <div fxFlex="85%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2 class="fontcolour font-family">{{"label.header.selectRejectionReason"|literal}}</h2>
        </div>
        <div fxFlex="10%" class="ml-btn">
          <button mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <mat-form-field class="full-width" *ngIf="!loading">
      <mat-label>Select reason</mat-label>
      <mat-select aria-label="select-reason-field" [formControl]="selectedReason" required
        [(value)]="rejectionSelected">
        <mat-option *ngFor="let rejection of rejectionTypes" [value]="rejection">
          <span attr.aria-label="rejection-reason-{{ rejection.reason }}">{{ rejection.reason
            }}</span>
        </mat-option>
        <mat-option [value]="'Other'">
          <span aria-label="rejection-reason-other">Other</span>
        </mat-option>
      </mat-select>
      <mat-error *ngIf="selectedReason.errors?.required">
        {{"label.materror.rejection"|literal}}
      </mat-error>
    </mat-form-field>
    <div *ngIf="loading">
      <mat-spinner class="no-record-card ml-10"> {{"label.tdatacell.noRecords" | literal}}
      </mat-spinner>
    </div>

    <mat-form-field *ngIf="rejectionSelected === 'Other'" class="full-width">
      <mat-label>{{"label.field.rejectionReason"|literal}}</mat-label>
      <input aria-label="select-reason-input-field" class="full-width"
        [formControl]="customReasonDetail" [(ngModel)]="customReason" required matInput />
      <mat-error *ngIf="customReasonDetail.errors?.required">
        {{"label.materror.rejection"|literal}}
      </mat-error>
    </mat-form-field>
    <div class="button-row">
      <button aria-label="rejection-type-save-btn" mat-raised-button class="green"
        (click)="saveRejection()">
        Save
      </button>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="themeService.useNewTheme">
  <mat-dialog-content>

    <div class="dialog-header-container">
      <h2 class="dialog-header">{{"label.header.selectRejectionReason"|literal}}</h2>
    </div>
    <mat-form-field class="custom-mat-input-style full-width" *ngIf="!loading">
      <mat-label>Select reason</mat-label>
      <mat-select aria-label="select-reason-field" [formControl]="selectedReason" required
        [(value)]="rejectionSelected" [autofocus]="true">
        <mat-option *ngFor="let rejection of rejectionTypes" [value]="rejection">
          <span attr.aria-label="rejection-reason-{{ rejection.reason }}">{{ rejection.reason
            }}</span>
        </mat-option>
        <mat-option [value]="'Other'">
          <span aria-label="rejection-reason-other">Other</span>
        </mat-option>
      </mat-select>
      <mat-error *ngIf="selectedReason.errors?.required">
        {{"label.materror.rejection"|literal}}
      </mat-error>
    </mat-form-field>

    <div *ngIf="loading" class="spinner">
      <mat-spinner> {{"label.tdatacell.noRecords" | literal}} </mat-spinner>
    </div>

    <mat-form-field *ngIf="rejectionSelected === 'Other'" class="custom-mat-input-style full-width">
      <mat-label>{{"label.field.rejectionReason"|literal}}</mat-label>
      <input aria-label="select-reason-input-field" class="full-width"
        [formControl]="customReasonDetail" [(ngModel)]="customReason" required matInput />
      <mat-error *ngIf="customReasonDetail.errors?.required">
        {{"label.materror.rejection"|literal}}
      </mat-error>
    </mat-form-field>

  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button">
      <button aria-label="create-deal-button" mat-raised-button (click)="closeDialog()"
        class="outlined-button" type="button">
        {{"label.button.cancel"|literal}}
      </button>
      <button aria-label="save-rejection-button" mat-raised-button (click)="saveRejection()"
        color="primary" type="button">
        {{"label.button.saveAndExit"|literal}}
      </button>
    </div>
  </mat-card-footer>
</ng-container>
