import { Component, OnInit, Inject } from "@angular/core";
import { listOfDocumentTypes } from "../../static-data";
import { ThemePalette } from "@angular/material/core";
import { ProgressBarMode } from "@angular/material/progress-bar";
import { UntypedFormControl, Validators } from "@angular/forms";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-generate-document-dialog",
  templateUrl: "./generate-document-dialog.component.html",
  styleUrls: ["./generate-document-dialog.component.scss"],
})
export class GenerateDocumentDialogComponent implements OnInit {
  private unsubscribe$ = new Subject();
  templateList: any = [];
  selectedDocumentType: any;
  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage: boolean = false;
  color: ThemePalette = "primary";
  mode: ProgressBarMode = "indeterminate";
  value = 50;
  bufferValue = 75;
  downloadAs: string = "pdf";
  documentType = new UntypedFormControl("", [Validators.required]);
  fileSize: any;
  JsonData: any;
  useNewThemeUI: any;

  constructor(
    private errorService: ErrorService,
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<GenerateDocumentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    public dataSharingService: DataSharingService,
    protected themeService: ThemeService
  ) {
    this.dataFromParentComponent = data;
    this.getAllTheTemplateDocuments();
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme;
    if (this.dataFromParentComponent.documentType) {
      this.selectedDocumentType = this.dataFromParentComponent.documentType;
    }
  }

  fileUpload(file) {
    // this.taskService.isFileUploaded = true;
    this.selectedFile = file;
    this.selectedFileName = file.name;
    this.fileSize = file.size;
    this.showFileSizeErrorMessage = false;

    if (this.fileSize >= 104857600) {
      this.showFileSizeErrorMessage = true;
    }
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
  }

  onUpload() {
    this.documentType.markAsTouched();
    if (this.documentType.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.showFileSizeErrorMessage) {
      return;
    }
    //
    let data = {
      documentTitle: this.selectedDocumentType.fileName,
      sampleId: this.selectedDocumentType.dmsId,
      businessProcessId: this.selectedDocumentType.businessProcessId,
      name: this.generateFileName(),
      type: "GENERATED",
      dealId:
        this.dataFromParentComponent && this.dataFromParentComponent.dealId
          ? this.dataFromParentComponent.dealId
          : 0,
      referenceList: [
        this.selectedDocumentType.dmsId.toString(),
        this.selectedDocumentType.businessProcessId,
      ],
      format: this.downloadAs,
    };

    this.dealService
      .generateDocument(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.showFileProgressBar = true;
            this.filePercentage = this.dealService.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            this.showFileProgressBar = true;
            this.showFileSizeErrorMessage = false;
            this.showFileProgressBar = false;
            this.selectedFile = null;
            this.selectedFileName = "";
            this.notificationMessage.success(
              JsonData["label.success.GenerateDocument"]
            );
            this.dialogRef.close(true);
          }
        },
        (error) => {
          this.showFileProgressBar = false;
        }
      );
  }
  generateFileName() {
    let fileNameWithoutExtension =
      this.dataFromParentComponent.dealName +
      "-" +
      this.selectedDocumentType.fileName;
    return (
      fileNameWithoutExtension.replace(/\..+$/, "") + "." + this.downloadAs
    );
  }

  getAllTheTemplateDocuments() {
    this.dealService
      .getBPTemplateList(
        this.dataSharingService.selectedApplicationData?.businessProcessDetail
          ?.id
      )
      .subscribe((res) => {
        this.templateList = res ? res : [];
        this.templateList = this.templateList.filter(
          (item) =>
            item.referenceList[1] ==
            this.dataFromParentComponent.businessProcessName
        );
      });
  }
}
