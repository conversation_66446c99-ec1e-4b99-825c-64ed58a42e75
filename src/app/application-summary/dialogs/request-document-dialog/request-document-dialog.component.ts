import { Component, OnInit, Inject } from "@angular/core";
import { ThemePalette } from "@angular/material/core";
import { ProgressBarMode } from "@angular/material/progress-bar";
import {
  FormControl,
  Validators,
  UntypedFormGroup,
  UntypedFormBuilder,
} from "@angular/forms";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { UploadDocumentDialogComponent } from "../upload-document-dialog/upload-document-dialog.component";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";
import {
  listOfDocumentTypes,
  listOfDocumentTypesForRequest,
} from "../../static-data";
import { Observable } from "rxjs";
import { startWith, map } from "rxjs/operators";
import { DOCUMENT } from "@angular/common";
import { IdentityService } from "src/app/shared-service/identity.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { EntityService } from "src/app/shared-service/entity.service";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-request-document-dialog",
  templateUrl: "./request-document-dialog.component.html",
  styleUrls: ["./request-document-dialog.component.scss"],
})
export class RequestDocumentDialogComponent implements OnInit {
  availableDocumentlist: any;
  chosenDocumentList: any = [];
  requestDocumentForm: UntypedFormGroup;

  businessProcessList: any = [];
  selectedBusinessProcess: any;
  defaultValidity: any = 24;
  // fileData: any = new FormData();
  fileData: FormData;
  dataFromParentComponent: any;
  selectedExpireAfter: any = 1;
  showFileTemplateProgressBar: boolean = false;

  public activeAvailableType: number;
  public activeContainer: string;
  userList: any[] = [];
  filteredOptions: Observable<string[]>;
  JsonData: any;
  emailList = [];
  entityEmail: { name: any; email: any };
  list = [];
  autoEmail: any;
  useNewThemeUI: any;

  constructor(
    private fb: UntypedFormBuilder,
    private errorService: ErrorService,
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<RequestDocumentDialogComponent>,
    public dataSharingService: DataSharingService,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    @Inject(DOCUMENT) private document: Document,
    private identityService: IdentityService,
    public entityService: EntityService,
    protected themeService: ThemeService
  ) {
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
    if (this.dataSharingService.entityItems !== undefined) {
      this.dataSharingService.entityItems.forEach((item) => {
        if (
          (this.getPropertyName(item) == "primaryContactEmail" &&
            this.getPropertyName(item) !== undefined) ||
          (this.getPropertyName(item) == "contactEmail" &&
            this.getPropertyName(item) !== undefined)
        ) {
          this.emailList.push(item);
        }
      });
      this.emailList.forEach((ele) => {
        if (ele[this.getPropertyName(ele)]?.value !== "") {
          this.list.push(ele[this.getPropertyName(ele)]?.value);
          this.autoEmail = ele[this.getPropertyName(ele)]?.value;
        }
      });
    }
    {
      this.generateRequestForm();
      this.getUserList();
      this.availableDocumentlist = listOfDocumentTypesForRequest.slice();
      this.dataFromParentComponent = data;
    }
  }
  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.getAllTheDocumentTypeList();
    this.filteredOptions = this.requestDocumentForm
      .get("contactDetails")
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value))
      );
    this.requestDocumentForm.controls.contactDetails.setValue(this.autoEmail);
  }

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.userList.filter(
      (option) => option.name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  getAllTheDocumentTypeList() {
    this.dealService
      .getBPDocumentTypeList(
        this.dataSharingService.selectedApplicationData?.businessProcessDetail
          ?.id
      )
      .subscribe((res: any) => {
        let data = res;
        //  let docList = data.filter(doc => doc.businessProcessDetail.name == this.dataSharingService.selectedApplicationData.businessProcessDetail.name);
        if (data) {
          this.availableDocumentlist = [];
          data.documentList.forEach((element) => {
            this.availableDocumentlist.push({ documentTypeName: element.name });
          });
        }
      });
  }

  generateRequestForm() {
    this.requestDocumentForm = this.fb.group({
      contactDetails: ["", Validators.required],
      linkValidPeriod: [""],
      notes: [""],
    });
  }

  public activeList(index: number, type): void {
    this.activeAvailableType = index;
    this.activeContainer = type;
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      this.activeAvailableType = undefined;
      this.activeContainer = undefined;
    }
  }

  onRequestSend() {
    if (this.requestDocumentForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.chosenDocumentList.length == 0) {
      this.notificationMessage.error(
        "Please select at least one document type"
      );
      return;
    }
    let mailIdRecipient = "";
    let contactName = this.requestDocumentForm.value.contactDetails;
    if (
      this.userList.filter(
        (option) => option.name == this.requestDocumentForm.value.contactDetails
      ).length == 0
    ) {
      let emailCheck = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      if (emailCheck.test(this.requestDocumentForm.value.contactDetails)) {
        mailIdRecipient = this.requestDocumentForm.value.contactDetails;
        contactName = "";
      } else {
        this.notificationMessage.error("Please provide valid email Id.");
        return;
      }
    } else {
      mailIdRecipient = this.userList.filter(
        (option) => option.name == contactName
      )[0].email;
    }

    let url = document.location.protocol + "//" + document.location.hostname;
    const urlCheck = url.indexOf("localhost");
    if (urlCheck != -1) {
      url =
        document.location.protocol +
        "//" +
        document.location.hostname +
        ":4200";
    }

    // let email =  mailIdRecipient
    // let data = {
    //   "dealId": this.dataFromParentComponent.dealId,
    //   "dealRequestDocumentList": this.chosenDocumentList,
    //   "mailId": email,

    //   "notes": this.requestDocumentForm.value.notes,
    //   "linkValidPeriod": this.requestDocumentForm.value.linkValidPeriod,
    //   "secureLink": url + '/linkToUploadRequestedDocuments/'
    // }
    this.fileData = new FormData();
    let email = [];
    email.push(mailIdRecipient);

    let dmsRequest = {
      reqName: "Please upload Requested Documents",
      // "dealId": this.dataFromParentComponent.dealId,
      reqDescription: this.requestDocumentForm.value.notes,
      docType: this.chosenDocumentList,
      email: email,

      // "reqDescription": this.requestDocumentForm.value.notes,
      linkValidPeriod: this.requestDocumentForm.value.linkValidPeriod,
      // "secureLink": url + '/linkToUploadRequestedDocuments/'
    };
    let originateRequest = {
      dealId: this.dataFromParentComponent.dealId,
    };

    this.fileData.append("originateRequest", JSON.stringify(originateRequest));
    this.fileData.append("dmsRequest", JSON.stringify(dmsRequest));

    if (contactName) {
      dmsRequest["contactName"] = contactName;
    }

    this.showFileTemplateProgressBar = true;

    this.dealService.sendRequestForDocumeents(this.fileData).subscribe(
      (res) => {
        this.showFileTemplateProgressBar = false;
        // this.selectedBusinessProcess = "";
        this.notificationMessage.success(JsonData["label.success.RequestSent"]);
        this.dialogRef.close(true);
      },
      (error) => {
        this.showFileTemplateProgressBar = false;
      }
    );
  }

  pushToAnotherContainer(index, fromArray, type) {
    if (
      fromArray == "chosenDocumentList" &&
      type == this.activeContainer &&
      index != undefined
    ) {
      this.availableDocumentlist.push(this.chosenDocumentList[index]);
      this.chosenDocumentList.splice(index, 1);
      this.activeAvailableType = undefined;
      this.activeContainer = undefined;
    }
    if (
      fromArray == "availableDocumentlist" &&
      type == this.activeContainer &&
      index != undefined
    ) {
      let data = this.availableDocumentlist[index];
      this.chosenDocumentList.push(data);
      this.availableDocumentlist.splice(index, 1);
      this.activeAvailableType = undefined;
      this.activeContainer = undefined;
    }

    // index = undefined
  }

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res: any) => {
        if (res) {
          this.userList = res?.map((item) => ({
            name: item?.firstName + " " + item?.lastName,
            email: item?.mailId,
          }));
        }
        this.list.forEach((item) => {
          this.entityEmail = {
            name: item,
            email: item,
          };
          if (this.entityEmail !== undefined) {
            this.userList.push(this.entityEmail);
          }
        });
      },
      (error) => {}
    );
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }
}
