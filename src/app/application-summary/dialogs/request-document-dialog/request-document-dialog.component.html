<mat-dialog-content *ngIf="!themeService.useNewTheme" class="" fxLayout="row wrap"
  fxLayoutGap="4px">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="90%" fxFlex.md="93%" fxFlex.xs="80%" fxFlex.sm="93%">
        <h2 class="headerLabel">{{"label.title.createDocumentRequest"|literal}}</h2>
      </div>
      <div fxFlex="9%" fxFlex.md="4%" fxFlex.xs="16%" fxFlex.sm="4%">
        <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>



    <form autocomplete="off" [formGroup]="requestDocumentForm">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <h4 class="mb-2">{{"label.title.selectDocuments"|literal}}</h4>
          <div cdkDropListGroup fxLayout="row wrap" fxLayoutGap="4px">
            <div class="width-100" fxFlex="43%" fxFlex.md="43%" fxFlex.xs="43%" fxFlex.sm="43%">
              <div class="documentTypeContainer" cdkDropList
                [cdkDropListData]="availableDocumentlist" (cdkDropListDropped)="drop($event)">
                <ng-container *ngFor="let type of availableDocumentlist; let i = index">
                  <div cdkDrag (click)="activeList(i , 'Available')"
                    class="documentTypeNameContainer pointer"
                    [class.activeColorOfType]="activeContainer == 'Available' && activeAvailableType === i">
                    {{type.name ? type.name: type.documentTypeName}}
                  </div>
                  <mat-divider></mat-divider>
                </ng-container>
              </div>
            </div>
            <div class="centeredArrows width-100" fxFlex="10%" fxFlex.md="10%" fxFlex.xs="10%"
              fxFlex.sm="10%">

              <div class="arrowRight">
                <mat-icon class="pointer"
                  (click)="pushToAnotherContainer(activeAvailableType, 'availableDocumentlist','Available')">arrow_right
                </mat-icon>
              </div>
              <div class="arrowLeft">
                <mat-icon class="pointer"
                  (click)="pushToAnotherContainer(activeAvailableType,'chosenDocumentList', 'Chosen')">arrow_left
                </mat-icon>
              </div>

            </div>
            <div class="width-100" fxFlex="43%" fxFlex.md="43%" fxFlex.xs="43%" fxFlex.sm="43%">
              <div class="documentTypeContainer" cdkDropList [cdkDropListData]="chosenDocumentList"
                (cdkDropListDropped)="drop($event)">
                <ng-container *ngFor="let chosenType of chosenDocumentList; let j = index">
                  <div cdkDrag (click)="activeList(j , 'Chosen')"
                    class="documentTypeNameContainer pointer"
                    [class.activeColorOfType]="activeContainer == 'Chosen' && activeAvailableType === j">
                    {{chosenType.name ? chosenType.name: chosenType.documentTypeName}}
                  </div>
                  <mat-divider></mat-divider>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="mt-5" fxLayout="row wrap" fxLayoutGap="4px">

        <div fxFlex="45%" fxFlex.md="45%" fxFlex.xs="45%" fxFlex.sm="45%">

          <mat-form-field class="example-full-width width-100">
            <mat-label>{{"label.field.contact"|literal}}</mat-label>
            <input type="text" required matInput formControlName="contactDetails"
              matTooltip="Enter contact person if user is defined in the system else enter email id."
              [matAutocomplete]="auto">
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
              <ng-container *ngFor="let option of filteredOptions | async">
                <mat-option [value]="option.name"
                  *ngIf="option.name && option.name != 'null null' && option.email">
                  {{option.name}}
                </mat-option>
              </ng-container>

            </mat-autocomplete>
            <mat-error *ngIf="requestDocumentForm.controls.contactDetails.errors?.required">
              {{"label.materror.Contact"|literal}}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="width-100" fxFlex="8%" fxFlex.md="8%" fxFlex.xs="8%" fxFlex.sm="8%">
        </div>
        <div fxFlex="44%" fxFlex.md="44%" fxFlex.xs="44%" fxFlex.sm="44%">
          <mat-form-field class="width-100">
            <mat-label>{{"label.field.secureLink"|literal}}</mat-label>
            <mat-select required formControlName="linkValidPeriod" [(ngModel)]="defaultValidity">
              <mat-option [value]="24">24 hours</mat-option>
              <mat-option [value]="48">48 Hours</mat-option>
              <mat-option [value]="168">7 days</mat-option>
              <mat-option [value]="360">15 days</mat-option>

            </mat-select>

          </mat-form-field>
        </div>

      </div>


      <div fxLayout="row wrap" fxLayoutGap="4px">

        <mat-form-field class="noteClass" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
          fxFlex.sm="100%">
          <mat-label>{{"label.field.note"|literal}}</mat-label>
          <textarea matInput formControlName="notes"></textarea>
        </mat-form-field>

      </div>


      <div class=" uploadBtnContainerCss" fxLayout="row wrap" fxLayoutGap="4px">
        <div class="centerBtns" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <button mat-raised-button class="green "
            (click)="onRequestSend()">{{"label.button.save"|literal}}</button>
        </div>
      </div>
    </form>


  </div>


</mat-dialog-content>



<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="row wrap">

      <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start center">
          <h2 class="headerLabel">{{"label.title.createDocumentRequest"|literal}}</h2>
        </div>
        <div fxLayoutAlign="end center">
          <span>
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon class="material-symbols-outlined">close</mat-icon>
            </button>
          </span>
        </div>
      </div>

      <div fxFlex="100%" class="m-t-15">
        <div fxFlex="100%">


          <form autocomplete="off" [formGroup]="requestDocumentForm">
            <div fxLayout="row wrap" fxLayoutGap="4px">
              <div class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <h4 class="mb-2">{{"label.title.selectDocuments"|literal}}</h4>
                <div cdkDropListGroup fxLayout="row wrap" fxLayoutGap="4px">
                  <div class="width-100" fxFlex="43%" fxFlex.md="43%" fxFlex.xs="43%"
                    fxFlex.sm="43%">
                    <div class="documentTypeContainer" cdkDropList
                      [cdkDropListData]="availableDocumentlist" (cdkDropListDropped)="drop($event)">
                      <ng-container *ngFor="let type of availableDocumentlist; let i = index">
                        <div cdkDrag (click)="activeList(i , 'Available')"
                          class="documentTypeNameContainer pointer"
                          [class.activeColorOfType]="activeContainer == 'Available' && activeAvailableType === i">
                          {{type.name ? type.name: type.documentTypeName}}
                        </div>
                        <mat-divider></mat-divider>
                      </ng-container>
                    </div>
                  </div>
                  <div class="centeredArrows width-100" fxFlex="10%" fxFlex.md="10%" fxFlex.xs="10%"
                    fxFlex.sm="10%">

                    <div class="arrowRight">
                      <mat-icon class="pointer"
                        (click)="pushToAnotherContainer(activeAvailableType, 'availableDocumentlist','Available')">arrow_right
                      </mat-icon>
                    </div>
                    <div class="arrowLeft">
                      <mat-icon class="pointer"
                        (click)="pushToAnotherContainer(activeAvailableType,'chosenDocumentList', 'Chosen')">arrow_left
                      </mat-icon>
                    </div>

                  </div>
                  <div class="width-100" fxFlex="43%" fxFlex.md="43%" fxFlex.xs="43%"
                    fxFlex.sm="43%">
                    <div class="documentTypeContainer" cdkDropList
                      [cdkDropListData]="chosenDocumentList" (cdkDropListDropped)="drop($event)">
                      <ng-container *ngFor="let chosenType of chosenDocumentList; let j = index">
                        <div cdkDrag (click)="activeList(j , 'Chosen')"
                          class="documentTypeNameContainer pointer"
                          [class.activeColorOfType]="activeContainer == 'Chosen' && activeAvailableType === j">
                          {{chosenType.name ? chosenType.name: chosenType.documentTypeName}}
                        </div>
                        <mat-divider></mat-divider>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-5" fxLayout="row wrap" fxLayoutGap="4px">

              <mat-form-field class="custom-mat-input-style noteClass" fxFlex="100%"
                fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                <mat-label>{{"label.field.contact"|literal}}</mat-label>
                <input type="text" required matInput formControlName="contactDetails"
                  matTooltip="Enter contact person if user is defined in the system else enter email id."
                  [matAutocomplete]="auto">
                <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
                  <ng-container *ngFor="let option of filteredOptions | async">
                    <mat-option [value]="option.name"
                      *ngIf="option.name && option.name != 'null null' && option.email">
                      {{option.name}}
                    </mat-option>
                  </ng-container>

                </mat-autocomplete>
                <mat-error *ngIf="requestDocumentForm.controls.contactDetails.errors?.required">
                  {{"label.materror.Contact"|literal}}
                </mat-error>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutGap="4px">

              <mat-form-field class="custom-mat-input-style noteClass" fxFlex="100%"
                fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                <mat-label>{{"label.field.secureLink"|literal}}</mat-label>
                <mat-select required formControlName="linkValidPeriod"
                  [(ngModel)]="defaultValidity">
                  <mat-option [value]="24">24 hours</mat-option>
                  <mat-option [value]="48">48 Hours</mat-option>
                  <mat-option [value]="168">7 days</mat-option>
                  <mat-option [value]="360">15 days</mat-option>

                </mat-select>

              </mat-form-field>
            </div>




            <div fxLayout="row wrap" fxLayoutGap="4px">

              <mat-form-field class="custom-mat-input-style noteClass" fxFlex="100%"
                fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                <mat-label>{{"label.field.note"|literal}}</mat-label>
                <textarea matInput formControlName="notes"></textarea>
              </mat-form-field>

            </div>

          </form>

        </div>
      </div>
    </div>


  </mat-dialog-content>

  <mat-card-footer>
    <div class="dialog-button" fxLayout="row wrap">
      <button color="primary" aria-label="request-document" mat-raised-button type="submit"
        (click)="onRequestSend()">{{"label.button.save"|literal}}</button>
    </div>
  </mat-card-footer>
</div>
