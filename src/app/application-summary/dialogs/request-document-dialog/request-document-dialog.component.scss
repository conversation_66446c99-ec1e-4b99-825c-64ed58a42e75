
/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-custom-css{
    padding:  0 !important;
    margin: 0  !important
}

.m-0{
    margin: 0;
}
.mb-2{
  margin : 0 0 2%
}

.centeredArrows{
    position: relative;
    display: flex;
    justify-content: center;
    // top:50%;
    // transform: translateY(-50%)
}

.uploadBtnContainerCss{
    justify-content: center;
    display: flex;
    margin-top: 1%;
    margin-bottom: 1%;
}


.uploadDocumentsInputs{
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
        bottom: 0 !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0 !important; 
    }
  }

.progressBar{
    // margin-top: -1.1% !important;
    width: 99.50% !important;
}

  .centerBtns{
      display: flex;
      justify-content: center !important;
  }


  .documentTypeContainer{
    width: 100%;
    border: 1px solid rgba(0,0,0,.42);
    min-height: 10vh;
    border-radius: 3px;
    max-height: 25vh;
    overflow: auto;
    display: block;
    height: 100%;
  }

.activeColorOfType{
  background-color: #f3f2f2;
  
}

  .cdk-drag-preview {
    // box-sizing: border-box;
    display: block;
    padding: 1% !important;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }
  
  .cdk-drag-placeholder {
    opacity: 0;
  }
  
//   .cdk-drag-animating {
//     transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
//   }
  
//   .example-box:last-child {
//     border: none;
//   }
  
  // .example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  //   transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  // }


  .documentTypeNameContainer{
    font-size: 0.85rem;
    font-weight: 400;
    padding: 1% 5%;
  }
  .headerLabel{
    margin: 1% 0 4%;
  }
  .arrowRight{
    top: 20%;
    position: absolute;
  }
  .arrowLeft{
    position: absolute;
    bottom: 5%;
  }
  .mt-5{
    margin-top: 5%;
  }
  .noteClass{
    width: 100%; 
    margin: 1% 0;
  }
