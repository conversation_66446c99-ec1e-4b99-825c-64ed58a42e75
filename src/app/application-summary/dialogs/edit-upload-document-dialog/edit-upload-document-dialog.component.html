<mat-dialog-content  class="mat-dialog-content-custom-css">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="85%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
      <h2> {{"label.title.editDocument"|literal}}</h2>
    </div>
    <div fxFlex="13%" >
      <!-- <button mat-button  (click)="closeDialog()"> -->
      <mat-icon (click)="closeDialog()" class="pointer">close</mat-icon>
      <!-- </button> -->
    </div>

    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-form-field class="width-100">
        <mat-label>{{"label.field.documentTypeEdit"|literal}}</mat-label>
        <input matInput #input  [(ngModel)]="selectedDocumentType"
            readonly/>
      </mat-form-field>
    </div>

    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class=" uploadDocumentsInputs">
      <mat-form-field  appFileDragNDrop (filesChangeEmiter)="fileUpload($event)" class="width-100 font-6">
        <span class="font-12">{{this.selectedFileName}}</span>
      
        <input matInput class="displayInput">
        <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
          (change)="fileUpload($event.target.files[0])" accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic"  readonly/>

      </mat-form-field>
      <section class="example-section" *ngIf="showFileProgressBar">
        <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
          [bufferValue]="bufferValue">
        </mat-progress-bar>
        
      </section>

    </div>

     <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <mat-form-field class="width-100">
  <mat-label>{{"label.field.tag"|literal}}</mat-label>
  <mat-chip-grid #chipList aria-label="tags selection">
    <mat-chip-row *ngFor="let tag of selectedtags"  
             [removable]="removable" (removed)="remove(tag)">
      {{tag}}
      <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
    </mat-chip-row>
    <input #tagInput [matAutocomplete]="auto" placeholder="New tag..."
           [formControl]="tagCtrl"
           [matChipInputFor]="chipList"
           [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
           (matChipInputTokenEnd)="add($event)">
  </mat-chip-grid>

  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
    <mat-option *ngFor="let tag of filteredTags | async " [value]="tag">
      {{tag}}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>

    </div>



    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class=" uploadBtnContainerCss">
      <button mat-raised-button class="green " (click)="onUpload()" [disabled]="showFileProgressBar">{{"label.button.updateDocument"|literal}}</button>
    </div>
  </div>


</mat-dialog-content>