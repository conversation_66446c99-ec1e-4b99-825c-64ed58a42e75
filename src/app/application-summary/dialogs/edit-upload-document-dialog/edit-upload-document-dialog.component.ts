import { <PERSON><PERSON><PERSON>, ENTER } from "@angular/cdk/keycodes";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import {
  Component,
  ElementRef,
  Inject,
  OnInit,
  ViewChild,
} from "@angular/core";
import { UntypedFormControl, Validators } from "@angular/forms";
import { MatAutocompleteSelectedEvent } from "@angular/material/autocomplete";
import { MatChipInputEvent } from "@angular/material/chips";
import { ThemePalette } from "@angular/material/core";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { ProgressBarMode } from "@angular/material/progress-bar";
import { Observable, Subject } from "rxjs";
import { map, startWith, takeUntil } from "rxjs/operators";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { listOfDocumentTypes } from "../../static-data";
import JsonData from "src/assets/data.json";

@Component({
  selector: "app-edit-upload-document-dialog",
  templateUrl: "./edit-upload-document-dialog.component.html",
  styleUrls: ["./edit-upload-document-dialog.component.scss"],
})
export class EditUploadDocumentDialogComponent implements OnInit {
  private unsubscribe$ = new Subject();
  documentTypeList: any = listOfDocumentTypes;
  selectedDocumentType: any;
  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage: boolean = false;
  color: ThemePalette = "primary";
  mode: ProgressBarMode = "indeterminate";
  value = 50;
  bufferValue = 75;
  tags = [];
  JsonData: any;

  @ViewChild("tagInput") tagInput: ElementRef<HTMLInputElement>;

  // chip List Value for tags

  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  selectedtags = [];

  rowData;

  documentType = new UntypedFormControl("", [Validators.required]);
  deal = new UntypedFormControl("", [Validators.required]);
  tagCtrl = new UntypedFormControl("", []);
  fileSize: any;
  filteredTags: Observable<any[]>;

  constructor(
    public matDialog: MatDialog,
    private errorService: ErrorService,
    public dialogRef: MatDialogRef<EditUploadDocumentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService
  ) {
    this.dataFromParentComponent = data;
    this.tags = this.dataFromParentComponent.tags;
    this.rowData = this.dataFromParentComponent.row;
    if (this.rowData.tags) {
      this.selectedtags = this.rowData.tags;
    }
    this.selectedFileName = this.rowData.fileName;
    // this.documentType.setValue(this.rowData.documentTitle)
    this.selectedDocumentType = this.rowData.documentTitle;

    this.filteredTags = this.tagCtrl.valueChanges.pipe(
      startWith(""),
      map((tag) => this._filter(tag))
    );
  }

  private _filter(value: string): string[] {
    if (typeof value === "object") {
      return this.tags;
    }
    const filterValue = value?.toLowerCase();

    if (filterValue) {
      return this.tags.filter((tag) => tag.toLowerCase().includes(filterValue));
    } else {
      return this.tags;
    }
  }

  ngOnInit() {}

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
  }

  add(event: MatChipInputEvent): void {
    if (this.selectedtags && this.selectedtags.length > 4) {
      this.notificationMessage.error("Max five tags allowed");
      return;
    }
    const value = (event.value || "").trim();
    const index = this.selectedtags.indexOf(value);
    if (index >= 0) {
      // do not add
    } else {
      // Add our fruit
      if (value) {
        this.selectedtags.push(value);
      }
    }

    // Clear the input value
    this.tagInput.nativeElement.value = "";
    this.tagCtrl.setValue(null);
  }

  remove(tag): void {
    const index = this.selectedtags.indexOf(tag);

    if (index >= 0) {
      this.selectedtags.splice(index, 1);
    }
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    if (this.selectedtags.length > 4) {
      this.notificationMessage.error("Max five tags allowed");
      return;
    }
    const index = this.selectedtags.indexOf(event.option.viewValue);
    if (index >= 0) {
      // this.selectedtags.splice(index, 1);
    } else {
      this.selectedtags.push(event.option.viewValue);
    }

    this.tagInput.nativeElement.value = "";
    this.tagCtrl.setValue(null);
  }

  fileUpload(file) {
    // this.taskService.isFileUploaded = true;
    if (file) {
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 104857600) {
        this.showFileSizeErrorMessage = true;
      }
    }
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  onUpload() {
    this.documentType.markAsTouched();

    let payload = {
      documentId: this.rowData.documentId,
      dmsId: this.rowData.dmsId,
      dealId: this.rowData.dealId,
      fileName: this.selectedFileName,
      size: this.rowData.size,
      type: this.rowData.type,
      documentTitle: this.rowData.documentTitle,
      referenceList: [
        "Originate",
        this.dataFromParentComponent?.dealId?.toString(),
      ],

      tags: this.selectedtags,
    };

    this.dealService.updateDocument(this.rowData.documentId, payload).subscribe(
      (response) => {
        this.showFileSizeErrorMessage = false;
        this.showFileProgressBar = false;
        this.selectedFile = null;
        this.selectedFileName = "";
        // this.selectedDocumentType = "";
        this.notificationMessage.success(
          JsonData["label.success.UploadDocument"]
        );
        this.dialogRef.close(true);
      },
      (error) => {
        this.showFileProgressBar = false;
      }
    );
  }
}
