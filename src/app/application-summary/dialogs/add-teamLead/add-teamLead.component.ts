import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { Component, Inject, OnInit } from "@angular/core";
import { IdentityService } from "../../../shared-service/identity.service";
import { DataSharingService } from "../../../common/dataSharing.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-add-teamLead",
  templateUrl: "./add-teamLead.component.html",
  styleUrls: ["./add-teamLead.component.scss"],
})
export class AddTeamLeadComponent {
  usersList: any;
  teamLeadselected: any;
  dealUserDetails: any;
  team = [
    "<PERSON>esh<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "PoojaP",
    "KaranT",
    "ParinS",
    "AmiD",
    "KalpanaC",
  ];

  constructor(
    private identityService: IdentityService,
    private dataSharingService: DataSharingService,
    private errorService: ErrorService,
    public dialogRef: MatDialogRef<AddTeamLeadComponent>,
    public matDialog: MatDialog,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    @Inject(MAT_DIALOG_DATA) public data,
    public themeService: ThemeService
  ) {
    this.usersList = data?.userList;
    const valuesToRemove = ["KaranT", "KalpanaC"];
    this.usersList = this.usersList.filter(
      (i) => valuesToRemove.indexOf(i?.identifier) === -1
    );

    if (this.usersList.length == 0) this.getUserList();

    if (data.selectedTeamLead) {
      this.teamLeadselected = data.selectedTeamLead;
    } else {
      this.teamLeadselected = localStorage.getItem("user");
      this.team.push(localStorage.getItem("user"));
    }
  }

  getUserList() {
    this.identityService.getAllUser().subscribe((res) => {
      this.usersList = res;
      const valuesToRemove = ["KaranT", "KalpanaC"];
      this.usersList = this.usersList.filter(
        (i) => valuesToRemove.indexOf(i?.identifier) === -1
      );
    });
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  saveTeamLead() {
    let userArray = [];

    userArray = this.usersList.slice();

    const leadName = userArray.filter(
      (item) => item?.identifier == this.teamLeadselected
    )[0];
    const dealId = this.data?.dealId;
    const leadId = this.teamLeadselected;
    const leadFullName = leadName?.firstName + " " + leadName.lastName;

    this.dealService
      .updateDealLead(dealId, leadId, leadFullName)
      .subscribe((res) => {
        this.getDealTeamFormmated();
        this.dialogRef.close(this.dealUserDetails);
      });
  }

  getDealTeamFormmated() {
    let userArray = [];
    userArray = this.dataSharingService.selectedApplicationData.dealTeamList;

    if (userArray.length != 0) {
      const index = userArray.findIndex(
        (item) => item.teamName == this.teamLeadselected
      );

      if (index !== -1) {
        userArray[index].isTeamLead = true;
      }

      this.dealUserDetails = userArray.map((obj) => ({
        teamName: obj,
      }));
    }
  }
}
