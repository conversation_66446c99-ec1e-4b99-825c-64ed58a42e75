<mat-dialog-content *ngIf="!useNewThemeUI" class="mat-dialog-content-custom-css">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxFlex="40%" fxFlex.md="20%" fxFlex.xs="80%" fxFlex.sm="40%">
        <h2>{{"label.title.uploadDocument"|literal}}</h2>
      </div>
      <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="80%" fxFlex.sm="40%" class="ml43">
        <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>


    <div *ngIf="dataFromParentComponent.showdeal" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
      fxFlex.sm="100%">
      <mat-form-field class="width-100">
        <mat-label>Search Deal</mat-label>
        <input type="text" matInput [formControl]="deal" [matAutocomplete]="auto" />
        <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn"
          (optionSelected)="onSelectionChange($event)">
          <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
            {{ getCustomerName(option?.dealCustomerList) }}
          </mat-option>
        </mat-autocomplete>
        <mat-error *ngIf="deal.errors?.required">
          {{"label.materror.deal"|literal}}

        </mat-error>
      </mat-form-field>
    </div>


    <div *ngIf="!dealdetailpage" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-form-field class="width-100">
        <mat-label>{{"label.field.documentType"|literal}}</mat-label>
        <mat-select required [formControl]="documentType" [(ngModel)]="selectedDocumentType">
          <mat-option *ngFor="let type of filteredUsers" [value]="type.name || type">{{
            type.name ? type.name: type
            }}</mat-option>
        </mat-select>
        <mat-error *ngIf="documentType.errors?.required">
          {{"label.materror.updateQDE"|literal}}

        </mat-error>
      </mat-form-field>
    </div>

    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
      class="uploadDocumentsInputs">
      <mat-form-field appFileDragNDrop (filesChangeEmiter)="fileUpload($event)" class="width-100">
        <button (click)="fileDropRef.click()" mat-button class="green">
          {{"label.button.chooseFile"|literal}}
        </button>
        <span class="font-12">{{ this.selectedFileName }}</span>
        <span *ngIf="!this.selectedFileName" class="chooseFile custom-input-info-text">
          {{"label.dropFile"|literal}}</span>
        <input matInput class="displayInput" />
        <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
          (change)="fileUpload($event.target.files[0])"
          accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic" />
      </mat-form-field>
      <section class="example-section" *ngIf="showFileProgressBar">
        <mat-progress-bar class="example-margin" [color]="color" [mode]="mode"
          [value]="filePercentage" [bufferValue]="bufferValue">
        </mat-progress-bar>
      </section>

      <small *ngIf="showFileSizeErrorMessage">
        {{"label.fileSize"|literal}} <strong>{{maxDocFileSize}}</strong>
      </small>


    </div>

    <div *ngIf="!dealdetailpage" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-form-field class="width-100">
        <mat-label>{{"label.field.tags"|literal}}</mat-label>
        <mat-chip-grid #chipList aria-label="tags selection">
          <mat-chip-row *ngFor="let tag of selectedtags" [removable]="removable"
            (removed)="remove(tag)">
            {{ tag }}
            <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
          </mat-chip-row>
          <input #tagInput [matAutocomplete]="auto" placeholder="New tag..." [formControl]="tagCtrl"
            [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            (matChipInputTokenEnd)="add($event)" />
        </mat-chip-grid>

        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
          <mat-option *ngFor="let tag of filteredTags | async" [value]="tag">
            {{ tag }}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
      <mat-hint class="noteForFile">{{"label.specificDocumentTags"|literal}}
      </mat-hint>
    </div>

    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
      class="uploadBtnContainerCss">
      <button mat-raised-button class="green" (click)="onUpload()" [disabled]="showFileProgressBar">
        {{"label.button.uploadDocumentDialog"|literal}}
      </button>
    </div>
  </div>
</mat-dialog-content>






<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="row wrap">

      <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start center">
          <h2>{{"label.title.uploadDocument"|literal}}</h2>
        </div>
        <div fxLayoutAlign="end center">
          <span>
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon class="material-symbols-outlined">close</mat-icon>
            </button>
          </span>
        </div>
      </div>

      <div fxFlex="100%" class="m-t-15">
        <div fxFlex="100%">

          <div *ngIf="dataFromParentComponent.showdeal">
            <mat-form-field fxFlex="100%" class="custom-mat-input-style">
              <mat-label>Search Deal</mat-label>
              <input type="text" matInput [formControl]="deal" [matAutocomplete]="auto" />
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn"
                (optionSelected)="onSelectionChange($event)">
                <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
                  {{ getCustomerName(option?.dealCustomerList) }}
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="deal.errors?.required"> {{"label.materror.deal"|literal}}
              </mat-error>
            </mat-form-field>
          </div>

          <div *ngIf="!dealdetailpage">
            <mat-form-field fxFlex="100%" class="custom-mat-input-style">
              <mat-label>{{"label.field.documentType"|literal}}</mat-label>
              <mat-select required [formControl]="documentType" [(ngModel)]="selectedDocumentType">
                <mat-option *ngFor="let type of filteredUsers" [value]="type.name || type">{{
                  type.name ? type.name: type }}</mat-option> </mat-select>
              <mat-error *ngIf="documentType.errors?.required">
                {{"label.materror.updateQDE"|literal}} </mat-error>
            </mat-form-field>
          </div>

          <div class="uploadDocumentsInputs">

            <mat-form-field fxFlex="100%" appFileDragNDrop (filesChangeEmiter)="fileUpload($event)"
              floatLabel="always" class="custom-mat-input-style">
              <mat-label>Select Document</mat-label>
              <button color="primary" (click)="fileDropRef.click()"
                mat-button>{{"label.button.chooseFile"|literal}}</button>
              <span>{{ this.selectedFileName }}</span>
              <span *ngIf="!this.selectedFileName" class="custom-input-info-text">
                {{"label.dropFile"|literal}}
              </span>
              <input matInput class="displayInput" />
              <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
                (change)="fileUpload($event.target.files[0])" accept=".xls,.xlsx" />
            </mat-form-field>

            <section class="example-section" *ngIf="showFileProgressBar">
              <mat-progress-bar class="example-margin" [color]="color" [mode]="mode"
                [value]="filePercentage" [bufferValue]="bufferValue">
              </mat-progress-bar>
            </section>

            <small *ngIf="showFileSizeErrorMessage">
              {{"label.fileSize"|literal}} <strong>{{maxDocFileSize}}</strong>
            </small>
          </div>

          <div *ngIf="!dealdetailpage">
            <mat-form-field fxFlex="100%" class="custom-mat-input-style">
              <mat-label>{{"label.field.tags"|literal}}</mat-label>
              <mat-chip-grid #chipList aria-label="tags selection">
                <mat-chip-row *ngFor="let tag of selectedtags" [removable]="removable"
                  (removed)="remove(tag)">
                  {{ tag }}
                  <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                </mat-chip-row>
                <input #tagInput [matAutocomplete]="auto" placeholder="New tag..."
                  [formControl]="tagCtrl" [matChipInputFor]="chipList"
                  [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                  (matChipInputTokenEnd)="add($event)" />
              </mat-chip-grid>

              <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
                <mat-option *ngFor="let tag of filteredTags | async" [value]="tag">
                  {{ tag }}
                </mat-option>
              </mat-autocomplete>
              <mat-hint [align]="'end'" class="hint">
                {{"label.specificDocumentTags"|literal}}
              </mat-hint>
            </mat-form-field>
          </div>

        </div>
      </div>


    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button" fxLayout="row wrap">
      <button color="primary" [disabled]="showFileProgressBar" aria-label="upload-document"
        mat-raised-button type="submit" (click)="onUpload()">
        {{"label.button.uploadDocumentDialog"|literal}}</button>
    </div>
  </mat-card-footer>
</div>
