import {
  Component,
  OnIni<PERSON>,
  Inject,
  ViewChild,
  ElementRef,
} from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { listOfDocumentTypes } from "../../static-data";
import { DealService } from "src/app/shared-service/deal.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import { ThemePalette } from "@angular/material/core";
import { ProgressBarMode } from "@angular/material/progress-bar";
import { ToasterService } from "src/app/common/toaster.service";
import { Validators, UntypedFormControl } from "@angular/forms";
import { Observable, Subject } from "rxjs";
import { map, startWith, takeUntil, filter } from "rxjs/operators";
import { COMMA, ENTER } from "@angular/cdk/keycodes";
import { MatChipInputEvent } from "@angular/material/chips";
import { MatAutocompleteSelectedEvent } from "@angular/material/autocomplete";
import { DataSharingService } from "../../../common/dataSharing.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";
import { DownloadFileService } from "src/app/shared-service/download-file.service";
@Component({
  selector: "app-upload-document-dialog",
  templateUrl: "./upload-document-dialog.component.html",
  styleUrls: ["./upload-document-dialog.component.scss"],
})
export class UploadDocumentDialogComponent implements OnInit {
  private unsubscribe$ = new Subject();
  filteredUsers: any = [];
  documentTypeList: any = this.filteredUsers;
  dealdetailpage: boolean = false;
  allDocumentList: any = [];
  selectedDocumentType: any;
  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage: boolean = false;
  color: ThemePalette = "primary";
  mode: ProgressBarMode = "indeterminate";
  value = 50;
  bufferValue = 75;
  tags = [];
  JsonData: any;
  maxDocFileSize;

  @ViewChild("tagInput") tagInput: ElementRef<HTMLInputElement>;

  // chip List Value for tags

  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  selectedtags = [];
  deals = [];

  documentType = new UntypedFormControl("", [Validators.required]);
  deal = new UntypedFormControl("", [Validators.required]);
  tagCtrl = new UntypedFormControl("", []);
  fileSize: any;
  filteredTags: Observable<any[]>;
  filteredOptions: any;
  useNewThemeUI: any;

  constructor(
    public matDialog: MatDialog,
    private errorService: ErrorService,

    public dialogRef: MatDialogRef<UploadDocumentDialogComponent>,
    public dataSharingService: DataSharingService,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    protected themeService: ThemeService,
    private downloadFileService: DownloadFileService
  ) {
    this.dealdetailpage = data?.dealpage;
    this.dataFromParentComponent = data;
    this.tags = this.dataFromParentComponent.tags;

    this.tags = this.dataFromParentComponent.tags
      ? this.dataFromParentComponent.tags
      : [];
    this.deals = this.dataFromParentComponent.deals;
    this.filteredTags = this.tagCtrl.valueChanges.pipe(
      startWith(""),
      map((tag) => this._filter(tag))
    );
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  // get Tags when We select deal for Quick upload
  getAllTags() {
    this.dealService
      .getTagsByDealId(this.deal.value.id)
      .subscribe((response: any) => {
        let tag;
        tag = response;
        this.tags = [];

        let uniquetags = [...new Set(tag.map((item) => item))];
        setTimeout(() => {
          uniquetags.filter((tag) => {
            this.tags.push(tag);
          });
        }, 2000);
      });
  }

  // filter tags
  private _filter(value: string): string[] {
    if (typeof value === "object") {
      return this.tags;
    }
    const filterValue = value?.toLowerCase();

    if (filterValue) {
      return this.tags.filter((tag) => tag.toLowerCase().includes(filterValue));
    } else {
      return this.tags;
    }
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.getAllTheDocumentTypeList();
    this.filteredOptions = this.deal.valueChanges.pipe(
      startWith(""),
      map((value) => this._filterDeal(value))
    );

    if (this.dataFromParentComponent.documentType) {
      this.selectedDocumentType = this.dataFromParentComponent.documentType;
      // this.documentType.disable();
    }

    this.downloadFileService
      .getFileSizeLimitFromCache()
      .subscribe((limit) => (this.maxDocFileSize = limit));
  }

  //Filter Deal
  private _filterDeal(value: string): string[] {
    if (typeof value === "object") {
      return this.deals;
    }
    const filterValue = value?.toLowerCase();
    if (filterValue) {
      return this.deals.filter((option) =>
        this.getCustomerName(option.dealCustomerList)
          .toLowerCase()
          .includes(filterValue)
      );
    } else {
      return this.deals;
    }
  }

  // get All Documents List for selected deals
  getAllTheDocumentTypeList() {
    this.dealService
      .getBPDocumentTypeList(
        this.dataSharingService.selectedApplicationData?.businessProcessDetail
          ?.id
      )
      .subscribe((res: any) => {
        let data = res;
        this.documentTypeList = data?.documentList;
        this.filteredUsers = this.documentTypeList.filter(
          ({ Upload }) => Upload === "Yes"
        );
      });
  }

  // On Quick Upload selection of deal change the document list and tags

  onSelectionChange(event) {
    let deal = event.option.value;

    this.getAllTags();

    let docList = this.allDocumentList.filter(
      (doc) => doc.businessProcessDetail.name == deal.businessProcessDetail.name
    );
    if (docList.length > 0) {
      this.documentTypeList = docList[0].documentList;
    }
  }

  // getDocList(docList) {
  //   let docList = docList.filter(doc => doc.businessProcessDetail.name == this.dataSharingService.selectedApplicationData.businessProcessDetail.name)
  //    this.allDocumentList = data;
  //    this.documentTypeList = docList[0].documentList;
  // }

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
  }

  getCustomerName(data) {
    let customer = data.filter((ele) => ele.coApplicantFlag == false);
    if (customer && customer.length != 0) {
      return customer[0]?.customerName;
    } else {
      if (data) return data[0]?.customerName || "";
    }
  }

  displayFn(user): string {
    let data = user.dealCustomerList;
    let customer = data?.filter((ele) => ele.coApplicantFlag == false);
    if (customer && customer?.length != 0) {
      return customer[0]?.customerName || "";
    } else {
      if (data) return data[0]?.customerName || "";
    }
  }
  // return this.getCustomerName(user.dealCustomerList)
  add(event: MatChipInputEvent): void {
    const value = (event.value || "").trim();

    if (this.selectedtags.length > 4) {
      this.notificationMessage.error("Max five tags allowed");
      return;
    }
    const index = this.selectedtags.indexOf(value);
    if (index >= 0) {
      // do not add
    } else {
      // Add our fruit
      if (value) {
        this.selectedtags.push(value);
      }
    }

    // Clear the input value
    this.tagInput.nativeElement.value = "";
    this.tagCtrl.setValue(null);
  }

  remove(tag): void {
    const index = this.selectedtags.indexOf(tag);

    if (index >= 0) {
      this.selectedtags.splice(index, 1);
    }
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    const index = this.selectedtags.indexOf(event.option.viewValue);
    if (this.selectedtags.length > 4) {
      this.notificationMessage.error("Max five tags allowed");
      return;
    }
    if (index >= 0) {
      // this.selectedtags.splice(index, 1);
    } else {
      this.selectedtags.push(event.option.viewValue);
    }

    this.tagInput.nativeElement.value = "";
    this.tagCtrl.setValue(null);
  }

  fileUpload(file) {
    // this.taskService.isFileUploaded = true;
    if (file) {
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 104857600) {
        this.showFileSizeErrorMessage = true;
      }

      // if(this.dealdetailpage){
      //   if(this.fileSize >= 10485760){
      //     this.showFileSizeErrorMessage = true;
      //   }
      // }
    }
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  onUpload() {
    this.documentType.markAsTouched();
    this.documentType.markAsTouched();
    // EQ - to sort a document Pitch Deck ...
    let docType = this.selectedDocumentType?.replace(/[0-9]/g, "")?.trim();

    let data = {
      type: "UPLOAD",
      dealId:
        this.dataFromParentComponent && this.dataFromParentComponent.dealId
          ? this.dataFromParentComponent.dealId
          : 0,
      documentTitle: docType,
      referenceList: [
        "Originate",
        this.dataFromParentComponent?.dealId?.toString(),
      ],
      tags: this.selectedtags,
    };

    if (this.dealdetailpage) {
      if (!this.selectedFileName) {
        this.notificationMessage.error(
          "Please fill in all the required fields with valid data."
        );
        return;
      }
    } else if (this.documentType.invalid || !this.selectedFileName) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.showFileSizeErrorMessage) {
      return;
    }
    // let data = {
    //   "type": "UPLOAD",
    //   "dealId": this.dataFromParentComponent && this.dataFromParentComponent.dealId ? this.dataFromParentComponent.dealId  : 0,
    //   "documentTitle": this.selectedDocumentType,
    //   "referenceList": ["Originate" , this.dataFromParentComponent?.dealId?.toString()],
    //   "tags":this.selectedtags
    // }

    this.fileData = new FormData();
    this.fileData.append("document", JSON.stringify(data));
    if (this.selectedFile == null) {
      this.fileData.append("file", JSON.stringify(this.selectedFile));
    } else {
      this.fileData.append("file", this.selectedFile);
    }

    this.dealService
      .uploadDocumentForDeal(this.fileData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.showFileProgressBar = true;
            this.filePercentage = this.dealService.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            let documemtname = this.selectedFile;
            this.showFileSizeErrorMessage = false;
            this.showFileProgressBar = false;
            this.selectedFile = null;
            this.selectedFileName = "";
            // this.selectedDocumentType = "";
            this.notificationMessage.success(
              JsonData["label.success.UploadDocument"]
            );
            this.dialogRef.close(documemtname?.name);
          }
        },
        (error) => {
          this.showFileProgressBar = false;
        }
      );
  }
}
