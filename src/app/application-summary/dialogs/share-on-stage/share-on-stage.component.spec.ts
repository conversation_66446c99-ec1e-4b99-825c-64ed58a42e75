import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ShareOnStageComponent } from './share-on-stage.component';

describe('ShareOnStageComponent', () => {
  let component: ShareOnStageComponent;
  let fixture: ComponentFixture<ShareOnStageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ShareOnStageComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ShareOnStageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
