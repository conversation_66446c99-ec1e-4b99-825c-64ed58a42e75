import { Component, Inject, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { Observable } from "rxjs";
import { map, startWith } from "rxjs/operators";
import { IdentityService } from "src/app/shared-service/identity.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-share-on-stage",
  templateUrl: "./share-on-stage.component.html",
  styleUrls: ["./share-on-stage.component.scss"],
})
export class ShareOnStageComponent implements OnInit {
  shareOnStageForm: UntypedFormGroup;
  stage: UntypedFormGroup;
  section: UntypedFormGroup;
  selectedBusinessProcessWithStagedetails: any = [];
  userList: any[] = [];
  selectedApplicationsData: any;
  currentStageName: any;
  filteredOptions: Observable<string[]>;
  defaultValidity: any = 24;
  businessProcessList: any = [];
  selectedBusinessProcess: any;
  dataFromParentComponent: any;
  selectedExpireAfter: any = 1;
  fileData: FormData;
  JsonData: any;
  customerEmail: any;
  email: any;
  sections: any;
  useNewThemeUI: any;

  constructor(
    private identityService: IdentityService,
    private errorService: ErrorService,
    private fb: UntypedFormBuilder,
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<ShareOnStageComponent>,
    public businessProcessService: BusinessProcessService,
    public dataSharingService: DataSharingService,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    protected themeService: ThemeService
  ) {
    this.dataFromParentComponent = data;
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.sections = this.dataFromParentComponent.sharedSections;
    this.generateRequestForm();
    //this.getUserList()

    this.filteredOptions = this.shareOnStageForm
      .get("contactDetails")
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value))
      );
    let entity = this.data.businessProcessEntityDetails.dealCustomerList.find(
      (item) => item.coApplicantFlag == false
    );
    // businessProcessEntityDefinition is deprecated...needs to update with businessProcessEntityDefinitionList
    if (
      this.dataSharingService.getDataById.businessProcessEntityDefinition ||
      this.dataSharingService.getDataById.businessProcessEntityDefinitionList
    )
      this.getCustomerEmail(entity);
  }
  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.userList.filter(
      (option) => option.name.toLowerCase().indexOf(filterValue) === 0
    );
  }
  closeDialog() {
    this.dialogRef.close(false);
  }

  getCustomerEmail(entityDetails) {
    this.businessProcessService
      .getCustomerEmail(entityDetails.entityId)
      .subscribe((res: any) => {
        if (res.entityType == "Person") {
          this.email = res.customerDetails.entityDetail.find(
            (item) => this.getPropertyName(item) == "contactEmail"
          );
          this.customerEmail = this.email.contactEmail.value
            ? this.email.contactEmail.value
            : "";
          this.shareOnStageForm
            .get("contactDetails")
            ?.setValue(this.customerEmail);
        }
      });
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res: any) => {
        if (res) {
          this.userList = res?.map((item) => ({
            name: item?.firstName + " " + item?.lastName,
            email: item?.mailId,
          }));
        }
      },
      (error) => {}
    );
  }
  generateRequestForm() {
    if (this.dataFromParentComponent.currentSectionName) {
      this.shareOnStageForm = this.fb.group({
        contactDetails: [
          this.customerEmail ? this.customerEmail : "",
          Validators.required,
        ],
        selectedSections: ["", Validators.required],
        //"contactDetails": ['', [Validators.email, Validators.pattern(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)]],
        linkValidPeriod: [""],
        notes: [""],
      });
    } else {
      this.shareOnStageForm = this.fb.group({
        contactDetails: [
          this.customerEmail ? this.customerEmail : "",
          Validators.required,
        ],
        selectedSections: [""],
        //"contactDetails": ['', [Validators.email, Validators.pattern(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)]],
        linkValidPeriod: [""],
        notes: [""],
      });
    }
  }

  onShareOnStage() {
    if (this.shareOnStageForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    let mailIdRecipient = "";
    let contactName = this.shareOnStageForm.value.contactDetails;
    if (
      this.userList.filter(
        (option) => option.name == this.shareOnStageForm.value.contactDetails
      ).length == 0
    ) {
      let emailCheck = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      if (emailCheck.test(this.shareOnStageForm.value.contactDetails)) {
        mailIdRecipient = this.shareOnStageForm.value.contactDetails;
        contactName = "";
      } else {
        this.notificationMessage.error("Please provide valid email Id.");
        return;
      }
    } else {
      mailIdRecipient = this.userList.filter(
        (option) => option.name == contactName
      )[0].email;
    }

    this.fileData = new FormData();
    let email = [];
    email.push(mailIdRecipient);
    let requestName;
    let originateRequestData;
    let successmsg;
    if (this.dataFromParentComponent.currentSectionName) {
      let sectionArr = [];
      let sharedSections = this.shareOnStageForm.get("selectedSections").value;
      sharedSections.forEach((element) => {
        let sectionjson = {
          name: element,
          subsection: "",
        };
        sectionArr.push(sectionjson);
      });

      requestName = "Sharable section link";
      successmsg = JsonData["label.success.ShareOnLinkSection"];
      originateRequestData = {
        dealId: this.dataFromParentComponent.dealId,
        collaborationType: "STAGE_SHARE",
        stage: this.dataFromParentComponent.currentStageName,
        sectionDetails: sectionArr,
      };
    } else {
      requestName = "Sharable stage link";
      successmsg = JsonData["label.success.ShareOnLink"];
      originateRequestData = {
        dealId: this.dataFromParentComponent.dealId,
        collaborationType: "STAGE_SHARE",
        stage: this.dataFromParentComponent.currentStageName,
        sectionDetails: [],
      };
    }
    let dmsRequest = {
      reqName: requestName,
      reqDescription: this.shareOnStageForm.value.notes,
      docType: null,
      email: email,
      linkValidPeriod: this.shareOnStageForm.value.linkValidPeriod,
    };
    let originateRequest = originateRequestData;
    this.fileData.append("originateRequest", JSON.stringify(originateRequest));

    this.fileData.append("dmsRequest", JSON.stringify(dmsRequest));

    if (contactName) {
      dmsRequest["contactName"] = contactName;
    }
    this.dealService
      .sendRequestForShareOnStage(this.fileData)
      .subscribe((res) => {
        this.selectedBusinessProcess = "";
        this.notificationMessage.success(successmsg);
        this.dialogRef.close(true);
      });
  }
}
