import { takeUntil } from "rxjs/operators";
import { Component, OnInit } from "@angular/core";
import { ThemePalette } from "@angular/material/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ProgressBarMode } from "@angular/material/progress-bar";
import { Subject } from "rxjs";
import { UploadDocumentDialogComponent } from "src/app/application-summary/dialogs/upload-document-dialog/upload-document-dialog.component";
import { ToasterService } from "src/app/common/toaster.service";
import { AssetServiceService } from "src/app/shared-service/asset-service.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-asset-upload-dialog",
  templateUrl: "./asset-upload-dialog.component.html",
  styleUrls: ["./asset-upload-dialog.component.scss"],
})
export class AssetUploadDialogComponent implements OnInit {
  filteredUsers: any = [];
  documentTypeList: any = this.filteredUsers;
  dealdetailpage: boolean = false;
  allDocumentList: any = [];
  selectedDocumentType: any;
  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage: boolean = false;
  color: ThemePalette = "primary";
  mode: ProgressBarMode = "indeterminate";
  value = 50;
  bufferValue = 75;
  tags = [];
  DisableButton: boolean = false;
  private unsubscribe$ = new Subject();
  fileSize: any;
  JsonData: any;
  constructor(
    public matDialog: MatDialog,
    public assetservice: AssetServiceService,
    private errorService: ErrorService,
    public dialogRef: MatDialogRef<UploadDocumentDialogComponent>,
    public notificationMessage: ToasterService,
    public themeService: ThemeService
  ) {}

  ngOnInit(): void {}
  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
  }

  fileUpload(file) {
    if (file) {
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 1048576) {
        this.showFileSizeErrorMessage = true;
      }
      this.DisableButton = true;
    }
  }

  uploadAsset() {
    let data = {
      file: this.selectedFile,
    };

    if (this.showFileSizeErrorMessage) {
      return;
    }

    this.fileData = new FormData();
    this.fileData.append("document", JSON.stringify(data));
    if (this.selectedFile == null) {
      this.fileData.append("file", JSON.stringify(this.selectedFile));
    } else {
      this.fileData.append("file", this.selectedFile);
    }

    this.assetservice
      .documentUpload(this.fileData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.showFileProgressBar = true;
            this.filePercentage = this.assetservice.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            this.showFileSizeErrorMessage = false;
            this.showFileSizeErrorMessage = false;
            this.showFileProgressBar = false;
            this.notificationMessage.success(
              JsonData["label.success.UploadAsset"]
            );
            this.dialogRef.close(true);
          }
        },
        (error) => {
          this.showFileProgressBar = false;
        }
      );
  }

  closeDialog() {
    this.dialogRef.close(false);
  }
}
