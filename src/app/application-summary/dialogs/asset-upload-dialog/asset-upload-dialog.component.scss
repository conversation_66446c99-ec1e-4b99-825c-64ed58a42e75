
/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-custom-css{
    // padding:  0 !important;
    // margin: 0  !important
}


.uploadBtnContainerCss{
    justify-content: center;
    display: flex;
    // margin-top: 6%;
    // margin-bottom: 1%
}

.noteForFile{
    display: flex;
    font-size: 11px;
    justify-content:center;
    width: 100%;  
}

.uploadDocumentsInputs{
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
        bottom: 0 !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0 !important; 
    }
  }


  .fileInfo{
    position: absolute;
    right: 0;
    margin-top: 2%;
  }
  .font-17{
    font-size: 17px;
     color: grey
  }

  ::ng-deep .mdc-dialog .mdc-dialog__content{
    overflow-y: hidden;
  }

  ::ng-deep .mdc-dialog__surface{
    overflow-y: hidden;
  }

  .displayInput{
    display: none !important;
  }