<div *ngIf="!themeService.useNewTheme">
<mat-dialog-content class="mat-dialog-content-custom-css">
    <div fxLayout="row wrap"  >
      <div fxFlex="85%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
        <h2>{{"label.title.uploadAsset"|literal}}</h2>
      </div>
      <div fxFlex="15%" >
      
        <mat-icon (click)="closeDialog()" class="pointer floatRight">close</mat-icon>
   
      </div>
      
  
  
  
     
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="80%" fxFlex.sm="80%" class=" uploadDocumentsInputs">
        <mat-form-field
          appFileDragNDrop
          (filesChangeEmiter)="fileUpload($event)"
          class="width-100 font-6"
        >
          <button (click)="fileDropRef.click()" mat-button class="green">
            {{"label.button.chooseFile"|literal}}
          </button>
          <span class="font-12">{{ this.selectedFileName }}</span>
          <span
            *ngIf="!this.selectedFileName"
           class="font-17"
          >
           {{"label.dropFile"|literal}}</span
          >
          <input matInput class="displayInput" />
          <input
            type="file"
            class="displayInput"
            #fileDropRef
            id="fileDropRef"
            (change)="fileUpload($event.target.files[0])"
            accept=".xls,.xlsx"
          />
        </mat-form-field>
        <section class="example-section" *ngIf="showFileProgressBar">
          <mat-progress-bar
            class="example-margin"
            [color]="color"
            [mode]="mode"
            [value]="filePercentage"
            [bufferValue]="bufferValue"
          >
          </mat-progress-bar>
        </section>
        <mat-hint *ngIf="!dealdetailpage"  class="noteForFile"
          >{{"label.AssetDocumentformat"|literal}}
        </mat-hint>
  
        <small
          *ngIf="showFileSizeErrorMessage"
          class="font-10"
        >
          {{"label.fileSize"|literal}} <strong>{{"label.Assetsize"|literal}}</strong>
        </small>
      </div>
  
  
  
      <div fxFlex="100%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%" class="uploadBtnContainerCss">
        <button [disabled]="!DisableButton"  (click)="uploadAsset()"
          mat-raised-button
          class="green"
        >
        {{"label.button.uploadDocumentDialog"|literal}}
        </button>
      </div>
    </div>
  </mat-dialog-content>
</div>


<!-- new UI -->
 
<div *ngIf="themeService.useNewTheme">
<mat-dialog-content>
  <div fxLayout="row wrap">

    <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
      <div fxLayoutAlign="start center">
        <h2>{{"label.title.uploadAsset"|literal}}</h2>
      </div>
      <div fxLayoutAlign="end center">
        <span>
        <button mat-icon-button (click)="closeDialog()">
          <mat-icon class="material-symbols-outlined">close</mat-icon>
        </button>
      </span>
      </div>
    </div>

    <div fxFlex="100%" class="m-t-15">
    <div fxLayout="row wrap">
      <mat-form-field fxFlex="100%" appFileDragNDrop (filesChangeEmiter)="fileUpload($event)">
        <button color="primary" (click)="fileDropRef.click()" mat-button>
          {{"label.button.chooseFile"|literal}}
        </button>
        <span>{{ this.selectedFileName }}</span>
        <span *ngIf="!this.selectedFileName">
         {{"label.dropFile"|literal}}
        </span>
        <input matInput class="displayInput" />
        <input type="file" class="displayInput" #fileDropRef id="fileDropRef" (change)="fileUpload($event.target.files[0])" accept=".xls,.xlsx"/>
      </mat-form-field>

      <section *ngIf="showFileProgressBar">
        <mat-progress-bar [color]="color" [mode]="mode" [value]="filePercentage" [bufferValue]="bufferValue">
        </mat-progress-bar>
      </section>
      
      <mat-hint *ngIf="!dealdetailpage" class="hint noteForFile">{{"label.AssetDocumentformat"|literal}}</mat-hint>

      <small *ngIf="showFileSizeErrorMessage">
        {{"label.fileSize"|literal}}<strong>{{"label.Assetsize"|literal}}</strong>
      </small>

    </div>

    <div class="dialog-button" fxLayout="row wrap">
      <button color="primary" [disabled]="!DisableButton" (click)="uploadAsset()" mat-raised-button>
      {{"label.button.uploadDocumentDialog"|literal}}
      </button>
    </div>
  </div>
  </div>

</mat-dialog-content>
</div>