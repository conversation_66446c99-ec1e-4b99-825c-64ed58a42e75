<div *ngIf="!useNewThemeUI">

  <div fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

    <mat-icon (click)="onClose()" class="pointer"> close</mat-icon>
  </div>

  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
      <h2 class="subTitle">{{"label.title.shareOnLinkRequestDetails"|literal}}</h2>
    </div>
  </div>


  <div class="height-auto">
    <table *ngIf="!showLoaderSpinner" mat-table [dataSource]="dataSource" fxFlex="100%"
      fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mat-elevation-z0  mat-table-width">

      <!-- Name Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef> Shared On </th>
        <td mat-cell *matCellDef="let element"> {{element.createdDate +'Z'| dateTime }} </td>
      </ng-container>

      <!-- Weight Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="element.status == 'REQUEST_WIP'">Link Shared</span>
          <span *ngIf="element.status == 'REQUEST_COMPLETED'">Details Received</span>
          <span *ngIf="element.status == 'LINK_EXPIRED'">Link Expired</span>
          <span *ngIf="element.status == 'RECALLED'">Link Recalled</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef> Shared By </th>
        <td mat-cell *matCellDef="let element"> {{element.createdBy}} </td>
      </ng-container>

      <ng-container matColumnDef="sharedTo">
        <th mat-header-cell *matHeaderCellDef> Shared To </th>
        <td mat-cell *matCellDef="let element"> {{element.email[0]}} </td>
      </ng-container>

      <ng-container matColumnDef="stage">
        <th mat-header-cell *matHeaderCellDef> Stage </th>
        <td mat-cell *matCellDef="let element"> {{element.stage}} </td>
      </ng-container>

      <ng-container matColumnDef="sections">
        <th mat-header-cell *matHeaderCellDef> Sections </th>
        <td mat-cell *matCellDef="let element"> {{getSections(element.sectionDetails)}} </td>
      </ng-container>

      <ng-container matColumnDef="action">
        <th class="w-15" mat-header-cell *matHeaderCellDef> </th>
        <td class="w-15" mat-cell *matCellDef="let element">
          <!-- <button  mat-raised-button class=" green" (click)="resendRequest(element)" >
              RESEND
            </button> -->
          <button mat-raised-button class=" green" (click)="recallSharedStage(element)"
            [disabled]="element.status &&  element.status !=  'REQUEST_WIP' ">
            RECALL
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;">
      </tr>
    </table>
    <div *ngIf="showLoaderSpinner && !useNewThemeUI">
      <mat-spinner class="ShowLoader"></mat-spinner>
    </div>
  </div>

</div>


<ng-container *ngIf="useNewThemeUI">
  <div fxLayout="row wrap">

    <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
      <div fxLayoutAlign="start center">
        <h2>{{"label.title.shareOnLinkRequestDetails" | literal}}</h2>
      </div>
      <div fxLayoutAlign="end center">
        <span>
          <button mat-icon-button (click)="onClose()">
            <mat-icon class="material-symbols-outlined">close</mat-icon>
          </button>
        </span>
      </div>
    </div>
    <table *ngIf="!showLoaderSpinner" mat-table [dataSource]="dataSource" fxFlex="100%"
      fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mat-elevation-z0  mat-table-width">

      <!-- Name Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef> Shared On </th>
        <td mat-cell *matCellDef="let element"> {{element.createdDate +'Z'| dateTime}} </td>
      </ng-container>

      <!-- Weight Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="element.status == 'REQUEST_WIP'">Link Shared</span>
          <span *ngIf="element.status == 'REQUEST_COMPLETED'">Details Received</span>
          <span *ngIf="element.status == 'LINK_EXPIRED'">Link Expired</span>
          <span *ngIf="element.status == 'RECALLED'">Link Recalled</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef> Shared By </th>
        <td mat-cell *matCellDef="let element"> {{element.createdBy}} </td>
      </ng-container>

      <ng-container matColumnDef="sharedTo">
        <th mat-header-cell *matHeaderCellDef> Shared To </th>
        <td mat-cell *matCellDef="let element"> {{element.email[0]}} </td>
      </ng-container>

      <ng-container matColumnDef="stage">
        <th mat-header-cell *matHeaderCellDef> Stage </th>
        <td mat-cell *matCellDef="let element"> {{element.stage}} </td>
      </ng-container>

      <ng-container matColumnDef="sections">
        <th mat-header-cell *matHeaderCellDef> Sections </th>
        <td mat-cell *matCellDef="let element"> {{getSections(element.sectionDetails)}} </td>
      </ng-container>

      <ng-container matColumnDef="action">
        <th class="w-15" mat-header-cell *matHeaderCellDef> </th>
        <td class="w-15" mat-cell *matCellDef="let element">
          <!-- <button  mat-raised-button class=" green" (click)="resendRequest(element)" >
                      RESEND
                    </button> -->
          <button class="blue outlined-button" mat-raised-button
            (click)="recallSharedStage(element)"
            [disabled]="element.status &&  element.status !=  'REQUEST_WIP' ">
            RECALL
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;">
      </tr>
    </table>

  </div>
  <div *ngIf="showLoaderSpinner && useNewThemeUI">
    <mat-spinner class="main-loader"></mat-spinner>
  </div>
</ng-container>
