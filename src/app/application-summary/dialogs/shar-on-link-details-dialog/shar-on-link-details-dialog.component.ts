import { Component, Inject, OnInit } from '@angular/core';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { IdentityService } from 'src/app/shared-service/identity.service';
import { RequestdetailsdialogComponent } from '../requestdetailsdialog/requestdetailsdialog.component';
import JsonData from 'src/assets/data.json';
import { DatePipe } from '@angular/common';
import { Utils, evalStringExpression, formStringExpression } from "../../../helpers/utils";
import { ThemeService } from 'src/app/theme.service';
@Component({
  selector: 'app-shar-on-link-details-dialog',
  templateUrl: './shar-on-link-details-dialog.component.html',
  styleUrls: ['./shar-on-link-details-dialog.component.scss']
})
export class SharOnLinkDetailsDialogComponent implements OnInit {
  fileData: any = new FormData();
 
  displayedColumns: string[] = ['createdBy','sharedTo','stage','sections','createdDate','status','action']

   dataSource :any
   //dataSource = ELEMENT_DATA;
  
 
  showNoDataMessage: any = false;
  noDataMessage: string;
  requestId:any;
  dealRequestDocumentList:any;
  createdDate:any;
  status:any;
  docType:any;
  table: any;
  selectedApplicationsData: any;
  showNoRecordsAvailbleMessage: boolean = false;
  showLoaderSpinner: boolean = true;
  refreshDataTable: any;
  element : any;
  ReqDetails:any
  private unsubscribe$ = new Subject();
  reqCreatedBy: any;
  JsonData:any;
  useNewThemeUI:any;
  
  constructor(public matDialog: MatDialog,
    public notificationMessage: ToasterService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public data,
    private bottomSheet: MatBottomSheetRef<SharOnLinkDetailsDialogComponent>,
    public dealService: DealService,
    private identityService: IdentityService,
    private dataSharingService:DataSharingService,
    private datePipe: DatePipe,
    protected themeService : ThemeService
    ) { 
      this.themeService.newThemeSwitch.subscribe(isNewTheme=>{ this.useNewThemeUI = isNewTheme; })
      this.ReqDetails = this.data.requestDetails
      this.getUserList()
    }

  ngOnInit(){
    this.useNewThemeUI = this.themeService.useNewTheme;
  }
  systemUsersList : any = []
  //this is to get full name of creator from userList
  getUserList() {
    this.showLoaderSpinner = true
    this.identityService.getAllUser().pipe( takeUntil(this.unsubscribe$) ).subscribe(res => {
      this.systemUsersList = [];
      this.systemUsersList = res.slice().map(ele =>{ return ({ id : ele.identifier , name : ele?.firstName + ' ' + ele?.lastName})} )
      this.ReqDetails.forEach(req => {
        let reqCreatedBy = this.systemUsersList.filter((item)=> item?.id == req?.createdBy) ;
        if(reqCreatedBy[0]?.name) req.creatorFullName = reqCreatedBy[0]?.name ;
      });
      this.dataSource = this.ReqDetails;
      this.showLoaderSpinner = false;
    }, (error) => {
      this.showLoaderSpinner = false;

    })
  }
  onClose() {
    
    this.bottomSheet.dismiss();
  
}
  recallSharedStage(request){

    this.dealService.recallSharedStage(request.requestId).subscribe( resp =>{
      this.notificationMessage.success(JsonData["label.success.SharedStage"]);
      this.bottomSheet.dismiss(true);
      
    }, 
    (err)=>{
      this.showLoaderSpinner = false;

    })
  }

  getSections(sectionDetails){
    let sections = [];
    sectionDetails.forEach(element => {
      sections.push(element.name); 
    });
    return sections;
  }

 
}
