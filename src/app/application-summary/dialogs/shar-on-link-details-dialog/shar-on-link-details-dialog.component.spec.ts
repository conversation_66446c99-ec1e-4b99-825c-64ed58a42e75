import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SharOnLinkDetailsDialogComponent } from './shar-on-link-details-dialog.component';

describe('SharOnLinkDetailsDialogComponent', () => {
  let component: SharOnLinkDetailsDialogComponent;
  let fixture: ComponentFixture<SharOnLinkDetailsDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SharOnLinkDetailsDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SharOnLinkDetailsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
