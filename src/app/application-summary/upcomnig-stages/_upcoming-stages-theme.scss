@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);
  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $accent-palette :map.get($color-config, 'accent');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);
  $accent-color :mat.get-color-from-palette($accent-palette, 400);
  $accent-contrast: mat.get-contrast-color-from-palette($accent-palette,400);

  .stages-drawer-content{
    .header-section{
        background: $primary-color;
        color: $primary-contrast;
    }

    .current-stage-section{
        background: mat.get-color-from-palette($primary-palette, 600);
        color: mat.get-contrast-color-from-palette($primary-palette, 600);
    }

    .stage-completed{
      color: $accent-color;
    }
    .completed-icon{
      background-color: $accent-color;
      color: $accent-contrast;
    }
    
    .stage-skipped{
      color: var(--faded-text-color);
    }
    .skipped-icon{
      background-color: var(--faded-text-color);
      color: $accent-contrast;
    }
    
    
  }

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .stages-drawer-content{
    .header-section{
        .header-text{
            @include mat.typography-level($typography-config, 'headline-5');
        }
    }

    .current-stage-section{
      .stage-title{
        @include mat.typography-level($typography-config, 'headline-6');
      }
      .active-stage{
        @include mat.typography-level($typography-config, 'headline-5');
      }
      .total-stages{
        @include mat.typography-level($typography-config, 'headline-6');
      }
      .active-stage-name{
        @include mat.typography-level($typography-config, 'subtitle-1');
      }
    }
    .stage-completed{
            @include mat.typography-level($typography-config, 'subtitle-1');
    }
    .stage-upcoming{
      @include mat.typography-level($typography-config, 'subtitle-1');
}
  }

}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}