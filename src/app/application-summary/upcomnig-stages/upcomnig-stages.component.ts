import { STEPPER_GLOBAL_OPTIONS } from "@angular/cdk/stepper";
import { Component, Input, OnInit } from "@angular/core";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { StagePreviewSheetComponent } from "../stage-preview-sheet/stage-preview-sheet.component";
import { BPStage } from "src/app/settings/bussiness-process-config/business-prcess.model";

@Component({
  selector: "app-upcomnig-stages",
  templateUrl: "./upcomnig-stages.component.html",
  styleUrls: ["./upcomnig-stages.component.scss"],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
  ],
})
export class UpcomnigStagesComponent implements OnInit {
  stageList = [];
  selectedStepIndex = 0;
  activeStage: BPStage;
  @Input() status: "upcoming" | "completed";
  constructor(
    public dataSharingService: DataSharingService,
    private bottomSheet: MatBottomSheet
  ) {}

  ngOnInit() {
    this.getStageList();
  }

  getStageList() {
    const stages =
      this.dataSharingService?.selectedBusinessProcessWithStagedetails;
    const currentStageName =
      this.dataSharingService.selectedApplicationData?.currentStageName;
    this.activeStage =
      this.dataSharingService.selectedBusinessProcessWithStagedetails?.find(
        (stage) => stage.name === currentStageName
      );
    if (stages?.length > 0 && this.activeStage) {
      this.stageList = stages
        .map((stage: BPStage) => ({
          name: stage.name,
          completed: stage.order < this.activeStage.order,
          current: stage.order === this.activeStage.order,
          upcoming: stage.order > this.activeStage.order,
          display:
            stage.name !== "Rejected" &&
            stage.name !== "Approved" &&
            stage.order !== 1 &&
            stage.display !== "Inactive",
        }))
        .filter((stage) => stage.display);

      this.selectedStepIndex = this.stageList.findIndex(
        (stage) => stage.current
      );
    }
  }

  getFilteredList() {
    return this.stageList.filter((stage) => stage[this.status]);
  }

  previewStage(stageIndex) {
    const selectedStage = this.stageList[stageIndex];
    this.dataSharingService.previousstagesDrawerToggle.next(false);

    this.bottomSheet.open(StagePreviewSheetComponent, {
      panelClass: "large-bottom-sheet",
      data: {
        selectedStageDetails: {
          name: selectedStage.name,
          index: stageIndex + 1,
          totalStages: this.stageList.length,
        },
      },
    });
  }

  isStageSkipped(stageName: string): boolean {
    const optionalStageSkipList =
      this.dataSharingService.selectedApplicationData?.optionalStageSkipList ||
      [];
    return optionalStageSkipList.some((stage) => stage.stageName === stageName);
  }
}
