<div class="stages-drawer-content">
  <div class="header-section" fxLayout="row" fxLayoutAlign="start center" >
    <div fxFlex="10" *ngIf="status === 'upcoming'">
      <button mat-icon-button (click)="dataSharingService.toggleUpcomingStagesDrawer(false)">
        <mat-icon>arrow_forward</mat-icon>
      </button>
    </div>
     <span *ngIf="status === 'completed'" fxFlexOffset="5" class="header-text">Previous Stages</span> 
     <span *ngIf="status === 'upcoming'" fxFlexOffset="5" class="header-text">Next Stages</span> 
     <div *ngIf="status === 'completed'" class="completed-close">
      <button mat-icon-button (click)="dataSharingService.toggleCompletedStagesDrawer(false)">
        <mat-icon>arrow_backward</mat-icon>
      </button>
    </div>

  </div>

  <div class="current-stage-section" fxLayout="column" fxLayoutAlign="center start">
    <div fxFlex="100%">
      <span class="stage-title">Current Stage</span>
    </div>
    <div fxFlex="100%">
      <span class="active-stage">{{selectedStepIndex + 1}}</span>
      <span class="total-stages">/{{this.stageList.length}}</span>
      <span class="active-stage-name">{{this.activeStage.name}}</span>
    </div>
  </div>

  <div class="stage-list">
    <mat-list>
      <ng-container *ngFor="let stage of stageList; let stageIndex = index">
      <mat-list-item *ngIf="stage[status]" (click)="stage.completed && !isStageSkipped(stage.name) ? previewStage(stageIndex) : null">
        <mat-icon *ngIf="stage.completed && !isStageSkipped(stage.name)" matListItemIcon class="completed-icon">check</mat-icon>
        <mat-icon *ngIf="isStageSkipped(stage.name)" matListItemIcon class="skipped-icon">check</mat-icon>
        <span *ngIf="stage.upcoming" matListItemIcon class="material-symbols-outlined">radio_button_unchecked</span>
        <div matListItemTitle [ngClass]="{'stage-completed': stage.completed, 'stage-upcoming': stage.upcoming, 'stage-skipped': isStageSkipped(stage.name)}">
        <span> {{stageIndex + 1}}</span>
        <span>{{ stage.name.length >50 ? stage.name.slice(0,35) + '...' : stage.name}}</span>
        </div>
      </mat-list-item>
    </ng-container>
    </mat-list>
  </div>
  
</div>