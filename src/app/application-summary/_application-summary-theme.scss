@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);

  .application-summary-containter {
    .application-summary-content {
      .summary-container-1 {

        .summary-header-panel {
          mat-expansion-panel {
            background-color: var(--container-color) !important;
          }
        }

        .toggle-view-icon {
          border: solid 1px var(--disabled-button-border-color);
        }

        .toggle-view-icon:hover {
          background: $primary-color;
          color: $primary-contrast;
        }
      }

      .summary-container-3 {

        .stage-info-main {
          background-color: var(--container-color) !important;

          .stage-info {
            .active-stage {
              background-color: $primary-color;
              color: $primary-contrast;
            }
          }
        }

        .next-stages-icon-container {
          border: solid 1px var(--disabled-button-border-color);
        }

        .next-stages-icon-container:hover {
          background: $primary-color;
          color: $primary-contrast;
        }

        .change-stage-icon {
          border: solid 1px var(--disabled-button-border-color);
        }

        .change-stage-icon:hover {
          background: $primary-color;
          color: $primary-contrast;
        }

      }

      .summary-container-4 {
        mat-form-field:hover {
          .mat-mdc-text-field-wrapper {
            background-color: $primary-color;
          }

          .mat-mdc-select-value {
            color: $primary-contrast;
          }

          .mat-mdc-select-arrow {
            color: $primary-contrast;
          }
        }
      }
    }

    .active-stage {
      background-color: $primary-color;
      color: $primary-contrast;
    }

  }

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .application-summary-containter {
    .application-summary-content {
      .summary-container-1 {

        .summary-header-panel {
          mat-expansion-panel {
            mat-expansion-panel-header {
              mat-panel-title {
                @include mat.typography-level($typography-config, 'headline-5');
              }
            }

            .panel-content {
              .summary-labels {
                @include mat.typography-level($typography-config, 'body-2');
                font-weight: 600;
              }
            }

          }
        }
      }

      .summary-container-3 {
        .stage-info {
          @include mat.typography-level($typography-config, 'headline-6');

          .stage-name {
            @include mat.typography-level($typography-config, 'headline-5');
          }
        }
      }

      .summary-container-4 {
        mat-form-field {
          mat-select {
            @include mat.typography-level($typography-config, 'headline-6');
          }
        }

      }
    }
  }

}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
