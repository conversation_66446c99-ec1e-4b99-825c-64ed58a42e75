.oldUI{
  .titleCSS {
    font-size: 24px;
    font-weight: 500;
    margin: 0;
    
  }
  
  ::ng-deep {.mat-mdc-menu-panel.user-menu { width: 400px; } }
  .subTitle {
    margin-top: 1%;
    font-size: 16px;
    font-weight: 400;
  }
  
  .labelChips {
    display: inline-block;
    margin-top: 0.5%;
  
    .mat-mdc-standard-chip {
      padding: 2px 5px !important;
      min-height: 22px !important;
      height: auto;
    }
  }
  /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version. */
  /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version. */
  .mat-chip-list-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    margin: -1px;
  }
  .scoreContainer {
    background-color: #fbff0f;
    width: fit-content;
    padding: 7% 10%;
    border-radius: 6px;
  }
  
  .greenBackground {
    background-color: green !important;
  }
  
  .scoreCSS {
    display: flex;
    justify-content: center;
    font-size: 25px;
  }
  
  .stepperContainer {
    margin: 2% 0;
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    ::ng-deep.mat-button-toggle-appearance-standard
      .mat-button-toggle-label-content {
      line-height: 25px;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    ::ng-deep.mat-button-toggle {
      background: transparent;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version. */
    ::ng-deep .mat-button-toggle-checked {
      background-color: transparent;
    }
  }
  
  .breadcrumb {
    width: 100%;
    display: flex;
    box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border-radius: 5px;
  
    counter-reset: flag;
  }
  
  .breadcrumb .stage {
    text-decoration: none;
    text-transform: capitalize;
    outline: none;
    display: flex;
    float: left;
    justify-content: center;
    font-size: 15px;
    line-height: 30px;
    color: white;
  
    background: #666;
    background: linear-gradient(#666, #333);
    position: relative;
  }
  
  .breadcrumb .stage:first-child {
    padding-left: 26px;
    border-radius: 5px 0 0 5px;
  }
  .breadcrumb .stage:first-child:before {
    left: 14px;
  }
  .breadcrumb .stage:last-child {
    border-radius: 0 5px 5px 0;
    padding-right: 100%;
  }
  
  .breadcrumb .stage:after {
    content: "";
    position: absolute;
    top: 0;
    right: -15px;
    width: 31px;
    height: 31px;
    transform: scale(0.707) rotate(45deg);
  
    z-index: 1;
  
    background: #666;
    background: linear-gradient(135deg, #666, #333);
  
    box-shadow: var(--box-shadow);
    border-radius: 0 5px 0 50px;
  }
  
  .flat .stage,
  .flat .stage:after {
    background: white;
    color: black;
    transition: all 0.5s;
  }
  .flat .stage:before {
    background: white;
    box-shadow: 0 0 0 1px #ccc;
  }
  
  .flat .stage.active,
  .flat .stage.active:after {
    background: #e1ab24 !important;
    color:white;
  }
  
  
  .flat .stage.completed,
  .flat .stage.completed:after {
    background: #53a515;
    color:white;
  }

  .flat .stage.skipped,
  .flat .stage.skipped:after {
    background: gray;
    color:white;
  }
  
  .onHyperLinkHover:hover{
    color: blue;
  }
  
  .applicationHeader{
    grid-gap: 0 !important;
  }
  
  .content-1{
    width:100%;
  }
  
  .content-2{
    display: inline-flex;
  }
  
  .content-3{
    display: inline-block
  }
  
  .buttonLeftNav{
    margin-top: -10px; 
    font-size: 21px;
    margin-right: -8px;
  }
  
  .rightNav{
    margin-right: -2% !important;
    margin-bottom: 1% !important; 
    margin-top: -3% !important;
  }
  
  .buttonRightNav{
    float: right; 
    margin-left: 1%;
  }
  .pt0{
    padding-top:0% !important
  }
  .bpName{
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-top:1% !important;
   // margin-left: -13% !important;
  }
  .labelName{
    font-size: 16px !important;
    font-weight: 400 !important;
     margin-top:1.5% !important;
  }
  .mb-10{
    margin-bottom: -8%;
  }
  
}

//adding panel class outside as it is not worked inside container class
.userlistCss,.update-team-panel{
  min-height: 250px;
  min-width: 15vw;
  overflow-y: auto;
}

.centerPosition{
  left : 50%;
  transform: translate(-50%);

}

//New UI

.application-summary-containter{

  .application-summary-content{
    mat-expansion-panel.top-action-rows-panel{
       box-shadow: none !important;
        ::ng-deep .mat-expansion-panel-body {
        padding: 0;
       }
       background: transparent !important;
    }

    .summary-container-1{
      mat-expansion-panel{ box-shadow: none !important;}
      
      .toggle-view-icon{
        transition: background 0.5s;
        cursor: pointer;
        border-radius: 5px;
        padding: 11px 9px;
        height: 47px !important;
        width: 45px !important;
      }
    }
  
    .summary-container-2{
      padding-top: 1%;
    }
    .summary-container-3{
      margin-top: 1%;
      .stage-info-main{
         padding: 1% 0;
         margin:0px 8px;
         border-radius: 8px;
      }
      .next-stages-icon-container{
        padding: 15px 10px;
        transition: background 0.5s;
        cursor: pointer;
        border-radius: 5px;
  
        .next-stages-list-icon{
          display: block;
        }
        .next-stages-arrow-icon{
          display: none;
        }
      }
  
      .next-stages-icon-container:hover{
        .next-stages-list-icon{
          display: none;
        }
        .next-stages-arrow-icon{
          display: block;
        }
      }
  
      .change-stage-icon{
        transition: background 0.5s;
        cursor: pointer;
        border-radius: 5px;
        padding: 15px 10px;
        height: 54px !important;
        width: 45px !important;
      }
  
      
  
      .stage-info{
        margin-left: 1%;
        .active-stage{
          display: inline-block;
          width: 30px; 
          height: 30px;
          line-height: 30px; 
          border-radius: 50%;
          text-align: center;
          margin-right: 10px;
        }
      }
      .width-100 {
        width: 100% !important;
      }
    }
    
  }

  .summary-container-5{
    flex: 1;
    overflow: hidden;
  }
  .mr-4{
    margin-right: 4%;
  }
  

  mat-divider.expand-divider:has(~ .expand-icon:hover) {
    border-top-color: var(--primary-color);
    border-top-width: 4px;
    border-radius: 8px;
    transition: border-top-color 0.5s;
  }

  .expand-icon:hover ~ mat-divider.expand-divider{
      border-top-color: var(--primary-color);
      border-top-width: 4px;
      border-radius: 8px;
      transition: border-top-color 0.5s;
  }

  .expand-icon:hover ~ mat-divider.expand-divider{
    border-top-color: var(--primary-color);
    border-top-width: 4px;
    border-radius: 8px;
    transition: border-top-color 0.5s;
}


  .expand-icon:hover{
    color: var(--primary-color);
  }


}

.application-summary-containter.view-port-height{
  display: flex;
  flex-direction: column;
  height: 90vh;
}

.active-stage{
  display: inline-block;
  width: 24px; 
  height: 24px;
  line-height: 25px; 
  border-radius: 50%;
  text-align: center;
  margin-right: 5px;
}




