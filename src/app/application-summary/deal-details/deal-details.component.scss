  .keyName {
    margin: 5% 0;
    font-size: 14px;
    font-weight: 500;
  }


  .valueContainer {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
  }


  ::ng-deep .mat-pseudo-checkbox-disabled {
    display: none !important;
  }

  ::ng-deep .disbledOption .mat-mdc-option:first-child .mat-pseudo-checkbox {
    display: none;
  }

  .hyperlinkColor:hover {
    text-decoration: underline !important;
  }

  .inputPicker {

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-infix {
      // padding: .5em 0;
      border-top: .24375em solid transparent !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
      bottom: 0 !important;
    }
  }

  .loaderInSideDealDetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .datePicker {

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
      // border-radius: 4px 4px 0 0;
      padding: .15em .75em 0 .75em !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
      bottom: 0 !important;
    }
  }


  .fixedHeightForFieldsCard {
    // height: auto;
    max-height: 450px;
    overflow-y: auto;
  }



  .searchedInput {
    ::ng-deep .mat-mdc-menu-panel {
      padding: 0 10px !important;
      min-width: 613px !important;
    }
  }

  .sharedStageHint {
    display: flex;
    align-items: center;
    margin-left: 1%;
  }

  // ---- To-Remove- Mat select search panel------//
  .example-viewport {
    height: 160px;
    // width: 471px;
    // border: 1px solid black;
  }

  ::ng-deep .mat-mdc-select-panel {
    max-height: 325px !important;
  }

  // ---- TO DO-Remove- Mat select search panel------//


  .expansionToggleHide {
    .mat-expansion-panel-header {
      padding: 0 0px;
    }

    .mat-mdc-option {
      width: 100% !important;
    }

    ::ng-deep.mat-content.mat-content-hide-toggle {
      margin-right: 0 !important;
    }

  }

  .expansionToggleShow {
    .mat-expansion-panel-header {
      padding-left: 0px
    }

    .mat-mdc-option {
      width: 99% !important;
    }

  }



  .universalItem {

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      height: 36px !important;
    }
  }

  .mat-drawer-content {
    min-height: 200px !important
  }

  ::ng-deep .mat-drawer-backdrop {
    z-index: 1;
  }

  ::ng-deep .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .single-line-input {
    width: 88%;
    /* Set the desired width */
    white-space: nowrap;
    /* Prevent line breaks */
    overflow: hidden;
    /* Hide overflowing text */
    text-overflow: ellipsis;
    /* Show ellipsis for overflowed text */
  }

  .website-input {
    float: right;
    position: absolute;
    bottom: -7%;
    left: 90%;
  }

  /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
  /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
  ::ng-deep .mat-select-search-panel {
    max-height: 300px !important;
    position: absolute !important;
    // top: 78px !important ;
  }

  .subsectionPanel {
    margin: 2%;
    box-shadow:
      0px 0px 5px 0px rgba(0, 0, 0, 0.2),
      0px 2px 2px 0px rgba(0, 0, 0, 0.14),
      0px 1px 5px 0px rgba(0, 0, 0, 0.12) !important;

    ::ng-deep .mat-expansion-panel-body {
      padding: 0px 16px !important;
    }
  }

  .iti__flag {
    height: 13px;
    background-position: -2413px 0px 1%;
    margin-bottom: -3% !important;
    margin-left: -14% !important;
  }

  .formcard {
    margin: 0 0 2% 0 !important;
    overflow-x: hidden !important;
    padding: 1% 0;
  }


  .loaderDiv {
    margin-top: -15px !important;
  }

  .loaderCard {
    margin-bottom: 3%;
    height: 10%;
  }

  .dealDetailsExpansionPanelFont {
    font-weight: bold;
  }

  .dealDetailsExtendedTextInput {
    width: 100%;
  }

  .dealDetailsRepetitiveSection {
    margin: 0 !important;
  }

  .dealDetailsRuleButton {
    float: right;
    margin-top: -9%;
  }

  .dealDetailsDocumentButton {
    float: right;
    margin-top: -5%;
  }

  .dealDetailsDocumentText {
    font-size: 17px;
    color: grey;
  }

  .dealDetailsInputDisabled {
    display: none;
  }

  .dealDetailsDocumentHintText {
    font-size: 10px;
    float: right;
    margin-right: 20%;
  }

  .dealDetailsDocDeleteButton {
    float: right;
    margin-top: -3%;
    margin-right: 19%;
  }



  .dealDetailsPublishButton {
    float: right;
    margin-top: -3%;
    margin-right: 0%;
  }

  .dealDetailsBooleanButtonSection {
    padding-top: 2%;
  }

  .dealDetailsBooleanYesButton {
    margin-right: 5%
  }

  .highlightSection {
    color: #e07571 !important;
  }

  .dealGenDocButton {
    float: right;
    margin-top: -3%;
    margin-right: 25%;
  }

  .dealGenDocPreviewButton {
    float: right;
    margin-top: -3%;
    // margin-right: 50%;
  }

  .dealGenDocDownloadButton {
    float: right;
    margin-top: -3%;
    // margin-right: 50%;
  }

  .mt-5 {
    margin-top: -5% !important
  }

  .p15 {
    padding: 15px !important
  }

  .selectInput {
    min-height: 48px !important;
    line-height: normal;
    height: auto;
  }

  // }

  //New UI


  ::ng-deep .mat-datepicker-content-container {
    width: 296px;
    height: 380px;
  }



  .deal-details-container {

    .action-buttons {
      padding: 0px 15px;
    }

    .section-tabs-width {
      ::ng-deep .mat-mdc-tab-header {
        width: 96% !important;
      }
    }

    //form{
    //  padding: 2%;
    //}

    .subsectionPanel {
      margin-bottom: 0 !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      margin-top: 2dvh;
      box-shadow: none !important;

      ::ng-deep .mat-expansion-panel-body {
        padding: 0px 16px !important;
      }
    }

    ::ng-deep .mat-mdc-tab-body {
      overflow: hidden;
    }

    ::ng-deep .mat-mdc-tab-body-wrapper {
      height: 100%;
    }

    ::ng-deep .mat-mdc-tab-body-content {
      padding-bottom: 5%;
    }

    .main-card {
      padding: 0 !important;
      //padding-bottom: 0%;
      box-shadow: none;
      background-color: transparent !important;

      .mat-expansion-panel {
        background-color: transparent !important;

        .mat-expansion-panel-header {
          position: relative;
          background-color: var(--container-color);
          overflow: hidden;

          &::after {
            content: "";
            position: absolute;
            inset: 0;
            background: var(--mat-expansion-header-hover-state-layer-color);
            opacity: 0;
            transition: opacity 0.2s;
            pointer-events: none;
          }

          &:hover::after,
          &:focus::after {
            opacity: 1;
          }
        }


      }

    }

    /* Remove bottom border when the panel is collapsed */
    mat-card .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header .mat-expansion-panel-header-title {
      border-bottom: none !important;
      /* Remove bottom border */
    }

    mat-card .mat-expansion-panel.mat-expanded .mat-expansion-panel-header .mat-expansion-panel-header-title {
      padding-bottom: 0.5dvh !important;
    }

  }

  .main-loader {
    display: flex;
    justify-content: center;
    margin-top: 5%;
  }

  .section-search-menu-panel {
    max-height: 350px;
    min-width: 15vw;
    overflow-y: auto;
  }

  .deal-search-tab-containter {
    float: right;
    margin-left: -4%;

    button {
      border-radius: 5px !important;
    }
  }

  .ml {
    margin-bottom: 1.5% !important;
  }

  .highlight-blank-option {
    background-color: #bdbdbd75;
    /* border: 1px solid #3f51b5; */
  }

  .preview-active-section .width-80 {
    width: 90%;
  }

  // //New UI Boolean Field Style start
  // .margin-for-boolean{
  //   margin-top: 2%;
  // }

  // .font-size-boolean
  // {
  //   font-weight: 600 !important;
  //    // color: rgba(0, 0, 0, 0.6) !important;
  //     font-size: 16px !important;
  //     margin: 21px 0px 10px 15px !important  ;
  // }

  // .boolean-background:hover{
  //   border-radius: 6px !important;
  // }

  // .boolean-background{
  //   border-radius: 6px !important;
  //   margin-bottom: 4% !important;
  // }
  // //New UI Boolean Field Style end
