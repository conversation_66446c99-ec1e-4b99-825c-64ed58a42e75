<mat-card appearance="outlined"  class="mat-card-top-border md-2">
  <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="stageItemsDetails.length != 0">
    <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%" *ngFor="let stageItem of stageItemsDetails ; let index = index">
      <div fxLayout="row wrap">
        <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%">
          <p class="keyName">{{stageItem.description}} :</p>
        </div>
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <p class="valueContainer">
            <span *ngIf="stageItem.inputType != 'Date'">{{stageItem.value  }}</span>
            <span *ngIf="stageItem.inputType === 'Date'">{{stageItem.value  | date}}</span>

          </p>
        </div>
      </div>
    </div>
  </div>
</mat-card>