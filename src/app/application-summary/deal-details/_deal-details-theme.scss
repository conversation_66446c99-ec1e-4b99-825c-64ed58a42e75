@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);


  .deal-details-container {

    .deal-search-tab-containter {
      button {
        background-color: var(--container-color) !important;
        ;
      }
    }

    .blue:hover {
      color: mat.get-color-from-palette($primary-palette, 400) !important;
    }

    .boolean-background:hover {
      background-color: var(--boolean-background-color-hover) !important;
    }

    .boolean-background {
      background-color: var(--boolean-background-color) !important;
    }

    .font-size-boolean {
      color: var(--boolean-font-color) !important;
    }
  }
}


@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .dealDetailsExpansionPanelFont {
    @include mat.typography-level($typography-config, 'headline-6');
  }

}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
