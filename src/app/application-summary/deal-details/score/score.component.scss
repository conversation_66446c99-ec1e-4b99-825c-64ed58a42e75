.scoreContainer {
    background-color: #fbff0f;
    width: fit-content;
    padding: 7% 10%;
    border-radius: 6px;
    margin-left: 33%;
  }

  .subTitle {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    font-size: 18px !important;
  }

.teamScoreCard{
  width: 65%;
}

  .verticalCentor{
    top: 50%;
    transform: translateY(-50%);
    position: relative;
  }

  .scoreCSS {
    display: flex;
    justify-content: center;
    font-size: 25px;
  }

  .overflow-scroll {
    overflow-y: auto;
    max-height: 59vh;
  }
  .extensionDivWidth {
    min-width: 70%;
  }
  
  /* Extra small devices (phones, 600px and down) */
  @media only screen and (max-width: 600px) {
    .extensionDivWidth {
        min-width: 100%;
    }
  }
  
  /* Small devices (portrait tablets and large phones, 600px and up) */
  @media only screen and (min-width: 600px) {
    .extensionDivWidth {
        min-width: 100%;
    }
  }

  .editor {
    height:20vw !important;
  }

  :host  { 
    ::ng-deep {
              .ck-editor__editable_inline {
                min-height: 25vw;
                max-height: 25vw;
                overflow-y: auto;
               }
  }
}
  
  /* Medium devices (landscape tablets, 768px and up) */
  @media only screen and (min-width: 768px) {
    .extensionDivWidth {
        min-width: 100%;
    }
  }
  
  /* Large devices (laptops/desktops, 992px and up) */
  @media only screen and (min-width: 992px) {
    .extensionDivWidth {
        min-width: 33%;
    }
  }
  
  /* Extra large devices (large laptops and desktops, 1200px and up) */
  @media only screen and (min-width: 1200px) {
    .extensionDivWidth {
       min-width:  49.8%;
       
    }
  }




:host  { 
  ::ng-deep {
    .mat-mdc-progress-spinner {
      left: 50% !important;
      /* top: 50%; */
      transform: translateX(-51%) !important;
  }
}
}

:host {
  ::ng-deep {
    .expansionHeader {
      .mat-expansion-panel-header {
        height: 13vh;
      }
    }
    
  }

}

:host ::ng-deep .track circle{       
  stroke-opacity: 0.3 !important;
}


.avg ::ng-deep .mat-mdc-progress-spinner circle, .mat-mdc-progress-spinner circle {
  position: relative;
  stroke:#FFBF00;
}

.good ::ng-deep .mat-mdc-progress-spinner circle, .mat-mdc-progress-spinner circle {
  position: relative;
  stroke:green;
}

.low ::ng-deep .mat-mdc-progress-spinner circle, .mat-mdc-progress-spinner circle {
  position: relative;
  stroke:red;
}



.mb-10 {
  margin-bottom: 2%;
}



.fab-add {
  z-index: 10000;
}

.fab-div {
  padding: 1%;
  z-index: 1000;
  position: fixed;
   bottom: 3%;
   right: 3%;
}


.spinnerGroup{
  position: inherit;
}

.outer {
  display: grid;
  grid-template: 1fr / 1fr;
  place-items: center;
}
.outer > * {
  grid-column: 1 / 1;
  grid-row: 1 / 1;
}
.outer .below {
  z-index: 2;
}
.outer .top {
  z-index: 1;
}
.background-spinner::ng-deep circle{
  stroke:#d3d1d1a9  !important;
}

.progressCard{
  .no-hover-effect ::ng-deep{
    position: absolute;
    left: 50%;
    top: 50%;
    box-shadow: none !important;
    transform: translate(-50%, -50%);
  box-shadow: none !important
} 
}
.loader{
 margin-left: 700px;  
}

  
.scoreNavBar{
  margin: 1.5% 0;
}

.scoreActionButtonsSection{
  float: right;
}

.scoreActionButton{
  margin-right: 10px;
}

.mainMenu{
  margin-bottom: 8%; 
  margin-top: 40px;
}



.scoreStageSelection{
  font-size: 12px;
  font-weight: 400;
}


.scoreCardContent{
  margin: 1%;
}

.scoreCardContentText{
  text-align: center;
  font-weight:500;
}

.scoreCardTeamAverageValue{
  position: absolute;
  font-size: 20px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.scoreCardTextAlign{
  text-align: center;
}

.scoreCardScoredDeal{
  text-align: center;
  margin: 2px;
  font-weight: 500;
}

.scoreCardScoreUpdate{
  text-align: center; 
  font-weight: 300;
}

.scoreCardViewReportSection{
  margin-top:2%;
}

.scoreCardTeamListName{
  font-size: 15px;
  font-weight: 400;
}

.scoreCardTeamMembersName{
  float: left;
}


.scoreCardTeamListIndiviualScore{
  float: right;
  margin-left:auto;
  margin-right: 13%;
}


