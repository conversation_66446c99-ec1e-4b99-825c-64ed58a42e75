<div class="scoreNavBar" fxLayout="row wrap" fxLayoutGap="4px">
 
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  <div class="scoreActionButtonsSection">
  <button mat-raised-button (click)="sendEmail('partner')" [disabled]=" disableSendEmail('partner')"
  type="button" class="green scoreActionButton" >
  {{"label.button.sendToReview" | literal}}
  </button>
  <button mat-raised-button (click)="sendEmail('team')" [disabled]=" disableSendEmail('team')"
  type="button" class="green scoreActionButton" >
  {{"label.button.sendToTeam" | literal}}
  </button>
  <button aria-label="save-score-btn" mat-raised-button (click)="saveDetails()" [disabled]="disableSaveButton()" 
  type="button" class="green scoreButton" >
  {{"label.button.save" | literal}}
  </button>
  </div>
  </div>
 </div>

<div fxLayout="row wrap" fxLayoutGap="4px" class="mainMenu">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <mat-card appearance="outlined" *ngIf="!showLoaderSpinner"   class="mat-card-top-border" fxFlex="100%" fxFlex.md="100%" fxFlex.sm="100%" fxHide.xs>
        <mat-card-content class="mainMenuCard" >

          <div fxLayout="row wrap" fxLayoutGap="4px">
            <div fxFlex="10%" fxFlex.md="23%" fxFlex.xs="100%" fxFlex.sm="48%">
              <h2 class="subTitle">{{"label.title.dealScore" | literal}}</h2>
            </div>
            <div fxFlex="20%" fxFlex.md="23%" fxFlex.xs="100%" fxFlex.sm="48%">
              <mat-form-field class="scoreStageSelection">
                <mat-label>{{"label.title.stageSelect" | literal}}</mat-label>
                <mat-select [(value)]="stageselected" (selectionChange)="changeStage($event)">
                  <mat-option
                   *ngIf="!IsCurrentStageExists()" 
                   [value]="selectedApplicationsData?.currentStageName">
                    {{selectedApplicationsData?.currentStageName}}</mat-option>
                  <mat-option *ngFor="let stage of scores" [value]="stage.stageName">
                    {{stage.stageName}}
                  </mat-option>

                </mat-select>
              </mat-form-field>
            </div>
            <div fxFlex="29%" fxFlex.md="23%" fxFlex.xs="100%" fxFlex.sm="48%">
              <div *ngIf=" selectedApplicationsData?.currentStageName === stageselected && (selectedApplicationsData?.currentStageName === 'Hold' || selectedApplicationsData?.currentStageName === 'AIR')">
                <mat-form-field class="scoreStageSelection" >
                  <mat-label>{{"label.title.score" |literal}}</mat-label>
                  <mat-select aria-label="average-score-field"  [(value)]="scoreselected" (selectionChange)="changeAvgScore($event)" [disabled]="DisableScore">
                    <mat-option *ngFor="let score of avgScores" [value]="score">
                      {{score}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
           <div class="margin-right-3" fxFlex="15%" fxFlex.md="24%" fxFlex.xs="100%"  fxFlex.sm="48%">
                  <mat-form-field class="scoreStageSelection">
                    <mat-label>{{"label.title.version" | literal}}</mat-label>
                    <mat-select [(ngModel)]="selectedVersion">
                      <mat-option [value]="this.currentScore?.version"
                        (click)="getSelectedVersion(this.currentScore?.version , 'currentScore')">
                        {{this.currentScore?.version}}</mat-option>
                      <ng-container *ngFor="let version of scoreHistory">

                        <mat-option [value]="version.version" (click)="getSelectedVersion(version , 'history')">
                          {{version.version}}
                        </mat-option>
                      </ng-container>




                    </mat-select>
                  </mat-form-field>
                </div>
           <div style="margin-right: 3% !important;" fxFlex="15%" fxFlex.md="30%" fxFlex.xs="100%"  fxFlex.sm="48%">
                  <mat-form-field class="scoreStageSelection" >
                    <mat-label>{{"label.title.compare" | literal}}</mat-label>
                    <mat-select disabled>
                      <mat-option>
                        0
                      </mat-option>
                      <mat-option>
                        1
                      </mat-option>

                    </mat-select>
                  </mat-form-field>
                </div>
           <div fxFlex="5%" fxFlex.md="10%" fxFlex.xs="100%" fxFlex.sm="48%">

                  <button [disabled]="disableRescoreBtn()" mat-raised-button (click)="rescoreDialog()" type="button" class="green">
                    {{"label.button.rescore" | literal}}
                  </button>
                <!-- </div>
              </div> -->
            </div>

          </div>




        </mat-card-content>
        <mat-divider class="dividerMargin"></mat-divider>
        <mat-card-content class="scoreCardContent">

          <div  fxLayout="row wrap" fxLayoutGap="4px">
            <div fxFlex="30%" fxFlex.md="30%" fxHide.xs fxFlex.sm="100%">
              <div class="uploader-status verticalCentor ">
                <h3 class="scoreCardContentText">&nbsp;{{"label.title.teamAverage" | literal}}</h3>



                <div class="spinnerGroup outer">

                  <div class="top">
                    <!-- <mat-progress-spinner strokeWidth="8" [diameter]="180" class="background-spinner" value="100">
                    </mat-progress-spinner> -->
                    <!-- <mat-progress-spinner strokeWidth="8" [diameter]="180" class="background-spinner" value="100">
                    </mat-progress-spinner> -->
                  </div>
                  <div class="below" >
                    <div [class.low]="teamAverageValue <=55" [class.good]="teamAverageValue >=60"
                      [class.avg]="teamAverageValue >55 && teamAverageValue<60">
                      <mat-progress-spinner strokeWidth="8" [diameter]="180" class="mainSpinner" class="mat-spinner-color"
                      [value]="teamAverageValue">
                    </mat-progress-spinner>
                    </div>
                  </div>
                  <!-- <button class="mat-elevation-z0 no-hover-effect" mat-fab color="">
                    {{teamAverageValue}}
                  </button> -->
                  <div class="scoreCardTeamAverageValue">{{teamAverage}}</div>
                </div>






                <!-- 
                <div [class.low]="teamAverageValue <=55" [class.good]="teamAverageValue >=60"
                  [class.avg]="teamAverageValue >55 && teamAverageValue<60">
                  <mat-progress-spinner strokeWidth="8" diameter="180" class="example-margin"
                    [value]="teamAverageValue">
                  </mat-progress-spinner>
                  
               
                
                </div> -->



                <div>
                  <h5 class="hyperlinkColor scoreCardTextAlign">{{"label.title.outOf5" | literal}}</h5>
                  <h4 class="scoreCardScoredDeal"> {{getScoredCount(this.teamsData , 'team')}} of {{getTotalLength(teamDealList , 'team')}} {{"label.title.membersScored" |literal}}
                  </h4>
                  <h4 class="scoreCardScoredDeal" *ngIf="!DisableCount"> {{getScoredCount(this.teamsData , 'partner')}} of {{getTotalLength(teamDealList , 'partner')}} {{"label.title.partnersScored" |literal}}
                  </h4>
                  <h5 class="scoreCardScoreUpdate"> {{getUpdatedAtScore()}}</h5>
                </div>
              </div>
            </div>
            <div fxFlex="13%" fxFlex.md="13%" fxHide.xs fxFlex.sm="10%">
            </div>
            <div fxFlex="45%" fxFlex.md="45%" fxHide.xs fxFlex.sm="100%">
              <div class="scoreCardViewReportSection" >

                <mat-card appearance="outlined" class="mat-card-top-border teamScoreCard" >
                  <div fxLayout="row" fxLayoutAlign="space-between center" class="m-1-p">
                    <h3 class="no-m">&nbsp;{{"label.title.teamScores" | literal}}</h3>
                    <button aria-label="view-report-btn"  [disabled]="disableWhenReject" (click)="openReport()" mat-raised-button class="blue">
                      View report
                    </button>
                  </div>
                  <!-- <mat-divider></mat-divider>
                 -->
                  <hr  class="width100"/>
                  <mat-card-content>
                    <ng-container *ngIf="showList">
                      <mat-list role="list" >
                        <ng-container *ngFor="let user of teamDealList">
                          <mat-list-item class="scoreCardTeamListName" role="listitem">
                            <div class="scoreCardTeamMembersName">{{user.teamName}}</div>
                            <div class="scoreCardTeamListIndiviualScore" > {{getIndividualScore(user.id)}}</div>
                          </mat-list-item>
                        </ng-container>

                      </mat-list>
                    </ng-container>

                  </mat-card-content>
                </mat-card>
              </div>
            </div>
          </div>


        </mat-card-content>
      </mat-card>
      <div  class="loader" *ngIf="showLoaderSpinner">
        <app-loader></app-loader>
      </div>
    </div>
  </div>
