import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { DealDetailsComponent } from "../../deal-details/deal-details.component";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { C<PERSON><PERSON>cyPipe, DatePipe } from "@angular/common";
import { FormBuilder } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { Router, ActivatedRoute } from "@angular/router";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ToasterService } from "src/app/common/toaster.service";
import { CommentsService } from "src/app/shared-module/comments/comments.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DealService } from "src/app/shared-service/deal.service";
import { DownloadFileService } from "src/app/shared-service/download-file.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { Utils } from "src/app/helpers/utils";
import { ActiveSectionService } from "../active-section.service";
import { takeUntil } from "rxjs";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";

@Component({
  selector: "app-active-section-preview",
  templateUrl: "../../deal-details/deal-details.component.html",
  styleUrls: ["../../deal-details/deal-details.component.scss"],
})
export class ActiveSectionPreviewComponent
  extends DealDetailsComponent
  implements OnInit
{
  @Input() sectionDetails;

  constructor(
    errorService: ErrorService,
    notificationMessage: ToasterService,
    businessProcessService: BusinessProcessService,
    router: Router,
    dataSharingService: DataSharingService,
    fb: FormBuilder,
    dealService: DealService,
    dialog: MatDialog,
    errorMessageService: ValidationErrorMessageService,
    currencyPipe: CurrencyPipe,
    identityService: IdentityService,
    activeRoute: ActivatedRoute,
    entityService: EntityService,
    matDialog: MatDialog,
    bottomsheet: MatBottomSheet,
    downloadFileService: DownloadFileService,
    currencyFormatService: CurrencyFormatService,
    commentService: CommentsService,
    datepipe: DatePipe,
    themeService: ThemeService,
    cdr: ChangeDetectorRef,
    unsavedChangesHandler: UnsavedChangesHandlerService,
    dataTypesUtils: DataTypesUtilsService,
    activeSectionService: ActiveSectionService
  ) {
    super(
      errorService,
      notificationMessage,
      businessProcessService,
      router,
      dataSharingService,
      fb,
      dealService,
      dialog,
      errorMessageService,
      currencyPipe,
      identityService,
      activeRoute,
      entityService,
      matDialog,
      bottomsheet,
      downloadFileService,
      currencyFormatService,
      commentService,
      datepipe,
      themeService,
      cdr,
      unsavedChangesHandler,
      dataTypesUtils,
      activeSectionService
    );
  }

  ngOnInit(): void {
    this.activeSectionService.previewActiveSection = true;
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.dataSharingService.backButton = false;
    this.getActivestagedetails(this.dataSharingService.selectedApplicationData);
    this.commentService.unAddedComment = "";
    this.dataSharingService.updatebuton = true;
    this.dataSharingService.hightlightFields = false;
    this.buttonRules = this.dataSharingService.FEeventRules;
    this.getconfigurableList();

    this.activeSectionService.onUpdate
      .pipe(takeUntil(this.destroy))
      .subscribe((item: any) => {
        this.onUpdate(item.apiDetails, item.reloadPage, item.eventName);
      });
  }

  divideAssetsAsPerSections(currentData) {
    if (this.stageMove != "updateStageDetails") {
      this.selectedStageTypeTabIndex =
        this.dataSharingService.selectedStageTypeTabIndex;
    }

    if (currentData.length) {
      this.allStageItemToValidateOnMoveToNextStage = currentData;
      this.generateReactiveForm(currentData);
      //Added OR codition for formly form elements where the current data length and no of form controls will not match.
      //This is because formly-form will add controls by key name which will not be directly available in currentData.

      if (
        currentData.length === Object.keys(this.assetsForm.value).length ||
        currentData.length === this.selectedStageAssetsFromDealItems.length
      ) {
        let sectionWiseData = [];
        this.aciveStageDetails.stageSection =
          this.aciveStageDetails.stageSection.sort(function (a, b) {
            if (a.order) {
              a.order - b.order;
            }
          });
        sectionWiseData = this.aciveStageDetails.stageSection.map((item) => ({
          sectionName: item?.section ? item.section : item,
          subSections: item.subSections,
          sectionFieldsData: [],
        }));

        if (
          sectionWiseData &&
          sectionWiseData.length != 0 &&
          currentData != 0
        ) {
          let numberOfItems = 0;
          let sectionIndex;
          sectionWiseData.forEach((item) => {
            numberOfItems++;
            const sectionItems = currentData.filter((stageItems) => {
              sectionIndex = this.getStageFileds(stageItems)?.[
                "section"
              ]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              if (
                this.getStageFileds(stageItems)?.["section"][sectionIndex]
                  ?.section == item.sectionName &&
                this.getStageFileds(stageItems).isSelected
              ) {
                return true;
              }
              return false;
            });

            //filtering and sorting only section attached stageItems
            let tempStageItems = [];
            const onlySectionItems = sectionItems.filter((element) => {
              sectionIndex = this.getStageFileds(element)?.[
                "section"
              ]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              if (
                !this.getSectionObject(
                  element,
                  this.selectedApplicationsData.currentStageName,
                  item.sectionName
                )?.subsection
              ) {
                return true;
              } else return false;
            });
            onlySectionItems.sort((a, b) => {
              const indexA = this.getStageFileds(a)?.["section"]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              const indexB = this.getStageFileds(b)?.["section"]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              const orderA = this.getStageFileds(a)?.["section"][indexA]?.order;
              const orderB = this.getStageFileds(b)?.["section"][indexB]?.order;
              if (orderA > orderB) {
                return 1;
              } else if (orderA < orderB) {
                return -1;
              } else {
                return 0;
              }
            });
            tempStageItems = [...onlySectionItems];
            item.sectionFieldsData.push({
              default: {
                subsectionItems: onlySectionItems,
                name: "default",
                hideRule: false,
                isHide: false,
              },
            });

            //filtering and sorting sub-section attached stageItems

            if (item?.subSections) {
              item.subSections.sort((a, b) => {
                return a?.order - b?.order;
              });

              item.subSections.forEach((subsec) => {
                const subsectionItems = sectionItems.filter(
                  (element) =>
                    this.getSectionObject(
                      element,
                      this.selectedApplicationsData.currentStageName,
                      item.sectionName
                    )?.subsection == subsec.subsectionName
                );
                subsectionItems.sort((a, b) => {
                  const indexA = this.getStageFileds(a)?.["section"]?.findIndex(
                    (newsection) => newsection.section == item.sectionName
                  );
                  const indexB = this.getStageFileds(b)?.["section"]?.findIndex(
                    (newsection) => newsection.section == item.sectionName
                  );
                  const orderA =
                    this.getStageFileds(a)?.["section"][indexA]?.order;
                  const orderB =
                    this.getStageFileds(b)?.["section"][indexB]?.order;
                  if (orderA > orderB) {
                    return 1;
                  } else if (orderA < orderB) {
                    return -1;
                  } else {
                    return 0;
                  }
                });
                tempStageItems = [...tempStageItems, ...subsectionItems];
                item.sectionFieldsData.push({
                  [Utils.camelCase(subsec.subsectionName)]: {
                    subsectionItems: subsectionItems,
                    name: subsec.subsectionName,
                    hideRule: subsec.subSectionRule,
                    isHide: false,
                  },
                });
              });
            }

            item.stageItems = [...tempStageItems];
          });

          if (numberOfItems === sectionWiseData.length) {
            if (this.sectionDetails.activeSubsectionName) {
              this.finalSectionWiseDataAssets = sectionWiseData
                .filter(
                  (section) =>
                    section.sectionName ===
                      this.sectionDetails.activeSectionName &&
                    section.sectionFieldsData.some((field) =>
                      Object.keys(field).find(
                        (key) =>
                          field[key]?.name ===
                          this.sectionDetails.activeSubsectionName
                      )
                    )
                )
                .map((section) => {
                  return {
                    ...section,
                    sectionFieldsData: section.sectionFieldsData.filter(
                      (field) =>
                        Object.keys(field).find(
                          (key) =>
                            field[key]?.name ===
                            this.sectionDetails.activeSubsectionName
                        )
                    ),
                  };
                });
            } else {
              this.finalSectionWiseDataAssets = sectionWiseData.filter(
                (section) =>
                  section.sectionName === this.sectionDetails.activeSectionName
              );
            }

            this.disableStageonShared();
            this.disableWhenReject =
              this.selectedApplicationsData.currentStatus == "Rejected"
                ? true
                : false;
            this.getHtmlready();
            this.showNoFieldsMessage = false;
            this.showNoLoader = false;
          }
        }
      }
    }

    if (
      this.highlightTabIndex.length &&
      this.dataSharingService.hightlightFields
    ) {
      this.assetsForm.markAllAsTouched();
    }
  }
}
