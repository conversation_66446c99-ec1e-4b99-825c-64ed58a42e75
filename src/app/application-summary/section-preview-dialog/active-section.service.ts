import { Injectable } from "@angular/core";
import { BehaviorSubject, Subject } from "rxjs";
import { MatDialog } from "@angular/material/dialog";
import { SectionPreviewDialogComponent } from "./section-preview-dialog.component";
import { FilePreviewComponent } from "src/app/dialogs/file-preview/file-preview.component";

@Injectable({
  providedIn: "root",
})
export class ActiveSectionService {
  constructor(private matDialog: MatDialog) {}
  previewActiveSection: boolean;

  onUpdate = new Subject<{
    apiDetails: any;
    reloadPage: boolean;
    eventName: string;
  }>();

  onUpdateActiveSection(
    apiDetails: any,
    reloadPage: boolean,
    eventName: string
  ): void {
    this.onUpdate.next({ apiDetails, reloadPage, eventName });
  }

  private filePreviewWidth = new BehaviorSubject<number>(50);
  filePreviewWidth$ = this.filePreviewWidth.asObservable();

  setFilePreviewWidth(width: number) {
    this.filePreviewWidth.next(width);
  }

  onSplitFilePreview(URL, fileName, activeSectionName, activeSubsectionName) {
    const matDialogRefSection = this.matDialog.open(
      SectionPreviewDialogComponent,
      {
        autoFocus: false,
        maxWidth: "90vw",
        width: "100vw",
        height: "98vh",
        position: { right: "1%" },
        hasBackdrop: true,
        panelClass: "flex-end-alignment",
        disableClose: true,
        data: {
          activeSectionName: activeSectionName,
          activeSubsectionName: activeSubsectionName,
        },
      }
    );

    const matDialogRefDocument = this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: "59vw",
      minWidth: "20vw",
      height: "98vh",
      hasBackdrop: false,
      position: { left: "1%" },
      disableClose: true,
      panelClass: "resizable-dialog-container",
      data: {
        previewURLString: URL,
        fileName: fileName,
      },
    });

    matDialogRefSection.afterClosed().subscribe((result) => {
      matDialogRefDocument.close();
      this.previewActiveSection = false;
    });
  }
}
