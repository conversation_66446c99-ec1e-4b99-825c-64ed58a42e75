import { Component, ElementRef, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ActiveSectionService } from "src/app/application-summary/section-preview-dialog/active-section.service";

@Component({
  selector: "app-section-preview-dialog",
  templateUrl: "./section-preview-dialog.component.html",
  styleUrls: ["./section-preview-dialog.component.scss"],
})
export class SectionPreviewDialogComponent implements OnInit {
  activeSectionName: any;
  activeSubsectionName: any;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<SectionPreviewDialogComponent>,
    public activeSectionService: ActiveSectionService,
    private el: ElementRef
  ) {
    this.activeSectionName = data.activeSectionName;
    this.activeSubsectionName = data.activeSubsectionName;
    this.attachDocumentKeyDownListener();
  }

  ngOnInit() {
    const dialogContainer = this.el.nativeElement.closest(
      "mat-dialog-container"
    );

    this.activeSectionService.filePreviewWidth$.subscribe(
      (filePreviewWidth) => {
        const sectionWidth = 100 - filePreviewWidth;
        dialogContainer.style.width = `${sectionWidth}%`;
      }
    );
  }

  onUpdate(apiDetails, reloadPage, eventName) {
    this.activeSectionService.onUpdateActiveSection(
      apiDetails,
      reloadPage,
      eventName
    );
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  private attachDocumentKeyDownListener() {
    document.addEventListener("keydown", (event: KeyboardEvent) => {
      this.handleKeyboardEvent(event);
    });
  }

  private handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === "Escape") {
      this.dialogRef.close(false);
    }
  }
}
