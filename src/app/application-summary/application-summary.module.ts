import { AddTeamLeadComponent } from "./dialogs/add-teamLead/add-teamLead.component";
import { AddRejectionComponent } from "./dialogs/add-rejection/add-rejection.component";
import { ApplicationSummaryRoutes } from "./application-summary.routing";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ApplicationSummaryComponent } from "./application-summary.component";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { DocumentsDetailComponent } from "./documents-detail/documents-detail.component";
import { UploadDocumentDialogComponent } from "./dialogs/upload-document-dialog/upload-document-dialog.component";
import { DealDetailsComponent } from "./deal-details/deal-details.component";

import { EditAssetsDetailsComponent } from "./deal-details/edit-assets-details/edit-assets-details.component";
import { ViewAssetDetailsComponent } from "./deal-details/view-asset-details/view-asset-details.component";
import { GenerateDocumentDialogComponent } from "./dialogs/generate-document-dialog/generate-document-dialog.component";
import { ScoreComponent } from "./deal-details/score/score.component";
import { RequestDocumentDialogComponent } from "./dialogs/request-document-dialog/request-document-dialog.component";
import { DealTaskComponent } from "./dealTask/dealTask.component";

import { EditUploadDocumentDialogComponent } from "./dialogs/edit-upload-document-dialog/edit-upload-document-dialog.component";
import { RequestdetailsdialogComponent } from "./dialogs/requestdetailsdialog/requestdetailsdialog.component";

import { ShareOnStageComponent } from "./dialogs/share-on-stage/share-on-stage.component";
import { SharOnLinkDetailsDialogComponent } from "./dialogs/shar-on-link-details-dialog/shar-on-link-details-dialog.component";
import { AssetUploadDialogComponent } from "./dialogs/asset-upload-dialog/asset-upload-dialog.component";

import {
  NgxMatDatetimePickerModule,
  NgxMatTimepickerModule,
  NgxMatDateAdapter,
} from "@angular-material-components/datetime-picker";
import { SelectTemplateConfigurationDialogComponent } from "../settings/document-template-configuration/select-template-configuration-dialog/select-template-configuration-dialog.component";
import { PreviousStagePreviewComponent } from "./stage-preview-sheet/previous-stage-preview/previous-stage-preview.component";
import { StagePreviewSheetComponent } from "./stage-preview-sheet/stage-preview-sheet.component";
import { EditQdeStageComponent } from "./dialogs/edit-qde-stage/edit-qde-stage.component";
import { StageMovementRemarksComponent } from "./dialogs/stage-movement-remarks/stage-movement-remarks.component";
import { StageMovementHistoryComponent } from "../history-record/stage-movement-history/stage-movement-history.component";
import { FloatingActionPanelComponent } from "./floating-action-panel/floating-action-panel.component";
import {
  MAT_COLOR_FORMATS,
  NGX_MAT_COLOR_FORMATS,
} from "@angular-material-components/color-picker";
import { MAT_DATE_LOCALE, DateAdapter } from "@angular/material/core";
import { SectionPreviewDialogComponent } from "./section-preview-dialog/section-preview-dialog.component";
import { ActiveSectionPreviewComponent } from "./section-preview-dialog/active-section-preview/active-section-preview.component";
import {
  CustomDateAdapter,
  CustomNgxMatDateAdapter,
} from "../common/date/zcp-date-adaptor";
import {
  NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  NgxMatMomentModule,
} from "@angular-material-components/moment-adapter";
import { DateFormattingService } from "../common/date/date-formatting.service";
import { SelectDocumentTypeDialogComponent } from "../settings/document-template-configuration/select-document-type-dialog/select-document-type-dialog.component";

@NgModule({
  imports: [
    CommonModule,
    SharedModuleModule,

    // BrowserAnimationsModule,
    // BrowserModule
    ApplicationSummaryRoutes,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatMomentModule,
  ],
  declarations: [
    ApplicationSummaryComponent,
    DocumentsDetailComponent,
    DealDetailsComponent,
    UploadDocumentDialogComponent,
    EditUploadDocumentDialogComponent,
    ViewAssetDetailsComponent,
    EditAssetsDetailsComponent,
    GenerateDocumentDialogComponent,
    SelectTemplateConfigurationDialogComponent,
    SelectDocumentTypeDialogComponent,
    ScoreComponent,
    DealTaskComponent,
    RequestDocumentDialogComponent,
    AddTeamLeadComponent,
    AddRejectionComponent,
    RequestdetailsdialogComponent,

    ShareOnStageComponent,
    SharOnLinkDetailsDialogComponent,
    AssetUploadDialogComponent,
    PreviousStagePreviewComponent,
    StagePreviewSheetComponent,
    EditQdeStageComponent,
    StageMovementRemarksComponent,
    StageMovementHistoryComponent,
    FloatingActionPanelComponent,
    SectionPreviewDialogComponent,
    ActiveSectionPreviewComponent,
  ],

  exports: [DealDetailsComponent],
  providers: [
    { provide: MAT_COLOR_FORMATS, useValue: NGX_MAT_COLOR_FORMATS },
    { provide: MAT_DATE_LOCALE, useValue: "en-GB" },
    { provide: DateAdapter, useClass: CustomDateAdapter },
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxMatDateAdapter,
      deps: [
        MAT_DATE_LOCALE,
        NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS,
        DateFormattingService,
      ],
    },
  ],
})
export class ApplicationSummaryModule {}
