import { COMM<PERSON>, ENTER } from "@angular/cdk/keycodes";
import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import { MatAutocompleteSelectedEvent } from "@angular/material/autocomplete";
import { MatChipInputEvent } from "@angular/material/chips";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, Subscription } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { EditTaskDialogComponent } from "src/app/dialogs/edit-task-dialog/edit-task-dialog.component";
import { EmailDialogComponent } from "src/app/dialogs/email-dialog/email-dialog.component";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ColumnSelectorDialogComponent } from "src/app/task/column-selector-dialog/column-selector-dialog.component";
import { TasksServiceService } from "src/app/task/tasks-service/tasks-service.service";
import {
  tasksStatusList,
  tasksTableColumnList,
} from "src/app/task/tasks-static-data";
import { ConceptualAddTaskDialogComponent } from "../../dialogs/conceptual-add-task-dialog/conceptual-add-task-dialog.component";
import { DealService } from "../../shared-service/deal.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { DealResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-deal-task",
  templateUrl: "./dealTask.component.html",
  styleUrls: ["./dealTask.component.scss"],
})
export class DealTaskComponent implements OnInit {
  clickEventsubscription: Subscription;
  showNoRecordsAvailbleMessage = true;
  showTaskSpinner: boolean;
  displayedColumns = [];
  allColumnsList = tasksTableColumnList;
  dataSource: MatTableDataSource<any>;
  selectedStatus: any = "Recently viewed";
  allTasks = [];
  selectedView: any = "List view";
  taskStatus = tasksStatusList;
  allTagList: any = [];
  tags: any = [];
  @Output() valueChange = new EventEmitter();
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  updateTaskId: any;
  isDeletedId: any;
  isSelectedTaskEmpty = true;
  selectedTask: any;
  serchText: string;
  selectedBusinessProcessWithStagedetails: any;
  businessProcessList: any;
  selectedApplicationsData;
  JsonData: any;
  aciveStageDetails: any;
  dealStatusWithStageOrder: any;
  disableWhenReject: boolean;

  get DEAL_RESOURCE() {
    return DealResource;
  }
  constructor(
    private matDialog: MatDialog,
    public taskService: TasksServiceService,
    private notification: ToasterService,
    private router: Router,
    private notificationMessage: ToasterService,
    private dataSharingService: DataSharingService,
    private errorService: ErrorService,
    private activeRoute: ActivatedRoute,
    private businessProcessService: BusinessProcessService,
    private dealService: DealService,
    protected themeService: ThemeService
  ) {
    this.displayedColumns = this.allColumnsList
      .map((item) => {
        if (item.isSelected) {
          return item.value;
        }
      })
      .filter((ele) => ele != undefined && ele != null);
  }

  ngOnInit() {
    this.disableWhenReject =
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
        ? true
        : false;

    if (!this.dataSharingService.selectedApplicationData) {
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("Id")) {
          this.dealService
            .getDealById(atob(params.get("Id")))
            .subscribe((response: any) => {
              this.dataSharingService.getDataById = response;
              this.dataSharingService.selectedApplicationData = response;
              this.selectedApplicationsData =
                this.dataSharingService.selectedApplicationData;
              this.dataSharingService.emitChangesOfSelectedApplicationData(
                this.selectedApplicationsData
              );
              this.getBusinessProcessList();
            });
        }
      });
    } else {
      this.getAllTasks();
    }
  }

  setStagesSelectedBusinessProcess(dealData) {
    const selectedBusinessProcessDetails = this.businessProcessList.filter(
      (item) =>
        item.name.toLowerCase() ===
        dealData.businessProcessDetail.name.toLowerCase()
    )[0];

    if (
      selectedBusinessProcessDetails &&
      selectedBusinessProcessDetails.businessProcessStageList.length != 0
    ) {
      const numberOfDeals = 0;
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order: selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !selectedBusinessProcessDetails.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        selectedBusinessProcessDetails.businessProcessStageList.push(
          rejectionObj
        );
      }
      selectedBusinessProcessDetails.businessProcessStageList =
        selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );

      const finalData =
        selectedBusinessProcessDetails.businessProcessStageList.sort(function (
          a,
          b
        ) {
          return a.order - b.order;
        });
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;

      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
    }
  }

  getBusinessProcessList() {
    this.businessProcessService.getBusinessProcess().subscribe(
      (response) => {
        this.businessProcessService.businessProcessList = response;
        this.businessProcessList =
          this.businessProcessService.businessProcessList;

        if (this.businessProcessList.length != 0) {
          // this.selectedbusinessProcess = this.businessProcessList[0].name;

          this.setStagesSelectedBusinessProcess(
            this.dataSharingService.selectedApplicationData
          );
          this.getActivestagedetails();
          this.getAllTasks();
        }
      },
      () => {
        this.showTaskSpinner = false;
      }
    );
  }

  getActivestagedetails() {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    this.dataSharingService.selectedApplicationDataChangeEmitted$.subscribe(
      (data) => {
        this.selectedApplicationsData = data;
      }
    );
    this.selectedBusinessProcessWithStagedetails =
      this.dataSharingService.selectedBusinessProcessWithStagedetails;

    if (
      this.selectedApplicationsData &&
      this.selectedBusinessProcessWithStagedetails
    ) {
      this.selectedBusinessProcessWithStagedetails.forEach((element, index) => {
        if (element.name === this.selectedApplicationsData.currentStageName) {
          this.aciveStageDetails = element;
          this.dealStatusWithStageOrder = element?.order;
          this.dataSharingService.stageOrderAfterRefresh = element?.order;
        }
      });
    }
  }

  // get all tasks
  getAllTasks() {
    this.taskService.getTask().subscribe((tasks) => {
      this.allTasks = tasks;

      if (this.updateTaskId) {
        this.selectedTask = this.allTasks.filter(
          (ele) => ele.taskId === this.updateTaskId
        )[0];
        this.updateTaskId = null;
      }
      if (this.isDeletedId) {
        if (
          this.allTasks.filter((ele) => ele.taskId === this.isDeletedId)
            .length === 0
        ) {
          this.selectedTask = {};
          this.isSelectedTaskEmpty = true;
        }
      }
      if (this.dataSharingService.selectedApplicationData) {
        this.allTasks = this.allTasks.filter((task) => {
          const dealData = task.taskTagList.filter(
            (data) =>
              data.name == "DEAL" &&
              data.value ==
                this.dataSharingService.selectedApplicationData
                  ?.dealCustomerList[0]?.customerName
          );
          if (dealData.length > 0) return dealData;
        });
      }

      this.allTasks = [...this.allTasks];

      this.onChangeStatus(this.selectedStatus);
      this.getTagList(this.allTasks);

      if (!this.dataSharingService.data) {
        this.onChangeTag(this.tags);
      }
    });
  }

  ngOnChanges() {
    this.onChangeStatus(this.selectedStatus);
    this.serchText = "";

    if (this.updateTaskId) {
      this.selectedTask = this.allTasks.filter(
        (ele) => ele.taskId === this.updateTaskId
      )[0];
      this.updateTaskId = null;
    }
    if (this.isDeletedId) {
      if (
        this.allTasks.filter((ele) => ele.taskId === this.isDeletedId)
          .length === 0
      ) {
        this.selectedTask = {};
        this.isSelectedTaskEmpty = true;
      }
    }
    this.getTagList(this.allTasks);
    if (this.tags.length != 0) {
      this.onChangeTag(this.tags);
    }
  }

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  ngAfterViewInit() {
    this.onChangeStatus(this.selectedStatus);
  }

  openColumnSelectorDialog() {
    const matDialogRef = this.matDialog.open(ColumnSelectorDialogComponent, {
      width: "40%",
      disableClose: true,
      panelClass: "mat-dialog-container-custom-css",
      data: {
        module: "tasks",
        list: this.allColumnsList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result.currentStageName) {
        this.displayedColumns = result.data
          .map((item) => {
            if (item.isSelected) {
              return item.value;
            }
          })
          .filter((ele) => ele != undefined && ele != null);
      }
    });
  }

  onChangeStatus(event) {
    this.selectedStatus = event;
    if (event === "all") {
      let filterdData = [];
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage =
        filterdData?.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }

    if (event === "Open" || event === "In progress" || event === "In review") {
      let filterdData = [];
      filterdData = this.allTasks.filter((item) => item.taskStatus === event);
      this.showNoRecordsAvailbleMessage =
        filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }

    if (event === "Todays tasks") {
      let filterdData = [];
      filterdData = this.allTasks.filter(
        (item) =>
          item.taskDueDate ==
            this.taskService.getDateFormatInPayload(new Date()) &&
          item.taskStatus != "Completed"
      );
      this.showNoRecordsAvailbleMessage =
        filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }

    if (event === "Recently created") {
      let filterdData = [];
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage =
        filterdData?.length != 0 ? false : true;
      const data = filterdData.sort(function (a, b) {
        return (
          <any>new Date(b.taskCreationDate) - <any>new Date(a.taskCreationDate)
        );
      });
      this.refreshDataTable(data);
    }

    if (event === "Recently viewed") {
      let filterdData = [];
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage =
        filterdData?.length != 0 ? false : true;
      const data = filterdData?.sort(function (a, b) {
        return (
          <any>new Date(b.recentViewDate) - <any>new Date(a.recentViewDate)
        );
      });
      this.refreshDataTable(data);
    }

    if (event === "Delegated tasks") {
      let filterdData = [];
      filterdData = this.allTasks.filter((item) => {
        if (item.delegatedTo && item.delegatedTo?.trim().length > 0) {
          return item;
        }
      });
      this.showNoRecordsAvailbleMessage =
        filterdData?.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }

    if (event === "Recently completed") {
      let filterdData = [];
      const today = new Date();
      const lastDayOfWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      filterdData = this.allTasks.filter((item) => {
        if (
          item.taskStatus === "Completed" &&
          new Date(item.modifiedDate) <= today &&
          new Date(item.modifiedDate) > lastDayOfWeek
        ) {
          return item;
        }
      });
      this.showNoRecordsAvailbleMessage =
        filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }
  }

  refreshDataTable(filterdData) {
    let data = filterdData;
    data = [...data];
    this.dataSource = new MatTableDataSource(data);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  openEditTaskDialog(elementData) {
    const matDialogRef = this.matDialog.open(EditTaskDialogComponent, {
      disableClose: true,
      data: {
        module: "tasks",
        data: elementData,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getAllTasks();
      }
    });
  }

  openDeleteDialog(element) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this Task ?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteTask(element);
      }
    });
  }

  // delete task
  deleteTask(element) {
    this.taskService.deleteTask(element.taskId).subscribe(
      (res) => {
        this.getAllTasks();
        this.notification.success(JsonData["label.success.DeleteTask"]);
      },
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showTaskSpinner = false;
      }
    );
  }

  onChange(actionName, value: any) {
    let emitData = { actionName: actionName, data: value };
    if (actionName === "changeDisplayUI") {
      this.selectedView = value;
      emitData = { actionName: actionName, data: this.selectedView };
    }

    if (actionName === "editTask") {
      const data = value;
      this.updateTaskId = data.taskId;

      emitData = { actionName: actionName, data: data };
      this.openEditTaskDialog(data);
    }

    if (actionName === "deleteTask") {
      const data = value;
      this.isDeletedId = data.taskId;
      (emitData = { actionName: actionName, data: data }),
        (this.isSelectedTaskEmpty = true);
      this.openDeleteDialog(data);
    }

    if (actionName === "updateTaskStatus") {
      const data = value;

      this.updateTaskId = data.taskId;
      this.updateTaskStatus(data["taskStatus"], data["taskId"]);
    }

    if (actionName === "updateViewTime") {
      const data = value;
      emitData = { actionName: actionName, data: data };
      this.updateViewTime(data["taskId"]);
    }

    if (actionName === "closeViewPage") {
      this.isSelectedTaskEmpty = true;
    }

    this.valueChange.emit(emitData);
  }

  // Update the view time
  updateViewTime(taskId) {
    this.taskService.updateViewTime(taskId).subscribe(
      (res) => {
        this.getAllTasks();
      },
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showTaskSpinner = false;
      }
    );
  }

  updateTaskStatus(taskStatus, taskId) {
    this.taskService.updateTaskStatus(taskStatus, taskId).subscribe(
      (res) => {
        this.notification.success(JsonData["label.success.UpdateTask"]);
        this.taskService.setBooleanToTrue(true);
        this.getAllTasks();
      },
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showTaskSpinner = false;

        this.getAllTasks();
      }
    );
  }

  openEmailDialog() {
    this.matDialog.open(EmailDialogComponent, {
      width: "75%",
      disableClose: true,
      panelClass: "mat-dialog-container-custom-css",
      data: {
        module: "tasks",
      },
    });
  }

  onCreate() {
    const matDialogRef = this.matDialog.open(ConceptualAddTaskDialogComponent, {
      disableClose: true,
      data: {
        module: "tasks",
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.selectedStatus = "Recently created";
        this.getAllTasks();
      }
    });
  }

  viewTask(data) {
    this.selectedTask = data;
    this.isSelectedTaskEmpty = false;
    this.onChange("updateViewTime", data);
  }

  onChangesReceived(data: any) {
    this.onChange(data.actionName, data.data);
  }

  getTagList(taskData) {
    this.allTagList = taskData.slice().map((element) => {
      return element.taskTagList;
    });

    this.allTagList = this.allTagList.flat(Infinity).filter((tag, index) => {
      delete tag.tagId;
      const singleTag = JSON.stringify(tag);
      return (
        index ===
        this.allTagList.flat(Infinity).findIndex((obj) => {
          return JSON.stringify(obj) === singleTag;
        })
      );
    });
  }

  visible = true;
  selectable = true;
  removable = true;
  addOnBlur = false;

  separatorKeysCodes = [ENTER, COMMA];

  tagCtrl = new UntypedFormControl();

  filteredTags: Observable<any[]>;

  @ViewChild("tagInput") tagInput: ElementRef;

  add(event: MatChipInputEvent): void {
    const input = event.input;
    const value = event.value;
    if ((value || "").trim()) {
      this.tags.push(value);
    }
    if (input) {
      input.value = "";
    }
    this.tagCtrl.setValue(null);
  }

  remove(tag: any): void {
    const index = this.tags.indexOf(tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
    }
    this.onChangeTag(this.tags);
  }

  private filter(value: any): any[] {
    return this.allTagList.filter((tag) =>
      tag.value.toLowerCase().includes(value.toString().toLowerCase())
    );
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    this.tags.push(event.option.value);
    this.tagInput.nativeElement.value = "";
    this.tagCtrl.setValue(null);
    this.onChangeTag(this.tags);
  }

  onChangeTag(tag) {
    let data = [];
    let investmentData: any = [];
    let finalData = [];
    if (tag.length === 0) {
      data = this.allTasks;
      this.dataSource = new MatTableDataSource(data);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
    if (tag.length != 0) {
      let count = 0;
      const numberOfinvestments = tag.filter(
        (ele) => ele.name.toLowerCase() === "investment"
      ).length;
      const numberOfModules = tag.filter(
        (ele) => ele.name.toLowerCase() === "module"
      ).length;
      this.allTasks.filter((item, index) => {
        count++;
        tag.forEach((oneTag) => {
          if (numberOfinvestments != 0) {
            if (
              item.taskTagList.filter(
                (e) =>
                  e.name.toLowerCase() === "investment" &&
                  "investment" === oneTag.name.toLowerCase() &&
                  e.value.toLowerCase() === oneTag.value.toLowerCase()
              ).length != 0
            ) {
              data.push(item);
              investmentData = data;
            }
          } else {
            if (
              item.taskTagList.filter(
                (e) =>
                  e.name.toLowerCase() === "module" &&
                  "module" === oneTag.name.toLowerCase() &&
                  e.value.toLowerCase() === oneTag.value.toLowerCase()
              ).length != 0
            ) {
              data.push(item);

              finalData = data;
              this.dataSource = new MatTableDataSource(finalData);
              this.dataSource.paginator = this.paginator;
              this.dataSource.sort = this.sort;
            }
          }
        });
      });

      if (numberOfModules == 0 && numberOfinvestments != 0) {
        finalData = investmentData;

        this.dataSource = new MatTableDataSource(finalData);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }

      if (numberOfModules != 0 && numberOfinvestments != 0) {
        investmentData.forEach((filteredTask) => {
          tag.forEach((oneTag) => {
            if (
              filteredTask.taskTagList.filter(
                (e) =>
                  e.name.toLowerCase() === "module" &&
                  "module" === oneTag.name.toLowerCase() &&
                  e.value.toLowerCase() === oneTag.value.toLowerCase()
              ).length != 0
            ) {
              finalData.push(filteredTask);

              this.dataSource = new MatTableDataSource(finalData);
              this.dataSource.paginator = this.paginator;
              this.dataSource.sort = this.sort;
              if (finalData.length == 0) {
                const emptyData = [];
                this.dataSource = new MatTableDataSource(emptyData);
                this.dataSource.paginator = this.paginator;
                this.dataSource.sort = this.sort;
              }
            }
          });
        });
      }
    }
  }

  getColor(value) {
    if (value.toLowerCase() != "deal") {
      return "accent";
    } else {
      return "warn";
    }
  }

  onCancelFromEditDeleteAction() {
    this.isDeletedId = null;
    this.updateTaskId = null;
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
}
