
<div *ngIf="isSelectedTaskEmpty" class="mt-30" fxLayout="row wrap" fxLayoutGap="4px">
  
  <mat-form-field fxFlex="25%" fxFlex.md="25%" fxFlex.xs="30%" fxFlex.sm="30%">
    <mat-label>{{"label.title.searchTask"|literal}}</mat-label>
    <input matInput (keyup)="applyFilter($event.target.value)" autocomplete="off" placeholder="Tasks" #input />
    <mat-icon matSuffix class="dealTaskSearchIcon">search</mat-icon>
  </mat-form-field>
  

  <div fxFlex="74%" fxFlex.md="74%" fxFlex.xs="68%" fxFlex.sm="69%">
    <button  mat-raised-button class="green dealTaskCreateButton"[disabled]="disableWhenReject" (click)="onCreate()" 
    *ifHasPermission="DEAL_RESOURCE.Task; scope:'CHANGE'">
      {{"label.button.create"|literal}}
    </button>
  </div>
</div>

<div *ngIf="isSelectedTaskEmpty" fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <mat-card appearance="outlined" class="mat-card-top-border" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <h1 class="table-header">{{"label.title.tasks"|literal}}</h1>
        <mat-divider></mat-divider>
        <mat-card-content>
          <div fxLayout="row wrap" fxLayoutGap="4px" class="tbl-div">

            <table mat-table [dataSource]="dataSource " matSort
              class="alignCenter  mat-table-width" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

              <!--  Column -->
              <ng-container matColumnDef="priorityAndUserType">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter dealTaskTableHeaderSortIcon" > </th>
                <td mat-cell class="alignCenter" *matCellDef="let row">
                  <mat-icon *ngIf="row.taskPriority !== 'Low'" matTooltip="{{row.taskPriority}}" [ngClass]="{
                  'highColor' : row.taskPriority === 'High',
                  'lowColor' : row.taskPriority === 'Low',
                  'mediumColor' : row.taskPriority === 'Medium'}" [inline]="true">priority_high</mat-icon>
                </td>

              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="taskName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-35"> {{"label.table.title.taskSummary"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskName}} </td>
              </ng-container>



              <!--  Column -->
              <ng-container matColumnDef="assigneeName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.assignedTo"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.assigneeName}} </td>
              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="taskDueDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.taskDueDate"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskDueDate }} </td>
              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="taskOwner">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-15"> {{"label.table.title.taskOwner"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskOwner }} </td>
              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="taskReminder">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-15"> {{"label.table.title.taskReminder"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskReminder }} </td>
              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="taskPriority">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.taskPriority"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskPriority }} </td>
              </ng-container>

              <!--  Column -->
              <ng-container matColumnDef="assigneeType">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.assigneeType"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.assigneeType }} </td>
              </ng-container>

              <!-- Column -->
              <ng-container matColumnDef="taskStatus">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.taskStatus"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskStatus }} </td>
              </ng-container>
              <!-- Column -->
              <ng-container matColumnDef="taskCreationDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> {{"label.table.title.taskCreationDate"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.createdDate  }} </td>
              </ng-container>



              <!-- Column -->
              <ng-container matColumnDef="delegatedTo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter"> {{"label.table.title.delegatedTo"|literal}} </th>
                <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.delegatedTo }} </td>
              </ng-container>

              <!-- Column -->
              <ng-container matColumnDef="action" >
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter "></th>
                <td class="alignCenter" mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">

                  <button mat-icon-button class="mat-icon-buttons-in-action-column blue" [ngClass]="disableWhenReject ? 'gray' :'blue'"  [disabled]="disableWhenReject" (click)="onChange('editTask' , element)"
                  *ifHasPermission="DEAL_RESOURCE.Task; scope:'CHANGE'">
                    <mat-icon class="pointer icon-white" 
                      matTooltip="Edit task">
                      edit
                    </mat-icon>
                  </button>

                  <button mat-icon-button class="mat-icon-buttons-in-action-column red" [ngClass]="disableWhenReject ? 'gray' :'red'" [disabled]="disableWhenReject" (click)="onChange('deleteTask' , element)" 
                  *ifHasPermission="DEAL_RESOURCE.Task; scope:'DELETE'">
                    <mat-icon class="pointer icon-white" 
                      matTooltip="Delete task">
                      delete</mat-icon>
                  </button>


                </td>
              </ng-container>

              <tr mat-header-row class="dealTaskTableRowTextProp" *matHeaderRowDef="displayedColumns;"></tr>
              <tr mat-row class="pointer dealTaskTableRowTextProp" 
                *matRowDef="let row; columns: displayedColumns; let i = index; " (click)="viewTask(row)"
                [class.task-row__alternate]="i % 2"></tr>
              <!-- <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="4">No data matching the filter</td>
          </tr> -->
            </table>

            <div  [hidden]="!showNoRecordsAvailbleMessage" class="mt-1 no-records-found ">
              <!-- <mat-card appearance="outlined" class="no-record-card"> {{"label.title.noRecordsFound"|literal}} </mat-card> -->
            </div>
          </div>
          <br>
          <div [hidden]="showNoRecordsAvailbleMessage">
            <mat-paginator [pageSizeOptions]="[10, 25,50, 100]"></mat-paginator>
          </div>
          <!-- <mat-paginator [pageSizeOptions]="[8, 10, 25, 100]">
          </mat-paginator> -->
        </mat-card-content>
      </mat-card>
    </div>
  </div>

</div>

<div *ngIf="!isSelectedTaskEmpty">
  <ng-container>
    <!-- ********** This is For simple basic task view without monitor tags *************- -->
    <ng-container>
      <div>
        <app-view-task-page [taskData]="selectedTask" (valueChange)='onChangesReceived($event)'>
          <div backButton>
            <button class="addTaskBtn" mat-button matTooltip="Close task details"
              (click)="isSelectedTaskEmpty = !isSelectedTaskEmpty">

              <ng-container>Close </ng-container>
            </button>
          </div>
        </app-view-task-page>
      </div>
    </ng-container>

    <!-- ************* This is For contextual task view with monitor tags *************- -->
    <!-- <app-view-task-dialog [parentName]="selectedView" [taskData]="selectedTask"
        (valueChange)='onChangesReceived($event)'>

      </app-view-task-dialog> -->
  </ng-container>
</div>
<br>