import { DatePipe } from "@angular/common";
import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl } from "@angular/forms";
import { MatAutocompleteTrigger } from "@angular/material/autocomplete";
import { MatStepper } from "@angular/material/stepper";
import { Observable } from "rxjs";
import { map, startWith } from "rxjs/operators";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ThemeService } from "src/app/theme.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import * as xlsx from "xlsx";
import { AssetDiff, AssetHistoryRecord } from "./history-record.model";

@Component({
  selector: "app-history-record",
  templateUrl: "./history-record.component.html",
  styleUrls: ["./history-record.component.scss"],
})
export class HistoryRecordComponent implements OnInit {
  @ViewChild("historyStepper") historyStepper: MatStepper;
  @ViewChild(MatAutocompleteTrigger) autoTrigger!: MatAutocompleteTrigger;
  historyRecords: AssetHistoryRecord[] = [];
  dataFromParent;
  showSideNavSpinner = false;
  selectedDetails: any;
  pageIndex: any = 0;
  pageSize: any;
  sortDirection: any;
  sortAsPerKeyName: any;
  totalNumber: any;
  pending: boolean;
  currentCount: any;
  dealAssetFieldNames;
  filteredOptions: Observable<any>;
  myControl = new FormControl();
  downloadData: any[];
  excelList: any[];
  searchKey: string;
  disableWhenReject: boolean;
  complexDataType: boolean;
  editorOptions = {
    theme: "vs-dark",
    language: "json",
    autoIndent: "full",
    readOnly: true,
  };
  advanceHistory: any;

  constructor(
    private dataSharingService: DataSharingService,
    private dealService: DealService,
    public datepipe: DatePipe,
    private readonly dateTimePipe: ZcpDateTimePipe,
    private notification: ToasterService,
    public readonly themeService: ThemeService
  ) {
    dataSharingService.historyDrawerToggle.subscribe((data) => {
      if (data) {
        // Intializing with default values
        this.sortDirection = "desc";
        this.sortAsPerKeyName = "historyEndDate";
        this.searchKey = "";
        this.totalNumber = 0;
        this.pending = false;
        this.currentCount = 0;
        this.pageSize = 15;
        this.historyRecords = [];
        this.dataFromParent = data;
        this.getHistoryData();
        this.historyStepper?.reset();
        this.myControl.reset();

        this.dealAssetFieldNames =
          this.dataSharingService.selectedApplicationData.dealAsset.dealAssetItem.map(
            (e: any) => {
              return {
                key: this.getPropertyName(e),
                displayName:
                  e[this.getPropertyName(e)]?.displayProperty?.displayName,
              };
            }
          );
        this.dealAssetFieldNames = this.dealAssetFieldNames?.sort(
          (a: any, b: any) => {
            const sortedArray = a["displayName"].localeCompare(
              b["displayName"]
            );
            return sortedArray;
          }
        );

        this.filteredOptions = this.myControl.valueChanges.pipe(
          startWith(""),
          map((value) => {
            const name = typeof value === "object" ? value?.displayName : value;
            return name ? this._filter(name) : this.dealAssetFieldNames.slice();
          })
        );
      }
    });
  }

  ngOnInit(): void {
    this.disableWhenReject =
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
        ? true
        : false;
  }

  private _filter(name: any) {
    if (!name) return;
    const filterValue = name?.toLowerCase();
    return this.dealAssetFieldNames.filter((option) =>
      option.displayName.toLowerCase().includes(filterValue)
    );
  }

  displayFn(value): string {
    return value ? value : "";
  }

  getPropertyName(element): any {
    return Object.entries(element)[0][0];
  }

  onPageRefresh() {
    this.getHistoryData();
  }

  getHistoryData() {
    this.complexDataType = false;
    if (!this.dataFromParent) return;
    this.showSideNavSpinner = true;
    this.historyRecords = [];

    const data = {
      sortDirection: this.sortDirection,
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      searchKey: this.searchKey,
    };

    const apiCall = this.dataFromParent?.isEntity
      ? "getEntityDetailsHistory"
      : "getDealAssetHistory";
    this.pending = true;
    this.dealService[apiCall](this.dataFromParent.id, data).subscribe((res) => {
      this.pending = false;
      this.currentCount = res["content"]?.length;
      this.totalNumber = res["totalElements"];
      this.historyRecords = res["content"]?.filter((e) => e?.diff.length > 0);
      this.showSideNavSpinner = false;
    });
  }

  getComplexHistoryData() {
    this.complexDataType = true;
    if (!this.dataFromParent) return;
    this.showSideNavSpinner = true;

    const data = {
      sortDirection: this.sortDirection,
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      searchKey: this.searchKey,
    };

    let apiCall = this.dataFromParent?.isEntity
      ? "getComplexEntityDetailsHistory"
      : "getDealComplexAssetHistory";
    this.pending = true;
    this.dealService[apiCall](this.dataFromParent.id, data).subscribe((res) => {
      this.advanceHistory = JSON.stringify(res.content, null, 2);
      this.pending = false;
      this.currentCount = res["content"]?.length;
      this.totalNumber = res["totalElements"];
      this.historyRecords = res["content"]?.filter((e) => e?.diff.length > 0);
      this.showSideNavSpinner = false;
    });
  }

  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getHistoryData();
  }

  sortData(sortKey: string) {
    this.sortDirection =
      this.sortAsPerKeyName != sortKey
        ? "desc"
        : this.sortDirection == "asc"
        ? "desc"
        : "asc";
    this.sortAsPerKeyName = sortKey;
    this.pageIndex = 0;
    this.historyRecords = [];
    this.getHistoryData();
  }

  toggleHistoryDrawer() {
    this.dataSharingService.toggleHistoryDrawer(null);
  }

  nextBatch() {
    const element = document.getElementById("historyDiv");
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = (e) => {
        if (element.scrollTop < lastScrollTop) {
          // upscroll
          return;
        }

        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        if (
          element.scrollTop + element.offsetHeight >= element.scrollHeight &&
          !this.pending
        ) {
          this.pageSize += 10;
          if (this.totalNumber && this.totalNumber > this.currentCount) {
            this.getHistoryData();
          }
        }
      };
    }
  }

  generateDataForDownload() {
    this.downloadData = [];
    this.historyRecords.forEach((historyRecord: AssetHistoryRecord) => {
      historyRecord.diff.forEach((diffItem: AssetDiff) => {
        const obj = {
          "Changed Date": this.datepipe
            .transform(historyRecord.changeDateTime.toString())
            .toString(),
          "Changed By": historyRecord.changedByUser,
          "Field Name": diffItem.path,
          "Old Value": this.getFormattedData(diffItem, "old"),
          "New Value": this.getFormattedData(diffItem, "new"),
        };
        this.downloadData.push(obj);
      });
    });
  }

  /**
   *
   * @param diff field diff record of type `AssetDiff`
   * @param key `'old'` or `'new'` key to access old/new value
   * @returns formatted value based on input type and same value by default
   */
  getFormattedData(diff: AssetDiff, key: "old" | "new") {
    switch (diff.inputType) {
      case ZcpDataTypes.DATE:
        return diff[key] ? this.datepipe.transform(diff[key]) : "-";
      case ZcpDataTypes.DATE_TIME:
        return diff[key] ? this.dateTimePipe.transform(diff[key]) : "-";
      default:
        return diff[key] ? diff[key].toString() : "-";
    }
  }

  exportFile() {
    const dealName =
      this.dataSharingService?.selectedApplicationData?.dealIdentifier;
    if (!this.historyRecords || this.historyRecords.length === 0) {
      this.notification.error("No history records available for export");
      return;
    }
    this.generateDataForDownload();
    if (this.downloadData) {
      const wscols = [
        { wch: 15 },
        { wch: 20 },
        { wch: 20 },
        { wch: 30 },
        { wch: 30 },
      ];
      const history = xlsx.utils.json_to_sheet(this.downloadData);
      history["!cols"] = wscols;
      const sheetNames = ["History"];
      const sheetobject = { History: history };
      const workbook: xlsx.WorkBook = {
        Sheets: sheetobject,
        SheetNames: sheetNames,
      };
      xlsx.writeFile(
        workbook,
        `${dealName}_Audit_${this.datepipe.transform(
          new Date(),
          "dd-MM-yyyy_HH:mm:ss"
        )}.csv`
      );
    }
  }

  clear(searchInput) {
    if (searchInput.value) {
      this.myControl.setValue("");
      this.searchKey = "";
      this.historyRecords = [];
      this.getHistoryData();
    }
  }

  searchWithAssetNode(event) {
    this.searchKey = event.option.value.key;
    this.myControl.setValue(event.option.value.displayName, {
      emitEvent: false,
    });
    this.getHistoryData();
    this.historyRecords = [];
  }

  close() {
    if (this.autoTrigger && this.autoTrigger.panelOpen) {
      this.autoTrigger.closePanel();
    }
  }
}
