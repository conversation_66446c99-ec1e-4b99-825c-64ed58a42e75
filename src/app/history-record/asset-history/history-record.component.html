<div fxLayout="row wrap" class="p-percent-2" fxLayoutAlign="space-between center">
  <div fxFlex="30%" fxFlex.md="30%" fxFlex.xs="100%" fxFlex.sm="100%">
    <h2>
      {{(complexDataType?"label.title.advanceHistory":"label.title.history") | literal}}
    </h2>

    <mat-menu #sortMenu>
      <button class="menu-item" mat-menu-item
        (click)="sortData('historyEndDate');$event.stopPropagation()">
        <span> {{"label.literals.sortDate" | literal}}</span>
        <mat-icon *ngIf="sortAsPerKeyName === 'historyEndDate'" class="sort-arrow">{{sortDirection
          === 'asc' ? 'arrow_upward':'arrow_downward'}}</mat-icon>
      </button>

    </mat-menu>

  </div>

  <div *ngIf="!themeService.useNewTheme" fxFlex="70%" fxFlex.md="70%" fxFlex.xs="100%"
    fxFlex.sm="100%" class="history-buttons">
    <span *ngIf="!complexDataType">
      <button class="history-icon" matTooltip="Advance History" mat-icon-button class="pointer blue"
        (click)="getComplexHistoryData()">
        <mat-icon>feed</mat-icon>
      </button>
      <button class="history-icon" matTooltip="Download History Audit" mat-icon-button
        class="pointer green" [disabled]="disableWhenReject" (click)="exportFile()">
        <mat-icon>download</mat-icon>
      </button>
    </span>
    <button (click)="complexDataType?getHistoryData():toggleHistoryDrawer()" mat-icon-button
      class="pointer">
      <mat-icon class="pointer">close</mat-icon>
    </button>
  </div>

  <div *ngIf="themeService.useNewTheme" ffxFlex="70%" fxFlex.md="70%" fxFlex.xs="100%"
    fxFlex.sm="100%" class="history-buttons" fxLayoutGap="10">
    <ng-container *ngIf="!complexDataType">
      <button class="history-icon" matTooltip="Advance History" matTooltipClass="accent-tooltip"
        mat-icon-button class="outlined-icon-button" (click)="getComplexHistoryData()">
        <span class="material-symbols-outlined">
          description
        </span>
      </button>
      <button class="history-icon" matTooltip="Download History Audit"
        matTooltipClass="accent-tooltip" mat-icon-button class="outlined-icon-button"
        [disabled]="disableWhenReject" (click)="exportFile()">
        <span class="material-symbols-outlined">
          download
        </span>
      </button>
    </ng-container>
    <button (click)="complexDataType?getHistoryData():toggleHistoryDrawer()" mat-icon-button
      class="outlined-icon-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>

<div *ngIf="!complexDataType">
  <mat-form-field appearance="outline" class="full-width" subscriptSizing="dynamic">
    <mat-icon matPrefix matTooltip="Sort By" (click)="$event.stopPropagation();"
      [matMenuTriggerFor]="sortMenu" aria-label="Sort" class="pointer">sort</mat-icon>

    <input matInput [formControl]="myControl" placeholder="Select field to filter..."
      [matAutocomplete]="auto" #searchInput>
    <mat-icon matSuffix *ngIf="searchInput.value"
      (click)="clear(searchInput);$event.stopPropagation()" class="pointer">cancel</mat-icon>

    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn"
      (optionSelected)="searchWithAssetNode($event)">
      <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
        {{option.displayName}}
      </mat-option>
    </mat-autocomplete>
  </mat-form-field>

  <cdk-virtual-scroll-viewport appendOnly itemSize="15" (scrolledIndexChange)="nextBatch()"
    class="example-viewport cdkVirtualScroll" id='historyDiv'>
    <mat-stepper class="asset-history-stepper" orientation="vertical" [linear]="false"
      #historyStepper>
      <ng-template matStepperIcon="edit">
        <mat-icon>history</mat-icon>
      </ng-template>
      <ng-template matStepperIcon="number">
        <mat-icon>history</mat-icon>
      </ng-template>

      <mat-step *ngFor="let historyRecord of historyRecords" completed="false">
        <ng-template matStepLabel>
          {{historyRecord.changedByUser+" modified on "+ (historyRecord.changeDateTime+'Z'|
          dateTime)}}
        </ng-template>
        <ng-template matStepContent>
          <mat-list>
            <mat-list-item *ngFor="let diff of historyRecord.diff ">

              <ng-container [ngSwitch]="diff?.inputType">
                <div *ngSwitchCase="'Date'" class="body-2">
                  <span class="bold">{{diff.path}}</span>&nbsp; {{'changed from'}}&nbsp;
                  <span class="bold">{{diff?.old ? (diff?.old | date) : '-'}}</span>
                  &nbsp;{{"to"}}&nbsp;
                  <span class="bold">{{diff?.new ? (diff?.new | date): '-'}}</span>
                </div>
                <div *ngSwitchCase="'Date And Time'" class="body-2">
                  <span class="bold">{{diff.path}}</span>&nbsp; {{'changed from'}}&nbsp;
                  <span class="bold">{{diff?.old ? (diff?.old+'z' | dateTime): '-'}}</span>
                  &nbsp;{{"to"}}&nbsp;
                  <span class="bold">{{diff?.new ? (diff?.new+'z' | dateTime): '-'}}</span>
                </div>
                <div *ngSwitchDefault class="body-2">
                  <span class="bold">{{diff.path}}</span>&nbsp; {{'changed from'}}&nbsp;
                  <span class="bold">{{diff?.old ? diff?.old: '-'}}</span> &nbsp;{{"to"}}&nbsp;
                  <span class="bold">{{diff?.new ? diff?.new: '-'}}</span>
                </div>
              </ng-container>

            </mat-list-item>
          </mat-list>
        </ng-template>
      </mat-step>

    </mat-stepper>

    <div *ngIf="!showSideNavSpinner && (!historyRecords || historyRecords?.length === 0)"
      class="mt-5 flex-center">
      {{"label.literals.noHistoryRecords" | literal}}
    </div>
    <div *ngIf="showSideNavSpinner" class="mt-5 flex-center">
      <mat-spinner>
      </mat-spinner>
    </div>

  </cdk-virtual-scroll-viewport>
</div>

<div *ngIf="complexDataType">
  <ngx-monaco-editor class="manaco-editor" [options]="editorOptions"
    [(ngModel)]="advanceHistory"></ngx-monaco-editor>
</div>
