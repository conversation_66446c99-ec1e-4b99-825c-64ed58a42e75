import { Component, OnD<PERSON>roy, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { Subscription } from "rxjs";
import { DealDetailsComponent } from "src/app/application-summary/deal-details/deal-details.component";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BPStage } from "src/app/settings/bussiness-process-config/business-prcess.model";
import { DealService } from "src/app/shared-service/deal.service";

@Component({
  selector: "app-stage-movement-history",
  templateUrl: "./stage-movement-history.component.html",
  styleUrls: ["./stage-movement-history.component.scss"],
  providers: [DealDetailsComponent],
})
export class StageMovementHistoryComponent implements OnInit, OnDestroy {
  loading = true;
  selectedStageIndex = 0;
  stageList: MappedStage[] = [];
  dealDataSubscription: Subscription;
  gettingDetails: boolean;
  currentStageIndex: any;

  constructor(
    private dataSharingService: DataSharingService,
    private dealService: DealService,
    private dealDetailsComp: DealDetailsComponent,
    private router: Router
  ) {
    this.dealDataSubscription =
      dataSharingService.selectedApplicationDataSource.subscribe(() => {
        if (
          router.url ==
            "/application-summary/stage-history/" +
              btoa(dataSharingService.selectedApplicationData.id) &&
          !this.gettingDetails
        )
          this.getStageMovementHistory();
      });
  }

  ngOnInit() {
    if (this.dataSharingService.selectedApplicationData) {
      this.getStageMovementHistory();
    } else {
      this.dealDetailsComp.onPageRefresh(true);
    }
  }

  ngOnDestroy() {
    this.dealDataSubscription.unsubscribe();
  }

  getStageMovementHistory() {
    this.gettingDetails = true;
    this.dealService
      .stageMovementHistory(this.dataSharingService.selectedApplicationData?.id)
      .subscribe({
        next: (
          historyList: {
            stageName: string;
            stageRemarks: stageMovementHistoryRecord[];
          }[]
        ) => {
          this.loading = false;
          this.gettingDetails = false;
          this.getStageList(historyList);
        },
        error: () => {
          this.loading = false;
          this.gettingDetails = false;
        },
      });
  }

  getStageList(
    historyList: {
      stageName: string;
      stageRemarks: stageMovementHistoryRecord[];
    }[]
  ) {
    const stages =
      this.dataSharingService?.selectedBusinessProcessWithStagedetails;
    const currentStageName =
      this.dataSharingService.selectedApplicationData?.currentStageName;
    const activeStage =
      this.dataSharingService.selectedBusinessProcessWithStagedetails?.find(
        (stage) => stage.name === currentStageName
      );
    if (stages?.length > 0 && activeStage) {
      this.stageList = [];
      stages.forEach((stage: BPStage) => {
        const stageRemarks = historyList.find(
          (stageHistoryRecord) => stageHistoryRecord.stageName == stage.name
        )?.stageRemarks;
        const mappedStageObj: MappedStage = {
          name: stage.name,
          remarks: stageRemarks,
          completed: stage.order < activeStage.order,
          current: stage.order === activeStage.order,
          upcoming: stage.order > activeStage.order,
          previous:
            stageRemarks?.length > 0 &&
            stageRemarks[0].movementDirection === "Previous",
          display:
            stage.name !== "Rejected" &&
            stage.name !== "Approved" &&
            stage.order !== 1 &&
            stage.display !== "Inactive",
        };
        if (mappedStageObj.display) this.stageList.push(mappedStageObj);
      });
      this.currentStageIndex = this.stageList.findIndex(
        (stage) => stage.current
      );

      this.selectedStageIndex = this.latestMovedStageIndex();
    }
  }

  latestMovedStageIndex() {
    const isPrevLastModified = () => {
      if (this.stageList[this.currentStageIndex + 1].remarks?.length == 0)
        return true;
      return (
        new Date(
          this.stageList[this.currentStageIndex - 1].remarks?.[0].createdDate
        ) >
        new Date(
          this.stageList[this.currentStageIndex + 1].remarks?.[0].createdDate
        )
      );
    };

    if (this.stageList.length === 0) return this.currentStageIndex;
    else if (this.stageList.length >= 2 || isPrevLastModified())
      return this.currentStageIndex - 1;
    else return this.currentStageIndex + 1;
  }
}

type stageMovementHistoryRecord = {
  createdBy: string;
  createdDate: string;
  remarks: string;
  movementDirection: string;
};

type MappedStage = {
  name: string;
  completed: boolean;
  current: boolean;
  previous: boolean;
  upcoming: boolean;
  display: boolean;
  remarks: stageMovementHistoryRecord[];
};
