.spinner-container,
.no-records-container {
    height: 55vh;
    align-items: center;
    justify-content: center;
    display: flex;
}

.stage-history-stepper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    padding-left: 20px;
    border: solid 1px var(--container-border-color);
    border-radius: 6px;
    height: fit-content;
    background-color: var(--secondary-container-color);
  }
  
  .step {
    display: flex;  
    position: relative;
    padding: 10px;
    width: 100%;
  }
  
  .step-indicator {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20px;
    padding-top: 10px;
  }
  
  .circle {
    width: 23px;
    height: 23px;
    border-radius: 50%;
    background-color: #838383;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s;
  }
  
  .line.completed,
  .circle.completed {
    background-color: var(--accent-color);
  }



  .line.previous,
  .circle.previous {
    background-color: var(--amber-color);
    .material-symbols-outlined.checkmark {
      color: black !important;
    }
  }


  line.wip{
    background-color: var(--accent-color) !important;
  }

  .circle.wip {
    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  svg {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    stroke: var(--accent-color);
  }
  
  .material-symbols-outlined.checkmark {
    font-size: 22px;
    color: #fff;
  }
  
  .line {
    width: 3px;
    background-color: #838383;
    height: 85%;
    position: absolute;
    top: -60px;
    z-index: 0;
    border-radius: 5px;
  }
  
  .step-content {
    flex-grow: 1;
    padding: 10px;
  }


  .step-content.active{
    background: var(--surface-color);
    border: solid 1px var(--primary-color);
    border-radius: 5px;
  }

  .step-content:hover{
    border-radius: 5px;
    background: var(--surface-color);
    transition: background 100ms;
  }
  
  .step-content h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
  
  .step-content p {
    margin: 5px 0 0;
    color: var(--secondary-text-color);
  }
  

  .remarks-list .mat-mdc-card-outlined {
      border-color: var(--container-border-color);
  }
  .p10{
    padding: 10px
  }



  .line.approved,
  .circle.approved {
    background-color: var(--accent-color);
  }