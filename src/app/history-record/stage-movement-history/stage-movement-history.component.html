<div class="stage-history-main-container m-b-30">
  <div class="stage-history-header">
    <div class="title p-percent-1" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10">
      <button mat-icon-button backButton matTooltip="Back">
        <span class="material-symbols-outlined">arrow_back</span>
      </button>
      <h2 class="no-m">Stage History</h2>
    </div>
  </div>

  <div fxLayout="row" *ngIf="stageList?.length > 0 && !loading">
    <div class="stage-history-stepper" fxFlex="30">
      <div class="step" *ngFor="let stage of stageList; let i = index;let last = last;"
        (click)="last ? $event.stopPropagation() : selectedStageIndex = i"
        [class.active]="selectedStageIndex === i">
        <div class="step-indicator">
          <div class="line" [class.completed]="stage.completed||stage.current"
            [class.previous]="stage.previous && !stage.current"
            *ngIf="i >0 && i < stageList.length"></div>

          <div class="circle" [class.approved]="(stage.current && last)"
            [class.completed]="stage.completed" [class.previous]="stage.previous"
            [class.wip]="stage.current">

            <ng-container
              *ngIf="(stage.completed || stage.previous) && !stage.current ; else stepInProgressOrNumber">

              <span class="material-symbols-outlined checkmark" *ngIf="stage.completed">check</span>
              <span class="material-symbols-outlined checkmark" *ngIf="stage.previous">undo</span>

            </ng-container>
            <ng-template #stepInProgressOrNumber>
              <ng-container *ngIf="stage.current && !last; else stepNumber">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"
                  preserveAspectRatio="xMidYMid" width="30" height="30"
                  style="shape-rendering: auto;">
                  <g>
                    <circle stroke-width="45" fill="none" r="20" cy="50" cx="50">
                      <animate begin="0s" calcMode="spline" keySplines="0 0.2 0.8 1" keyTimes="0;1"
                        values="0;40" dur="6.25s" repeatCount="indefinite" attributeName="r">
                      </animate>
                      <animate begin="0s" calcMode="spline" keySplines="0.2 0 0.8 1" keyTimes="0;1"
                        values="1;0" dur="6.25s" repeatCount="indefinite" attributeName="opacity">
                      </animate>
                    </circle>
                    <circle stroke-width="45" fill="none" r="20" cy="50" cx="50">
                      <animate begin="-3.125s" calcMode="spline" keySplines="0 0.2 0.8 1"
                        keyTimes="0;1" values="0;40" dur="6.25s" repeatCount="indefinite"
                        attributeName="r"></animate>
                      <animate begin="-3.125s" calcMode="spline" keySplines="0.2 0 0.8 1"
                        keyTimes="0;1" values="1;0" dur="6.25s" repeatCount="indefinite"
                        attributeName="opacity"></animate>
                    </circle>

                  </g>
                </svg>

              </ng-container>
              <ng-template #stepNumber>

                <span class="material-symbols-outlined checkmark" *ngIf="last">flag_circle</span>
              </ng-template>
            </ng-template>

          </div>
        </div>
        <div [class.pointer]="!last" [ngClass]="last ? 'p10' : 'step-content'"
          [class.active]="selectedStageIndex === i">
          <div class="subtitle-1" [class.bold]="stage.current">{{ stage.name }}</div>
          <p>
            <ng-container *ngIf="stage?.remarks?.length>0 else emptyUI">
              <div>{{"Latest date of action"}}</div>
              <span>{{ stage.remarks[0].createdDate+'Z' | dateTime }}</span>
            </ng-container>
            <ng-template #emptyUI>
              <div fxLayout="column" style="color: transparent;">
                <div>{{"Latest date of action"}}</div>
                <span>-</span>
              </div>
            </ng-template>
          </p>

        </div>
      </div>
    </div>
    <mat-divider vertical="true" class="m-h-2"></mat-divider>
    <div fxFlex>

      <div class="title p-v-1-p" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10">
        <h2 class="no-m">Remarks</h2>
      </div>

      <div class="p-v-1-p remarks-list">
        <mat-card appearance="outlined" class="m-v-1"
          *ngFor="let remark of stageList[selectedStageIndex]?.remarks ? stageList[selectedStageIndex]?.remarks : []; let i = index">
          <mat-card-content>
            <div fxLayout="row" fxLayoutAlign="start start" fxLayoutGap="10">
              <div fxFlex="5">
                <div class="circle"
                  [class.completed]="remark.movementDirection==='Next' || remark.movementDirection==='Approve'"
                  [class.previous]="remark.movementDirection==='Previous'">
                  <span class="material-symbols-outlined checkmark"
                    *ngIf="remark.movementDirection==='Next'">check</span>
                  <span class="material-symbols-outlined checkmark"
                    *ngIf="remark.movementDirection==='Previous'">undo</span>
                  <span class="material-symbols-outlined checkmark"
                    *ngIf="remark.movementDirection==='Approve'"> flag_circle</span>
                </div>
              </div>
              <div fxFlex>
                <div fxLayout="column" fxLayoutAlign="start start" fxLayoutGap="10">
                  <div class="body-1">
                    <span class="bold">Date of Action -&nbsp;</span>
                    <span class="secondary-text-color">{{remark.createdDate+'Z' | dateTime}}</span>
                  </div>
                  <div class="body-1">
                    <span class="bold">Done By -&nbsp; </span>
                    <span class="secondary-text-color">{{remark.createdBy | uppercase}}</span>
                  </div>
                  <div class="body-1">
                    <span class="bold">Remark -&nbsp;</span>
                    <span class="secondary-text-color">{{remark.remarks ? remark.remarks :
                      'N/A'}}</span>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
        <div
          *ngIf="(stageList && (!stageList[selectedStageIndex]?.remarks ||  stageList[selectedStageIndex]?.remarks?.length === 0))"
          class="no-records-container">
          <div class="no-records-found"></div>
        </div>
      </div>

    </div>
  </div>

  <div *ngIf="loading" class="spinner-container">
    <mat-spinner>
    </mat-spinner>
  </div>

  <div *ngIf="stageList?.length === 0" class="no-records-container">
    <div class="no-records-found"></div>
  </div>

</div>
