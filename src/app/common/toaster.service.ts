import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { take } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class ToasterService {
  constructor(private readonly toastr: ToastrService) {}

  info(message: string) {
    this.toastr.info(message, null, {
      positionClass: "toast-top-center",
      progressBar: true,
    });
  }

  infoWithOnTap(message: string, tapAction: Function) {
    this.toastr
      .info(message, null, {
        positionClass: "toast-top-center",
        disableTimeOut: true,
        progressBar: true,
      })
      .onTap.pipe(take(1))
      .subscribe(() => tapAction());
  }

  error(message: string | any[] | {}, keepOpen?) {
    let msg = "";

    if (typeof message === "string") msg = message;
    if (typeof message !== "string") msg = message ? message[0] : "";
    if (typeof message === "object") msg = message ? String(message) : "";

    if (msg.length)
      this.toastr.error(msg, null, {
        timeOut: keepOpen ? keepOpen : 150000,
        positionClass: "toast-top-center",
      });
  }

  success(message: string, timeOut?: number) {
    this.toastr.success(message, null, {
      positionClass: "toast-bottom-center",
      progressBar: true,
      timeOut: timeOut ? timeOut : 5000,
    });
  }

  errorList(message: string, keepOpen?) {
    this.toastr.error(message, null, {
      timeOut: 150000,
      disableTimeOut: keepOpen,
      positionClass: "toast-top-center",
    });
  }

  infoList(message: string, keepOpen?) {
    this.toastr.warning(message, null, {
      timeOut: 150000,
      disableTimeOut: keepOpen,
      positionClass: "toast-top-center",
      progressBar: true,
    });
  }

  clearToasts() {
    this.toastr.clear();
  }
}
