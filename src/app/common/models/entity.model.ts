export enum EntityType {
  PERSON = "Person",
  COMPANY = "Company",
  FUND = "Fund",
}

export type EntityBasicDetailsResponse = {
  id: string;
  entityName: string;
  entityType: string;
  subType: string;
  createdBy: string;
  createdDate: string;
  entityStatus: string;
  version: number;
}[];

export type EntityDefinitionByNameResponse = {
  id: string;
  entityName: string;
  entityType: string;
  version: number;
  subType: string;
  entityDetail: any;
  status: string;
  entityStatus: string;
  dataModelDomain: {
    dataModelId: string;
    dataModelName: string;
    createdBy: string;
    createdDate: string;
    modifiedBy: string;
    modifiedDate: string;
  };
  defaultEntity: boolean;
};
