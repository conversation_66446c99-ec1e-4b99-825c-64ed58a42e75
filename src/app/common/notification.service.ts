import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { map } from "rxjs/operators";

@Injectable({
  providedIn: "root",
})
export class NotificationService {
  pageSize = 20;
  pageIndex = 0;
  sortingKey = "createdDate";
  sortBy = "DESC";
  initiator = "Originate";
  unreadNotificationsCount: number = 0;

  constructor(
    private http: HttpClient,
    @Inject("tasksBaseUrl") private notificationUrl: string
  ) {}

  getNotifications(readFlag, pageSize) {
    return this.http
      .get(
        `${this.notificationUrl}/notification/pagination?readFlag=${readFlag}&sortingKey=${this.sortingKey}&sortBy=${this.sortBy}&page=${this.pageIndex}&size=${pageSize}&initiator=${this.initiator}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  notificationRead(data) {
    return this.http.put(`${this.notificationUrl}/notification`, data).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  notificationAllRead(data) {
    const markAsRead = true;
    const createdBy = localStorage.getItem("user");
    return this.http
      .put(
        `${this.notificationUrl}/notification?createdBy=${createdBy}&markAllAsRead=${markAsRead}`,
        data
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  notifyMentions(data) {
    return this.http
      .post(
        `${this.notificationUrl}/notification/mentionedUser?initiator=Originate`,
        data
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }
}
