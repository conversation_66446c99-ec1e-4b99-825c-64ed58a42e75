import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class DateFormattingService {
  readonly defaultDateFormatConfig = {
    dateFormat: "dd/MM/yyyy",
    timeFormat: "HH:mm:ss",
  };
  readonly dateFormats = [
    {
      format: "dd MMMM yyyy",
      formatWithExample: "dd MMMM yyyy (e.g., 25 October 2023)",
    },
    {
      format: "dd/MMMM/yyyy",
      formatWithExample: "dd/MMMM/yyyy (e.g., 25/October/2023)",
    },
    {
      format: "dd-MMMM-yyyy",
      formatWithExample: "dd-MMMM-yyyy (e.g., 25-October-2023)",
    },
    {
      format: "MMMM-dd-yyyy",
      formatWithExample: "MMMM-dd-yyyy (e.g., October-25-2023)",
    },
    {
      format: "MMMM dd yyyy",
      formatWithExample: "MMMM dd yyyy (e.g., October 25 2023)",
    },
    {
      format: "MMMM/dd/yyyy",
      formatWithExample: "MMMM/dd/yyyy (e.g., October/25/2023)",
    },
    {
      format: "yyyy-MM-dd",
      formatWithExample: "yyyy-MM-dd (e.g., 2023-10-25)",
    },
    { format: "MMM d, y", formatWithExample: "MMM d, y (e.g., Oct 25, 2023)" },
    {
      format: "MM/dd/yyyy",
      formatWithExample: "MM/dd/yyyy (e.g., 10/25/2023)",
    },
    {
      format: "dd/MM/yyyy",
      formatWithExample: "dd/MM/yyyy (e.g., 25/10/2023)",
    },
    {
      format: "dd-MM-yyyy",
      formatWithExample: "dd-MM-yyyy (e.g., 25-10-2023)",
    },
    {
      format: "dd-MMM-yyyy",
      formatWithExample: "dd-MMM-yyyy (e.g., 25-Oct-2023)",
    },
  ];

  readonly timeFormats = [
    { format: "h:mm a", formatWithExample: "h:mm A ( e.g. 3:45 PM )" },
    { format: "hh:mm a", formatWithExample: "hh:mm A ( e.g. 03:45 PM )" },
    { format: "h:mm:ss a", formatWithExample: "h:mm:ss A ( e.g. 3:45:30 PM )" },
    {
      format: "hh:mm:ss a",
      formatWithExample: "hh:mm:ss A ( e.g. 03:45:30 PM )",
    },
    {
      format: "h:mm:ss.SSS a",
      formatWithExample: "h:mm:ss.SSS A ( e.g. 3:45:30.123 PM )",
    },
    {
      format: "hh:mm:ss.SSS a",
      formatWithExample: "hh:mm:ss.SSS A ( e.g. 03:45:30.123 PM )",
    },
    { format: "H:mm", formatWithExample: "H:mm ( e.g. 15:45 )" },
    { format: "HH:mm", formatWithExample: "HH:mm ( e.g. 15:45 )" },
    { format: "H:mm:ss", formatWithExample: "H:mm:ss ( e.g. 15:45:30 )" },
    { format: "HH:mm:ss", formatWithExample: "HH:mm:ss ( e.g. 15:45:30 )" },
    {
      format: "H:mm:ss.SSS",
      formatWithExample: "H:mm:ss.SSS ( e.g. 05:45:30.123 )",
    },
    {
      format: "HH:mm:ss.SSS",
      formatWithExample: "HH:mm:ss.SSS ( e.g. 15:45:30.123 )",
    },
    {
      format: "HH:mm:ss.SSSZ",
      formatWithExample: "HH:mm:ss.SSSZ ( e.g. 15:45:30.123+0000 )",
    },
  ];

  private readonly dateFormatSubject = new BehaviorSubject<string>(
    this.defaultDateFormatConfig.dateFormat
  ); // Default date format

  private readonly dateTimeFormatSubject = new BehaviorSubject<string>(
    this.defaultDateFormatConfig.timeFormat
  ); // Default date time format

  dateFormat$ = this.dateFormatSubject.asObservable();
  dateTimeFormat$ = this.dateTimeFormatSubject.asObservable();

  // setDateFormat(format: string) {
  //   this.dateFormatSubject.next(format);
  // }

  setDateTimeFormat(formatConfig: { dateFormat: string; timeFormat: string }) {
    this.dateFormatSubject.next(formatConfig.dateFormat);
    this.dateTimeFormatSubject.next(formatConfig.timeFormat);
  }

  getDateFormat() {
    return this.dateFormatSubject.value;
  }

  getTimeFormat() {
    return this.dateTimeFormatSubject.value;
  }

  getDateTimeFormat() {
    return this.getDateFormat() + " " + this.getTimeFormat();
  }

  getAdaptorDateTimeFormat() {
    return this.getDateFormat().toUpperCase() + " " + this.getTimeFormat();
  }
}
