import { DatePipe } from "@angular/common";
import { Inject, LOCALE_ID, Pipe, PipeTransform } from "@angular/core";
import { DateFormattingService } from "./date-formatting.service";

@Pipe({
  name: "dateTime",
  pure: false,
  standalone: true,
})
export class ZcpDateTimePipe extends DatePipe implements PipeTransform {
  constructor(
    @Inject(LOCALE_ID) locale: string,
    private readonly dateFormatService: DateFormattingService
  ) {
    super(locale);
  }

  override transform(
    value: null,
    format?: string,
    timezone?: string,
    locale?: string
  ): null;

  override transform(
    value: string | number | Date,
    format?: string,
    timezone?: string,
    locale?: string
  ): string | null;

  override transform(
    value: string | number | Date | null,
    format?: string,
    timezone?: string,
    locale?: string
  ): string | null {
    if (value === null) {
      return null;
    }

    const userFormat = format || this.dateFormatService.getDateTimeFormat();
    return super.transform(value, userFormat, timezone, locale);
  }
}
