import { Inject, Injectable, OnDestroy, Optional } from "@angular/core";
import { MAT_DATE_LOCALE } from "@angular/material/core";
import { MomentDateAdapter } from "@angular/material-moment-adapter";
import * as moment from "moment";
import { DateFormattingService } from "./date-formatting.service";
import {
  NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  NgxMatMomentAdapter,
} from "@angular-material-components/moment-adapter";
import { Subscription } from "rxjs";

@Injectable()
export class CustomDateAdapter extends MomentDateAdapter implements OnDestroy {
  formatSubscription: Subscription;
  constructor(private readonly dateFormatService: DateFormattingService) {
    super(MAT_DATE_LOCALE.toString());

    this.formatSubscription = dateFormatService.dateFormat$.subscribe(
      (newFormat) => {
        if (newFormat) {
          this.selectedDateFormat = newFormat.toUpperCase();
        }
      }
    );
  }
  private selectedDateFormat = this.dateFormatService
    .getDateFormat()
    .toUpperCase();
  override parse(value: any): moment.Moment | null {
    return moment(value, this.selectedDateFormat, true);
  }

  override format(date: moment.Moment, displayFormat: string): string {
    return date.format(this.selectedDateFormat);
  }

  ngOnDestroy(): void {
    this.formatSubscription.unsubscribe();
  }
}

@Injectable()
export class CustomNgxMatDateAdapter
  extends NgxMatMomentAdapter
  implements OnDestroy
{
  formatSubscription: Subscription;
  constructor(
    @Optional() @Inject(MAT_DATE_LOCALE) dateLocale: string,
    @Optional() @Inject(NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS) options: any,
    private readonly dateFormatService: DateFormattingService
  ) {
    super(dateLocale, options);

    this.formatSubscription = dateFormatService.dateTimeFormat$.subscribe(
      (newFormat) => {
        if (newFormat) {
          this.selectedDateTimeFormat =
            this.dateFormatService.getAdaptorDateTimeFormat();
        }
      }
    );
  }

  private selectedDateTimeFormat =
    this.dateFormatService.getAdaptorDateTimeFormat();

  // Parse the input date using the selected format
  override parse(
    value: any,
    parseFormat: string | string[]
  ): moment.Moment | null {
    if (!value || typeof value !== "string") return null;

    return moment(value, this.selectedDateTimeFormat, true);
  }

  // Format the displayed date based on the selected format
  override format(date: moment.Moment, displayFormat: string): string {
    date = this.clone(date);
    if (!this.isValid(date)) {
      return "";
    }
    return date.format(this.selectedDateTimeFormat);
  }

  ngOnDestroy(): void {
    this.formatSubscription.unsubscribe();
  }
}
