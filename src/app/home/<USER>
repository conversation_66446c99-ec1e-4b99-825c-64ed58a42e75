.head-card-1{
  background-color: #2699Fb;
  height: 0px;
    width: 35px;
    float: none;
    padding: 14px 0px 10px 0px;
 }
  
 .bg {
  /* The image used */
  background-image: url("../../assets/dashboard.png");

  /* Full height */
  height: 100%; 

  /* Center and scale the image nicely */
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
 .head-card-2{
  background-color: #87a2c5;
    width: 35px;
    float: none;
    padding: 14px 0px 10px 0px;
 }
  
  
 .head-card-3{
  background-color: #53d622;
  height: 0px;
    width: 35px;
    float: none;
    padding: 14px 0px 10px 0px;
 }
  
  
 .head-card-4{
  background-color: #d65959;
  height: 0px;
    width: 35px;
    float: none;
    padding: 14px 0px 10px 0px;
 }
  
 .main-card-1{
  // background-color: #74a9ed94 !important;
  // border-radius: 16px;
  // border: 4px solid #74a9ed;
  // margin-top: -40px;
  height: 100%;
 }
  
 .main-card-2{
  // background-color: #a5f2a773 !important;
  // border-radius: 16px;
  // border: 4px solid #a5f2a7;
  // margin-top: -40px;
  height: 100%;
 }
  
  
 .main-card-3{
  // background-color: #c1d5ef6e !important;
  // border-radius: 16px;
  // border: 4px solid #c1d5ef;
  // margin-top: -40px;
  height: 100%;
 }

 .fixedHeightWidth{
  height:400px; width: 100vw;
}


.w-8{
  width: 8%;
}

.w-40{
  width: 40%
} 
.displayNone{
  display: none;
}

 .relative-position{
   position: relative;
 }
 table {
  width: 100%;
}

//  .cardTitle{
 /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version. */
 ::ng-deep .cardTitle .mat-card-header-text {
    margin: 0 !important; 
   }
 
  .center{
    position: absolute;;
    top: 70%;
    left: 50%;
    transform: translate(-50% ,-50%);
  }
 .main-card-4{
  // background-color: #efc1c18f !important;
  // border-radius: 16px;
  // border: 4px solid #efc1c1;
  // margin-top: -40px;
  height: 100%;
 }
  

 .centorMessage{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
 }

 .datePicker{
  // ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  //     padding: 0 !important
  // }
}
 .second-div-card{
  background-color:#c1d5ef !important ;
 }

 .customize {
  display: flex;
  align-items: center;
  font-size: 16px;
 }

 .blue-color {
   color: blue;
 }
 /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version. */
 .taskheader .mat-card-header-text {
  margin: 0  !important;
}
.header
{
  font-size: 16px !important;
  margin: -1px -42px 16px !important;
}
 .blue-color {
   color: blue;
 }
 .title{
   float: left;
   margin-left: -6%;
   margin-top: -1%;
 }
 .selectStatusCss {

  bottom: 0 !important;
  font-weight: 500;
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-infix {
      text-align: end !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-wrapper{
      padding: 0 !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-infix{
      padding: 0 !important;
  }

}
 
//  .matcard-quickaction-content
//  {
//       font-size: 17px !important;
//  }
 

// Floating button Code By- Vinayak Patil

.fab-div {

  padding: 1%;

  z-index: 1000;

  position: fixed;

  bottom: 3%;

  right: 3%;

}

.mat-mdc-menu-item {
 font-family: Roboto, "Helvetica Neue", sans-serif;
 font-size: 19px;
 font-weight: 400;
 }

.loanaccount{
  width: 800px;
}

.iconclass{
  height: 40px !important;
    width: 51px !important;
}
.SelectBusinessProcess{
  margin: 10% 0 !important;
}
.width-20{
  width : 20% !important
}


