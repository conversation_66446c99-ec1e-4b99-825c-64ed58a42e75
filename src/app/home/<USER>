import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { DataSharingService } from '../common/dataSharing.service';
import { ToasterService } from '../common/toaster.service';
import { ApplicationLabelService } from '../shared-service/application-label.service';
import { DealService } from '../shared-service/deal.service';
import { IdentityService } from '../shared-service/identity.service';
import { BusinessProcessService } from '../shared-service/businessProcess.service';
import { TasksServiceService } from '../task/tasks-service/tasks-service.service';
import { CurrencyFormatService } from '../common/currency/currency-format.service';
import { ErrorService } from '../shared-service/error.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  selectedBusinessProcess = "";
  @Input() businessProcessListFromParent: any;
  businessProcessList: any;
  showNoDataMessage: any = false;
  searchedBP: any;
  user: any;
  selectedBusinessProcessId: any = 0;

  constructor( private errorService :ErrorService,
    public dataSharingService: DataSharingService,
    public applicationLabelService: ApplicationLabelService,
    private businessProcessService: BusinessProcessService,
    public taskService : TasksServiceService,
    public identityService : IdentityService,
    public notificationMessage: ToasterService) { }


  ngOnInit() {
    this.user = localStorage.getItem('user');
    this.getBusinessProcessList();
  }

  displayedColumns: string[];
  dataSource = new MatTableDataSource<any>();

  @ViewChild(MatPaginator) paginator: MatPaginator;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  // Equanimity requirement
  getPropertyName(element) {
    
    return Object.entries(element)[0][0];
  }

 
  onSelectOfBusinessProcess(value) {
    
    this.dataSharingService.selectedBusinessProcessIdCustom = value;
    this.selectedBusinessProcess = this.businessProcessList.filter(businessProcess => businessProcess.id == value)[0].name
    this.selectedBusinessProcessId = this.businessProcessList.filter(businessProcess => businessProcess.id == value)[0].id
    this.dataSharingService.getBusinessProcessId(value);
 }
 
  getBusinessProcessList() {
    this.businessProcessService.getAllBusinessProcessList().subscribe(response => { 
      this.businessProcessService.businessProcessList = response;
      this.businessProcessList = this.businessProcessService.businessProcessList;
      
      if (this.businessProcessList.length != 0) {
        this.selectedBusinessProcess = this.businessProcessList[0].name;
        this.selectedBusinessProcessId = this.businessProcessList[0].id;
        this.dataSharingService.selectedBusinessProcessIdCustom = this.businessProcessList[0].id;
      } else {
        this.showNoDataMessage = true;  
      }
    }, (error) => {
      this.showNoDataMessage = true;
    })

    
  }


getBPList(list) {
  if (this.searchedBP) {
    return this.businessProcessList
      .slice()
      .filter((list) => list?.name?.toLowerCase().includes(this.searchedBP.toLowerCase()));
  } else {
    return this.businessProcessList;
  }
}

filterBP($event){
    this.searchedBP = $event;
}
}

