<div fxLayout="row wrap" fxLayoutGap="10px">
  <div fxFlex="12%" fxFlex.md="16%" fxFlex.xs="40%" fxFlex.sm="27%" fxFlex.lg ="11%" >
    <p  class="inputLabelsInDeal SelectBusinessProcess"> Select Business Process : </p>
  </div>
  <mat-form-field class="selectStatusCss width-20" >
          <mat-select (selectionChange)="onSelectOfBusinessProcess($event.value)" [(ngModel)]="selectedBusinessProcessId"
            [ngModelOptions]="{standalone: true}">
            <mat-option>
              <ngx-mat-select-search  placeholderLabel="Search Business Process" 
            noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterBP($event)" [ngModelOptions]="{standalone: true}"
            ></ngx-mat-select-search>
          </mat-option>
            <mat-option *ngFor="let type of getBPList(businessProcessList)" [value]="type.id">{{type.name}}</mat-option>      
     </mat-select>   
  </mat-form-field>
  </div>
  <div class="mb-5" >
  <div fxLayout="row wrap"  >
    
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
   
      <mat-card appearance="outlined" class="custom-elevation  mat-card-top-border" >

        <mat-card-content *ngIf="selectedBusinessProcessId" class="mt-5px">
          <app-custom-search></app-custom-search>
        </mat-card-content>
      </mat-card>
    </div>
</div>
