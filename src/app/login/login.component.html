<div  *ngIf="showLoginPage">
  <div class="page_loader"></div>
  <div class="login login-background">
    <div class="login-inner login-box-inner welcomeContainerCss align-self-center pad-0 bg-img login-box-logo-section">
      <div class="logo">
        <img src="assets/imgs/finnate_Originate.png" alt="Originate Finnate" class="logo-blue">
        <img src="assets/imgs/finnate_originate_trans.png" alt="Originate Finnate" class="logo-white">
      </div>
      <div class="container loginFormDiv" >
        <div class="row login-box d-flex">
          <div class="login-box-inner align-self-center pad-0 form-info login-box-black">
            <div class="form-section clearfix">

              <form fxLayout="column" [formGroup]="loginForm" class="login-form" id="login-form" autocomplete="off">

                <mat-form-field appearance="fill" class="fontWeight500 width100 border-bottom" >
                  <mat-icon matPrefix  class="mat-icon-theme icons">account_circle</mat-icon>
                  <input aria-label="tenant-name-input-field"  matInput placeholder="Tenant" 
                    formControlName="tenantIdentifier">
                  <mat-error *ngIf="loginForm.controls.tenantIdentifier.errors?.required">
                    {{"label.materror.Tenant"|literal}}

                  </mat-error>
                </mat-form-field>
                <mat-form-field appearance="fill" class="fontWeight500 width100 border-bottom">
                  <mat-icon matPrefix  class="mat-icon-theme icons">person</mat-icon>
                  <input aria-label="user-name-input-field"  #username matInput placeholder="Username" id="username"
                     formControlName="username">
                  <mat-error *ngIf="loginForm.controls.username.errors?.required">
                    {{"label.materror.Username"|literal}}

                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="fill" class="fontWeight500 width100 border-bottom">
                  <mat-icon matPrefix  class="mat-icon-theme icons">lock</mat-icon>
                  <input aria-label="password-input-field"  matInput #password id="password" 
                    [type]="hide ? 'password' : 'text'" placeholder="Password" formControlName="password">
                  <mat-icon matSuffix class="pointer mat-icon-theme" (click)="hide = !hide">{{hide ? 'visibility_off' :
                    'visibility'}}</mat-icon>
                  <mat-error *ngIf="loginForm.controls.password.errors?.required">
                    {{"label.materror.Password"|literal}}

                  </mat-error>
                </mat-form-field>
                <div class="loginErrorContainer errorMessageColor" *ngIf="loginError">
                  <small>{{"label.validation.enterUsernameAndPassword"|literal}}</small>
                </div>
                <button aria-label="login-btn"  mat-stroked-button class="login-btn dark-login-btn loginButton" (click)="submit()">
                  Login
                </button>
              

              </form>



            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="!showLoginPage">
  <app-change-password (valueChange)='onChangesReceived($event)'></app-change-password>
</div>