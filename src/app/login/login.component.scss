
.forgot-password-container {
  margin-top: 5%;
}

.gridGapZero {
  grid-gap: 0 !important;
}


.loginCard {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
  width: 55%;
  border-radius: 12px;
  overflow: hidden;
  padding: 0px;

}

.welcomeContainerCss {
  display: flex;
  flex-flow: column;
  min-height: 485px;
}

.welcomeCss {
 
  font-weight: 400;
  padding: 10% 10% 5%;
  font-size: 30px;
  margin-bottom: 0px;
  
}


.signInCss {

  font-weight: 400;
  padding: 7% 10% 5%;
  font-size: 25px;
  margin-bottom: 0px;
}

.loginPageSideImage {
  background-image: url(../../assets/orange-graph-lines.svg);
  background-size: 100%;
  min-height: 41vh;
  min-width: 100%;
  flex: 1 1 auto;
  background-repeat: no-repeat;
  background-position: center bottom;
}

.width100 {
  width: 100%;
}

.fontWeight500 {
  font-weight: 500;
}

.loginBtnContainer {
  display: flex;
  justify-content: center;
}

.forgotPasswordLinkContainer {
  display: flex;
  justify-content: center;
  margin: 10% 0 2%;
  font-size: 14px;
  font-weight: 400;
}

.loginErrorContainer {
  display: flex;
  justify-content: center;
 
}

.loginInputContainer {
  input.mat-mdc-input-element {
    margin-top: -0.0625em !important;
    margin-bottom: 2% !important;
  }
  /*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-infix {
    border-top: none !important;
  }
}


/** Login start **/
body {
  font-family: 'Open Sans', sans-serif;
  color: #535353;
}

.login .login-inner {
  z-index: 999;
  position: relative;
  min-height: 100vh;
  text-align: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden;
}


.login h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: 'Jost', sans-serif;
      display: flex;
}


.login .login-box {
  background: #fff;
  margin: 0 auto;
  box-shadow: 0 0 25px rgb(0 0 0 / 82%);
  background-size: cover;
  position: relative;
  max-width: 450px;
  display: flex;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-right-radius:12px;
}

.login .login-box-inner {
  flex: 0 0 auto;
  width: 100%;
 
}

.login-box-black{
  border-top-left-radius: 9px;
  border-bottom-left-radius: 9px;
  border-top-right-radius: 9px;
  border-bottom-right-radius:9px;
}

.login .form-section {
  text-align: center;
 padding: 47px 47px;
  border-top-right-radius: 10%;
  border-bottom-right-radius: 10%;
 
}

.login .pad-0 {
  padding: 0;
}

.login .logo img {
  margin-bottom: 20px;
  height: 120px;
}

.login .form-section h3 {
  text-align: center;
  margin:0 7px 18px 28px;
  font-size: 23px;
  font-weight: 400;
}

.login .form-section button:focus {
  outline: none;
  outline: 0 auto -webkit-focus-ring-color;
}

.login .logo-other {
  display: none;
}


.login .info {
  position: relative;
  width: 320px;
  margin: 0 22%;
  height: 320px;
  top: 5rem;
}

.login .info .box h3 {
  font-size: 18px;
  text-transform: uppercase;
  margin-bottom: 20px;
  font-weight: 500;
  margin-left: 24%;
}

.login .info .box {
  position: absolute;
  width: 270px;
  margin: 0 auto;
  height: 270px;
  overflow: hidden;
  padding: 20px;
  border: solid 5px rgb(255 255 255 / 20%);
}

.login .info .box .content {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border: solid 1px rgb(255 255 255 / 20%);
  padding: 60px 20px;
}

.login .info .box span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  box-sizing: border-box;
}

.login .info .box span:nth-child(1) {
  transform: rotate(0deg);
}

.login .info .box span:nth-child(2) {
  transform: rotate(90deg);
}

.login .info .box span:nth-child(3) {
  transform: rotate(180deg);
}

.login .info .box span:nth-child(4) {
  transform: rotate(270deg);
}

.login .info .box span:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #fff;
}

@keyframes animate {
  0% {
    transform: scaleX(0);
    transform-origin: left;
  }

  50% {
    transform: scaleX(1);
    transform-origin: left;
  }

  50.1% {
    transform: scaleX(1);
    transform-origin: right;
  }

  100% {
    transform: scaleX(0);
    transform-origin: right;
  }
}

.login.login-background {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.login.login-background .cube {
  position: absolute;
  top: 80vh;
  right: 200px;
  width: 10px;
  height: 10px;
  border: solid 1px red;
  transform-origin: top left;
  transform: scale(0) rotate(0deg) translate(-50%, -50%);
}

.login.login-background .cube:nth-child(2n) {
  border-color: #00cad4;
}

.login.login-background .cube:nth-child(2) {
  right: 300px;
  border-color: yellow;
  top: 200px;
}

.login.login-background .cube:nth-child(3) {
  right: 50%;
  top: 150px;
  border-color: blue;
}

.login.login-background .cube:nth-child(4) {
  left: 10%;
  top: 150px;
}

.login.login-background .cube:nth-child(5) {
  right: 50%;
  bottom: 150px;
  border-color: black;
}

.login.login-background .cube:nth-child(6) {
  right: 10%;
  top: 50%;
  border-color: aquamarine;
}

/* Animate Background*/
@keyframes Gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes cube {
  from {
    transform: scale(0) rotate(0deg) translate(-50%, -50%);
    opacity: 1;
  }

  to {
    transform: scale(20) rotate(960deg) translate(-50%, -50%);
    opacity: 0;
  }
}




#login-form {
  .login-input {
    width: 22rem;
    margin-bottom: 0.6rem;
  }

  .login-button {
    width: 14rem;
    margin-top: 1rem;
    background-color: steelblue;
    color: black;
  }

  mat-spinner {
    float: right;
    position: relative;
    top: 15px;
    left: 20px;
  }

  .login-form-icon {
    position: relative;
    top: -5px;
  }

  .btn-lg {
    font-size: 17px;
    padding: 0 50px;
    line-height: 50px;
    margin: 0 120px;
    color: #fff;
    border-radius: 30px;
  }

  .btn-theme {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    border: none;
    outline: none !important;
    color: #ffffff;
    text-transform: capitalize;
    transition: all 0.3s linear;
    z-index: 1;
    overflow: hidden;
    cursor: pointer;
    font-weight: 400;
    border-radius: 50px;
  }

  .btn-theme:hover {
    color: #fff;
  }

  .btn-theme:hover:after {
    transform: perspective(200px) scaleX(1.05) rotateX(0deg) translateZ(0);
    transition: transform 0.4s linear, transform 0.4s linear;
  }

  .btn-theme:after {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    border-radius: 30px;
    transform: perspective(200px) scaleX(0.1) rotateX(90deg) translateZ(0px);
    transform-origin: bottom center;
    transition: transform 0.4s linear, transform 0.4s linear;
    z-index: -1;
  }

}

.login-form{
  width: 100% !important;
}

.border-bottom{
  margin-bottom: 5%;
}
.loginFormDiv{
  width: 78% !important;
  border: none;
  margin-top: 0% !important;
  padding: 0% !important;
}
.icons{
  margin-right: 0.3em !important;
}
.loginButton{
  width: 100% !important;
  margin-bottom: 24px !important;
}

