<div class="loginPage">
  <mat-card appearance="outlined" class="loginCard">
    <div fxLayout="row wrap" fxLayoutGap="4px">

      <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%" class="welcomeContainerCss">
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <h2 class="welcomeCss">Welcome !</h2>
          </div>
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <hr class="hrTagCss">
          </div>
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <h3 class="signInCss">Change password</h3>
          </div>
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class=" loginPageSideImage">

          </div>

        </div>

      </div>

      <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="logoCss">
            <img src="../../assets/centelon_login_page_logo.png" width="200px" height="75px" alt="" srcset="">
          </div>
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="passwordClass" >
            <form autocomplete="off">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="changePasswordContainer">
                  <mat-form-field appearance="fill" class="width-100">
                    <mat-icon class="pointer" matSuffix (click)="hide = !hide">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                    <input matInput  [type]="hide ? 'password' : 'text'" placeholder="New password" [formControl]="newPassword"
                      name="newpassword" autocomplete="new-password ">
                    
                  </mat-form-field>
                  <mat-error class="change-password-error font-12"  *ngIf="newPassword.errors && newPassword.errors.pattern">

                    Password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter and 1 number

                  </mat-error>

                </div>
                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="changePasswordContainer">
                  <mat-form-field appearance="fill" class="width-100">
                    <mat-icon class="pointer"  matSuffix (click)="hide2 = !hide2">{{hide2 ? 'visibility_off' : 'visibility'}}</mat-icon>
                    <input matInput [type]="hide2 ? 'password' : 'text'" (keyup)="checkMatchPwd()" placeholder="Re-enter new password"
                      [formControl]="reEnterNewPassword" name="reenternewpassword" autocomplete="new-password">
                     
                  </mat-form-field>
                  <mat-error class="change-password-error font-12"  *ngIf="reEnterNewPassword.touched && matchError">
                    Password does not match.
                  </mat-error>
                 
                </div>

              </div>
            </form>
          </div>

          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="changeBtnContainer">
            <button  mat-stroked-button class="blue width-87" (click)="change()">
              CHANGE
            </button>
          </div>

        </div>
      </div>
    </div>
  </mat-card>
</div>