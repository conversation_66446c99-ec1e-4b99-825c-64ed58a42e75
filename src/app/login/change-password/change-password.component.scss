.header-card {
  width: 100%;
  height: 100vh;
}

.forgot-password-container {
  margin-top: 5%;
}

.gridGapZero {
  grid-gap: 0 !important;
}

.loginPage {
  height: inherit;
  width: 100%;
  background-color: #f1dfdff7;
}

.loginCard {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
  width: 55%;
  border-radius: 12px;
  overflow: hidden;
  padding: 0px;
  box-shadow: rgb(17 17 17 / 34%) 0px 0px 90px 10px;
}

.welcomeContainerCss {
  background: linear-gradient(30deg, black, grey);
  display: flex;
  flex-flow: column;
  min-height: 415px;
}

.welcomeCss {
  color: white;
  font-weight: 400;
  padding: 10% 10% 5%;
  font-size: 30px;
  margin-bottom: 0px;
}

.hrTagCss {
  background-color: white;
  border: 0px none;
  color: white;
  height: 20%;
  margin-left: 11%;
  width: 15%;
}

.signInCss {
  color: white;
  font-weight: 400;
  padding: 7% 10% 5%;
  font-size: 25px;
  margin-bottom: 0px;
}

.loginPageSideImage {
  background-image: url(../../../assets/orange-graph-lines.svg);
  background-size: 100%;
  min-height: 41vh;
  min-width: 100%;
  flex: 1 1 auto;
  background-repeat: no-repeat;
  background-position: center bottom;
}

.logoCss {
  display: flex;
  justify-content: center;
  margin: 4% 0;
}

.width100 {
  width: 100%;
}

.fontWeight500 {
  font-weight: 500;
}

.changeBtnContainer {
  display: flex;
  justify-content: center;
  margin-top:2%
}

.forgotPasswordLinkContainer {
  display: flex;
  justify-content: center;
  margin: 10% 0 2%;
  font-size: 14px;
  font-weight: 400;
}

.loginErrorContainer {
  display: flex;
  justify-content: center;
}

.changePasswordContainer {
  margin-top: 5% !important;
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0.34375em !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-underline {
    bottom: 0.34375em !important;
  }

  input.mat-mdc-input-element {
    margin-top: -0.0625em !important;
    margin-bottom: 2% !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-infix {
    border-top: none !important;
  }
}
.passwordClass{
  margin: 5% 7% 0 !important;
}
.width-87{
  width: 87% !important;
}
