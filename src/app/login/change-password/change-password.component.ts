import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { ToasterService } from '../../common/toaster.service';
import { IdentityService } from 'src/app/shared-service/identity.service';
import { UntypedFormControl, Validators } from '@angular/forms';
import JsonData from 'src/assets/data.json';
@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  error: boolean;
  matchError: string = "";
  JsonData:any;
  @Output() valueChange = new EventEmitter();
  hide = true;
  hide2 = true
  constructor(private identityService: IdentityService, private notification: ToasterService) { }
  newPassword = new UntypedFormControl('', [Validators.required, Validators.pattern('((?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,30})')
  ]);
  reEnterNewPassword = new UntypedFormControl('', [Validators.required]);
  // hide = false;
  ngOnInit() {
    this.error = true;
  }

  checkMatchPwd() {

    if (this.newPassword.value !== this.reEnterNewPassword.value) {
      this.error = true;
      this.matchError = "Password does not match"
    } else {
      this.matchError = ""
      this.error = false;
    }

  }


  checkPwd(str) {
    if (str.length < 8) {
      this.error = true;
      return ("Password should contain min 8 characters required");
    } else if (str.length > 50) {
      this.error = true;
      return ("Password should contain max 8 characters required");
    } else if (str.search(/\d/) == -1) {
      this.error = true;
      return ("Password should contain atleast 1 number");
    } else if (str.search(/[a-zA-Z]/) == -1) {
      this.error = true;
      return ("Password should contain atleast 1 alphabet");
    } else if (str.search(/[^a-zA-Z0-9\!\@\#\$\%\^\&\*\(\)\_\+]/) != -1) {
      this.error = true;
      return ("Password should contain atleast one speacial character");
    } else {
      if (this.newPassword.value == this.reEnterNewPassword.value) {
        this.error = false;
      }

    }

  }


  change() {
    this.newPassword.markAsTouched();
    this.reEnterNewPassword.markAsTouched()
    if(this.newPassword.invalid && this.reEnterNewPassword.invalid){
       return
    }
  
    let userId = localStorage.getItem('user');

    if (this.newPassword.value == this.reEnterNewPassword.value) {

      let password = window.btoa(this.newPassword.value);
      let payload = { password: password }

      this.identityService.forgetPassword(userId, payload).subscribe((res) => {
 
        this.notification.success(JsonData["label.success.Password"]);
        this.valueChange.emit(true);

      }, (err) => {



      })


    } else {




    }




  }

}
