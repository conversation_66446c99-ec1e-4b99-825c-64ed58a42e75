import { AuthService } from "./../guard/auth.service";
import { Router } from "@angular/router";
import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import {
  UntypedFormGroup,
  UntypedFormControl,
  Validators,
} from "@angular/forms";
import { IdentityService } from "../shared-service/identity.service";
import { DataSharingService } from "../common/dataSharing.service";
// import { SecureLinkApiService } from '../secure-link-request/service/secure-link-api.service';
import { MatDialog } from "@angular/material/dialog";
import { ToasterService } from "../common/toaster.service";
import { ThemeService } from "../theme.service";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { PageLayoutService } from "../shared-service/page-layout.service";
import { UserIdleService } from "angular-user-idle";
import { TokenHelperService } from "../shared-service/token-helper.service";
import { AccessControlService } from "../settings/roles-actions-configuration/access-control.service";
@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent implements OnInit {
  loginView: boolean;
  loginForm: UntypedFormGroup;
  loginError = false;
  isDarkMode: any;
  hide = true;
  @ViewChild("username") username: ElementRef;
  @ViewChild("password") password: ElementRef;
  showLoginPage = true;
  usersList: any;
  constructor(
    private themeService: ThemeService,
    private route: Router,
    private toaster: ToasterService,
    private dialogRef: MatDialog,
    private identityService: IdentityService,
    private bottomSheet: MatBottomSheet,
    private dataSharingService: DataSharingService,
    private authService: AuthService,
    private userIdleService: UserIdleService,
    public pageLayoutService: PageLayoutService,
    public tokenHelper: TokenHelperService,
    private accessControlService: AccessControlService
  ) {
    const sessionTimeout =
      route.getCurrentNavigation()?.extras?.state?.sessionTimeout;
    this.clearServiceData();
    localStorage.removeItem("accessToken");
    localStorage.removeItem("accessTokenExpiration");
    localStorage.removeItem("user");
    localStorage.removeItem("tenantIdentifier");
    localStorage.removeItem("fileMaxSizeCache");
    this.dialogRef.closeAll();
    this.bottomSheet.dismiss();
    !sessionTimeout ? this.toaster.clearToasts() : "";
    this.generateLoginForm();
  }

  clearServiceData() {
    this.dataSharingService.pageIndex = 0;

    this.dataSharingService.pageSize = 25;
    this.dataSharingService.selectedSortKeyForDashboard = "createdDate";
    this.dataSharingService.selectedDealListTitle = "To score - Hold";
    this.dataSharingService.selectedDateRangeForDeal = null;
    this.dataSharingService.selectedSortDirection = "desc";
    this.dataSharingService.endDate = "all";
    this.dataSharingService.startDate = "all";
    this.dataSharingService.pageIndexFordeals = 0;
    this.dataSharingService.pageSizeFordeals = 25;
    this.dataSharingService.sortDirectionFordeals = "desc";
    this.dataSharingService.sortAsPerKeyNameFordeals = "createdDate";
    this.dataSharingService.selectedBusinessProcessforDeals = "";
    this.dataSharingService.selectedBusinessProcessName = null;
    this.dataSharingService.searchKeydeals = "";

    this.dataSharingService.selectedFilter = "";

    this.dataSharingService.pageIndexforpersondetails = 0;
    this.dataSharingService.pageSizeForpersondetails = 25;
    this.dataSharingService.sortDirectionForpersondetails = "desc";
    this.dataSharingService.sortAsPerKeyNameforpersondetails = "createdDate";

    this.dataSharingService.pageSizeforentitydetails = 25;
    this.dataSharingService.pageIndexforentitydetails = 0;
    this.dataSharingService.sortDirectionforentitydetails = "desc";
    this.dataSharingService.sortAsPerKeyNameforentitydetails = "createdDate";

    this.dataSharingService.pageSizeforentity = 25;
    this.dataSharingService.pageIndexforentity = 0;
    this.dataSharingService.sortDirectionforentity = "desc";
    this.dataSharingService.sortAsPerKeyNameforentity = "createdDate";

    this.dataSharingService.pageSizeforPerson = 25;
    this.dataSharingService.pageIndexforPerson = 0;
    this.dataSharingService.sortDirectionforPerson = "desc";
    this.dataSharingService.sortAsPerKeyNameforPerson = "createdDate";

    this.dataSharingService.pageSizeforupgraderecords = 25;
    this.dataSharingService.pageIndexforupgraderecords = 0;
    this.dataSharingService.sortDirectionforupgraderecords = "desc";
    this.dataSharingService.sortAsPerKeyNameforupgraderecords = "createdDate";

    this.dataSharingService.pageSizeforviewupgraderecords = 25;
    this.dataSharingService.pageIndexforviewupgraderecords = 0;
    this.dataSharingService.sortDirectionforviewupgraderecords = "desc";
    this.dataSharingService.sortAsPerKeyNameforviewupgraderecords =
      "createdDate";
  }

  ngOnInit() {
    this.loginView = true;

    // let theme = localStorage.getItem('user-theme') ? localStorage.getItem('user-theme') : 'light-mode'

    // if(theme == 'dark-mode'){
    //   this.isDarkMode
    //   let data = {
    //     value: 'dark'
    //   }

    //   this.toggleDarkMode(data)

    // } else if(theme == 'light-mode'){
    //   !this.isDarkMode

    //   let data = {
    //     value: 'light'
    //   }

    //   this.toggleDarkMode(data)

    // } else{
    //   this.themeService.initTheme();
    //   this.isDarkMode = this.themeService.isDarkMode();
    // }
  }

  submit() {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("accessTokenExpiration");
    localStorage.removeItem("user");
    localStorage.removeItem("tenantIdentifier");
    localStorage.removeItem("fileMaxSizeCache");

    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }
    this.loginForm.value.password = window.btoa(this.loginForm.value.password);
    this.dataSharingService.currentUserName = this.loginForm.value.username;
    localStorage.setItem("user", this.loginForm.value.username);
    localStorage.setItem(
      "tenantIdentifier",
      this.loginForm.value.tenantIdentifier
    );

    localStorage.setItem("currency", "INR");
    this.identityService.login(this.loginForm.value).subscribe(
      (res: any) => {
        this.loginError = false;
        const passwordExpiry = new Date(res.passwordExpiration);

        localStorage.setItem("user", this.loginForm.value.username);
        this.dataSharingService.userPassword = this.loginForm.value.password;
        if (!localStorage.getItem("user-theme")) {
          localStorage.setItem("user-theme", "light-mode");
        }
        if (
          this.username.nativeElement.value ==
            this.password.nativeElement.value &&
          passwordExpiry <= new Date()
        ) {
          console.log("Password expired");
          this.showLoginPage = false;

          this.loginForm.reset();
          this.toaster.error("First time Login - Please change the password");
        } else if (passwordExpiry <= new Date()) {
          this.showLoginPage = false;
          this.toaster.error(
            "Password has expired! Please change the password to continue"
          );
        } else {
          this.loginError = false;

          this.identityService.setCurrentUserValue(res);
          this.getLoggedInUserDetails();
        }
      },
      (err) => {
        this.loginError = true;
      }
    );
  }

  getLoggedInUserDetails() {
    this.identityService.getAllUser().subscribe((res) => {
      this.usersList = res;
      localStorage.setItem("userList", this.usersList);
      const currentUser = this.usersList.find(
        (item) => item.identifier == this.loginForm.value.username
      );
      const userRole = currentUser.role
        ? currentUser.role
        : currentUser.roleMappings;
      localStorage.setItem("userRole", userRole);
      this.getAllConfigurationDetails();
    });
  }

  toggleDarkMode(event) {
    if (event.value == "dark") {
      this.isDarkMode;
      this.themeService.update("dark-mode");
    }
    if (event.value == "light") {
      !this.isDarkMode;
      this.themeService.update("light-mode");
    }
  }

  generateLoginForm() {
    this.loginForm = new UntypedFormGroup({
      tenantIdentifier: new UntypedFormControl("", [Validators.required]),
      username: new UntypedFormControl("", [Validators.required]),
      password: new UntypedFormControl("", [Validators.required]),
    });
  }

  forgotPassword() {
    this.route.navigate(["/forgot-password"]);
  }

  onChangesReceived(data: any) {
    if (data) {
      this.showLoginPage = true;
    }
  }

  getAllConfigurationDetails() {
    this.pageLayoutService
      .getAllConfigurationDetails()
      .subscribe((res: any) => {
        if (res) {
          const sidebarItems = res.find(
            (config) => config.configIdentifier == "SIDE_BAR"
          );
          this.dataSharingService.setSidebarItems(sidebarItems);
          localStorage.setItem("sidebarItems", JSON.stringify(sidebarItems));
          const sectorList = res.find(
            (config) => config.configIdentifier == "SECTOR_SUBSECTOR_LIST"
          );
          this.dataSharingService.configurablePickListData = sectorList;
          localStorage.setItem("sectorList", JSON.stringify(sectorList));

          const currencyList = res.find(
            (config) => config.configIdentifier == "CURRENCY_LIST"
          );
          this.dataSharingService.setcurrencyItems(currencyList);
          localStorage.setItem("currencyList", JSON.stringify(currencyList));

          const Documentlist = res.find(
            (config) => config.configIdentifier == "DEFAULT_DOCUMENT_TYPE"
          );
          this.dataSharingService.setdoclist(Documentlist);
          localStorage.setItem("Documentlist", JSON.stringify(Documentlist));

          const address = res.find(
            (config) => config.configIdentifier == "ADDRESS"
          );
          const ausurl = address?.configDetails?.find(
            (ausAdd) => ausAdd.country == "Australia"
          );
          localStorage.setItem("ausUrl", JSON.stringify(ausurl?.url));

          this.setIdleTimeout(res);
        }
        if (this.authService.redirectUrl) {
          this.route.navigate([this.authService.redirectUrl]);
          this.authService.redirectUrl = null;
        } else {
          this.route.navigate([`/dashboards/${btoa("0")}`]);
        }
        this.accessControlService.updatePermissions.next("");
      });
  }

  setIdleTimeout(res) {
    const data = res?.find((ele) => ele.configIdentifier == "PROJECT_CONFIG");

    const projectTitle = data?.configDetails.find(
      (config) => config.configName == "BUSINESS_TITLE"
    );
    localStorage.setItem("projectTitle", projectTitle.displayName);
    this.dataSharingService.setProjectTitle(projectTitle.displayName);

    const idleTimeConfig = data?.configDetails.find(
      (config) => config.configName == "IDLE_TIMEOUT"
    );
    const idleTime = idleTimeConfig;
    const timeout = this.userIdleService.getConfigValue().timeout;
    const totalTime =
      idleTime["hours"] * 60 * 60 +
      idleTime["minutes"] * 60 +
      idleTime["seconds"] -
      timeout;
    localStorage.setItem("idleTime", totalTime.toString());
    this.userIdleService.stopWatching();
    this.userIdleService.setConfigValues({ idle: totalTime });
    this.tokenHelper.newEvent("login");
  }
}
