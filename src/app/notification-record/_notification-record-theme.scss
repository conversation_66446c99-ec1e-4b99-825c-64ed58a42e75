@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);
  $primary-palette: map.get($color-config, 'primary');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, 400);


  .notification-panel {
    .date-display-field {
      border: 1px solid $primary-color;
    }

    .notification-container {

      .group-header {
        background-color: mat.get-color-from-palette($primary-palette, 100);
        color: mat.get-contrast-color-from-palette($primary-palette, 100);
      }

      .notify-icon {
        color: $primary-color;
        background-color: mat.get-color-from-palette($primary-palette, 50);
      }

      .notif-card {
        background-color: var(--container-color);
      }

      .unread-notification {
        color: $primary-color;
      }

      .no-notifications-found {
        border-color: mat.get-color-from-palette($primary-palette, 400) !important;
        background-color: var(--no-data-here-text-color)
      }

      .no-data-title,
      .no-data-subtitle {
        color: mat.get-contrast-color-from-palette($primary-palette, 50);
      }
    }


  }

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .notification-panel {
    .notification-title {
      @include mat.typography-level($typography-config, 'headline-6');
    }

    .notification-container {

      .notify-title {
        @include mat.typography-level($typography-config, 'subtitle-1');
        font-weight: 600;
      }

      .notify-desc {
        @include mat.typography-level($typography-config, 'subtitle-2');
      }

      .no-data-title {
        @include mat.typography-level($typography-config, 'headline-6');
      }

      .no-data-subtitle {
        @include mat.typography-level($typography-config, 'subtitle-2');
      }
    }

    .date-display-field {
      span {
        @include mat.typography-level($typography-config, 'subtitle-1');
      }
    }
  }
}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
