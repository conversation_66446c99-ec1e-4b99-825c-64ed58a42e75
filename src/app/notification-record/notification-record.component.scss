::ng-deep .notification-panel {
  border-radius: 10px !important;
  overflow: hidden !important;

  .notification-header {
    position: relative;
    height: 50px;
    padding: 0px 10px;
  }

  .hidden-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    width: 0;
    height: 0;
  }

  .date-display-field {
    border-radius: 5px;
    padding: 6px;
  }

  .notification-container {
    padding: 10px;
    max-height: 90vh;
    overflow-y: auto;

    .spinner-container {
      height: 85vh;
    }

    .notif-card {
      padding: 12px;
      margin-bottom: 2px;
    }

    .notify-icon {
      padding: 10px;
      border-radius: 10px;
    }
  }

  .no-notifications-found {
    border: 2px solid;
    border-radius: 10px;
    height: 85vh;
    padding: 6%;
  }

}
