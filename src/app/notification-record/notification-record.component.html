<div class="notification-panel">
  <div fxLayout="row" fxLayoutAlign="space-between center" class="notification-header">
    <span fxLayoutAlign="start center" class="notification-title">Notifications</span>
    <div fxLayoutGap="10px" fxLayoutAlign="end center">
      <!-- <div *ngIf="notifications.length > 0">
        <ng-container *ngIf="!showDateInput">
          <input matInput [matDatepicker]="picker" #dateInput class="hidden-input"
            (dateChange)="onDateChange($event)" readonly />
          <button class="outlined-icon-button" mat-icon-button (click)="picker.open()"
            matTooltip="Date Filter" matTooltipClass="accent-tooltip">
            <span class="material-symbols-outlined">event</span>
          </button>
          <mat-datepicker #picker></mat-datepicker>
        </ng-container>

        <ng-container *ngIf="showDateInput">
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10"
            class="date-display-field">
            <span>{{selectedDate | date:'dd/MM/yyyy'}}</span>
            <mat-icon class="pointer" (click)="clearDate()">close</mat-icon>
          </div>
        </ng-container>
      </div> -->
      <div tabindex="0" matRipple class="subtitle-1 link pointer" (keydown.enter)="markAllasRead()"
        (click)="markAllasRead()" *ngIf="this.notificationService.unreadNotificationsCount > 0">
        {{"label.mark.all.as.read" | literal}}
      </div>
      <button class="outlined-icon-button" mat-icon-button matTooltip="Close"
        matTooltipClass="accent-tooltip" (click)="toggleNotificationDrawer()">
        <span class="material-symbols-outlined">
          close
        </span>
      </button>
    </div>
  </div>

  <div class="notification-container">

    <div fxLayout="column" fxLayoutGap="10" class="no-notifications-found"
      fxLayoutAlign="center center" *ngIf="notifications.length === 0 && !spinner">
      <div class="no-data-found-img">
      </div>
      <div class="no-data-title">
        <span>{{"label.no.notification.yet" | literal}}</span>
      </div>
      <div class="no-data-subtitle">
        <span>{{"lable.notifications.inform" | literal}}</span>
      </div>
    </div>

    <div *ngIf="spinner" class="spinner-container" fxLayout="column" fxLayoutAlign="center center">
      <mat-spinner></mat-spinner>
    </div>


    <div *ngIf="notifications.length > 0 && !spinner" (click)="$event.stopPropagation();">
      <ng-container *ngFor="let group of groupedNotifications | keyvalue : sortNotificationGroups">
        <p class="group-header p-10">{{ group.key }}</p>

        <div *ngFor="let notification of group.value" class="notif-card">
          <div fxLayout="row" fxFlex="100%" fxLayoutGap="20">
            <div fxLayout="column" fxLayoutAlign="center start" class="notify-icon">
              <span class="material-symbols-outlined">
                {{ getNotificationMeta(notification.moduleName,
                notification.notificationType).icon }}
              </span>
            </div>

            <div fxLayout="column" fxLayoutAlign="center start" class="pointer">
              <div class="notify-title"
                [ngClass]="{ 'unread-notification': !notification.readFlag }"
                (click)="navigateToDetails(notification)">
                {{ getNotificationMeta(notification.moduleName,
                notification.notificationType).title }}
              </div>
              <div class="notify-desc" (click)="navigateToDetails(notification)">
                {{ notification.notificationMessage }}
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <span class="subtitle-1 link pointer" mat-raised-button color="primary" (click)="loadMore()"
        *ngIf="notifications.length < totalElements">
        {{"label.see.more" | literal}}
      </span>
    </div>
  </div>
</div>
