import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator } from "@angular/material/paginator";
import { Router } from "@angular/router";
import { NotificationService } from "src/app/common/notification.service";
import { ToasterService } from "../common/toaster.service";
import { ErrorService } from "../shared-service/error.service";
import { DataSharingService } from "../common/dataSharing.service";
import { formatDate } from "@angular/common";

@Component({
  selector: "app-notification-record",
  templateUrl: "./notification-record.component.html",
  styleUrls: ["./notification-record.component.scss"],
})
export class NotificationRecordComponent implements OnInit {
  notifications: any = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  dataSource: any = [];
  spinner = false;
  pageSize = this.notificationService.pageSize;
  pageIndex = this.notificationService.pageIndex;
  totalElements: number = 0;
  unreadCount: number = 0;
  groupedNotifications = {};
  // showDateInput = false;
  // selectedDate: Date | null = null;

  constructor(
    private route: Router,
    public notificationService: NotificationService,
    private errorService: ErrorService,
    private notificationMessage: ToasterService,
    private datasharingService: DataSharingService
  ) {
    this.getNotifications();
  }

  ngOnInit(): void {}

  navigateToDetails(notif) {
    if (!notif.readFlag) {
      notif.readFlag = true;
      this.notificationService.notificationRead([notif]).subscribe(
        (resp) => {
          this.route.navigate([this.getRoute(notif)]);
          this.getNotifications();
        },
        (error) => {
          this.getNotifications();
          let errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
        }
      );
    } else {
      this.route.navigate([this.getRoute(notif)]);
    }
  }

  getRoute(notification) {
    this.datasharingService.selectedApplicationData = null;
    switch (notification.moduleName) {
      case "Document":
        return (
          "/application-summary/documents/" + btoa(notification?.moduleEntityId)
        );
      case "STAGE_SHARE":
      case "Comment":
      case "Deal":
      case "Deal Notifications":
        return (
          "/application-summary/details/" + btoa(notification?.moduleEntityId)
        );

      case "Score":
        return (
          "/application-summary/dealAnalysis/" +
          btoa(notification?.moduleEntityId)
        );
      case "Communication":
        return "planner/tasks";
      case "Business Process":
        return "/stage/" + btoa(notification?.moduleEntityId);
    }
  }

  loadMore() {
    this.pageSize += 20;
    this.notificationService.pageSize = this.pageSize;
    this.getNotifications();
  }

  getNotifications() {
    const readFlag = "all"; //to get both read/unread notifications
    this.spinner = true;

    this.notificationService
      .getNotifications(readFlag, this.pageSize)
      .subscribe(
        (resp: any) => {
          this.spinner = false;

          if (resp?.content) {
            this.notifications = resp.content;
            this.groupNotificationsByDate();
          }
          this.totalElements = resp?.totalElements;
          this.unreadCount = this.notifications.filter(
            (notif) => !notif.readFlag
          ).length;
          this.notificationService.unreadNotificationsCount = this.unreadCount;
        },
        (error) => {
          const errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
        }
      );
  }
  // onDateChange(event: any) {
  //   this.selectedDate = event.value;
  //   const formattedDate = event.value.format("YYYY-MM-DD");
  //   console.log(formattedDate);
  //   this.showDateInput = true;
  //   this.getNotifications();
  // }

  // clearDate() {
  //   this.selectedDate = null;
  //   this.showDateInput = false;
  // }

  groupNotificationsByDate() {
    const today = new Date();
    const todayStr = formatDate(today, "yyyy-MM-dd", "en-US");
    const yesterdayStr = formatDate(
      new Date(today.setDate(today.getDate() - 1)),
      "yyyy-MM-dd",
      "en-US"
    );

    this.groupedNotifications = {};

    this.notifications.forEach((notif: any) => {
      const notifDateStr = formatDate(notif.createdDate, "yyyy-MM-dd", "en-US");

      let groupLabel = "";
      if (notifDateStr === todayStr) {
        groupLabel = "Today";
      } else if (notifDateStr === yesterdayStr) {
        groupLabel = "Yesterday";
      } else {
        groupLabel = formatDate(notif.createdDate, "MMMM d, yyyy", "en-US"); // e.g., April 29, 2025
      }

      if (!this.groupedNotifications[groupLabel]) {
        this.groupedNotifications[groupLabel] = [];
      }

      this.groupedNotifications[groupLabel].push(notif);
    });
  }

  sortNotificationGroups = (a: any, b: any): number => {
    const order = ["Today", "Yesterday"];

    const aIndex = order.indexOf(a.key);
    const bIndex = order.indexOf(b.key);

    if (aIndex !== -1 || bIndex !== -1) {
      return (aIndex === -1 ? 2 : aIndex) - (bIndex === -1 ? 2 : bIndex);
    }
    return new Date(b.key).getTime() - new Date(a.key).getTime();
  };

  getNotificationMeta(
    moduleName: string,
    notificationType?: string
  ): { icon: string; title: string } {
    const map = {
      Deal: {
        default: { icon: "message", title: "Deal Notification" },
        DEAL_CREATION: { icon: "add", title: "Create Deal" },
        STAGE_MOVEMENT: { icon: "move_up", title: "Stage Movement" },
        REJECT_DEAL: { icon: "close", title: "Reject Deal" },
        REOPEN_DEAL: { icon: "folder_open", title: "Reopen Deal" },
        APPROVE_DEAL: { icon: "task_alt", title: "Approve Deal" },
      },
      "Business Process": {
        default: { icon: "file_copy", title: "Business Process" },
        CLONE_BUSINESS_PROCESS: {
          icon: "file_copy",
          title: "Business Process",
        },
        CREATE_COPY_CONFIGURATION: {
          icon: "file_copy",
          title: "Copy COnfiguration",
        },
        UPDATE_COPY_CONFIGURATION: {
          icon: "file_copy",
          title: "Copy COnfiguration",
        },
        UPDATE_RULE_COPY_CONFIGURATION: {
          icon: "file_copy",
          title: "Copy COnfiguration",
        },
      },
      Score: {
        default: { icon: "speed", title: "Score Update" },
        ADD_SCORE: { icon: "scoreboard", title: "Add Score" },
        SEND_TO_TEAM: { icon: "send", title: "Send To Team" },
      },
      Dashboard: {
        default: { icon: "add_chart", title: "Dashboard" },
        CLONE_DASHBOARD: { icon: "file_copy", title: "Clone Dashboard" },
      },
      STAGE_SHARE: {
        default: { icon: "ios_share", title: "Stage Share Completion" },
        STAGE_SHARE: { icon: "ios_share", title: "Stage Share Completion" },
      },
      "Bulk Movement": {
        default: { icon: "move_group", title: "Bulk Movement" },
        BULK_MOVEMENT: { icon: "move_group", title: "Bulk Movement" },
      },
      Document: {
        default: { icon: "share", title: "Document Shared" },
      },
      Communication: {
        default: { icon: "assignment_turned_in", title: "Communication" },
      },
      Approval: {
        default: { icon: "check_circle", title: "Approval Update" },
      },
      Comment: {
        default: { icon: "alternate_email", title: "New Comment" },
      },
      "Deal Notifications": {
        default: { icon: "message", title: "Deal Notification" },
      },
    };

    if (map[moduleName]) {
      if (notificationType && map[moduleName][notificationType]) {
        return map[moduleName][notificationType];
      }
      return map[moduleName]["default"];
    }

    return { icon: "notifications", title: "Notification" };
  }

  markAllasRead() {
    this.notifications.forEach((notif) => {
      notif.readFlag = true;
    });
    this.notificationService.notificationAllRead(this.notifications).subscribe(
      (resp) => {
        this.getNotifications();
      },
      (error) => {
        this.getNotifications();
        let errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
      }
    );
  }

  stringWithEllipsis(data) {
    const doc = new DOMParser().parseFromString(data, "text/html");
    return this.datasharingService.stringWithEllipsis(
      doc.body.textContent || "",
      60
    );
  }

  toggleNotificationDrawer() {
    this.datasharingService.toggleNotificationDrawer(null);
  }
}
