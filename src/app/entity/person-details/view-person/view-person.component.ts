import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { personDetailsTabs } from 'src/app/application-summary/static-data';
import { EntityService } from '../../../shared-service/entity.service';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { EditEntityDialogComponent } from 'src/app/dialogs/edit-entity-dialog/edit-entity-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { EntityResource } from 'src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface';
import { ThemeService } from 'src/app/theme.service';

@Component({
  selector: 'app-view-person',
  templateUrl: './view-person.component.html',
  styleUrls: ['./view-person.component.scss']
})
export class ViewPersonComponent {
  companyDetailsTabs = personDetailsTabs;
  customerId;
  edit;
  person;
  companyId;
  disableActionButton = false;
  activeTabIndex = 0;

  get ENTITY_RESOURCE(){
    return EntityResource;
  }
  
  constructor(private router: Router, 
    private entityService: EntityService, 
    private dialog: MatDialog,
    public notificationMessage: ToasterService, 
    private dataSharingService : DataSharingService,
    public themeService: ThemeService) {
    window.scrollTo(0, 0);
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.customerId = state ? state['customerId'] : '';
    this.edit = state ? state['edit'] : '';
    this.companyId = state ? state['companyId'] : '';
    this.entityService.getCustomerEntityDetails().subscribe(event=>{
      if(event){
        this.person = this.entityService.customerDetails;
      }
    });
    this.dataSharingService.disableActionButton.subscribe(event=>  this.disableActionButton = event)
    this.entityService.customerIdEmitter.subscribe(customerId =>{
      this.customerId = customerId;
    })
  }


  getLink(link:string):string|void{
    if(this.customerId){
     return link+btoa(this.customerId);
    }
   }

   getSidebarItembyName(itemName){
    if(this.dataSharingService.getSidebarItembyName(itemName)){
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0] ;
      return item?.displayName ? item?.displayName : itemName;
    }
    else return itemName;
  }
 
  editing(link,index){
    if(!this.disableActionButton)  {
      this.activeTabIndex = index;
      this.router.navigate(['../../'+this.getLink(link)])
    }
    else this.notificationMessage.error("Please save or cancel the modified changes.",5000);
    
  }

  editEntityName(){
    
    const matDialogRef = this.dialog.open(EditEntityDialogComponent, {
      autoFocus: false,
      width: '40%',
      disableClose: true ,
      data:{ 
        entityName : this.entityService.customerDetails?.name,
        customerId : this.entityService.customerDetails?.customerId
       }
    });

    matDialogRef.afterClosed().subscribe(result => {
      if(result){
        this.person.name = result.newEntityName;
      }
    });

  }

}
