import {
  After<PERSON>ontentChecked,
  ChangeDete<PERSON><PERSON><PERSON>,
  Component,
  OnInit,
} from "@angular/core";
import { EntityService } from "src/app/shared-service/entity.service";
import { FormGroup, FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { ToasterService } from "src/app/common/toaster.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { takeUntil } from "rxjs/operators";
import { Observable, Subject } from "rxjs";
import { DataSharingService } from "../../../../common/dataSharing.service";
import { DealService } from "../../../../shared-service/deal.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import {
  CurrencyFormatService,
  DEFAULT_DECIMAL,
} from "src/app/common/currency/currency-format.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { DashboardHeaderComponent } from "src/app/dashboard/dashboard-header/dashboard-header.component";

import { EntityResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import {
  FormSources,
  SourceInfo,
} from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";
@Component({
  selector: "app-person-detail",
  templateUrl: "./person-detail.component.html",
  styleUrls: ["./person-detail.component.scss"],
})
export class PersonDetailComponent implements OnInit, AfterContentChecked {
  customerForm: FormGroup;
  keyInformation = [];
  customerId;
  person;
  companyId;
  persondetails;
  maxDate;
  edit;
  filteredOptions: Observable<any[]>;
  showNoRecordsAvailbleMessage;
  showLoaderSpinner = false;
  user;
  dataFromDt;
  mandatoryValueForAddress = "";
  JsonData = JsonData;
  selectedExtension;
  arryObjEntyDetails = [];
  disableActionButton = false;
  localTime;
  sourceInfo: SourceInfo;

  get ENTITY_RESOURCE() {
    return EntityResource;
  }

  constructor(
    private entityService: EntityService,
    private errorService: ErrorService,
    private errorMessageService: ValidationErrorMessageService,
    private router: Router,
    private dataSharingService: DataSharingService,
    private dealService: DealService,
    private notificationMessage: ToasterService,
    private fb: FormBuilder,
    public identityService: IdentityService,
    public activeRoute: ActivatedRoute,
    private changeDetector: ChangeDetectorRef,
    public currencyFormatService: CurrencyFormatService,
    private currencyPipe: CurrencyPipe,
    public datePipe: DatePipe,
    public dashboardHeaderComponent: DashboardHeaderComponent,
    public themeService: ThemeService,
    private dataTypesUtils: DataTypesUtilsService,
    private unsavedChangesHandler: UnsavedChangesHandlerService
  ) {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.customerId = state ? state["customerId"] : "";
    this.edit = state ? state["edit"] : "";
    this.companyId = state ? state["companyId"] : "";
  }

  ngOnInit() {
    this.dashboardHeaderComponent.getSidebarHighlight();
    this.dataSharingService.updatebuton = false;
    this.dataSharingService.getPersonData().subscribe((data) => {
      this.onUpdate(true, data);
    });
    this.user = localStorage.getItem("user");
    this.maxDate = new Date();
    this.customerForm = this.fb.group({});
    if (this.customerId) {
      if (this.entityService.customerDetails) {
        this.person = JSON.parse(
          JSON.stringify(this.entityService?.customerDetails)
        );
        this.dataSharingService.companydetails = this.person;
      } else {
        this.person = this.entityService?.persons?.find(
          (company) => company.customerId == this.customerId
        );
        this.dataSharingService.companydetails = this.person;
      }

      this.setPersonDetails();
      this.entityService.customerIdEmitter.emit(this.customerId);
    } else {
      this.activeRoute.paramMap.subscribe((params) => {
        if (params.get("id")) {
          this.customerId = atob(params.get("id"));
          this.getCustomerDetails(atob(params.get("id")));
          this.entityService.customerIdEmitter.emit(this.customerId);

          //
        }
      });
    }
  }
  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }

  private unsubscribe$ = new Subject();

  getCustomerDetails(id) {
    this.showLoaderSpinner = true;
    this.entityService.customerDetails = null;
    this.entityService
      .getCustomerDetails(id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res: any) => {
          if (res) {
            this.entityService.customerDetails = res;
            this.entityService.setCustomerDetails(res);

            this.person = JSON.parse(
              JSON.stringify(this.entityService?.customerDetails)
            );
            this.edit = true;
            this.setPersonDetails();
            this.showLoaderSpinner = false;
          }
        },
        (err) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  disableSaveButton(isDirty) {
    this.disableActionButton = !isDirty ? true : false;
    // this.dataSharingService.setDisableFlag(!this.disableActionButton);
    this.unsavedChangesHandler.setUnsavedChanges(isDirty);

    localStorage.setItem("Editing-Item", "Person");
    return this.disableActionButton;
  }

  onCancel() {
    this.person = JSON.parse(
      JSON.stringify(this.entityService?.customerDetails)
    );
    this.setPersonDetails();
  }

  setPersonDetails() {
    this.person?.customerDetails?.entityDetail?.forEach((element) => {
      //formlyModel Setting
      const key = Object.entries(element)[0][0];

      if (element[key].inputType === "Date") {
        if (
          !element[key]?.value &&
          element[key]?.displayProperty?.defaultValues == "Today"
        ) {
          element[key].value = new Date();
        }
      }
      if (element[key].inputType === "Date And Time") {
        if (element[key]?.value) {
          element[key].value = this.getLocalTime(element[key]?.value);
        } else if (
          element[key]?.displayProperty?.defaultValues ==
          "Today With Current Time"
        ) {
          const newtime = new Date();
          element[key].value = newtime;
        }
      }
      if (element[key].inputType === "Time" && element[key]?.value) {
        const is12HrFormatEnabled =
          element[this.getPropertyName(element)]?.is12HrFormatEnabled === "Y";
        element[key].value = this.getFormattedLocalTime(
          element[key]?.value,
          is12HrFormatEnabled
        );
      }
      if (
        element[this.getPropertyName(element)]?.inputType == "Currency" &&
        element[this.getPropertyName(element)].value
      ) {
        const decimalPlaces =
          element[this.getPropertyName(element)]?.displayProperty
            ?.decimalPlaces ?? DEFAULT_DECIMAL;

        if (
          element[this.getPropertyName(element)].value &&
          typeof element[this.getPropertyName(element)].value != "number" &&
          !/^\,+$/.test(
            element[this.getPropertyName(element)].value?.toString()?.charAt(0)
          )
        ) {
          element[this.getPropertyName(element)].value = element[
            this.getPropertyName(element)
          ].value?.replace(/,/g, "");
        }
        element[this.getPropertyName(element)].value =
          this.currencyPipe.transform(
            element[this.getPropertyName(element)].value,
            element[this.getPropertyName(element)]?.displayProperty
              ?.defaultValues,
            "",
            `1.${decimalPlaces}-${decimalPlaces}`
          );
      }
    });
    const newArrObj = JSON.parse(
      JSON.stringify(
        this.entityService?.customerDetails?.customerDetails?.entityDetail
      )
    );
    this.arryObjEntyDetails = newArrObj.map((u) => Object.assign({}, u));
    this.keyInformation = this.person?.customerDetails?.entityDetail;
    this.customerForm = this.createGroup(this.keyInformation);
    this.validate();
    this.persondetails = this.person?.customerDetails?.personDetails;
    this.dealService.allDealItems = this.keyInformation;

    this.sourceInfo = {
      name: this.person.name,
      entityType: this.person.entityType,
      extensionName: this.person.entityName,
      type: FormSources.Entity,
      id: this.person.customerId,
    };
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  createGroup(data) {
    const group = this.fb.group({});
    data?.forEach((control) => {
      group.addControl(
        this.getPropertyName(control),
        this.fb.control(control[this.getPropertyName(control)].value)
      );
    });
    return group;
  }

  validate() {
    this.keyInformation?.forEach((element) => {
      if (
        element &&
        element[this.getPropertyName(element)].inputType !== "formly" &&
        this.errorMessageService.getValidation(
          element[this.getPropertyName(element)]?.displayProperty?.mandatory,
          element[this.getPropertyName(element)].inputType,
          element[this.getPropertyName(element)]?.displayProperty?.validation
        )
      ) {
        this.customerForm.controls[this.getPropertyName(element)].setValidators(
          this.errorMessageService.getValidation(
            element[this.getPropertyName(element)]?.displayProperty?.mandatory,
            element[this.getPropertyName(element)].inputType,
            element[this.getPropertyName(element)]?.displayProperty?.validation
          )
        );
        this.customerForm.controls[
          this.getPropertyName(element)
        ].updateValueAndValidity();
      }
    });
  }

  getChangedFields() {
    const oldValues =
      this.entityService.customerDetails?.customerDetails?.entityDetail;
    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(oldValues, this.customerForm);

    return {
      customerDetails: {
        entityDetail: updatedVals,
      },
      entityLinkageList: updatedLinkage,
    };
  }

  receiveDataFromChild(data: string) {
    this.localTime = data;
  }

  isRequiredInvalid(): boolean {
    for (const controlName in this.customerForm.controls) {
      if (this.customerForm.controls.hasOwnProperty(controlName)) {
        const control = this.customerForm.get(controlName);
        if (
          control &&
          control.invalid &&
          !(control.errors && control.errors["required"])
        ) {
          return true;
        }
      }
    }
    return false;
  }

  onUpdate(savePromptFlag, apiDetails): Promise<boolean> {
    let saveAPI: any;
    typeof apiDetails == "object"
      ? (saveAPI = apiDetails.saveAPI)
      : (saveAPI = apiDetails);
    if (saveAPI === "save") {
      this.customerForm.markAllAsTouched();
    }
    if (saveAPI === "draft" && this.isRequiredInvalid()) {
      this.notificationMessage.error("Please fill the fields with valid data.");
      return;
    }
    if (
      (this.customerForm.invalid || this.checkAddressFormInvalidity()) &&
      saveAPI === "save"
    ) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    } else {
      return new Promise((resolve) => {
        this.entityService
          .updateCompany(this.getChangedFields(), this.customerId, saveAPI)
          .subscribe({
            next: (res: any) => {
              this.dataSharingService.setDisableFlag(false);
              if (!savePromptFlag) {
                this.getCustomerDetails(this.customerId);
              }
              if (saveAPI == "draft") {
                this.notificationMessage.success(
                  JsonData["label.success.DraftDealUpdate"]
                );
                typeof apiDetails == "object" ? apiDetails.callBack() : "";
              } else {
                this.notificationMessage.success(
                  JsonData["label.success.PersonDetails"]
                );
                typeof apiDetails == "object" ? apiDetails.callBack() : "";
              }
              if (res && res?.infoList?.length > 0) {
                this.notificationMessage.infoList(
                  "Warning:\n• " + res.infoList.join("\n• "),
                  true
                );
              }
              resolve(true);
            },
            error: (error) => {
              this.showLoaderSpinner = false;
              resolve(false);
            },
          });
      });
    }
  }

  openHistoryDrawer() {
    const data = { id: this.customerId, isEntity: true };
    this.dataSharingService.toggleHistoryDrawer(data);
  }

  getTime(createdDate) {
    if (createdDate) {
      const oldDate: any = new Date(createdDate);
      const timeZoneOffset = new Date().getTimezoneOffset();
      const subbed = new Date(oldDate - timeZoneOffset * 60 * 1000);
      const time =
        subbed.getHours() +
        ":" +
        subbed.getMinutes() +
        ":" +
        subbed.getSeconds();
      return time;
    } else {
      return;
    }
  }

  getLocalTime(value) {
    return this.dataSharingService.utcToLocalTime(value);
  }

  getFormattedLocalTime(value, is12HrFormatEnabled) {
    return this.dataSharingService.convertUTCToLocalTimeFormat(
      value,
      is12HrFormatEnabled
    );
  }

  receiveData(data: { data1; data2 }) {
    this.dataFromDt = data.data1;
    this.mandatoryValueForAddress = data.data2;
  }

  checkAddressFormInvalidity() {
    if (this.mandatoryValueForAddress == "Y") {
      for (const key in this.dataFromDt) {
        if (this.dataFromDt.hasOwnProperty(key)) {
          if (this.dataFromDt[key] == undefined) {
            return true;
          }
        }
      }
    } else {
      return false;
    }
  }
}
