<div *ngIf="!themeService.useNewTheme">
  <div class="mt-30">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

      </div>
    </div>
    <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="ml-20">

      <div class="closeButton">
        <span *ifHasPermission="ENTITY_RESOURCE.History; scope:'READ'">
          <button mat-raised-button class="green historyButton" (click)="openHistoryDrawer()">
            {{"label.button.history" | literal}}
          </button>
        </span>
        <span *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
          <button mat-raised-button class="green" *ngIf="edit" (click)="onUpdate(false, 'save')"
            [disabled]="disableSaveButton(this.customerForm.dirty)">
            {{"label.button.save"|literal}}
          </button>
        </span>
        <span *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
          <button mat-raised-button class="green margin-right-0-5" *ngIf="edit"
            (click)="onUpdate(false, 'draft')"
            [disabled]="disableSaveButton(this.customerForm.dirty)">
            {{"label.button.draftSave"|literal}}
          </button>
        </span>
        <button mat-raised-button class="red margin-right-0-5" *ngIf="!this.disableActionButton"
          (click)="onCancel()">
          {{"label.button.cancel"|literal}}
        </button>
      </div>
    </div>
  </div>
  <div class="mt-30">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <mat-card appearance="outlined" class="mat-card-top-border">
          <!-- <h2 class="table-header">{{( person?.entityDefinition?.entityName ? person?.entityDefinition?.entityName : '') + ' Information'}}</h2> -->
          <!-- <hr /> -->
          <mat-card-header class="mat-heading-h1"></mat-card-header>
          <mat-card-content *ngIf="!showLoaderSpinner">
            <data-types [sourceInfo]="sourceInfo" (dataEvent)="receiveData($event)"
              [parentData]="{gridConfig: {cellSpan:8,gridSpan:6,fieldWidthPercent:80,inFieldLabel:false , dialogValue:false},sectionData:{sectionName:'Default',stageItems:keyInformation,subSections:'',sectionFieldsData :[{default:{subsectionItems:keyInformation ,name:'default',hideRule: false,isHide:false}}],id:this.customerId},currentStage:'Default',form:customerForm}"></data-types>
          </mat-card-content>

          <div *ngIf="showLoaderSpinner">
            <mat-spinner class="ShowLoader"> </mat-spinner>
          </div>

        </mat-card>

        <br />
      </div>

    </div>
  </div>
</div>

<div *ngIf="themeService.useNewTheme">
  <div class="entity-details-main-containter">

    <div class="entity-actions-containter p-v-15" fxLayout="row" fxLayoutAlign="end center"
      fxLayoutGap="10">

      <span *ifHasPermission="ENTITY_RESOURCE.History; scope:'READ'">
        <button mat-icon-button class="outlined-icon-button" type="button"
           matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="History" (click)="openHistoryDrawer()">
          <span class="material-symbols-outlined">history</span>
        </button>
      </span>

      <span *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
        <button mat-icon-button class="colored-icon-button" type="button" *ngIf="edit"
          [disabled]="disableSaveButton(this.customerForm.dirty)" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Save" (click)="onUpdate(false, 'save')">
          <span class="material-symbols-outlined">save</span>
        </button>
      </span>
      <span *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
        <button mat-icon-button class="colored-icon-button" type="button" *ngIf="edit"
          [disabled]="disableSaveButton(this.customerForm.dirty)" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Draft Save"
          (click)="onUpdate(false, 'draft')">
          <span class="material-symbols-outlined">save_as</span>
        </button>
      </span>
      <button mat-icon-button class="outlined-icon-button" type="button"
        *ngIf="!this.disableActionButton" (click)="onCancel()" matTooltipPosition="above"
        matTooltipClass="accent-tooltip" matTooltip="Cancel">
        <span class="material-symbols-outlined">close_small</span>
      </button>

    </div>

    <div class="entity-details-fields-container">
      <div class="main-loader" *ngIf="showLoaderSpinner">
        <mat-spinner></mat-spinner>
      </div>
      <data-types [sourceInfo]="sourceInfo" (dataEvent)="receiveData($event)"
        [parentData]="{gridConfig: {cellSpan:12,gridSpan:6,fieldWidthPercent:80,inFieldLabel:true},sectionData:{sectionName:'Default',stageItems:keyInformation,subSections:'',sectionFieldsData :[{default:{subsectionItems:keyInformation ,name:'default',hideRule: false,isHide:false}}],id:this.customerId},currentStage:'Default',form:customerForm}"></data-types>
    </div>
  </div>
</div>
