<div *ngIf="!themeService.useNewTheme" class="oldUI">
  <div fxLayout="row wrap" fxLayoutGap="4px" class="md-2">
    <div fxFlex="9%" fxFlex.md="16%" fxFlex.xs="40%" fxFlex.sm="27%" fxFlex.lg="9%"
      class="personDetailsEntityTextSection">
      <p class="inputLabelsInDeal personDetailsEntityText">Select Extension :</p>
    </div>
    <div fxFlex="18%" fxFlex.md="14%" fxFlex.xs="55%" fxFlex.sm="28%" fxFlex.lg="20%">
      <div class="businessProcessListContainer personDetailsEntityInputSection">
        <mat-form-field class="personDetailsEntityInputField">
          <mat-select [disabled]="showLoaderSpinner" [(ngModel)]="selectedPersonExtensionName"
            (selectionChange)="onSelectOfExtension($event.value)"
            [ngModelOptions]="{ standalone: true }"
            aria-label="select-person-ext-{{selectedPersonExtensionName}}">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search Person"
                noEntriesFoundLabel="No matching found" ngModel
                (ngModelChange)="filterExtension($event)"
                attr.aria-label="search-person-ext-{{selectedPersonExtensionName}}"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let entity of getList(entityList)" [value]="entity.entityName">{{
              entity.entityName
              }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div fxFlex="17%" fxFlex.md="20%" fxFlex.xs="80%" fxFlex.sm="40%" fxFlex.lg="17%"
      fxFlexOffset="49">
      <button [disabled]="showLoaderSpinner" mat-raised-button
        class="green personDetailsEntityCreateButton" (click)="addPerson()"
        attr.aria-label="create-details-{{selectedPersonExtensionName}}"
        *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
        {{ "label.button.create" | literal }}
      </button>
      <mat-menu #menu="matMenu"> </mat-menu>
    </div>
  </div>

  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-card appearance="outlined" class="mat-card-top-border">
        <div fxLayout="row wrap" fxLayoutGap="4px"
          class="personDetailsEntitySelectedPersonSection closestyle">
          <div fxFlex="70%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <h1 class="table-header personDetailsEntitySelectedPersonTxt">
              {{selectedPersonExtensionName}}</h1>
          </div>
          <div fxFlex="25%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="ml-4">
            <mat-icon class="filterListIcon" mat-icon-button [matMenuTriggerFor]="myMenu"
              [ngClass]="{ 'selected-icon': isSelected }"
              matTooltip="select search column">filter_list</mat-icon>

            <mat-menu class="my-class" #myMenu="matMenu">
              <mat-selection-list>
                <mat-list-option *ngFor="let value of coloumnSelect" [value]="value"
                  (click)="multipleSelectForFilter($event, value)">
                  {{ value[getPropertyName(value)].displayProperty?.displayName }}
                </mat-list-option>
              </mat-selection-list>
            </mat-menu>
            <mat-form-field class="personDetailsEntitySelectedPersonInputField">
              <mat-label>{{ 'Search ' + selectedPersonExtensionName }}</mat-label>
              <input matInput [(ngModel)]="searchKey" (keyup)="applyFilter($event)"
                placeholder="Date Format - (YYYY-MM-DD)" autocomplete="off" #input
                attr.aria-label="search-details-{{selectedPersonExtensionName}}" />
              <mat-icon matSuffix
                class="personDetailsEntitySelectedPersonInputFieldSearchIcon">search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <hr />
        <mat-card-header class="mat-heading-h1"></mat-card-header>
        <mat-card-content>

          <div class="dataTableCss mat-table-wrap-text">
            <div *ngIf="!showLoaderSpinner">
              <app-ag-grid-table parentName="personEntity"
                [displayedColumns]="this.dispalyColumnsForAgGridTable" [listViewData]="listViewData"
                (valueChange)='onChangesReceived($event)'
                [totalNumberRecords]='totalNumber'></app-ag-grid-table>
            </div>





            <div *ngIf="showLoaderSpinner ">
              <mat-spinner class="personDetailsEntityLoader"> </mat-spinner>
            </div>

            <!-- <div class="no-records-found mt-1 mb-2"  *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner" >
          <mat-card appearance="outlined" class="no-record-card"> No records found </mat-card>
        </div> -->
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>


<ng-container *ngIf="themeService.useNewTheme">

  <div class="entity-list-containter">

    <div class="entity-list-sub-containter-1" fxLayout="row wrap" fxLayoutGap="10px"
      fxLayoutAlign="space-between center">

      <div class="entity-select" fxFlex="row" fxLayoutGap="10px" fxLayoutAlign="start center">
        <div class="back-btn">
          <button mat-icon-button backButton matTooltip="Back">
            <mat-icon>arrow_back</mat-icon></button>
        </div>

        <mat-form-field>
          <mat-select [(ngModel)]="selectedPersonExtensionName"
            (selectionChange)="onSelectOfExtension($event.value)"
            [ngModelOptions]="{ standalone: true }"
            aria-label="select-person-ext-{{selectedPersonExtensionName}}">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search Person"
                noEntriesFoundLabel="No matching found" ngModel
                (ngModelChange)="filterExtension($event)"
                attr.aria-label="search-person-ext-{{selectedPersonExtensionName}}">
              </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let entity of getList(entityList)" [value]="entity.entityName">{{
              entity.entityName}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div>
        <button mat-icon-button class="colored-icon-button large-icon-button"
          [disabled]="getList(entityList)?.length === 0 ||  showLoaderSpinner"
          attr.aria-label="create-details-{{selectedPersonExtensionName}}" (click)="addPerson()"
          matTooltipPosition="above" matTooltipClass="accent-tooltip" matTooltip="Create">
          <span class="material-symbols-outlined">add</span>
        </button>
      </div>

    </div>

    <div class="entity-list-sub-containter-2" fxLayout="row" fxLayoutAlign="end center">

      <div class="filter-section">
        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="15" class="search-field">
          <div>
            <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
              <mat-icon matIconPrefix>search</mat-icon>
              <input attr.aria-label="search-details-{{selectedPersonExtensionName}}" matInput
                #input [(ngModel)]="searchKey" (keyup)="applyFilter($event)"
                [placeholder]="inputType === 'date' ? 'Enter date (YYYY-MM-DD)' : 'Search'">
            </mat-form-field>
          </div>
          <div>
            <button mat-icon-button [matMenuTriggerFor]="myMenu"
              class="outlined-icon-button large-icon-button" matTooltipClass="accent-tooltip"
              matTooltip="select search column">
              <mat-icon [color]="isSelected ? 'accent' : ''">filter_list </mat-icon></button>
            <mat-menu #myMenu="matMenu">
              <div class="menuPanel">
                <mat-selection-list>
                  <mat-list-option togglePosition="before" *ngFor="let value of coloumnSelect"
                    [value]="value" (click)="multipleSelectForFilter($event, value)">
                    {{ value[getPropertyName(value)].displayProperty?.displayName }}
                  </mat-list-option>
                </mat-selection-list>
              </div>
            </mat-menu>
          </div>
        </div>
      </div>
    </div>

    <div class="entity-list-sub-containter-3">

      <div *ngIf="!showLoaderSpinner">
        <app-ag-grid-table parentName="personEntity"
          [displayedColumns]="this.dispalyColumnsForAgGridTable" [listViewData]="listViewData"
          (valueChange)='onChangesReceived($event)'
          [totalNumberRecords]='totalNumber'></app-ag-grid-table>
      </div>


      <div class="table-spinner" *ngIf="showLoaderSpinner">
        <mat-spinner> </mat-spinner>
      </div>


    </div>



  </div>

</ng-container>
