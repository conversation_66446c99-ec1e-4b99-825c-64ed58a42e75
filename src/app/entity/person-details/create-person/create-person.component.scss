::ng-deep .mat-pseudo-checkbox-disabled {
    display: none !important;
}
::ng-deep .disbledOption .mat-mdc-option:first-child .mat-pseudo-checkbox{ display: none; }

.keyName{
    // margin: 5% 0;
    margin: 0 !important;
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize !important
}

/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-form-custom-css{
    min-height: fit-content;
    max-height: 80vh !important;
  }
  .example-viewport {
    height: 200px;
    // width: 838px;
    // border: 1px solid black;
  }


 ::ng-deep .mat-mdc-select-panel{
    max-height: 310px !important;
  }

  .formField{
    mat-form-field{
      padding-bottom: 5px;
    }
  }

  .createPersonLoaderSection{
    margin-left: -6%;
  }

  .createPersonButton{
    display: flex;
    justify-content: center;
    position: sticky;
    padding: 5px;
  }

  .mt1{
    margin-left: 92% !important;
    margin-top: -6% !important;

  }

  .btnAlign{
    padding-left: 10%;
   }

   .table-spinner{
    margin-top:5%;
    display: flex;
    justify-content: center;
  }

  .form-container {
    max-height: 500px; 
    overflow-y: auto;  
  }