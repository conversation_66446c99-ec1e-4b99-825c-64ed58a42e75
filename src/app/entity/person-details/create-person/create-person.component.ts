import { Component, Inject, OnInit, ViewChild } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { MatAccordion } from "@angular/material/expansion";
import JsonData from "src/assets/data.json";
import {
  evalStringExpression,
  formStringExpression,
} from "src/app/helpers/utils";
import { DealService } from "src/app/shared-service/deal.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { DatePipe } from "@angular/common";
import { ThemeService } from "src/app/theme.service";
import {
  FormSources,
  SourceInfo,
} from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
@Component({
  selector: "app-create-person",
  templateUrl: "./create-person.component.html",
  styleUrls: ["./create-person.component.scss"],
})
export class CreatePersonComponent implements OnInit {
  @ViewChild("accordionRef") accordionRef: MatAccordion;
  user: any;
  createCompanyForm: FormGroup;
  entityList: any = [];
  maxDate: any;
  companyId: any;
  entityItems;
  showLoaderSpinner;
  JsonData: any;
  /**
   *Default entities
   *
   * @type {*}
   * @memberof CreateCompanyComponent
   */
  entities: any;
  selectedPersonExtensionName: any;
  selectedExtension: any;
  extension: FormControl;
  name: FormControl;

  localTime: any;
  useNewThemeUI: any;
  sourceInfo: SourceInfo;

  constructor(
    private fb: FormBuilder,
    private errorService: ErrorService,
    private dialogRef: MatDialogRef<CreatePersonComponent>,
    private errorMessageService: ValidationErrorMessageService,
    private entityService: EntityService,
    public notificationMessage: ToasterService,
    public dataSharingService: DataSharingService,
    private dealService: DealService,
    public currencyFormatService: CurrencyFormatService,
    public themeService: ThemeService,
    @Inject(MAT_DIALOG_DATA) public data,
    public datePipe: DatePipe,
    private dataTypesUtils: DataTypesUtilsService
  ) {
    this.selectedPersonExtensionName = data.selectedPersonExtensionName;
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.user = localStorage.getItem("user");
    const today = new Date();
    this.maxDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    if (this.data?.isFromQDE) {
      this.getEntity();
    } else {
      this.entityList = this.entityService.basePlusPersonExtensions;
      this.changeExtension(this.selectedPersonExtensionName);
    }
    this.name = new FormControl("", [
      Validators.required,
      Validators.pattern(this.errorMessageService.nameRegex),
    ]);
    this.extension = new FormControl("", [Validators.required]);
    this.sourceInfo = <SourceInfo>{ type: FormSources.Entity };
  }

  getEntity() {
    this.showLoaderSpinner = true;
    this.entityService.getEntitiesDetails().subscribe((res: any) => {
      if (res.length != 0) {
        this.entities = res;
        this.entityService.getExtensionsDetails().subscribe((res) => {
          this.entityList = this.entities.concat(res);
          this.entityList = this.entityList.filter(
            (item) =>
              item.entityName.toLowerCase() ==
                this.selectedPersonExtensionName.toLowerCase() &&
              item.status.toLowerCase() == "active"
          );
          this.changeExtension(this.selectedPersonExtensionName);
          this.showLoaderSpinner = false;
        });
      }
    });
  }

  // get Error message
  getErrorMessage(formName, controlName, ele, customValidation?: any) {
    if (customValidation && controlName.inputType == "Phone Number") {
      try {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const phoneUtil =
          require("google-libphonenumber").PhoneNumberUtil.getInstance();
        return !phoneUtil.isValidNumberForRegion(
          phoneUtil.parse(
            customValidation,
            controlName.displayProperty.defaultValues.countryCode
          ),
          controlName.displayProperty.defaultValues.countryCode
        );
      } catch {
        return;
      }
    } else {
      const sectionDetails = ele[this.getPropertyName(ele)]?.displayProperty;

      const flatAssetItem = Object.assign({}, ...this.entityItems);
      const exper = formStringExpression(sectionDetails?.validateRule, [
        "controls",
        "entity",
        "val",
      ]);
      const vals = evalStringExpression(exper, this, [
        this.createCompanyForm.controls,
        flatAssetItem,
        Validators,
      ]);
      return this.errorMessageService.getErrorMessage(
        this,
        formName,
        controlName,
        vals
      );
    }
  }

  // create dynamic form
  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      group.addControl(this.getPropertyName(control), this.fb.control(""));
    });
    return group;
  }

  validate() {
    this.entityItems.forEach((element) => {
      if (
        element &&
        element[this.getPropertyName(element)].inputType !== "formly" &&
        this.errorMessageService.getValidation(
          element[this.getPropertyName(element)].displayProperty.mandatory,
          element[this.getPropertyName(element)].inputType,
          element[this.getPropertyName(element)]?.displayProperty.validation
        )
      ) {
        this.createCompanyForm.controls[
          this.getPropertyName(element)
        ].setValidators(
          this.errorMessageService.getValidation(
            element[this.getPropertyName(element)].displayProperty.mandatory,
            element[this.getPropertyName(element)].inputType,
            element[this.getPropertyName(element)]?.displayProperty.validation
          )
        );
        this.createCompanyForm.controls[
          this.getPropertyName(element)
        ].updateValueAndValidity();
      }
    });
    this.showLoaderSpinner = false;
  }

  // On chnage person type
  changeExtension(extensionName) {
    this.selectedPersonExtensionName = extensionName;
    this.selectedExtension = this.entityList.find(
      (extension) => extension.entityName == extensionName
    );
    this.entityItems =
      this.selectedExtension?.entityDetail?.entityDetail.filter(
        (element) =>
          element[this.getPropertyName(element)].displayProperty?.isForFormView
      );
    this.entityItems.forEach((element) => {
      //formlyModel Setting
      const key = Object.entries(element)[0][0];

      if (element[key].inputType === "Date") {
        if (element[key]?.displayProperty?.defaultValues == "Today") {
          element[key].value = new Date();
        }
      }

      if (element[key].inputType === "Date And Time") {
        if (
          element[key]?.displayProperty?.defaultValues ==
          "Today With Current Time"
        ) {
          const newtime = this.datePipe.transform(
            new Date(),
            "yyyy-MM-ddTHH:mm:ss"
          );
          element[key].value = newtime;
        } else if (element[key]?.displayProperty?.defaultValues == "") {
          const newtime = this.datePipe.transform(
            new Date(),
            "yyyy-MM-ddTHH:mm:ss"
          );
          element[key].value = newtime;
        }
      }
    });
    if (this.createCompanyForm) this.createCompanyForm.reset();
    this.createCompanyForm = this.createGroup(this.entityItems);
    this.dealService.allDealItems = this.entityItems;

    this.createCompanyForm.reset();
    this.validate();
  }

  validateForm() {
    // if(!this.assetForm.valid) return true;
    if (this.createCompanyForm.value.name.trim().length == 0) {
      this.createCompanyForm.controls["name"].setValue(
        this.createCompanyForm.value?.name?.trim()
      );
      return true;
    }
  }

  closeDialog() {
    this.dialogRef.close();
    this.createCompanyForm.reset();
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  receiveDataFromChild(data: string) {
    this.localTime = data;
  }
  createPerson() {
    this.createCompanyForm.markAllAsTouched();
    this.name.markAsTouched();
    this.extension.markAsTouched();
    if (
      this.createCompanyForm.invalid ||
      this.name.invalid ||
      this.extension.invalid
    ) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    this.setSourceInfo(this.entityItems);
    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(
        this.selectedExtension.entityDetail.entityDetail,
        this.createCompanyForm,
        true
      );
    const payload = {
      name: this.name.value,
      entityType: this.selectedExtension.entityType,
      entityId: this.selectedExtension.id,
      companyId: this.companyId || null,
      customerDetails: {
        entityDetail: updatedVals,
      },
      entityLinkageList: updatedLinkage,
    };

    this.entityService.newEntityCreate(payload).subscribe(
      (res: any) => {
        this.notificationMessage.success(
          `${this.selectedExtension.entityType} ` +
            JsonData["label.success.AddPerson"]
        );
        if (!this.data?.isFromQDE) {
          this.entityService.selectedPersonExtensionName =
            this.selectedPersonExtensionName;
        }
        if (res && res?.infoList?.length > 0) {
          this.notificationMessage.infoList(
            "Warning:\n• " + res.infoList.join("\n• "),
            true
          );
        }
        this.createCompanyForm.reset();
        this.dialogRef.close(res);
      },
      (error) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  setSourceInfo(fields) {
    if (!fields.find((e) => this.getPropertyName(e) == "sourceIsOriginate")) {
      const sourceIsOriginate = {
        name: "Source Is Originate",
        inputType: "Text",
        stages: [
          {
            section: [
              {
                order: 0,
                section: "Default",
                subsection: "",
                _hide: true,
              },
            ],
            isMasked: "",
            stageName: "Default",
            isSelected: "",
            isEncrypted: "",
            isMandatory: "",
          },
        ],
        displayProperty: {
          validation: "",
          displayName: "Source Is Originate",
          defaultValues: "",
          isForFormView: false,
          isForListView: false,
        },
        value: "Yes",
      };
      fields.push({ sourceIsOriginate: sourceIsOriginate });
    }
  }

  getTime(createdDate) {
    if (createdDate) {
      const oldDate: any = new Date(createdDate);
      const timeZoneOffset = new Date().getTimezoneOffset();
      const subbed = new Date(oldDate - timeZoneOffset * 60 * 1000);
      const time =
        subbed.getHours() +
        ":" +
        subbed.getMinutes() +
        ":" +
        subbed.getSeconds();
      return time;
    } else {
      return;
    }
  }
}
