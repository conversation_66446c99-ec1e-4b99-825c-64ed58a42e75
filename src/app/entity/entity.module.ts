import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { EntityRoutingModule } from './entity-routing.module';
import { CompanyDetailsComponent } from './company-details/company-details.component';
import { SharedModuleModule } from '../shared-module/shared-module.module';
import { CreateCompanyComponent } from './company-details/create-company/create-company.component';
import { ViewCompanyComponent } from './company-details/view-company/view-company.component';
import { DetailComponent } from './company-details/view-company/detail/detail.component';
import { DocumentsComponent } from './company-details/view-company/documents/documents.component';
import { EditDetailComponent } from './company-details/view-company/detail/edit-detail/edit-detail.component';
import { PersonDetailsComponent } from './person-details/person-details.component';
import { ViewPersonComponent } from './person-details/view-person/view-person.component';
import { PersonDetailComponent } from './person-details/view-person/person-detail/person-detail.component';
import { CompanyLinkedDealsComponent } from './company-details/view-company/company-linked-deals/company-linked-deals.component';
import { AssociatedPersonsComponent } from './company-details/view-company/associated-persons/associated-persons.component';


@NgModule({
  declarations: [CompanyDetailsComponent, CreateCompanyComponent, ViewCompanyComponent,DetailComponent,DocumentsComponent,EditDetailComponent,
    PersonDetailsComponent,
  ViewCompanyComponent,
  ViewPersonComponent,
  PersonDetailComponent,
  CompanyLinkedDealsComponent,
  AssociatedPersonsComponent],
  imports: [
    CommonModule,
    EntityRoutingModule,
    SharedModuleModule
  ]
})
export class EntityModule { }
