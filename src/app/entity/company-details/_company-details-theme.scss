@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);

  .entity-list-containter{

    .entity-list-sub-containter-1{
  
      .entity-select:hover{
        mat-form-field{
          .mat-mdc-text-field-wrapper{
            background-color: mat.get-color-from-palette($primary-palette,100);
          }

          .mat-mdc-select-value{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }

          .mat-mdc-select-arrow{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }
        }
      }
  
    }
    
    .blue:hover{
      color: mat.get-color-from-palette($primary-palette, 400) !important;
    }
    
    .entity-list-sub-containter-3{
        th{
          background-color: var(--container-color);
        }
       .delete-icon:hover{
          color: mat.get-color-from-palette($warn-palette, 400) !important;
        }
    }
  
  }


}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

.entity-list-containter{

.entity-list-sub-containter-1{

    .entity-select{
        mat-form-field{
            mat-select{
                @include mat.typography-level($typography-config, 'headline-6');
              }
           }
        }
    }

  .entity-list-sub-containter-2{
    .selected-filter{
      @include mat.typography-level($typography-config, 'headline-5');

    }
  }

  .entity-list-sub-containter-3{

  }

}
}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}