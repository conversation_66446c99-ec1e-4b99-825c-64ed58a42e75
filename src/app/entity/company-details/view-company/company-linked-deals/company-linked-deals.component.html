 <div *ngIf="!themeService.useNewTheme" class="oldUI">
 <div fxLayout="row wrap" fxLayoutGap="4px" class="mt-30 closestyle">
    <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    </div>
    <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%" class="companyLinkDetailsNewButtonSection ml-4">
      <button class="green companyLinkDetailsButton" mat-raised-button [disabled]="disableNewBtn"
      (click)="newCustomer()">NEW</button>
    </div>

  </div>
  
<div class="mt-30">
  <mat-card appearance="outlined" class="mat-card-top-border">
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <h2 class="table-header">{{this.getSidebarItembyName('Deal')}}</h2>
    </div>

    <hr />
    <mat-card-header class="mat-heading-h1"></mat-card-header>
    <mat-card-content>
     <div 
      class="task-table-container mat-elevation-z0  mat-table-width task-table-container">

      <table mat-table [dataSource]="dealdataSource " matSort [style.minHeight]="dealdataSource?.data.length == 0? '150px':''"
        class="  mat-elevation-z0  mat-table-width">



        <!--  Column -->
        <ng-container matColumnDef="dealCustomerList">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22"> Description </th>
          <td mat-cell class="pointer hyperlinkColor" *matCellDef="let row"> 
            <span  
              (click)="navigateToSummaryPage(row)">
              <p [matTooltip]="row?.dealIdentifier">
                {{stringWithEllipsisEntity(row?.dealIdentifier)}}</p>
            </span>
          </td>
        </ng-container>

        

        <!--  Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Status </th>
          <td mat-cell class=" " *matCellDef="let row"> <span class="">{{getDynamicStatusValue(row?.currentStatus)}}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="completionDate" >
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="completionDate"> Completion Date </th>
          <td mat-cell class="completionValue" *matCellDef="let row" ><span class="completionValue">{{this.getAssetValue(row.dealAsset?.dealAssetItem,'completionDate')}}</span>
          </td>
        </ng-container>

        <!--  Column -->
        <ng-container matColumnDef="stage">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Stage </th>
          <td mat-cell class=" " *matCellDef="let row"> <span class="">{{row.currentStageName}}</span>
          </td>
        </ng-container>
      


        <!--  Column -->
        <ng-container matColumnDef="teamLead">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-18"> {{getTeamLeadDisplayName()}}</th>
          <td mat-cell class=" " *matCellDef="let row"> {{this.getUsersName(row.dealTeamList)}} </td>
        </ng-container>



        <!--  Column -->
        <ng-container matColumnDef="createdDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22"> Requested Date </th>
          <td mat-cell class=" " *matCellDef="let row"> {{row.dealAsset?.dealAssetItem?.creationDate?.value | date}} </td>
        </ng-container>






        <!--  Column -->
        <ng-container matColumnDef="businessProcess">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-17">Business Process </th>
          <td mat-cell class=" " *matCellDef="let row">
            {{stringWithEllipsisEntity(row.businessProcessDetail.name)}} 
          </td>

        </ng-container>

        <!-- Column -->
        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="w-12  "> </th>
          <td class=" " mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">



          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="dealDisplayColumns;" class="companyLinkDetailsAlignment"></tr>
        <tr mat-row  class="companyLinkDetailsAlignment"
          *matRowDef="let row; columns: dealDisplayColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
        <div *ngIf="!showLoaderSpinner">
          <tr class="mat-row width100" *matNoDataRow>
            <td class="no-records-found"  colspan="12"></td>
          </tr>
        </div>
      </table>



      <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <mat-paginator class="" [pageSizeOptions]="[8, 25,50, 100]" [length]="totalNumbers"   (page)="onPaginationChanged($event)" [pageSize]="pageSize" [pageIndex]="pageIndex"></mat-paginator>
      </div>

      <div *ngIf="showLoaderSpinner">
        <mat-spinner class="no-record-card ShowLoader">
        </mat-spinner>
      </div>
    </div>
     </mat-card-content>
  </mat-card>
</div>
</div>

<div *ngIf="themeService.useNewTheme" class="application-container">

  <div fxLayout="row wrap">
    <div fxLayout="row" fxFlex="100%" fxLayoutAlign="end center" class="m-v-15">
      <button class="colored-icon-button large-icon-button m-r-15" mat-icon-button [disabled]="disableNewBtn"
      (click)="newCustomer()" matTooltipPosition="above" matTooltipClass="accent-tooltip" matTooltip="New">
      <span class="material-symbols-outlined">add</span>
      </button>
    </div>

    <div class="full-width m-b-15">
      <table mat-table [dataSource]="dealdataSource " matSort [style.minHeight]="dealdataSource?.data.length === 0? '150px':''">
        
      <ng-container matColumnDef="dealCustomerList">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
        <td mat-cell class="pointer link" *matCellDef="let row"> 
          <span  
            (click)="navigateToSummaryPage(row)">
            <p matTooltipClass="accent-tooltip" [matTooltip]="row?.dealIdentifier">
              {{stringWithEllipsisEntity(row?.dealIdentifier)}}</p>
          </span>
        </td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
        <td mat-cell *matCellDef="let row"> <span class="">{{getDynamicStatusValue(row?.currentStatus)}}</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="completionDate" >
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Completion Date </th>
        <td mat-cell *matCellDef="let row" ><span>{{this.getAssetValue(row.dealAsset?.dealAssetItem,'completionDate')}}</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="stage">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Stage </th>
        <td mat-cell *matCellDef="let row"> <span>{{row.currentStageName}}</span>
        </td>
      </ng-container>
    
      <ng-container matColumnDef="teamLead">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> {{getTeamLeadDisplayName()}}</th>
        <td mat-cell *matCellDef="let row"> {{this.getUsersName(row.dealTeamList)}} </td>
      </ng-container>

      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Requested Date </th>
        <td mat-cell *matCellDef="let row"> {{row.dealAsset?.dealAssetItem?.creationDate?.value | date}} </td>
      </ng-container>

      <ng-container matColumnDef="businessProcess">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Business Process </th>
        <td mat-cell *matCellDef="let row">
          {{stringWithEllipsisEntity(row.businessProcessDetail.name)}} 
        </td>
      </ng-container>

      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
        <td mat-cell *matCellDef="let element" (click)="$event.stopPropagation()"></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="dealDisplayColumns;"></tr>
      <tr mat-row 
        *matRowDef="let row; columns: dealDisplayColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
        <div *ngIf="!showLoaderSpinner">
          <tr *matNoDataRow>
            <td class="no-records-found" colspan="12"></td>
          </tr>
        </div>
      </table>

      <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <mat-paginator class="" [pageSizeOptions]="[8, 25,50, 100]" [length]="totalNumbers"   (page)="onPaginationChanged($event)" [pageSize]="pageSize" [pageIndex]="pageIndex"></mat-paginator>
      </div>

      <div class="table-spinner" *ngIf="showLoaderSpinner">
        <mat-spinner></mat-spinner>
      </div>
    </div>
  </div>

</div>

