import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatTableDataSource } from "@angular/material/table";
import { ActivatedRoute, Router } from "@angular/router";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DealService } from "src/app/shared-service/deal.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { ThemeService } from "src/app/theme.service";
import JsonData from "src/assets/data.json";

@Component({
  selector: "app-company-linked-deals",
  templateUrl: "./company-linked-deals.component.html",
  styleUrls: ["./company-linked-deals.component.scss"],
})
export class CompanyLinkedDealsComponent implements OnInit {
  selectedBusinessProcess: any;
  JsonData = JsonData;
  showLoaderSpinner: boolean;
  dealdataSource: MatTableDataSource<any>;
  sortDirection: any;
  totalNumbers: any;
  currentCustomerDealList: any;
  paginator: any;
  sort: any;
  showNoRecordsAvailbleMessage: boolean;
  selectedBusinessProcessId: any;
  customerId: any;
  dealDisplayColumns: string[] = [
    "dealCustomerList",
    "businessProcess",
    "stage",
    "status",
    "completionDate",
    "teamLead",
  ];
  pageSize: any;
  pageIndex: any;
  userList: any = [];
  businessProcessList: any;
  disableNewBtn: boolean = false;
  private unsubscribe$ = new Subject();

  constructor(
    private dataSharingService: DataSharingService,
    private dialog: MatDialog,
    private entityService: EntityService,
    private datePipe: DatePipe,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private dealService: DealService,
    private identityService: IdentityService,
    private businessProcessService: BusinessProcessService,
    public themeService: ThemeService
  ) {
    this.getUserList();

    this.activatedRoute.paramMap.subscribe((params: any) => {
      if (params.get("id")) {
        this.customerId = atob(params.get("id"));
        this.getAllDealsForcurrentCustomer();

        if (entityService?.customerDetails) {
          this.getBusinessProcessList();
        } else {
          this.entityService
            .getCustomerDetails(this.customerId)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((res: any) => {
              if (res) {
                this.entityService.customerDetails = res;
                this.entityService.setCustomerDetails(res);
                this.getBusinessProcessList();
              }
            });
        }
        this.entityService.customerIdEmitter.emit(this.customerId);
      }
    });
  }

  ngOnInit(): void {}

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res: any) => {
        this.userList = res;
      },
      (error) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  newCustomer() {
    this.businessProcessService
      .getBusinessProcessById(this.selectedBusinessProcessId)
      .subscribe((data) => {
        this.dataSharingService.getDataById = data;
        this.dataSharingService.DealFromCompany = true;
        const matDialogRef = this.dialog.open(NewCustomerComponent, {
          autoFocus: false,
          width: "45%",
          disableClose: true,
          data: {
            dealFrom: "company",
            selectedBusinessProcessId: this.selectedBusinessProcessId,
            selectedBusinessProcess: data.name,
            selectedPrimaryActorDetails: this.entityService.customerDetails,
          },
        });

        matDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.getAllDealsForcurrentCustomer();
          }
        });
      });
  }

  getBusinessProcessList() {
    this.businessProcessService
      .getEntityBusinessProcessList(this.entityService?.customerDetails)
      .subscribe((data) => {
        this.businessProcessService.businessProcessList = data;
        this.businessProcessList =
          this.businessProcessService.businessProcessList;
        if (
          this.businessProcessService.businessProcessList &&
          this.businessProcessService.businessProcessList.length != 0
        ) {
          this.selectedBusinessProcessId =
            this.businessProcessService.businessProcessList[0].id;
          this.disableNewBtn = false;
        } else {
          this.disableNewBtn = true;
        }
      });
  }

  getAllDealsForcurrentCustomer() {
    this.showLoaderSpinner = true;
    this.dealdataSource = new MatTableDataSource([]);

    let data = {
      sortBy: this.sortDirection
        ? this.dataSharingService.sortDirectionforentitydetails.toUpperCase()
        : "DESC",
      sortingKey: this.dataSharingService.sortAsPerKeyNameforentitydetails,
      pageIndex: this.dataSharingService.pageIndexforentitydetails,
      pageSize: this.dataSharingService.pageSizeforentitydetails,
    };

    this.dealService
      .getAllDealsForcurrentCustomer(data, this.customerId)
      .subscribe((deals) => {
        this.showLoaderSpinner = false;
        let allDeals: any;
        allDeals = deals;
        let self = this;
        this.totalNumbers = allDeals["totalElements"];
        this.currentCustomerDealList = allDeals["content"];
        this.showLoaderSpinner = false;

        this.dealdataSource = new MatTableDataSource(
          this.currentCustomerDealList
        );
        this.dealdataSource.paginator = this.paginator;
        this.dealdataSource.sort = this.sort;
      }),
      (error) => {
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      };
  }

  navigateToSummaryPage(data) {
    if (this.selectedBusinessProcessId) {
      this.dataSharingService.selectedApplicationData = undefined;
      this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);

      this.router.navigate(["/application-summary/details/" + btoa(data?.id)]);
    }
  }

  getAssetValue(assetItem, str) {
    if (str == "completionDate") {
      let date = assetItem.find((item) => this.getPropertyName(item) == str);
      return date?.completionDate?.value
        ? this.datePipe.transform(date?.completionDate?.value)
        : "-";
    }
  }

  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.dataSharingService.pageIndexforentitydetails = event.pageIndex;
    this.pageSize = event.pageSize;
    this.dataSharingService.pageSizeforentitydetails = event.pageSize;
    this.getAllDealsForcurrentCustomer();
  }

  stringWithEllipsisEntity(str) {
    return this.dataSharingService.stringWithEllipsis(str, 20);
  }

  getUsersName(teamList) {
    let teamLead = teamList.filter((ele) => ele.isTeamLead)[0];

    let userId = teamLead?.teamName;

    let user = [];
    user = this.userList.filter((ele) => ele.identifier == userId);
    let userObj = user[0];

    let leadName = "";
    userObj
      ? (leadName = userObj?.firstName + " " + userObj?.lastName)
      : (leadName = "-");
    return leadName;
  }

  getTeamLeadDisplayName() {
    if (this.dataSharingService.getDataById == undefined) {
      return "Lead";
    } else {
      if (
        this.dataSharingService.getDataById?.currentStatus == "Rejected" ||
        this.dataSharingService.getDataById?.currentStatus == "In progress" ||
        this.dataSharingService.getDataById?.currentStatus == "Approved"
      ) {
        let leadData =
          this.dataSharingService.getDataById?.dealAsset?.dealAssetItem
            ?.slice()
            ?.filter((ele) => this.getPropertyName(ele) == "teamLead");
        return leadData[0].teamLead.displayProperty.displayName
          ? leadData[0].teamLead.displayProperty.displayName
          : "Lead";
      } else {
        let leadData = this.dataSharingService.getDataById?.assetItems
          ?.slice()
          ?.filter((ele) => this.getPropertyName(ele) == "teamLead");
        return leadData[0]?.teamLead.displayProperty.displayName
          ? leadData[0]?.teamLead.displayProperty.displayName
          : "Lead";
      }
    }
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      let item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  getDynamicStatusValue(status) {
    return status == "Rejected"
      ? JsonData["label.button.rejectedStatus"]
      : status == "Approved"
      ? JsonData["label.button.approvedStatus"]
      : status;
  }
}
