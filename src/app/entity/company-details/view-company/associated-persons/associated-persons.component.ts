import { Component, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { EntityService } from 'src/app/shared-service/entity.service';
import { ThemeService } from 'src/app/theme.service';

@Component({
  selector: 'app-associated-persons',
  templateUrl: './associated-persons.component.html',
  styleUrls: ['./associated-persons.component.scss']
})
export class AssociatedPersonsComponent implements OnInit {
  personPageSize: any;
  personsdataSource: MatTableDataSource<unknown>;
  personsDisplayColumns: string[] = ["Name", "Email", 'Phone'];
  totalPersonsLength: number = 0;
  showLoaderSpinner:  boolean = false;
  personPageIndex:number = 0 ;
  

  @ViewChild(MatPaginator) paginator: MatPaginator;

  @ViewChild(MatSort, { static: true }) sort: MatSort;
  customerId: string;
  unsubscribe$ = new EventEmitter;

  constructor(
    private dataSharingService : DataSharingService,
    private dealService : DealService,
    private entityService : EntityService,
    private router : Router,
    private activatedRoute : ActivatedRoute,
    public themeService: ThemeService
  ) { 

    this.activatedRoute.paramMap.subscribe((params: any) => {
      if (params.get('id')) {
        this.customerId = atob(params.get('id'));
        if(!entityService.customerDetails){
          this.entityService.getCustomerDetails(this.customerId).pipe(takeUntil(this.unsubscribe$)).subscribe((res: any) => {
            if (res) {
              this.entityService.customerDetails = res;
              this.entityService.setCustomerDetails(res);
            }
          })
        }
        this.getPersonListByCompanyId();
        this.entityService.customerIdEmitter.emit(this.customerId)
      }

    }) 
   
  }

  ngOnInit(): void {
  }

    // get the property Name 
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  getPersonListByCompanyId() {
    this.showLoaderSpinner = true;
    this.dealService.getPersonsByCustomerId(this.customerId).subscribe((persons) => {
      let personList:any = persons;
      this.totalPersonsLength = personList.length;
      this.personsdataSource = new MatTableDataSource(personList);
      this.personsdataSource.paginator = this.paginator;
      this.personsdataSource.sort = this.sort;
      this.showLoaderSpinner = false;

    },(err)=>{
      this.showLoaderSpinner = false;
    })
  }

  navigateToPerson(row) {
    this.dataSharingService.newSubPageNameValue(row.name);
    this.dataSharingService.subPageEntityIdValue(row.customerId);
    this.dataSharingService.companyIdOfPersonValue(row.companyId);
    this.entityService.customerDetails = row;
    this.entityService.setCustomerDetails(row)
    this.router.navigate([`entity/viewperson/detail/${btoa(row.customerId)}`],{ state: { data: { customerId:row.customerId,edit:true,companyId:row.companyId,element:row.entityDefinition } } });
  }

  getValue(row, nodeName) {
    let item = row.customerDetails?.entityDetail.find((item) => this.getPropertyName(item) == nodeName);
    if (item) {
      return item[this.getPropertyName(item)]?.value || "";
    }
    return "";

  }
  onPersonPaginationChanged(event) {
    this.personPageIndex = event.pageIndex;
    this.personPageSize = event.pageSize;
    this.personsdataSource.paginator = this.paginator;
  }


}
