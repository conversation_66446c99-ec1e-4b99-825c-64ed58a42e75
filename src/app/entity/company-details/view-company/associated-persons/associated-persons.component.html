<div *ngIf="!themeService.useNewTheme">
<mat-card appearance="outlined" class="mat-card-top-border mt-15">
    <h2 class="table-header">{{"label.title.persons"|literal}}</h2>
    <hr />
    <mat-card-header class="mat-heading-h1"></mat-card-header>
    <mat-card-content>
    <div 
      class="task-table-container mat-elevation-z0  mat-table-width task-table-container">

      <table mat-table [dataSource]="personsdataSource " matSort  [style.minHeight]="personsdataSource?.data.length == 0? '150px':''"
        class="  mat-elevation-z0  mat-table-width">

        <ng-container matColumnDef="Name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22"> Name </th>
          <td mat-cell class="pointer hyperlinkColor" *matCellDef="let row"> 
            <span  
              (click)="navigateToPerson(row)" class="customDescription">
              <p [matTooltip]="row?.name">{{row?.name}}</p>
              
            </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="Email">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Email </th>
          <td mat-cell class=" " *matCellDef="let row"> <span class="">{{getValue(row,'contactEmail')}}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="Phone">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Phone </th>
          <td mat-cell class=" " *matCellDef="let row"> <span class="">{{ getValue(row,'contactPhone')}}</span>
          </td>
        </ng-container>
       

        <tr mat-header-row class="textAlign" *matHeaderRowDef="personsDisplayColumns;"></tr>
        <tr mat-row  class="textAlign"
          *matRowDef="let row; columns: personsDisplayColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
        <div *ngIf="!showLoaderSpinner">
          <tr class="mat-row width100" *matNoDataRow>
            <td class="no-records-found" colspan="12"  ></td>
          </tr>
        </div>
      </table>
    </div>
      <div *ngIf="!showLoaderSpinner">
        <mat-paginator class="" [pageSizeOptions]="[ 8, 25, 50, 100]" [length]="totalPersonsLength" [pageSize]="personPageSize" 
        [pageIndex]="personPageIndex" (page)="onPersonPaginationChanged($event)"></mat-paginator>
      </div>

       <div *ngIf="showLoaderSpinner">
        <mat-spinner class="no-record-card ShowLoader" ></mat-spinner>
      </div>
     </mat-card-content>
  </mat-card>
</div>

<div *ngIf="themeService.useNewTheme" fxLayout="row wrap">
  <div class="full-width m-t-20">
  <table mat-table [dataSource]="personsdataSource " matSort  [style.minHeight]="personsdataSource?.data.length === 0? '150px':''">

    <ng-container matColumnDef="Name">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
      <td mat-cell class="pointer link" *matCellDef="let row"> 
        <span  
          (click)="navigateToPerson(row)">
          <p matTooltipClass="accent-tooltip" [matTooltip]="row?.name">{{row?.name}}</p>
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="Email">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
      <td mat-cell *matCellDef="let row"> <span>{{getValue(row,'contactEmail')}}</span>
      </td>
    </ng-container>

    <ng-container matColumnDef="Phone">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Phone</th>
      <td mat-cell *matCellDef="let row"> <span>{{ getValue(row,'contactPhone')}}</span>
      </td>
    </ng-container>
   

    <tr mat-header-row *matHeaderRowDef="personsDisplayColumns;"></tr>
    <tr mat-row
      *matRowDef="let row; columns: personsDisplayColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
    <div *ngIf="!showLoaderSpinner">
      <tr *matNoDataRow>
        <td class="no-records-found" colspan="12"></td>
      </tr>
    </div>
    </table>
    <div *ngIf="!showLoaderSpinner">
      <mat-paginator class="" [pageSizeOptions]="[ 8, 25, 50, 100]" [length]="totalPersonsLength" [pageSize]="personPageSize" 
      [pageIndex]="personPageIndex" (page)="onPersonPaginationChanged($event)"></mat-paginator>
    </div>

     <div class="table-spinner" *ngIf="showLoaderSpinner">
      <mat-spinner></mat-spinner>
    </div>
  </div>
</div>
