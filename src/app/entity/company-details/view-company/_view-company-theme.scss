@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
    $color-config: mat.get-color-config($theme);
    $background-palette: map.get($color-config, 'background');
    $primary-palette: map.get($color-config, 'primary');

    .view-entity-containter{
        nav{
            background-color: var(--container-color) !important;
        }
    }

    .blue:hover{
      color: mat.get-color-from-palette($primary-palette, 400) !important;
    }

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .view-entity-containter{
  .view-entity-header{
    .conf-heading{
        @include mat.typography-level($typography-config, 'headline-5');
    }
  }

  .nav-surface{

  }

  }
}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}