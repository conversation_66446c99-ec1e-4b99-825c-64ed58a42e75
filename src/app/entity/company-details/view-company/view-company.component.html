<div *ngIf="!themeService.useNewTheme">
<div class="mt-15">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div class="textSection" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <h2 class="config-heading">
        {{company?.name || ''}}
      </h2>
      <button mat-icon-button class="bottom blue" (click)="editEntityName()" *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
        <mat-icon>
          edit
        </mat-icon>
      </button>
    </div>
  </div>
  </div>
  <nav mat-tab-nav-bar [tabPanel]="tabPanel">
    <ng-container *ngFor="let link of companyDetailsTabs; let i = index">
      <a mat-tab-link [routerLink]="'../../'+ getLink(link.link)" routerLinkActive #rla="routerLinkActive"
      [active]="rla.isActive" (click)="editing(link.link,i)">
      {{ getSidebarItembyName(link?.label)}}
    </a>
    </ng-container>
  </nav>
  <mat-tab-nav-panel #tabPanel>
    <router-outlet></router-outlet>
  </mat-tab-nav-panel>
</div>

<div *ngIf="themeService.useNewTheme">
  <div class="view-entity-containter">
    <div class="view-entity-header m-b-35" fxLayout="row">
      <div fxFlex="5%" class="back-button" fxLayoutAlign="start center">
        <button mat-icon-button backButton matTooltipClass="accent-tooltip" matTooltip="Back">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
      <div fxFlex="95%" class="conf-heading" fxLayoutAlign="start center" fxLayoutGap="10">
        <span>
          {{company?.name || ''}}
        </span>
        <button mat-icon-button (click)="editEntityName()" *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
          <span class="material-symbols-outlined">edit</span>
        </button>
      </div>
    </div>
      <nav mat-tab-nav-bar [tabPanel]="tabPanel">
        <ng-container *ngFor="let link of companyDetailsTabs; let i = index">
          <a mat-tab-link [routerLink]="'../../'+ getLink(link.link)" routerLinkActive #rla="routerLinkActive"
          [active]="rla.isActive" (click)="editing(link.link,i)">
          {{ getSidebarItembyName(link?.label)}}
        </a>
        </ng-container>
      </nav>
      <mat-tab-nav-panel #tabPanel>
        <router-outlet></router-outlet>
      </mat-tab-nav-panel>
  </div>
</div>

