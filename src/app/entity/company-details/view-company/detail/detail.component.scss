table {
    width: 100%;
  }

  .overflow-scroll {
    overflow-y: auto;
    max-height: 40vh;
}

.companydetails{
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-underline {
      bottom: 0 !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important; 
  }
}

.tbl-div {
  position: relative;
  overflow: auto;
}
.keyName{
  margin: 5% 0;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize
}

.formField{
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-infix {
       border-top: .24375em solid transparent !important;
   }
   /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
   ::ng-deep .mat-form-field-wrapper {
       padding-bottom: 0 !important;
   }
   /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
   ::ng-deep .mat-form-field-underline {
       bottom: 0 !important; 
      }
}

.datePicker{
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
      padding: .15em .75em 0 .75em !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
  ::ng-deep .mat-form-field-underline {
   bottom: 0 !important; 
  }
}

.dealamount{
  height: 18px;
    padding-right: 4px;
}
.example-viewport {
  height: 200px;

}

::ng-deep .mat-mdc-select-panel {
  max-height: 310px !important;
}

::ng-deep .mat-drawer-backdrop {
  z-index: 1;
}

::ng-deep .mat-drawer-inner-container {
  overflow: hidden !important;
}


.completionValue{
  text-align: center !important 
}

.completionDate{
   padding-left: 10px;
}

.website-input{
  float: right;
  position: absolute;
  bottom: -7%;
  left: 90%;
}

.single-line-input {
  width: 88%;
  white-space: nowrap; 
  overflow: hidden; 
  text-overflow: ellipsis; 
}

.table-container1 {
  max-height: 500px; 
  overflow-y: auto; 
}

.sticky-header {
  position: sticky;
  top: 0;
  background-color: #fff; 
  z-index: 2;
}
.ml-20{
  margin-left: 20% !important;

}

.entity-details-main-containter{
  .entity-details-fields-container{
    .main-loader{
      display: flex;
      justify-content: center;
      margin-top: 5%;
    }
  }
}

