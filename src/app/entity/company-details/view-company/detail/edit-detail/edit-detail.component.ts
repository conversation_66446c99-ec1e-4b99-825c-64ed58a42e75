import { Component, Inject, OnInit, Optional } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

@Component({
  selector: "app-edit-detail",
  templateUrl: "./edit-detail.component.html",
  styleUrls: ["./edit-detail.component.scss"],
})
export class EditDetailComponent implements OnInit {
  readonly ZCP_DATA_TYPE = ZcpDataTypes;

  conditionExpression = "Text";
  minDate: Date;
  mandatory = ["Yes", "No"];
  maxDate: Date;
  picklistValues;
  dataTypes = [
    "Text",
    "Date",
    "Alphanumeric",
    "Number",
    "Picklist",
    "Long Text",
    "Email",
    "Currency",
    "Boolean",
    "Multiple Static Picklist",
  ];
  editItemForm: UntypedFormGroup;

  constructor(
    public dialogRef: MatDialogRef<EditDetailComponent>,
    private fb: UntypedFormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) public item: any
  ) {}

  ngOnInit(): void {
    this.InitialFormValue();
    this.conditionExpression = this.item.item.type;
    this.editItemForm.patchValue({
      description: this.item.item.description,
      dataType: this.item.item.type,
      value: this.item.item.value,
    });

    this.editItemForm.get("description").disable();

    var today = new Date();
    this.maxDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );

    this.updateValidation();
  }

  // change data type
  changeDatatype(event) {
    this.conditionExpression = event.value;
    this.editItemForm.controls["dataType"].setValue(event.value);
  }

  updateValidation() {
    switch (this.conditionExpression) {
      case "Picklist": {
        this.picklistValues = this.item?.item.defaultValue.split(",");
        break;
      }

      case "Email": {
        this.editItemForm.get("value").setValidators([Validators.email]);
        //this.picklistValues = this.item?.item.defaultValue.split(',');
        break;
      }
      case "Number":
        {
          this.editItemForm
            .get("value")
            .setValidators([Validators.maxLength(10)]);
          //this.picklistValues = this.item?.item.defaultValue.split(',');
          break;
        }

        Validators.minLength(5);

      default:
        break;
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }

  myFilter = (d: Date | null): boolean => {
    const day = (d || new Date()).getDay();
    // Prevent Saturday and Sunday from being selected.
    return day !== 0 && day !== 6;
  };

  InitialFormValue() {
    this.editItemForm = this.fb.group({
      description: ["", []],
      value: [""],
      dataType: [""],
    });
  }
  validateForm() {
    // if(!this.assetForm.valid) return true;
    // if (this.editItemForm.value.description?.trim().length == 0) return true
  }

  editItemToTemplate() {
    if (this.editItemForm.invalid) {
      this.editItemForm.controls["description"].setValue(
        this.editItemForm.value?.description?.trim()
      );
      this.editItemForm.markAllAsTouched();
      return;
    }
    let data = {
      id: this.item.item.id,
      description: this.item?.item?.description,
      type: this.editItemForm.value.dataType,
      value: this.editItemForm.value.value,
    };
    // this.service.addItem(data);
    // this.dialog.closeAll();
    this.dialogRef.close(data);
    //  this.dialogRef.close(true)
  }
}
