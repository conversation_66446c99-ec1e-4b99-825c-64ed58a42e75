<div class="create-asset-dialog">
  <div>
    <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
      <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <h2>{{"label.title.editItem"|literal}}</h2>
      </div>
      <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%">
        <!-- <button mat-button (click)="closeDialog()"> -->
        <mat-icon (click)="closeDialog()" class="pointer">close</mat-icon>
        <!-- </button> -->
      </div>
    </div>
  </div>

  <div>
    <form [formGroup]="editItemForm" novalidate class="mt-30">
      <ng-container>
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <!-- description  -->
            <mat-form-field class="width-100">
              <mat-label>Description</mat-label>
              <input class="width-100" disabled autocomplete="off" formControlName="description"
                name="description" matInput />
            </mat-form-field>
            <!-- data type -->
            <mat-form-field class="width-100">
              <mat-label>Data Type</mat-label>
              <mat-select disableRipple [disabled]="true" required
                (selectionChange)="changeDatatype($event)" formControlName="dataType">
                <mat-option *ngFor="let datatype of dataTypes" [value]="datatype">{{ datatype
                  }}</mat-option>
              </mat-select>
            </mat-form-field>

            <div [ngSwitch]="conditionExpression">
              <!-- text -->
              <div *ngSwitchCase="'Text'">
                <mat-form-field class="width-100">
                  <mat-label> Value</mat-label>
                  <input class="width-100" formControlName="value" name="value" matInput />
                </mat-form-field>
              </div>

              <!-- date -->
              <div *ngSwitchCase="'Date'">
                <mat-form-field class="example-full-width width-100">
                  <mat-label>Choose a date</mat-label>
                  <input class="width-100" matInput formControlName="value" [max]="maxDate"
                    name="value" [matDatepickerFilter]="myFilter" [matDatepicker]="picker" />
                  <mat-datepicker-toggle class="width-100" matSuffix
                    [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker class="width-100" #picker></mat-datepicker>
                </mat-form-field>

              </div>
              <div *ngSwitchCase="'Number'">

                <!-- number -->
                <div *ngSwitchCase="'Number'">
                  <mat-form-field class="width-100">
                    <mat-label> Value</mat-label>
                    <input type="number" class="width-100" formControlName="value" maxLength="10"
                      name="value" matInput />
                  </mat-form-field>
                </div>
              </div>

              <div *ngSwitchCase="'Picklist'">

                <!-- number -->
                <div *ngSwitchCase="'Picklist'">
                  <mat-form-field class="width-100">
                    <mat-label>Value</mat-label>
                    <mat-select disableRipple formControlName="value">
                      <mat-option *ngFor="let option of picklistValues" [value]="option">{{ option
                        }}</mat-option>
                    </mat-select>
                    <!-- <textarea formControlName="value" name="value" matInput></textarea> -->

                    <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
                  </mat-form-field>
                </div>
              </div>

              <div *ngSwitchCase="'Multiple Static Picklist'">

                <!-- number -->
                <div *ngSwitchCase="'Multiple Static Picklist'">
                  <mat-form-field class="width-100">
                    <mat-label>Value</mat-label>
                    <mat-select disableRipple formControlName="value">
                      <mat-option *ngFor="let option of picklistValues" [value]="option">{{ option
                        }}</mat-option>
                    </mat-select>
                    <!-- <textarea formControlName="value" name="value" matInput></textarea> -->

                    <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
                  </mat-form-field>
                </div>
              </div>

              <div *ngSwitchCase="'Alphanumeric'">

                <!-- alphanumeric -->
                <div *ngSwitchCase="'Alphanumeric'">
                  <mat-form-field class="width-100">
                    <mat-label>Value</mat-label>
                    <input class="width-100" formControlName="value" name="value" matInput />
                  </mat-form-field>
                </div>
              </div>
              <div *ngSwitchCase="'Boolean'">

                <!-- alphanumeric -->
                <div *ngSwitchCase="'Boolean'">
                  <mat-radio-group aria-label="Select an option" formControlName="value"
                    value="value">
                    <mat-radio-button color="primary" class="margin-right-3"
                      *ngFor="let dropDown of mandatory" [value]="dropDown">{{ dropDown
                      }}</mat-radio-button>

                  </mat-radio-group>
                </div>
              </div>
              <div *ngSwitchCase="'Email'">

                <!-- alphanumeric -->
                <div *ngSwitchCase="'Email'">
                  <mat-form-field class="width-100">
                    <mat-label>Value</mat-label>
                    <input type="email" class="width-100" formControlName="value" name="value"
                      matInput />
                  </mat-form-field>
                </div>
              </div>

              <div *ngSwitchCase="'Currency'">

                <!-- alphanumeric -->
                <div *ngSwitchCase="'Currency'">
                  <mat-form-field class="width-100">
                    <mat-label>Value</mat-label>
                    <input type="number" class="width-100" formControlName="value" name="value"
                      matInput />
                  </mat-form-field>
                </div>
              </div>

              <ng-container *ngSwitchCase="ZCP_DATA_TYPE.TITLE"></ng-container>


              <div *ngSwitchDefault>

                <mat-form-field class="width-100">
                  <mat-label> Value</mat-label>
                  <input class="width-100" formControlName="value" name="value" matInput />
                </mat-form-field>
              </div>
            </div>


            <br />
          </div>
        </div>
      </ng-container>
      <br />
      <div class="button-row">
        <button mat-raised-button class="green"
          (click)="editItemToTemplate()">{{"label.button.update"|literal}}</button>
      </div>
    </form>
  </div>
