import { ValidationErrorMessageService } from "./../../../shared-service/validation-error-message.service";
import { Component, Inject, OnInit, ViewChild } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreatePersonComponent } from "../../person-details/create-person/create-person.component";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { MatAccordion } from "@angular/material/expansion";
import JsonData from "src/assets/data.json";
import { DealService } from "src/app/shared-service/deal.service";
import { DatePipe } from "@angular/common";
import { ThemeService } from "src/app/theme.service";
import {
  FormSources,
  SourceInfo,
} from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { catchError, finalize, forkJoin, Observable, throwError } from "rxjs";
import { EntityType } from "src/app/common/models/entity.model";
import { WorkspaceService } from "src/app/workspace/workspace.service";

@Component({
  selector: "app-create-company",
  templateUrl: "./create-company.component.html",
  styleUrls: ["./create-company.component.scss"],
})
export class CreateCompanyComponent implements OnInit {
  @ViewChild("accordionRef") accordionRef: MatAccordion;

  createCompanyForm: FormGroup;
  extentionList: any = [];
  maxDate: any;
  entityItems;
  JsonData: any;
  user: any;
  companyName: FormControl;
  extension: FormControl;
  selectedCompanyExtensionId: any;
  showLoaderSpinner = false;
  selectedExtension: any;
  useNewThemeUI: any;
  sourceInfo: SourceInfo;
  entityList: any[];
  isLoading = true;

  constructor(
    private errorService: ErrorService,
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CreatePersonComponent>,
    private errorMessageService: ValidationErrorMessageService,
    private entityService: EntityService,
    public notificationMessage: ToasterService,
    private dealService: DealService,
    public dataSharingService: DataSharingService,
    public currencyFormatService: CurrencyFormatService,
    public themeService: ThemeService,
    @Inject(MAT_DIALOG_DATA) public data,
    public datePipe: DatePipe,
    private dataTypesUtils: DataTypesUtilsService,
    private workspaceService: WorkspaceService
  ) {
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
    this.selectedCompanyExtensionId = data.selectedCompanyExtensionId;
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.user = localStorage.getItem("user");
    const today = new Date();
    this.maxDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    forkJoin({
      entitiesResponse: this.entityService.getEntitiesDetails() as Observable<
        any[]
      >,
      extensionsResponse:
        this.entityService.getExtensionsDetails() as Observable<any[]>,
    })
      .pipe(
        finalize(() => (this.isLoading = false)),
        catchError((err) => {
          this.showLoaderSpinner = false;
          return throwError(() => err);
        })
      )
      .subscribe(({ entitiesResponse, extensionsResponse }) => {
        this.entityList = entitiesResponse
          ?.filter(
            (item) =>
              item.entityType.toLowerCase() ===
                EntityType.COMPANY.toLocaleLowerCase() &&
              item.status.toLowerCase() == "active"
          )
          .concat(
            extensionsResponse?.filter(
              (item) =>
                item.entityType.toLowerCase() ===
                  EntityType.COMPANY.toLocaleLowerCase() &&
                item.status.toLowerCase() == "active"
            )
          );

        this.entityService.basePlusCompanyExtensions = [...this.entityList];
        this.extentionList = structuredClone(this.entityList);
        this.changeExtension(this.selectedCompanyExtensionId);
      });
    this.companyName = new FormControl("", [
      Validators.required,
      Validators.pattern(this.errorMessageService.nameRegex),
    ]);
    this.extension = new FormControl("", [Validators.required]);
    this.sourceInfo = <SourceInfo>{ type: FormSources.Entity };
  }

  closeDialog() {
    this.dialogRef.close();
    this.createCompanyForm.reset();
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      group.addControl(this.getPropertyName(control), this.fb.control(""));
    });
    return group;
  }

  validate() {
    this.entityItems.forEach((element) => {
      if (
        element &&
        element[this.getPropertyName(element)].inputType !== "formly" &&
        this.errorMessageService.getValidation(
          element[this.getPropertyName(element)].displayProperty.mandatory,
          element[this.getPropertyName(element)].inputType,
          element[this.getPropertyName(element)]?.displayProperty.validation
        )
      ) {
        this.createCompanyForm.controls[
          this.getPropertyName(element)
        ].setValidators(
          this.errorMessageService.getValidation(
            element[this.getPropertyName(element)].displayProperty.mandatory,
            element[this.getPropertyName(element)].inputType,
            element[this.getPropertyName(element)]?.displayProperty.validation
          )
        );
        this.createCompanyForm.controls[
          this.getPropertyName(element)
        ].updateValueAndValidity();
      }
    });
  }

  changeExtension(changedExtensionId) {
    //this.createCompanyForm.reset();
    this.selectedCompanyExtensionId = changedExtensionId;
    this.selectedExtension = this.extentionList.find(
      (extension) => extension.id == changedExtensionId
    );
    this.entityItems =
      this.selectedExtension?.entityDetail?.entityDetail.filter(
        (element) =>
          element[this.getPropertyName(element)].displayProperty?.isForFormView
      );
    this.entityItems.forEach((element) => {
      //FormlyModel setting
      const key = Object.entries(element)[0][0];

      if (element[key].inputType === "Date") {
        if (element[key]?.displayProperty?.defaultValues == "Today") {
          element[key].value = new Date();
        }
      }

      if (element[key].inputType === "Date And Time") {
        if (
          element[key]?.displayProperty?.defaultValues ==
          "Today With Current Time"
        ) {
          const newtime = this.datePipe.transform(
            new Date(),
            "yyyy-MM-ddTHH:mm:ss"
          );
          element[key].value = newtime;
        } else if (element[key]?.displayProperty?.defaultValues == "") {
          const newtime = this.datePipe.transform(
            new Date(),
            "yyyy-MM-ddTHH:mm:ss"
          );
          element[key].value = newtime;
        }
      }
    });
    if (this.createCompanyForm) this.createCompanyForm.reset();
    this.dealService.allDealItems = this.entityItems;
    this.createCompanyForm = this.createGroup(this.entityItems);
    this.validate();
  }

  validateForm() {
    // if(!this.assetForm.valid) return true;
    if (this.createCompanyForm.value.companyName.trim().length == 0) {
      this.createCompanyForm.controls["companyName"].setValue(
        this.createCompanyForm.value?.companyName?.trim()
      );
      return true;
    }
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  createCompany() {
    this.createCompanyForm.markAllAsTouched();
    this.companyName.markAsTouched();
    this.extension.markAsTouched();
    if (
      this.createCompanyForm.invalid ||
      this.companyName.invalid ||
      this.extension.invalid
    ) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    this.setSourceInfo(this.entityItems);

    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(
        this.selectedExtension.entityDetail.entityDetail,
        this.createCompanyForm,
        true
      );

    const payload = {
      name: this.companyName.value,
      entityType: "Company",
      entityId: this.selectedCompanyExtensionId,
      customerDetails: {
        entityDetail: updatedVals,
      },
      entityLinkageList: updatedLinkage,
    };

    this.entityService.addCompany(payload).subscribe(
      (res: any) => {
        this.notificationMessage.success(JsonData["label.success.Company"]);
        this.entityService.selectedCompanyExtensionId =
          this.selectedCompanyExtensionId;
        this.createCompanyForm.reset();
        this.dialogRef.close(this.selectedCompanyExtensionId);
        if (this.data.isFromCard) {
          this.workspaceService.createdEntityId = res.customerId;
        }
        if (res && res?.infoList?.length > 0) {
          this.notificationMessage.infoList(
            "Warning:\n• " + res.infoList.join("\n• "),
            true
          );
        }
      },
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showLoaderSpinner = false;
      }
    );
  }

  setSourceInfo(fields) {
    if (!fields.find((e) => this.getPropertyName(e) == "sourceIsOriginate")) {
      const sourceIsOriginate = {
        name: "Source Is Originate",
        inputType: "Text",
        stages: [
          {
            section: [
              {
                order: 0,
                section: "Default",
                subsection: "",
                _hide: true,
              },
            ],
            isMasked: "",
            stageName: "Default",
            isSelected: "",
            isEncrypted: "",
            isMandatory: "",
          },
        ],
        displayProperty: {
          validation: "",
          displayName: "Source Is Originate",
          defaultValues: "",
          isForFormView: false,
          isForListView: false,
        },
        value: "Yes",
      };
      fields.push({ sourceIsOriginate: sourceIsOriginate });
    }
  }

  isShowOnForm(stageItem, inputType) {
    return (
      stageItem[this.getPropertyName(stageItem)].displayProperty
        ?.isForFormView &&
      stageItem[this.getPropertyName(stageItem)].inputType === inputType
    );
  }

  getTime(createdDate) {
    if (createdDate) {
      const oldDate: any = new Date(createdDate);
      const timeZoneOffset = new Date().getTimezoneOffset();
      const subbed = new Date(oldDate - timeZoneOffset * 60 * 1000);
      const time =
        subbed.getHours() +
        ":" +
        subbed.getMinutes() +
        ":" +
        subbed.getSeconds();
      return time;
    } else {
      return;
    }
  }
}
