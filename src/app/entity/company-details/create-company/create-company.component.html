<mat-dialog-content class="mat-dialog-content-form-custom-css">
  @if(isLoading) {
  <div class="spinner-center">
    <mat-spinner></mat-spinner>
  </div>
  }
  @else{
  <div class="create-asset-dialog">
    <div>
      <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
        <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <h2>{{"Create " + selectedExtension?.entityName}}</h2>
        </div>
        <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%" class="btnAlign">
          <button *ngIf="!useNewThemeUI" mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
          <button *ngIf="useNewThemeUI" mat-icon-button (click)="closeDialog()">
            <mat-icon aria-label="create-company-close-btn" class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <div class="createCompanyLoaderSection" *ngIf="showLoaderSpinner">
      <mat-spinner></mat-spinner>
    </div>
    <div class="form-container">
      <ng-container *ngIf="!showLoaderSpinner">
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <mat-form-field class="width-100 custom-mat-input-style">
              <mat-label>{{selectedExtension?.entityName + " Name"}}</mat-label>
              <input attr.aria-label="{{selectedExtension?.entityName +'-Name'}}" class="width-100"
                required [formControl]="companyName" matInput placeholder="" autocomplete="off" />
              <mat-error *ngIf="
                    companyName.touched &&
                    companyName.errors?.required
                  ">
                {{selectedExtension?.entityName+' Name is required'}}
              </mat-error>
              <mat-error *ngIf="companyName.hasError('pattern')">
                {{"label.materror.nameValidation" |literal}}
              </mat-error>

            </mat-form-field>
            <mat-form-field class="width-100 custom-mat-input-style">
              <mat-label>{{"label.field.companyType"|literal}}</mat-label>
              <mat-select disableRipple required (selectionChange)="changeExtension($event.value)"
                name="Company Type" [formControl]="extension"
                [(ngModel)]="selectedCompanyExtensionId">
                <mat-option *ngFor="let extension of extentionList" [value]="extension.id">{{
                  extension.entityName }}</mat-option>
              </mat-select>
              <mat-error *ngIf="
                    extension.touched &&
                    extension.errors?.required
                  ">
                {{"label.validation.companyType"|literal}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <data-types [sourceInfo]="sourceInfo"
          [parentData]="{gridConfig: {cellSpan:12,gridSpan:12,fieldWidthPercent:100,inFieldLabel:true,dialogValue:true},sectionData:{sectionName:'Default',stageItems:entityItems,subSections:'',sectionFieldsData :[{default:{subsectionItems:entityItems ,name:'default',hideRule: false,isHide:false}}]},currentStage:'Default',form:createCompanyForm}"></data-types>
      </ng-container>
    </div>
  </div>
  }
</mat-dialog-content>
<mat-card-footer>

  <div *ngIf="!themeService.useNewTheme" class="createCompanyCreateButton">
    <button attr.aria-label="create-{{selectedExtension?.entityName}}-button" mat-raised-button
      (click)="createCompany()" class="green" type="button">
      {{"label.button.create"|literal}}
    </button>
  </div>
  <div *ngIf="themeService.useNewTheme" class="createCompanyCreateButton">
    <button color="primary" attr.aria-label="create-{{selectedExtension?.entityName}}-button"
      mat-raised-button (click)="createCompany()" type="button">
      {{"label.button.create"|literal}}
    </button>
  </div>
</mat-card-footer>
