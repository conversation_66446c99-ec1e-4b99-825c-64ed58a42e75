import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { CompanyDetailsComponent } from "./company-details/company-details.component";
import { ViewCompanyComponent } from "./company-details/view-company/view-company.component";
import { DetailComponent } from "./company-details/view-company/detail/detail.component";
import { DocumentsComponent } from "./company-details/view-company/documents/documents.component";
import { PersonDetailsComponent } from "./person-details/person-details.component";
import { ViewPersonComponent } from "./person-details/view-person/view-person.component";
import { PersonDetailComponent } from "./person-details/view-person/person-detail/person-detail.component";
import { CompanyLinkedDealsComponent } from "./company-details/view-company/company-linked-deals/company-linked-deals.component";
import { AssociatedPersonsComponent } from "./company-details/view-company/associated-persons/associated-persons.component";
import { unsavedChangesGuard } from "../guard/unsaved-changes.guard";

const routes: Routes = [
  { path: "companies", component: CompanyDetailsComponent },
  {
    path: "viewcompany",
    component: ViewCompanyComponent,
    children: [
      { path: "", redirectTo: "detail", pathMatch: "full" },
      {
        path: "detail/:id",
        component: DetailComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      { path: "associated-deals/:id", component: CompanyLinkedDealsComponent },
      { path: "associated-persons/:id", component: AssociatedPersonsComponent },
      { path: "document", component: DocumentsComponent },
    ],
  },

  { path: "persons", component: PersonDetailsComponent },
  {
    path: "viewperson",
    component: ViewPersonComponent,
    children: [
      { path: "", redirectTo: "detail", pathMatch: "full" },
      {
        path: "detail/:id",
        component: PersonDetailComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      { path: "associated-deals/:id", component: CompanyLinkedDealsComponent },
      { path: "document", component: DocumentsComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EntityRoutingModule {}
