import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { SkipErrorHeaders } from "./shared.model";

@Injectable({
  providedIn: "root",
})
export class PageLayoutService {
  constructor(
    private http: HttpClient,
    @Inject("originateBaseUrl") private baseUrl: string,
    @Inject("tasksBaseUrl") private notificationUrl: string,
    @Inject("downloadFileURL") private dmsUrl: string
  ) {}

  getAllConfigurationDetails() {
    return this.http.get(`${this.baseUrl}/configuration-details`).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  getConfigurationDetailsByIdentifier(identifier) {
    const headers = new HttpHeaders({
      [SkipErrorHeaders["SKIP-400-ERROR"]]: "true",
    });

    return this.http
      .get(
        `${this.baseUrl}/configuration-details/configurable-identifier/${identifier}`,
        { headers }
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  updateConfigurationDetailsByIdentifier(id, data) {
    return this.http
      .put(`${this.baseUrl}/configuration-details/${id}`, data)
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  addConfigurationDetail(data) {
    return this.http.post(`${this.baseUrl}/configuration-details/`, data).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  getAllAutomatedReportData() {
    return this.http.get(`${this.baseUrl}/reports`).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  getSortedReport(sortBy, sortingKey) {
    return this.http
      .get(`${this.baseUrl}/reports/?sortBy=${sortBy}&sortingKey=${sortingKey}`)
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  getAutomatedReportById(id) {
    return this.http.get(`${this.baseUrl}/reports/${id}`).pipe(
      map((res) => {
        return res;
      })
    );
  }

  updateAutomatedReport(data) {
    return this.http.put(`${this.baseUrl}/reports/update`, data).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  createQueryReport(data) {
    return this.http.post(`${this.baseUrl}/reports/create`, data).pipe(
      map((res) => {
        return res;
      })
    );
  }

  getDeleteAutomatedReport(id) {
    return this.http.delete(`${this.baseUrl}/reports/${id}?mode=sync`).pipe(
      map((res) => {
        return res;
      })
    );
  }
}
