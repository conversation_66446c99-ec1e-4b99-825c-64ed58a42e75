import { Component, OnInit } from '@angular/core';
import { meetingSummaryTabs, meetingData } from '../dummy-data';
import { Router, ActivatedRoute } from '@angular/router';
import {Location} from '@angular/common'; 
import { PlannerService } from '../service/planner.service';
@Component({
  selector: 'app-meetings',
  templateUrl: './meetings.component.html',
  styleUrls: ['./meetings.component.scss']
})
export class MeetingsComponent implements OnInit {
  meetingSummaryTabs: any = meetingSummaryTabs;
  meetingsSourceList: any[] = [];
  meetingSourceId: any = 0;
  meetingDetails: any;
  showSummaryPage: boolean = true;
  activeSourceTab: any = 0;
  selectedSubTask : any = 'notes';
  selectedSourceName : any = '';
  showSubDealDetails : boolean = true
  days = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
  constructor(private router: Router, private route: ActivatedRoute ,
     private location: Location ,public plannerService : PlannerService) {
    this.route.paramMap.subscribe(params => {
      this.meetingSourceId = params.get('id');
      this.activeSourceTab = params.get('dealIndex');
    });

    if (this.meetingSourceId) this.getMeetingDetails(this.meetingSourceId)
  }

  ngOnInit() {
  }

  getMeetingDetails(id) {
    this.meetingDetails = meetingData.filter(ele => ele.id == this.meetingSourceId)[0]
    
    if (this.meetingDetails && this.meetingDetails.meetingSource) {
      this.meetingsSourceList = this.meetingDetails.meetingSource;
      this.getDealDetails( this.activeSourceTab)
      this.showSummaryPage = false;
    }
  }


  getActiveLink() {
    return this.activeSourceTab
  }

  getDealDetails(index) {
    this.activeSourceTab  = index;
    this.getSubDealDetails('' , true)
    this.selectedSourceName = this.meetingsSourceList[index].name
    this.showSubDealDetails = false
    this.getSubDealDetails('notes' , true)
  }

  getSubDealDetails(link , reload) {
       
    if(reload) setTimeout(function(){  }, 3000);
    this.selectedSubTask = link;
    this.router.navigate(['/planner/meetings/' +  this.meetingSourceId + '/' +  this.activeSourceTab + '/' + link ])
  }

  getDayName(date){
      let nowDate = new Date(date)
      return this.days[ nowDate.getDay() ];
  }
}
