<ng-container *ngIf="showSummaryPage">
  <div class="loaderInSideSummary">
    <mat-spinner></mat-spinner>
  </div>
</ng-container>

<ng-container *ngIf="!showSummaryPage" class="width-100">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="60%" fxFlex.md="70%" fxFlex.xs="100%" fxFlex.sm="100%">
      <h2 class="showSummaryPage">
        {{ meetingDetails.meetingSummary }}
      </h2>
    </div>
    <div fxFlex="22%" fxFlex.md="15%" fxFlex.xs="50%" fxFlex.sm="100%">
        <p class="meetingDate">
         Organizer : {{ meetingDetails.createdBy }}
    </p>
      </div>
      <div fxFlex="15%" fxFlex.md="15%" fxFlex.xs="50%" fxFlex.sm="100%">
          <p class="meetingDate">
            {{getDayName(meetingDetails.createdDate)}},  {{ meetingDetails.createdDate | date }}
      </p>
        </div>
  </div>

  <nav mat-tab-nav-bar>
    <ng-container *ngFor="let link of meetingsSourceList; let index=index">
      <a mat-tab-link routerLinkActive (click)="getDealDetails(index)" #rla1="routerLinkActive"
        [active]="index == this.activeSourceTab">
        {{link.name}}
      </a>
    </ng-container>


  </nav>

  <mat-card appearance="outlined" class="mat-card-top-border showSubDealDetails" *ngIf="!showSubDealDetails">

    <mat-card-content>
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="74%" fxFlex.md="74%" fxFlex.xs="100%" fxFlex.sm="100%">
          <h2 class="sourceName">
            {{ selectedSourceName }}
          </h2>
          <small class="card-title-font">{{this.meetingsSourceList[this.activeSourceTab].lead}}</small>
        </div>
      </div>
    </mat-card-content>

    <mat-divider></mat-divider>

    <nav mat-tab-nav-bar>
      <ng-container *ngFor="let sublink of meetingSummaryTabs">
        <a mat-tab-link [disabled]='sublink.isDisable' (click)="getSubDealDetails(sublink.link , false)" routerLinkActive
          #rla="routerLinkActive" [active]="sublink.link == this.selectedSubTask ">
          {{sublink.label}}
        </a>

      </ng-container>
    </nav>

    <ng-container >
      <router-outlet></router-outlet>
    </ng-container>
  </mat-card>
  <mat-card appearance="outlined" *ngIf="showSubDealDetails" class="mat-card-top-border meetingMatCard" >
    <ng-container >
      <mat-spinner></mat-spinner>
    </ng-container>
  </mat-card>
</ng-container>