import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MeetingsComponent } from './meetings.component';
import { SharedModuleModule } from 'src/app/shared-module/shared-module.module';
import { MeetingsRoutes } from './meetings.routing';
import { MeetingNotesComponent } from './meeting-notes/meeting-notes.component';
import { MeetingDetailsComponent } from './meeting-details/meeting-details.component';
import { NoteDetailsComponent } from './meeting-notes/note-details/note-details.component';
import { MeetingMinutesComponent } from './meeting-minutes/meeting-minutes.component';
import { MinuteDetailComponent } from './meeting-minutes/minute-detail/minute-detail.component';
import { MeetingDocumentsComponent } from './meeting-documents/meeting-documents.component';
import { MeetingTasksComponent } from './meeting-tasks/meeting-tasks.component';
import { TasksModule } from '../tasks/tasks.module';

@NgModule({
  imports: [
    CommonModule,
    SharedModuleModule,
    MeetingsRoutes,
    TasksModule
  ],
  declarations: [
    MeetingsComponent,
    MeetingNotesComponent,
    MeetingDetailsComponent,
    NoteDetailsComponent,
    MeetingMinutesComponent,
    MinuteDetailComponent,
    MeetingDocumentsComponent,
    MeetingTasksComponent
  ]
})
export class MeetingsModule { }
