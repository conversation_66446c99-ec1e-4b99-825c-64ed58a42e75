import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { meetingData } from '../../dummy-data';
import { UntypedFormControl, Validators } from '@angular/forms';

@Component({
  selector: 'app-meeting-notes',
  templateUrl: './meeting-notes.component.html',
  styleUrls: ['./meeting-notes.component.scss']
})
export class MeetingNotesComponent implements OnInit {
  allNotes : any = [];
  addNoteControl = new UntypedFormControl('', [Validators.required])
  meetingSourceId : any;
  activeSourceTab : any = 0;
  selectedNoteDetail : any = []
  showAddInput : boolean = false;
  selectedNoteIndex = 0;
  meetingDetails : any;
  userListInMeeting : any = []
  constructor(private router: Router, private route: ActivatedRoute ) {
   
  this.route.parent.params.subscribe(
      (params : Params) => {
        this.meetingSourceId = params.id;
        this.activeSourceTab = params['dealIndex'];
        this.selectedNoteIndex = 0;
        this.getNoteDetails(this.activeSourceTab)
      });
     
  }

  ngOnChanges(){
 
  
  }
  getNoteDetails(id){
    this.meetingDetails = meetingData.filter(ele => ele.id == this.meetingSourceId)[0];
    
    if (this.meetingDetails && this.meetingDetails.meetingSource) {
     this.allNotes = this.meetingDetails.meetingSource[id].meetingAgendaDetail;
     this.userListInMeeting = this.meetingDetails.userList;
     this.onNoteSelect(this.selectedNoteIndex)
    }
  }
  ngOnInit() {
    // this.allNotes = 
  }
  onClose(){
    this.showAddInput = false
  }
  onSubmit(){
    this.addNoteControl.markAsTouched()
    if(this.addNoteControl.invalid){
      return
    }
    let note = {
      agendaTitle : this.addNoteControl.value,
      agendaDetails :[]
    }
    this.allNotes.push(note);
    this.selectedNoteIndex = this.allNotes.length - 1;
    this.selectedNoteDetail = this.allNotes[this.allNotes.length - 1];
    this.showAddInput = false
  }

  onAdd(){
    this.addNoteControl.reset()
    this.showAddInput = true
  }
  onNoteSelect(i){
    this.selectedNoteIndex = i;
    this.selectedNoteDetail = this.allNotes[i]
  }
  onChangesReceived(event){
    
  }

  deleteNote(index){
    if(this.selectedNoteIndex == index) this.selectedNoteIndex = this.selectedNoteIndex -1;
    this.allNotes.splice(index, 1);
    this.onNoteSelect(this.selectedNoteIndex)
  }
}
