<div fxLayout="row wrap" fxLayoutGap="4px" class="margin-2-1">

  <div fxFlex="30%" fxFlex.md="40%" fxFlex.xs="100%" fxFlex.sm="100%">

    <div>
      <button *ngIf="!showAddInput" mat-icon-button class=" black addIcon " (click)="onAdd()" >
          <mat-icon>add</mat-icon>
      </button>
    </div>
    <div class="width-100" *ngIf="showAddInput">
      <div fxLayout="row wrap" fxLayoutGap="4px" class="position-relative width-100" >
        <div fxFlex="100%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
          

          <mat-form-field class="width-100">
              <mat-label>Agenda</mat-label>
              <input matInput [formControl]="addNoteControl" required >
              
              <button (click)="onSubmit()"  matSuffix mat-icon-button class="black">
                  <mat-icon>check </mat-icon>
                </button> 
             
              <button (click)="onClose()" matSuffix mat-icon-button class="black ">
                  <mat-icon>close</mat-icon>
              </button> 
              <mat-error *ngIf="addNoteControl.errors?.required">
                {{"label.materror.Agenda"|literal}}
          </mat-error>
            </mat-form-field>
            <mat-divider class="position-absolute"></mat-divider>
        </div>


      </div>
    </div>

  

    <ng-container *ngIf="allNotes.length != 0">
      <ng-container *ngFor="let note of allNotes; let i = index">
        <div  class="singleNoteContainer" [class.selectedNote]="i == selectedNoteIndex" >
          <p class="note pointer width-90"  (click)="onNoteSelect(i)">
            {{ note.agendaTitle }}
          </p>
          <button mat-icon-button class=" closeNoteIconCss" (click)="deleteNote(i)">
            <mat-icon >cancel</mat-icon>
          </button>
        </div>
        <mat-divider class="margin-0-5"></mat-divider>
      </ng-container>
    </ng-container>
  </div>
  <div fxFlex="3%" fxFlex.md="4%" fxFlex.xs="0%" fxFlex.sm="0%">
    <mat-divider classheight-100="true"></mat-divider>
  </div>
  <div fxFlex="66%" fxFlex.md="66%" fxFlex.xs="100%" fxFlex.sm="100%" >
    <app-note-details [userList]="userListInMeeting" [noteDetail]="selectedNoteDetail" (onEvents)='onChangesReceived($event)'></app-note-details>
  </div>
</div>