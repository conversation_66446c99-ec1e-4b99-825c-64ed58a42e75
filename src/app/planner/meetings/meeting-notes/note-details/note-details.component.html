<div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
  <div fxFlex="78%" fxFlex.md="80%" fxFlex.xs="90%" fxFlex.sm="90%">
    <h2 class="agendaTitle">{{this.noteDetail ?.agendaTitle}}</h2>
  </div>
  <div fxFlex="20%" fxFlex.md="10%" fxFlex.xs="90%" fxFlex.sm="90%" class="addIcon">
    <button mat-icon-button class=" black " (click)="newComment()">
      <mat-icon>add</mat-icon>
    </button>
    <button mat-raised-button class=" green ">
      SAVE
    </button>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutGap="4px" class="notesdivider">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="90%" fxFlex.sm="90%">
    <mat-divider></mat-divider>
  </div>
</div>

<ng-container *ngFor="let commentData of agendaDetails ; let commentIndex = index">
  <mat-card appearance="outlined" class="noteCard">

    <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
      <div fxFlex="94%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
        <mat-form-field class="width-100" appearance="outline">
          <mat-label>Note</mat-label>
          <textarea rows="3" cols="40" matInput [(ngModel)]='commentData.comment' [ngModelOptions]="{standalone: true}"></textarea>
        </mat-form-field>

      </div>
      <div class="matIconsInMeeting" fxFlex="5%" fxFlex.md="5%" fxFlex.xs="5%" fxFlex.sm="5%">
        <button mat-icon-button class="  black " (click)="onDelete('commentData' ,commentIndex , 0)">
          <mat-icon>cancel</mat-icon>
        </button>
      </div>

    </div>
    <div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
      <div fxFlex="20%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">
          <button mat-icon-button class=" black " (click)="openCreateActionDialog('commentData' , commentIndex)">
              <mat-icon class="pointer icon-white">
                  assignment_turned_in</mat-icon>
            </button>
        <button mat-icon-button class="black  ">
          <mat-icon class="pointer icon-white" (click)="openUploadDialog('commentData' , commentIndex, 0)">
              publish</mat-icon>
          
        </button>
        <button mat-icon-button class=" black " (click)="showAssigneeDialog('commentData' , commentIndex)">
          <mat-icon class="pointer icon-white">
            person_add</mat-icon>
        </button>
      </div>
      <div class="commentData" fxFlex="63%" fxFlex.md="56%" fxFlex.xs="20%" fxFlex.sm="20%">
        <ng-container *ngIf="commentData.assignedTo && (!showAssignedToInput || commentIndex != selectedCommentIndex)">
          <mat-chip-option class="documentListContainer">
            {{commentData.assignedTo}}
            <mat-icon class="displayInput" matChipRemove>close</mat-icon>
          </mat-chip-option>
        </ng-container>
        <ng-container *ngIf="showAssignedToInput && commentIndex == selectedCommentIndex">
         
          <mat-form-field class="width-60 " >
            
            <mat-select [(ngModel)]='commentData.assignedTo' [ngModelOptions]="{standalone: true}">
              <ng-container *ngFor="let user of userListInMeeting">
                <mat-option [value]="user.name">{{user.name}} </mat-option>
              </ng-container>
          
            </mat-select>
          </mat-form-field>
        </ng-container>
      </div>
      <div class="noteMinutes" fxFlex="12%" fxFlex.md="25%" fxFlex.xs="25%" fxFlex.sm="25%">

        <p> Minutes </p>
        <mat-checkbox  color="primary ml-5" [(ngModel)]='commentData.isCreateMinute'
          [ngModelOptions]="{standalone: true}">

        </mat-checkbox>

      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
        <div fxFlex="20%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
        </div>
      <div fxFlex="60%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
        
        <ng-container *ngIf="commentData?.documents?.length !=0">
          <mat-chip-listbox  class="doumentsChipList">
            <mat-chip-option *ngFor="let document of commentData.documents" class="documentListContainer">
              <mat-icon>attachment</mat-icon>
              {{document.name}}
              <mat-icon matChipRemove>close</mat-icon>
            </mat-chip-option>
          </mat-chip-listbox>
        </ng-container>
      </div>
    </div>
    <mat-card-content class="notesAction" *ngFor="let actionData of commentData.actions ; let actionIndex = index">
      <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
        <div fxFlex="94%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
          <h4 class="margin-1">Action</h4>
        </div>
        <div fxFlex="94%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
          <mat-form-field class="width-100" appearance="outline">
            <mat-label>Action</mat-label>
            <textarea rows="3" cols="40" matInput [(ngModel)]="actionData.description" [ngModelOptions]="{standalone: true}"></textarea>
          </mat-form-field>

        </div>
        <div class="matIconsInMeeting" fxFlex="5%" fxFlex.md="5%" fxFlex.xs="5%" fxFlex.sm="5%">
          <button mat-icon-button class="  black " (click)="onDelete('actionData',commentIndex ,actionIndex )">
            <mat-icon>cancel</mat-icon>
          </button>
        </div>

      </div>
      <div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
        <div fxFlex="14%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">
          <button mat-icon-button class="black  ">
            <mat-icon class="pointer icon-white" (click)="openUploadDialog('actionData' , commentIndex , actionIndex)">
                publish</mat-icon>
              
          </button>
          <button mat-icon-button class=" black " (click)="showAssigneeDialog('actionData', actionIndex)">
            <mat-icon class="pointer icon-white">
              person_add</mat-icon>
          </button>
        </div>
        <div class="commentData" fxFlex="63%" fxFlex.md="56%" fxFlex.xs="20%" fxFlex.sm="20%">

          <ng-container *ngIf="actionData.assignedTo && (!showAssignedToInputAction || actionIndex != selectedCommentIndex)">
            <mat-chip-option class="documentListContainer">
              {{actionData.assignedTo}}
              <mat-icon class="matChipRemove" matChipRemove>close</mat-icon>
            </mat-chip-option>
          </ng-container>
          <ng-container *ngIf="showAssignedToInputAction && actionIndex == selectedCommentIndex">
           
            <mat-form-field class="width-60" >
            
              <mat-select [(ngModel)]='actionData.assignedTo' [ngModelOptions]="{standalone: true}">
                <ng-container *ngFor="let user of userListInMeeting">
                  <mat-option [value]="user.name">{{user.name}} </mat-option>
                </ng-container>
            
              </mat-select>
            </mat-form-field>
          </ng-container>
        </div>
        
      </div>
      <div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
          <div fxFlex="14%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
            </div>
        <div fxFlex="60%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
           
          <ng-container *ngIf="actionData?.documents?.length !=0">
            <mat-chip-listbox  class="doumentsChipList">
              <mat-chip-option *ngFor="let files of actionData.documents" class="documentListContainer">
                  <mat-icon>attachment</mat-icon>
                {{files.name}}
                <mat-icon matChipRemove>close</mat-icon>
              </mat-chip-option>
            </mat-chip-listbox>
          </ng-container>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</ng-container>