import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { UploadPlannerDocumentsComponent } from '../../../dialogs/upload-planner-documents/upload-planner-documents.component';
import { MatDialog } from '@angular/material/dialog';
import { CreateActionComponent } from '../../../dialogs/createAction/createAction.component';

@Component({
  selector: 'app-note-details',
  templateUrl: './note-details.component.html',
  styleUrls: ['./note-details.component.scss']
})
export class NoteDetailsComponent implements OnInit {
  @Input() noteDetail: any;
  @Input() userList: any;
  @Output() onEvents = new EventEmitter();
  agendaDetails: any;
  showAssignedToInput : boolean = false;
  showAssignedToInputAction : boolean = false;
  selectedActionIndex = 0;
  selectedCommentIndex = 0;
  userListInMeeting : any = []
  constructor(public matDialog: MatDialog, ) { }

  ngOnInit() {

    this.agendaDetails = this.noteDetail ?.agendaDetails ;
    this.userListInMeeting = this.userList
  }

  ngOnChanges() {
   
    this.showAssignedToInput = false;
    this.showAssignedToInputAction = false;
    this.agendaDetails = this.noteDetail ?.agendaDetails;
    this.userListInMeeting = this.userList
  }

  onSubmitAssignee(sectionName){
    if(sectionName == 'commentData')   this.showAssignedToInput = false;
    if(sectionName == 'actionData') this.showAssignedToInputAction = false;
  }
  onCancelAssigneeTo(sectionName){
   if(sectionName == 'commentData')  this.showAssignedToInput = false;
   if(sectionName == 'actionData') this.showAssignedToInputAction = false;
  }

  openUploadDialog(sectionName , commentIndex , actionIndex) {
    const matDialogRef = this.matDialog.open(UploadPlannerDocumentsComponent, {
      autoFocus: false,
      width: '40%',
      data: {
        module: sectionName,
      },
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {
        
        if(sectionName == 'commentData'){
         
          this.agendaDetails[commentIndex].documents.unshift({name : result.selectedFileName})
        }
        if(sectionName == 'actionData'){
        
          this.agendaDetails[commentIndex].actions[actionIndex].documents.push({name : result.selectedFileName});
        }
      }
    })
  }

  newComment() {
    let data = {
      comment: "",
      documents: [

      ],
      assignedTo: "",
      isCreateMinute: false,
      actions: []
    }

    this.agendaDetails.unshift(data)
  }
  showAssigneeDialog(sectionName , index){
    if(sectionName == 'commentData') this.showAssignedToInput = !this.showAssignedToInput; this.selectedCommentIndex = index;
    if(sectionName == 'actionData') this.showAssignedToInputAction = !this.showAssignedToInputAction;this.selectedActionIndex = index;
  }

  openCreateActionDialog(data , index){
    // const matDialogRef = this.matDialog.open(CreateActionComponent, {
    //   autoFocus: false,
    //   width: '40%',
    //   data: {
    //     module: 'commentData',
    //     userList : this.userListInMeeting
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
        
    //     this.agendaDetails[index].actions.unshift(result)
    //   }
    // })
    let action =   {
      description : "",
      documents :[],
      assignedTo :"",
      isCreateMinute : false,
    }
    this.agendaDetails[index].actions.unshift(action)
  }

  onDelete(sectionName , commentIndex , actionIndex){
    if(sectionName == 'commentData'){ 
      this.agendaDetails.splice(commentIndex, 1);
    }
    if(sectionName == 'actionData'){
      this.selectedActionIndex = actionIndex;
      this.agendaDetails[commentIndex].actions.splice(actionIndex, 1);
    }
  }
}
