 .matIconsInMeeting{
  ::ng-deep .mat-icon {
    height: 22px !important;
    width: 22px !important;
  }
}


.matTextareaInMeeting{
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0.34375em !important; 
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    .mat-form-field-appearance-outline .mat-form-field-wrapper {
      margin: 0 !important;
  }
}

.documentListContainer{
  // padding: 5% 15%;
  font-size: 12px;
  color: #757575;
  background: no-repeat;
  border: 1px solid #c7c7c7;
}

.doumentsChipList{
  /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version.*/
  ::ng-deep .mat-chip-list-wrapper {
    margin : 0 !important
  }
}
.commentData{
  display: inline-flex;
  padding: 1% 0 !important;
}
.notesAction{
  padding: 0 0 0 2% !important;
}

.notesdivider{
  margin: 2% 0 5% !important;
  position:relative
}
.noteCard{
  margin: 0.5% !important;
}
.noteMinutes{
  display: inline-flex;
  padding: 0% 0 1% !important;
}
.matChipRemove{
  display: none;
}