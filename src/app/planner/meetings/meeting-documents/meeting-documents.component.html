<div class="actionBtnsContainer" fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="50%">
    <!-- <button mat-raised-button class="actionBtns green" (click)="openUploadTemplate(type)">CREATE DOCUMENT
        TEMPLATE</button> -->
  </div>
  <div fxFlex="29%" fxFlex.md="29%" fxFlex.xs="90%" fxFlex.sm="49%">
    <mat-form-field class="searchInput meetingSearchInput" >
      <mat-icon matSuffix>search</mat-icon>
      <input matInput (keyup)="applyFilter($event)" [(ngModel)]="searchKey" placeholder="Search Document" #input>
    </mat-form-field>
  </div>



</div>
<mat-card appearance="outlined" class="mat-elevation-z0">
  <!-- <mat-card-content class="documentsTitle">
  
  
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="70%">
          <h2 class="subTitle">Documents</h2>
        </div>
      </div>
  
      <hr />
    </mat-card-content> -->

  <mat-card-content>
    <div class=" mat-elevation-z0   mat-table-width task-table-container">

      <table mat-table [dataSource]="dataSource" matSort class="   mat-table-width height-300" >



        <!--  Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22">Document Name </th>
          <td mat-cell class=" " *matCellDef="let row"> {{row.fileName}} </td>
        </ng-container>



        <!--  Column -->
        <ng-container matColumnDef="created">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22"> Created Date</th>
          <td mat-cell class=" " *matCellDef="let row"> {{row.createdDate | date}} </td>
        </ng-container>






        <!-- Column -->
        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="w-12  "> </th>
          <td class=" " mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">
            <button mat-icon-button class="mat-icon-buttons-in-action-column gray">
              <!-- (click)="previewFile(element.fileName , element.dmsId , element.type)" -->
              <mat-icon class="pointer icon-white">
                remove_red_eye
              </mat-icon>
            </button>
            <!-- (click)="downloadFile(element.fileName , element.dmsId)" -->
            <button mat-icon-button class="mat-icon-buttons-in-action-column green">
              <mat-icon class="pointer icon-white">
                get_app
              </mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row class="textAlign" *matHeaderRowDef="displayedColumns;"></tr>
        <tr mat-row class="pointer textAlign" 
          *matRowDef="let row; columns: displayedColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
        <div *ngIf="!showNoRecordsAvailbleMessage">
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="4">No data matching the filter</td>
          </tr>
        </div>
      </table>



      <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <mat-paginator class="" [pageIndex]="pageIndex" [pageSizeOptions]="[8, 25,50, 100]" (page)="handlePage($event)" [pageSize]="50" >
        </mat-paginator>
      </div>
      <div class="no-records-found"  *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <!-- <mat-card appearance="outlined" class="no-record-card mat-elevation-z0"> No records found </mat-card> -->
      </div>
      <div *ngIf="showLoaderSpinner">
        <mat-spinner class="no-record-card ShowLoader" > No records found </mat-spinner>
      </div>
    </div>

  </mat-card-content>


</mat-card>