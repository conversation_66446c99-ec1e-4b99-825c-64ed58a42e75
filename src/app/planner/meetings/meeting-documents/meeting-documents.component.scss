.actionBtns {
    margin: 0 1% 0 0;
  }
  .actionBtnsContainer {
    margin: 1% 0 0;
    // ::ng-deep .mat-form-field-wrapper {
    //   padding-bottom: 0 !important;
    //   // background-color: white !important;
    // }
  
    // .mat-form-field-appearance-outline .mat-form-field-wrapper {
    //   margin: 0 !important;
    // }
  }
  
  .documentsTitle {
    margin-bottom: 0.5%;
  }
  
  .selectdocumentTypeList {
    font-size: 14px;
    font-weight: 500 !important;
    /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
    ::ng-deep .mat-select-value {
      max-width: fit-content;
      width: fit-content;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 1% !important;
    }
  }
  
  .selectDocumentListInput {
    font-size: 14px;
    font-weight: 500;
  }
  
  .w-17 {
    width: 17%;
  }
  
  .w-9 {
    width: 9%;
  }
  .w-12 {
    width: 12%;
  }
  
  .w-22 {
    width: 22%;
  }
  
  /* TODO(mdc-migration): The following rule targets internal classes of paginator that may no longer apply for the MDC version.*/
  .mat-paginator-center {
    display: flex;
    justify-content: center;
  }
  
  .subTitle {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
  
  .task-table-container{
  overflow: auto;
  }
  
  
  .pdfViewer{
    width: 100%;
    height:100%
  }
  
  iframe { background-color:white !important; }
  
  // {
  // ::ng-deep .searchInput .mat-form-field-infix {
    // padding: .5em 0;
    // border-top: 0.4375em solid transparent !important;
    // }
  // }
  