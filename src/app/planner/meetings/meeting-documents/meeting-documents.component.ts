import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { DomSanitizer } from '@angular/platform-browser';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { BusinessProcessService } from 'src/app/shared-service/businessProcess.service';
import { DownloadFileService } from 'src/app/shared-service/download-file.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { MatDialog } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { CreateTemplateDialogComponent } from 'src/app/settings/document-template-configuration/create-template-dialog/create-template-dialog.component';
import { FilePreviewComponent } from 'src/app/dialogs/file-preview/file-preview.component';
import { saveAs } from 'file-saver';
import { doucmentList } from '../../dummy-data';
import { ConfirmationDialogComponent } from 'src/app/dialogs/confirmation-dialog/confirmation-dialog.component';
import { ThemeService } from 'src/app/theme.service';
@Component({
  selector: 'app-meeting-documents',
  templateUrl: './meeting-documents.component.html',
  styleUrls: ['./meeting-documents.component.scss']
})
export class MeetingDocumentsComponent implements OnInit {

  type: any = ""
  viewDocument: boolean = false;

  selectedApplicationsData: any;
  dataSource: MatTableDataSource<any>;
  searchKey: any = ""
  businessProcessList: any[] = []
  pdfFileBaseURL: any = "";
  imageBaseURL: any;
  pageIndex: any = 0;
  private paginator: MatPaginator;
  @ViewChild(MatPaginator, { static: false }) set matPaginator(mp: MatPaginator) {
    this.paginator = mp;
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
    }
  }

  @ViewChild('pdfViewer') documentElement: ElementRef;

  @ViewChild(MatSort, { static: false }) sort: MatSort;
  displayedColumns: any[] = ["name", "created", "action"];
  showNoRecordsAvailbleMessage: boolean = false;
  showLoaderSpinner: boolean = true
  tableData = [
  ]
  constructor(public dom: DomSanitizer,
    private dataSharingService : DataSharingService,
    public matDialog: MatDialog,
    public themeService: ThemeService,
    public notificationMessage: ToasterService) {



  }

  ngOnInit() {

    this.getAllTheDocuments()

  }

  public handlePage(e: any) {

    this.pageIndex = e.pageIndex;
  }


  // ngAfterViewInit() {
  //   if (this.dataSource) {
  //     this.dataSource.paginator = this.paginator;
  //     this.dataSource.sort = this.sort;
  //   }
  // }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  refreshDataTable(filterdData) {
    let data = []

    data = filterdData

    this.dataSource = new MatTableDataSource(data);
    setTimeout(() => {
      this.dataSource.paginator = this.paginator;
    });
    this.dataSource.sort = this.sort;
    this.pageIndex = this.pageIndex;
    this.searchKey = ""
    this.showNoRecordsAvailbleMessage = true;

    if (data.length != 0) {
      data = [...data]

      this.dataSource = new MatTableDataSource(data);
      setTimeout(() => {
        this.dataSource.paginator = this.paginator;
      });
      this.dataSource.sort = this.sort;
      this.pageIndex = this.pageIndex;
      this.searchKey = ""
      this.showNoRecordsAvailbleMessage = false;

    }

  }

  getAllTheDocuments() {
    let data = doucmentList
    // this.dealService.getAllTemplateList().subscribe(res => {
      this.showLoaderSpinner = false
      this.refreshDataTable(data)
    // }, (error) => {
    //   this.showLoaderSpinner = false;
    //   this.showNoRecordsAvailbleMessage = true;
    // })
  }







  openDeleteDialog(row) {
    let buttonList;
    if (this.themeService.useNewTheme){ 
      buttonList =[
        {value:true , label :"Yes,Delete"},
        {value:false ,label:"Cancel"}
      ]
      }
    else { 
      buttonList =[
        {value:true , label :"DELETE", color:"red"},
        {value:false ,label:"CANCEL", color:"blue"}
      ]
    }
    let message ="Are you sure you want to delete this Template?"
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      data : {
        message : message,
        buttonList:buttonList
      },
    })
    matDialogRef.afterClosed().subscribe(result => {

      if (result) {
        this.deleteTemplate(row)
      }
    })
  }

  deleteTemplate(row) {
    // this.dealService.deleteTemplate(row.documentId).subscribe(res => {
    //   this.getAllTheTemplateDocuments()
    //   this.notificationMessage.success("Template deleted successfully.")
    // }, (err) => {

    // })
  }



  downloadFile(fileName, dmsId) {
    // this.downloadFileService.downloadTemplateFile(dmsId).subscribe((res: any) => {
    //   const blob = new Blob([res], { type: 'application/octet-stream' });
    //   const file = new File([blob], fileName, { type: 'application/octet-stream' });
    //   saveAs(file);
    //   this.notificationMessage.success('Template downloaded successfully.');

    // })
  }


  previewFile(fileName, dmsId, type) {


    // this.downloadFileService.filePreviewUrl(dmsId).subscribe((res: any) => {

    //   this.onFilePreview(res, fileName)

    // })

  }



  onFilePreview(URL, fileName) {

    const matDialogRef = this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '100%',
      width: '100%',
      data: {
        previewURLString: URL,
        fileName: fileName
      },
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {

      }
    })
  }

  domSanitize(src) {
    return this.dom.bypassSecurityTrustResourceUrl(src)
  }



}



