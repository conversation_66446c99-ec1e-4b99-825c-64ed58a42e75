import { Component, OnInit, ViewChild } from '@angular/core';
import { tasksTableColumnList, tasksStatusList } from '../../dummy-data';
import { TaskService } from '../../service/task.service';
import { Subscription } from 'rxjs';

import { MatDialog } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { TableViewTasksComponent } from '../../tasks/table-view-tasks/table-view-tasks.component';
import { ErrorService } from 'src/app/shared-service/error.service';
import JsonData from 'src/assets/data.json';

@Component({
  selector: 'app-meeting-tasks',
  templateUrl: './meeting-tasks.component.html',
  styleUrls: ['./meeting-tasks.component.scss']
})
export class MeetingTasksComponent implements OnInit {

  clickEventsubscription: Subscription;
  showTaskSpinner: boolean;
  JsonData:any
  allColumnsList = tasksTableColumnList;

  selectedStatus: any = 'Recently created';
  allTasks = [];
  selectedView: any = 'Table view';
  taskStatus = tasksStatusList;

  // @ViewChild('splitUI')
  // private splitUI: SplitViewComponent;

  @ViewChild('tableUI')
  private tableUI: TableViewTasksComponent;

  // @ViewChild('kanbanUI')
  // private kanbanUI: KanbanViewComponent;

  constructor(private matDialog: MatDialog,
    private errorService :ErrorService,
    public notificationMessage: ToasterService,
    public taskService: TaskService,
    private notification: ToasterService, ) {
    this.showTaskSpinner = true;

    this.getAllTaskList();
  }


  ngOnInit() {
    // this.taskService.refreshAllTasks.subscribe((refreshAllTasks) => {
    //   this.getAllTaskList();
    // });
  }

  init() {
    this.ngOnInit()
  }

//Handle child event
  onChangesReceived(data: any) {

    if (data.actionName === "addTaskUi") {
      this.openAddTaskDialog()
    }
    if (data.actionName === "editTask") {
      this.openEditTaskDialog(data.data)
    }
    if (data.actionName === "deleteTask") {
      this.openDeleteDialog(data.data)
    }

    if (data.actionName === "viewTask") {
      this.openViewTaskDialog(data.data);
      this.updateViewTime(data.data.taskId)
    }

    if (data.actionName === "updateViewTime") {
      // this.openViewTaskDialog(data.data);
      this.updateViewTime(data.data.taskId)
    }

    if (data.actionName === "updateTaskStatus") {
      this.updateTaskStatus(data.data['taskStatus'], data.data['taskId'])
    }

    if (data.actionName === "createEmail") {
      this.openEmailDialog()
    }
  }

  ngAfterViewInit() {

  }



  openAddTaskDialog() {
    // window.scroll(0,0)
    // const matDialogRef = this.matDialog.open(AddBasicTaskDialogComponent, {
    //   // width: '65%',
    //   // // height: '80vh',
    //   // panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.taskService.selectedFilter = "Recently created";
    //     this.getAllTaskList();
    //   }
    // })

  }

  /***
   * In follwing function open model as per application.
   * EmailDialogComponent : for monitor application;
   * CreateEmailComponent : this one is basic email model.
   */

  openEmailDialog(){
    // const matDialogRef = this.matDialog.open(EmailDialogComponent, {
    //   // width: '75%',
    //   // // height: '80vh',
    //   // panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     // this.getAllTaskList();
    //   }
    // })
  }






  getAllTaskList() {
    this.taskService.getTask().subscribe(res => {
      if (res) {
        this.allTasks = res;
        this.allTasks = [...this.allTasks];
        
        if (this.allTasks.length != 0) {
          this.showTaskSpinner = false;
        }
      }
    }, (err) => {
      this.showTaskSpinner = false;
    })
  }


  openEditTaskDialog(elementData) {
    // const matDialogRef = this.matDialog.open(EditTaskDialogComponent, {
    //   // width: '65%',
    //   // panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //     data: elementData
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.getAllTaskList();
    //   } else {
    //     if(this.splitUI){
    //       this.splitUI.onCancelFromEditDeleteAction();
    //     }
    //     if(this.tableUI){
    //       this.tableUI.onCancelFromEditDeleteAction();
    //     }
    //     if(this.kanbanUI){
    //       this.kanbanUI.onCancelFromEditDeleteAction();
    //     }
      
    //   }
    // })

  }


  openViewTaskDialog(elementData) {
    // const matDialogRef = this.matDialog.open(ViewTaskDialogComponent, {
    //   width: '70%',
    //   panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //     data: elementData
    //   },
    // })
    
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.getAllTaskList();
    //   }
    // })
  }





// delete task
  deleteTask(element) {
    this.taskService.deleteTask(element.taskId).subscribe(res => {
      this.getAllTaskList()
      this.notification.success(JsonData["label.success.DeleteTask"]);
   

    }, (error) => {
      let errors = this.errorService.ErrorHandling(error)
      this.notificationMessage.error(errors);
      this.showTaskSpinner = false;

      
    })

  

  }

  openDeleteDialog(element) {
    // const matDialogRef = this.matDialog.open(DeleteTaskComponent, {
    //   width: '35%',
    //   height: '25vh',
    //   panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //   },
    // })


    // const matDialogRef = this.matDialog.open(DeleteConfimationDialogComponent, {
    //   data: "Are you sure you want to delete this Task ?",
    //   // width : "25%",
    //   // height : '20vh'
    //  })

     
    // //  matDialogRef.afterClosed().subscribe(result => {
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.deleteTask(element)
    //   } else {
    //     if(this.splitUI){
    //       this.splitUI.onCancelFromEditDeleteAction();
    //     }
    //     if(this.tableUI){
    //       this.tableUI.onCancelFromEditDeleteAction();
    //     }
    //     if(this.kanbanUI){
    //       this.kanbanUI.onCancelFromEditDeleteAction();
    //     }
    //   }
    // })

  }


  updateTaskStatus(taskStatus, taskId) {
    this.taskService.updateTaskStatus(taskStatus, taskId).subscribe(res => {
     
      this.notification.success(JsonData["label.success.UpdateTask"]);
      this.taskService.setBooleanToTrue(true);
      this.getAllTaskList();
    }, (error) => {
     
      let errors = this.errorService.ErrorHandling(error)
      this.notificationMessage.error(errors);
      this.showTaskSpinner = false;

      
      this.getAllTaskList();
    })
  }

  updateViewTime(taskId) {
    this.taskService.updateViewTime(taskId).subscribe(res => {

      // this.notification.success('Task status updated successfully');
      this.getAllTaskList();
    }, (error) => {
      
      let errors = this.errorService.ErrorHandling(error)
      this.notificationMessage.error(errors);
      this.showTaskSpinner = false;

      
    })
  }



}
