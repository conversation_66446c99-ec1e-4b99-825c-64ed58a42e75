<div fxLayout="row wrap" fxLayoutGap="4px" class="margin-2-1">

  <div fxFlex="30%" fxFlex.md="40%" fxFlex.xs="100%" fxFlex.sm="100%">

   
    <div class="width-100" *ngIf="showAddInput">
      <div fxLayout="row wrap" fxLayoutGap="4px" class=" width-100 position-relative" >
        <div fxFlex="100%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
          

          <mat-form-field class="width-100">
              <mat-label>Agenda</mat-label>
              <input matInput [formControl]="addMinuteControl" required >
              
              <button (click)="onSubmit()"  matSuffix mat-icon-button class="black">
                  <mat-icon>check </mat-icon>
                </button> 
             
              <button (click)="onClose()" matSuffix mat-icon-button class="black ">
                  <mat-icon>close</mat-icon>
              </button> 
              <mat-error *ngIf="addMinuteControl.errors?.required">
                {{"label.materror.Agenda"|literal}}
                
          </mat-error>
            </mat-form-field>
            <mat-divider class="position-absolute"></mat-divider>
        </div>


      </div>
    </div>

  

    <ng-container *ngIf="allMinutes.length != 0">
      <ng-container *ngFor="let minute of allMinutes; let i = index">
        <div  class="singleMinuteContainer" [class.selectedMinute]="i == selectedMinuteIndex" >
          <p class="note pointer width-90"  (click)="onMinuteSelect(i)">
            {{ minute.agendaTitle }}
          </p>
          
        </div>
        <mat-divider class="margin-0-5"></mat-divider>
      </ng-container>
    </ng-container>
  </div>
  <div fxFlex="3%" fxFlex.md="4%" fxFlex.xs="0%" fxFlex.sm="0%">
    <mat-divider class="height-100" [vertical]="true"></mat-divider>
  </div>
  <div fxFlex="66%" fxFlex.md="66%" fxFlex.xs="100%" fxFlex.sm="100%" class="padding-0-1">
    <app-minute-detail [userList]="userListInMeeting" [minuteDetail]="selectedMinuteDetail" (onEvents)='onChangesReceived($event)'></app-minute-detail>
  </div>
</div>