import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { meetingData } from '../../dummy-data';

@Component({
  selector: 'app-meeting-minutes',
  templateUrl: './meeting-minutes.component.html',
  styleUrls: ['./meeting-minutes.component.scss']
})
export class MeetingMinutesComponent implements OnInit {

  allMinutes : any = [];
  addMinuteControl = new UntypedFormControl('', [Validators.required])
  meetingSourceId : any;
  activeSourceTab : any = 0;
  selectedMinuteDetail : any = []
  showAddInput : boolean = false;
  selectedMinuteIndex = 0;
  meetingDetails : any;
  userListInMeeting : any = []
  constructor(private router: Router, private route: ActivatedRoute ) {
   
  this.route.parent.params.subscribe(
      (params : Params) => {
        this.meetingSourceId = params.id;
        this.activeSourceTab = params['dealIndex'];
        this.selectedMinuteIndex = 0;
        this.getMinuteDetails(this.activeSourceTab)
      });
     
  }

  ngOnChanges(){
 
  
  }
  getMinuteDetails(id){
    this.meetingDetails = meetingData.filter(ele => ele.id == this.meetingSourceId)[0];
    
    if (this.meetingDetails && this.meetingDetails.meetingSource) {

     this.allMinutes =this.meetingDetails.meetingSource[id].meetingAgendaDetail;
     this.userListInMeeting = this.meetingDetails.userList;
     this.onMinuteSelect(this.selectedMinuteIndex)
    }
  }
  ngOnInit() {
    // this.allMinutes = 
  }
  onClose(){
    this.showAddInput = false
  }
  onSubmit(){
    this.addMinuteControl.markAsTouched()
    if(this.addMinuteControl.invalid){
      return
    }
    let Minute = {
      agendaTitle : this.addMinuteControl.value,
      agendaDetails :[]
    }
    this.allMinutes.push(Minute);
    this.selectedMinuteIndex = this.allMinutes.length - 1;
    this.selectedMinuteDetail = this.allMinutes[this.allMinutes.length - 1];
    this.showAddInput = false
  }

  onAdd(){
    this.addMinuteControl.reset()
    this.showAddInput = true
  }
  onMinuteSelect(i){
    this.selectedMinuteIndex = i;
    this.selectedMinuteDetail = this.allMinutes[i]
  }
  onChangesReceived(event){
    
  }

  deleteMinute(index){
    if(this.selectedMinuteIndex == index) this.selectedMinuteIndex = this.selectedMinuteIndex -1;
    this.allMinutes.splice(index, 1);
    this.onMinuteSelect(this.selectedMinuteIndex)
  }
}
