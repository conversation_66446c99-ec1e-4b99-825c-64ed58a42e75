<div fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="84%" fxFlex.md="80%" fxFlex.xs="90%" fxFlex.sm="90%">
    <h2 class="agendaTitle">{{this.minuteDetail ?.agendaTitle}}</h2>
  </div>
  <div fxFlex="15%" fxFlex.md="10%" fxFlex.xs="90%" fxFlex.sm="90%">
    <!-- <button mat-raised-button class=" green ">
      SAVE
    </button> -->
  </div>
</div>
<div fxLayout="row wrap" fxLayoutGap="4px" class="minuteDivider">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="90%" fxFlex.sm="90%">
    <mat-divider></mat-divider>
  </div>
</div>

<ng-container *ngFor="let commentData of agendaDetails ; let commentIndex = index">
  <div class="showSubDealDetails">

    <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting">
      <div fxFlex="10%" fxFlex.md="10%" fxFlex.xs="10%" fxFlex.sm="10%">
        <button *ngIf="commentData.assignedTo" matTooltip="Assignee - {{commentData.assignedTo}}" mat-mini-fab
          class="assignedToCss">
          {{getInitialsOfString(commentData.assignedTo)}}
        </button>
      </div>
      <div fxFlex="88%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
        <p>{{commentData.comment}}</p>
      </div>


    </div>
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="9%" fxFlex.md="10%" fxFlex.xs="10%" fxFlex.sm="10%" class="meetingButtons"></div>
     

      <div fxFlex="12%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
        <button mat-icon-button class="black  " (click)="showAddCommentEvent(commentIndex)">

          <mat-icon class="pointer icon-white"> question_answer</mat-icon>

        </button>
        <button mat-icon-button class=" black " (click)="openUploadDialog('commentData' , commentIndex , 0)">
          <mat-icon class="pointer icon-white">
            publish</mat-icon>
        </button>

      </div>

      <div fxFlex="76%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">

        <ng-container *ngIf="commentData?.documents?.length !=0">
          <mat-chip-listbox class="doumentsChipList">
            <mat-chip-option *ngFor="let document of commentData.documents" class="documentsListContainer">
              <mat-icon>attachment</mat-icon> &nbsp;
              {{document.name}}

            </mat-chip-option>
          </mat-chip-listbox>
        </ng-container>
      </div>


    </div>
    <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="showAddComment && commentIndex == selectedCommentIndex" class="matTextareaInMeeting minuteButtons" >
      <div fxFlex="8%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%"></div>
      <div fxFlex="79%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
        <mat-form-field class="width-100" appearance="outline">
          <mat-label>New comment</mat-label>

          <textarea rows="3" cols="40" [formControl]='description' matInput required ></textarea>

        </mat-form-field>
      </div>
      <div fxFlex="10%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
        <button mat-icon-button class="black  " (click)="addComment(commentIndex)">

          <mat-icon class="pointer icon-white">done</mat-icon>

        </button>
        <button mat-icon-button class=" black " (click)="closeAddComment('commentData' , commentIndex , 0)">
          <mat-icon class="pointer icon-white">cancel</mat-icon>
        </button>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
      <div fxFlex="7%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%"></div>
      <div fxFlex="84%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
        <mat-tab-group mat-align-tabs="start" [selectedIndex]="getSelectedIndex(commentIndex)" (selectedTabChange)="onTabChange($event)">
          <mat-tab label="Actions">
            <div class="md-2" *ngFor="let actionData of commentData?.actions ; let actionIndex = index">
              <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
                <!-- <div fxFlex="4%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%"></div> -->
                <div fxFlex="84%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
                  <h3 class="minuteAction">Action</h3>
                </div>

              </div>
              <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
                <div fxFlex="7%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%"></div>
                <div fxFlex="84%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">


                  <p>{{actionData.description}}</p>
                </div>
              </div>
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="10%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">

                </div>
                <div class="actionData" fxFlex="5%" fxFlex.md="56%" fxFlex.xs="20%" fxFlex.sm="20%"
                  *ngIf="actionData.assignedTo">

                  <ng-container>

                    <div matTooltip="Assignee - {{actionData.assignedTo}}" class="assigneesToActions">
                      <span class="assigneeName"> {{getInitialsOfString(actionData.assignedTo)}}</span>
                    </div>
                  </ng-container>

                </div>

                <div fxFlex="60%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                  class="customDescription">

                  <ng-container *ngIf="actionData?.documents?.length !=0">
                    <mat-chip-listbox class="doumentsChipList">
                      <mat-chip-option *ngFor="let files of actionData.documents" class="documentsListContainer">
                        <mat-icon>attachment</mat-icon> &nbsp;
                        {{files.name}}

                      </mat-chip-option>
                    </mat-chip-listbox>
                  </ng-container>
                </div>
              </div>

            </div>
          </mat-tab>
          <mat-tab label="Comments">
            <div class="md-2" *ngFor="let minutesComment of commentData?.minutesComments ;">
              <div fxLayout="row wrap" fxLayoutGap="4px" class="matTextareaInMeeting minuteButtons" >
                <!-- <div fxFlex="7%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%"></div> -->
                <div fxFlex="84%" fxFlex.md="94%" fxFlex.xs="94%" fxFlex.sm="94%">
                  <span  class="pointer hyperlinkColor selectedRole">{{minutesComment.createdBy}}</span> added a comment - {{minutesComment.createdDate | date}}
                  <p>{{minutesComment?.commentDescription}}</p>
                </div>
              </div>
              <mat-divider></mat-divider>
            </div>
          </mat-tab>

        </mat-tab-group>
      </div>
    </div>





  </div>
  <mat-divider *ngIf="agendaDetails.length > 1"  class="position-relative"></mat-divider>
</ng-container>