import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { UploadPlannerDocumentsComponent } from 'src/app/planner/dialogs/upload-planner-documents/upload-planner-documents.component';
import { CreateCommentInMinuteComponent } from 'src/app/planner/dialogs/create-comment-in-minute/create-comment-in-minute.component';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { UntypedFormControl } from '@angular/forms';

@Component({
  selector: 'app-minute-detail',
  templateUrl: './minute-detail.component.html',
  styleUrls: ['./minute-detail.component.scss']
})
export class MinuteDetailComponent implements OnInit {

  @Input() minuteDetail: any;
  @Input() userList: any;
  @Output() onEvents = new EventEmitter();
  showAddComment : boolean = false
  agendaDetails: any;
  showAssignedToInput: boolean = false;
  showAssignedToInputAction: boolean = false;
  selectedActionIndex = 0;
  selectedCommentIndex = 0;
  userListInMeeting: any = []
  currentTabIndex: number = 0;
  description = new UntypedFormControl();

  constructor(public matDialog: MatDialog, ) { }

  ngOnInit() {
    let agendaList = this.minuteDetail ?.agendaDetails;
    this.agendaDetails = agendaList ?.filter(ele => ele.isCreateMinute) ;
    this.userListInMeeting = this.userList
  }

  ngOnChanges() {

    this.showAssignedToInput = false;
    this.showAssignedToInputAction = false;
    let agendaList = this.minuteDetail ?.agendaDetails;
    this.agendaDetails = agendaList ?.filter(ele => ele.isCreateMinute) ;
    this.userListInMeeting = this.userList
  }

  onSubmitAssignee(sectionName) {
    if (sectionName == 'commentData') this.showAssignedToInput = false;
    if (sectionName == 'actionData') this.showAssignedToInputAction = false;
  }
  onCancelAssigneeTo(sectionName) {
    if (sectionName == 'commentData') this.showAssignedToInput = false;
    if (sectionName == 'actionData') this.showAssignedToInputAction = false;
  }

  openUploadDialog(sectionName, commentIndex, actionIndex) {
    const matDialogRef = this.matDialog.open(UploadPlannerDocumentsComponent, {
      autoFocus: false,
      width: '40%',
      data: {
        module: sectionName,
      },
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {

        if (sectionName == 'commentData') {

          this.agendaDetails[commentIndex].documents.unshift({ name: result.selectedFileName })
        }
        if (sectionName == 'actionData') {

          this.agendaDetails[commentIndex].actions[actionIndex].documents.push({ name: result.selectedFileName });
        }
      }
    })
  }

  newComment() {
    let data = {
      comment: "",
      documents: [

      ],
      assignedTo: "",
      isCreateMinute: false,
      actions: []
    }

    this.agendaDetails.unshift(data)
  }
  showAssigneeDialog(sectionName, index) {
    if (sectionName == 'commentData') this.showAssignedToInput = !this.showAssignedToInput; this.selectedCommentIndex = index;
    if (sectionName == 'actionData') this.showAssignedToInputAction = !this.showAssignedToInputAction; this.selectedActionIndex = index;
  }

  openCreateActionDialog(data, index) {

    let action = {
      description: "",
      documents: [],
      assignedTo: "",
      isCreateMinute: false,
    }
    this.agendaDetails[index].actions.unshift(action)
  }

  showAddCommentEvent(index){
    this.selectedCommentIndex = index
    this.showAddComment = true;
    this.description.reset()
  }

  addComment(index) {
  
    // const matDialogRef = this.matDialog.open(CreateCommentInMinuteComponent, {
    //   autoFocus: false,
    //   width: '40%',
    //   data: {
    //     module: 'commentData',
    //     userList: this.userListInMeeting
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {

      let data =   {
   
        commentDescription  :  this.description.value ,
        createdBy : localStorage.getItem('user'),
        createdDate : new Date()
      }

       if(this.agendaDetails[index].minutesComments){
        this.agendaDetails[index].minutesComments.unshift(data);
       }else{
        this.agendaDetails[index].minutesComments = [];
        this.agendaDetails[index].minutesComments.unshift(data);
       }
        this.currentTabIndex = 1;
        this.showAddComment = false;
    //   }
    // })
  }
  closeAddComment(sectionName, commentIndex, actionIndex){
    this.description.reset()
    this.showAddComment = false;

  }

  onDelete(sectionName, commentIndex, actionIndex) {
    if (sectionName == 'commentData') {
      this.agendaDetails.splice(commentIndex, 1);
    }
    if (sectionName == 'actionData') {
      this.selectedActionIndex = actionIndex;
      this.agendaDetails[commentIndex].actions.splice(actionIndex, 1);
    }
  }


  getInitialsOfString(string: any) {

    if (string) {
      return string.trim().match(/\b(\w)/g).join('').toUpperCase();
    } else {
      return
    }
  }


  getSelectedIndex(index): number {
    return this.currentTabIndex
  }

  onTabChange(event: MatTabChangeEvent) {
    // this.currentTabIndex = event.index
  }
}

