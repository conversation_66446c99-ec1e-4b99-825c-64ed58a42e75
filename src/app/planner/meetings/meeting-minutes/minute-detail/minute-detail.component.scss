.matIconsInMeeting{
    ::ng-deep .mat-icon {
      height: 22px !important;
      width: 22px !important;
    }
  }
  
  
  .matTextareaInMeeting{
      /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
      ::ng-deep .mat-form-field-wrapper {
          padding-bottom: 0.34375em !important; 
      }
      /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
      .mat-form-field-appearance-outline .mat-form-field-wrapper {
        margin: 0 !important;
    }
  }
  
  .documentsListContainer{
    // padding: 5% 15%;
    font-size: 12px;
    color: #757575;
    background: no-repeat;
    // border: 1px solid #c7c7c7;
  }
  
  .doumentsChipList{
    /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version.*/
    ::ng-deep .mat-chip-list-wrapper {
      margin : 0 !important
    }
  }

  .assigneesToActions{
    width: 30px;
    height: 30px;
    background: #00000066;
    color: white;
    border-radius: 50%;
    position: relative
  }

  .assigneeName{
    margin: 22%;
    align-self: center;
    /* top: -4%; */
    position: absolute;
    font-size: 12px;
    font-weight: 400;
  }


  .assignedToCss{
    background:#00000066 !important; box-shadow: none;  color: white;
    // margin: 2% 0 !important
  }
  
  .minuteDivider{
    margin: 2% 0 5% !important;
     position:relative
  }
  .minuteAction{
    margin:1% 0 !important;
  }
  .actionData{
    display: inline-flex;
     padding: 1% 0 !important;
  }