p{
    margin: 0;
}

.p-title-2{
    padding: 4% 2%;
    font-size: 14px;
    font-weight: 400;
}

.p-title-edit-2{
    padding: 2.5% 2%;
    font-size: 14px;
    font-weight: 400;
}

.documentsListContainer{
    // padding: 5% 15%;
    font-size: 12px;
    color: #757575;
    background: white;
    border: 1px solid #c7c7c7;
  }

  .doumentsChipList{
    /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version.*/
    ::ng-deep .mat-chip-list-wrapper {
      margin : 0 !important
    }
  }
  

.matIconsInMeeting{
    ::ng-deep .mat-icon {
      height: 22px !important;
      width: 22px !important;
    }
  }
  .meetingDiv{
    margin: 1% 1% !important;
  }
  .meetingDetails{
    font-size: 20px !important;
    margin: 0 3% !important;
  }
  .matDivider{
    position: relative !important;
  }