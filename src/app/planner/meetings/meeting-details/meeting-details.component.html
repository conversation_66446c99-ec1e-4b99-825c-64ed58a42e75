<div fxLayout="row wrap" fxLayoutGap="4px" class="meetingDiv">
 
  <div fxFlex="98%" fxFlex.md="98%" fxFlex.xs="90%" fxFlex.sm="90%">



    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Mode</p>
      </div>
      <div fxFlex="30%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%"
        class="p-title-edit-2 customDescription">
        <ng-container *ngIf="isEditMode">
          <p>{{meetingDetails?.meetingMode }}</p>
          <mat-icon class="pointer meetingDetails" [inline]="true"  (click)="onEdit(false , 'mode')">edit</mat-icon>
          <!-- </button> -->

        </ng-container>
        <ng-container *ngIf="!isEditMode">
          <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">

            <mat-select [(ngModel)]="meetingDetails.meetingMode" [ngModelOptions]="{standalone: true}">
              <mat-option value="Virtual Meeting">Virtual Meeting</mat-option>
              <mat-option value="In-person meeting">In-person Meeting</mat-option>
              <mat-option value="Document sent over email">Document sent over email</mat-option>
            </mat-select>
            <button matSuffix mat-icon-button class="  black " (click)="onEdit(true , 'mode')">
              <mat-icon>done</mat-icon>
            </button>
          </mat-form-field>

        </ng-container>
      </div>
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Date</p>
      </div>
      <div fxFlex="30%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p> {{meetingDetails?.dueDate  | date}}</p>
      </div>
    </div>
    <mat-divider class="position-relative"></mat-divider>

    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Agenda</p>
      </div>
      <div fxFlex="60%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">

        <ng-container *ngFor="let agenda of allAgendaList">
          <p class="margin-bottom-2">{{agenda.agendaTitle}}</p>
        </ng-container>


      </div>
    </div>
    <mat-divider class="position-relative"></mat-divider>

    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Attendees</p>
      </div>
      <div fxFlex="30%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <ng-container *ngIf="meetingDetails.meetingMode != 'In-person meeting'">
          <ng-container *ngFor="let user of userListInMeeting">
            <p *ngIf="user.isAttended" class="margin-bottom-2">{{user.name}}</p>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="meetingDetails.meetingMode == 'In-person meeting'">
          <div>
            <ng-container *ngIf="meetingDetails.meetingMode == 'In-person meeting' && isEditMode">
              <button mat-icon-button class=" black " *ngIf="isAddAttendee" (click)="onEdit(false , 'addUser')">
                <mat-icon>add</mat-icon>
              </button>
              <ng-container *ngIf="!isAddAttendee">

                <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">

                  <mat-select>
                    <ng-container *ngFor="let user of userListInMeeting">
                      <mat-option [value]="user.name">{{user.name}}</mat-option>
                    </ng-container>
                  </mat-select>
                  <button matSuffix mat-icon-button class="black " (click)="onEdit(true , 'addUser')">
                    <mat-icon>done</mat-icon>
                  </button>
                </mat-form-field>


              </ng-container>
            </ng-container>
          </div>

          <mat-chip-listbox class="doumentsChipList">
            <ng-container *ngFor="let user of userListInMeeting">

              <mat-chip-option *ngIf="user.isAttended" class="documentsListContainer">

                {{user.name}}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip-option>
            </ng-container>
          </mat-chip-listbox>


        </ng-container>

      </div>
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Apologies</p>
      </div>
      <div fxFlex="30%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <ng-container *ngIf="meetingDetails.meetingMode != 'In-person meeting'">
          <ng-container *ngFor="let user of userListInMeeting">
            <p *ngIf="!(user.isAttended)" class="margin-bottom-2">{{user.name}}</p>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="meetingDetails.meetingMode == 'In-person meeting'">
          <div>
            <ng-container *ngIf="meetingDetails.meetingMode == 'In-person meeting' && isEditMode">
              <button mat-icon-button class=" black " *ngIf="isAddApologies" (click)="onEdit(false , 'addApologies')">
                <mat-icon>add</mat-icon>
              </button>
              <ng-container *ngIf="!isAddApologies">

                <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">

                  <mat-select>
                    <ng-container *ngFor="let user of userListInMeeting">
                      <mat-option [value]="user.name">{{user.name}}</mat-option>
                    </ng-container>
                  </mat-select>
                  <button matSuffix mat-icon-button class="black " (click)="onEdit(true , 'addApologies')">
                    <mat-icon>done</mat-icon>
                  </button>
                </mat-form-field>


              </ng-container>
            </ng-container>
          </div>
          <mat-chip-listbox class="doumentsChipList">
            <ng-container *ngFor="let user of userListInMeeting">
              <mat-chip-option *ngIf="!(user.isAttended)" class="documentsListContainer">

                {{user.name}}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip-option>
            </ng-container>

          </mat-chip-listbox>

        </ng-container>
      </div>
    </div>
    <mat-divider class="matDivider"></mat-divider>



    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="19%" fxFlex.md="30%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2">
        <p class="font-weight-500">Chair's Report</p>
      </div>
      <div>


      </div>
      <div fxFlex="62%" fxFlex.md="88%" fxFlex.xs="90%" fxFlex.sm="90%" class="p-title-edit-2 padding-right-0"
       >

        <mat-form-field class="width-100" appearance="outline">
          <textarea [disabled]="isEditReport" rows="3" cols="40" matInput [(ngModel)]="meetingDetails.chairsReport"
            [ngModelOptions]="{standalone: true}"></textarea>
        </mat-form-field>


      </div>
      <div class="matIconsInMeeting meetingButtons "  fxFlex="5%" fxFlex.md="5%" fxFlex.xs="5%"
        fxFlex.sm="5%">
        <button *ngIf="isEditReport" mat-icon-button class="  black " (click)="onEdit(false , 'report')">
         <span class="material-symbols-outlined">
edit
</span>
        </button>
        <button *ngIf="!isEditReport" mat-icon-button class="  black " (click)="onEdit(true , 'report')">
          <mat-icon>done</mat-icon>
        </button>
        <button mat-icon-button class="  black ">
          <mat-icon>cancel</mat-icon>
        </button>
      </div>
    </div>

  </div>
</div>