import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { meetingData } from '../../dummy-data';
import { DataSharingService } from 'src/app/common/dataSharing.service';

@Component({
  selector: 'app-meeting-details',
  templateUrl: './meeting-details.component.html',
  styleUrls: ['./meeting-details.component.scss']
})
export class MeetingDetailsComponent implements OnInit {
  meetingSourceId: any;
  activeSourceTab: any;
  meetingDetails: any;
  userListInMeeting: any = [];
  isEditReport: boolean = true;
  isEditMode: boolean = true;
  allAgendaList: any = [];
  isEditAttendees: boolean = true;
  isAddAttendee: boolean = true;
  isAddApologies: boolean = true;
  constructor(private router: Router, private route: ActivatedRoute,
   private dataSharingService:DataSharingService) {
    this.route.parent.params.subscribe(
      (params: Params) => {
        this.meetingSourceId = params.id;
        this.activeSourceTab = params['dealIndex'];
        this.getMeetingDetails(this.activeSourceTab)
      });
  }

  ngOnInit() {
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str,50);
  }

  getMeetingDetails(id) {
    this.meetingDetails = meetingData.filter(ele => ele.id == this.meetingSourceId)[0];

    if (this.meetingDetails && this.meetingDetails.meetingSource) {
      this.allAgendaList = this.meetingDetails.meetingSource[id].meetingAgendaDetail;
      this.userListInMeeting = this.meetingDetails.userList;

    }
  }

  onEdit(isEdit, fieldName) {
    if (fieldName == 'report') {
      this.isEditReport = isEdit
    }
    if (fieldName == 'mode') {
      this.isEditMode = isEdit;

    }
    if (fieldName == 'addUser') {
      
      this.isAddAttendee = isEdit;
      
    }
    
    if (fieldName == 'addApologies') {
      
      this.isAddApologies = isEdit;
      
    }
  }


  getUserNames(userList) {
    let names = userList ?.map(ele => ele.name) + "";
    return names
  }

  getAgendaList(list) {
    let names = list ?.map(ele => ele.agendaTitle) + "";
    return names
  }



}
