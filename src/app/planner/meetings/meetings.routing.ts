import { Routes, RouterModule } from '@angular/router';
import { MeetingsComponent } from './meetings.component';
import { MeetingNotesComponent } from './meeting-notes/meeting-notes.component';
import { MeetingDetailsComponent } from './meeting-details/meeting-details.component';
import { MeetingMinutesComponent } from './meeting-minutes/meeting-minutes.component';
import { MeetingDocumentsComponent } from './meeting-documents/meeting-documents.component';
import { MeetingTasksComponent } from './meeting-tasks/meeting-tasks.component';

const routes: Routes = [
  { path : '' , component : MeetingsComponent ,
   children : [
    { path : 'notes' , component : MeetingNotesComponent},
    { path : 'details' , component : MeetingDetailsComponent},
    { path : 'minutes' , component : MeetingMinutesComponent},
    { path : 'documents' , component : MeetingDocumentsComponent},
    { path : 'tasks' , component : MeetingTasksComponent}
   ]
  },
  
];

export const MeetingsRoutes = RouterModule.forChild(routes);
