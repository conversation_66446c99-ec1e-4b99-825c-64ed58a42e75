<h1>{{this.getSidebarItembyName('Planner')}}</h1>
<mat-divider></mat-divider>
<div>
    <nav mat-tab-nav-bar [tabPanel]="tabPanel" class="plannerNavBar">
      <a
        mat-tab-link
        *ngFor="let link of plannerTabs"
        [disabled]="!link?.link"
        [routerLink]="link.link"
        routerLinkActive
        #rla="routerLinkActive"
        [active]="rla.isActive"
      >
        {{ link.label }}
      </a>
    </nav>
  </div>
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-tab-nav-panel #tabPanel>
        <router-outlet></router-outlet>
      </mat-tab-nav-panel>
    </div>
  </div>
  
  