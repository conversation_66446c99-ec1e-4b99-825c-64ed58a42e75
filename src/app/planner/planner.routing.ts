import { Routes, RouterModule } from '@angular/router';
import { ListViewComponent } from './list-view/list-view.component';
import { NotificationRecordComponent } from '../notification-record/notification-record.component';
import { PlannerComponent } from './planner.component';

const routes: Routes = [
  { path : '' , component : PlannerComponent,
  children : [
  { path : '' , pathMatch : 'full' , redirectTo : 'tasks' },
  { path : 'tasks' , loadChildren: () => import('../task/task.module').then(m => m.TaskModule)},
  { path : 'notifications' , component:NotificationRecordComponent},
  { path : 'meetings/:id/:dealIndex' , loadChildren: () => import('./meetings/meetings.module').then(m => m.MeetingsModule)},
  { path : 'meetings/:id' , loadChildren: () => import('./meetings/meetings.module').then(m => m.MeetingsModule)},
  { path : 'meetings' , component:ListViewComponent},
  ]}
];

export const PlannerRoutes = RouterModule.forChild(routes);
