import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { PlannerComponent } from "./planner.component";
import { PlannerRoutes } from "./planner.routing";
import { ListViewComponent } from "./list-view/list-view.component";
import { CreateMeetingComponent } from "./dialogs/create-meeting/create-meeting.component";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { ContextualCreateMeetingComponent } from "./contextual-planner/dialogs/contextual-create-meeting/contextual-create-meeting.component";
import { UploadPlannerDocumentsComponent } from "./dialogs/upload-planner-documents/upload-planner-documents.component";
import { CreateActionComponent } from "./dialogs/createAction/createAction.component";
import { CreateCommentInMinuteComponent } from "./dialogs/create-comment-in-minute/create-comment-in-minute.component";
// import { NotificationRecordComponent } from "../notification-record/notification-record.component";

@NgModule({
  imports: [CommonModule, PlannerRoutes, SharedModuleModule],
  declarations: [
    PlannerComponent,
    ListViewComponent,
    CreateMeetingComponent,
    ContextualCreateMeetingComponent,
    UploadPlannerDocumentsComponent,
    CreateActionComponent,
    CreateCommentInMinuteComponent,
    // NotificationRecordComponent,
  ],
})
export class PlannerModule {}
