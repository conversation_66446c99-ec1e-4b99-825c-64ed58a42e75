import { Component, OnInit, Output, EventEmitter, Input, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ApplicationLabelService } from 'src/app/shared-service/application-label.service';
import { BusinessProcessService } from 'src/app/shared-service/businessProcess.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { DatePipe } from '@angular/common';
import { NewCustomerComponent } from 'src/app/application/application/new-customer/new-customer.component';
import { CreateMeetingComponent } from '../dialogs/create-meeting/create-meeting.component';
import { PlannerService } from '../service/planner.service';
import { ContextualCreateMeetingComponent } from '../contextual-planner/dialogs/contextual-create-meeting/contextual-create-meeting.component';
import { meetingData } from '../dummy-data';
import { DashboardHeaderComponent } from 'src/app/dashboard/dashboard-header/dashboard-header.component';
@Component({
  selector: 'app-list-view',
  templateUrl: './list-view.component.html',
  styleUrls: ['./list-view.component.scss']
})
export class ListViewComponent implements OnInit {



  showLoader: any = true;


  data: any;
  counter: number = 0;
  listOfMeetings: any = [];
  showNoDataMessage: any = false;
  noDataMessage: string;
  dataSource: MatTableDataSource<any>;
  showNoRecordsAvailbleMessage: boolean = false;
  showLoaderSpinner: boolean = true;
  searchKey: any = "";
  @ViewChild(MatPaginator) paginator: MatPaginator;

  @ViewChild(MatSort, { static: true }) sort: MatSort;


  selectedFilter = ""

  displayedColumns: any[] = ["meetingSummary", "status", "createdDate", "dueDate", "type", "assignedTo", "action"]



  constructor(private dialog: MatDialog,
    public router: Router,
    private datePipe: DatePipe, public plannerService: PlannerService,
    private dataSharingService: DataSharingService, private dashboardHeaderComponent : DashboardHeaderComponent) {
   
   
  }


  ngOnChanges() {

  }

  ngOnInit(): void {
    this.data = []
    this.plannerService.emitChangesOfSelectedAllMeetingData(meetingData);
    this.getAllMeetings()
  }



  createMeeting() {
    const matDialogRef = this.dialog.open(ContextualCreateMeetingComponent, {
      autoFocus: false,
      width: '45%',
      disableClose: true,
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.listOfMeetings.push(result)
        this.refreshDataTable(this.listOfMeetings);
      }
    })

  }







  navigateToSummaryPage(data) {

    this.plannerService.emitChangesOfSelectedOneMeetingData(data);
    this.dashboardHeaderComponent.getSidebarHighlight('/planner');
    this.router.navigate(['/planner/meetings/' + data.id + '/' + 0 ])

  }
  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str,50);
  }

  getWidth(length) {
    let w = 100 / length;
    return w.toFixed(2) + "%"
  }


  getAllMeetings() {
   let meetingList = []
    this.plannerService.allMeetingDataChangeEmitted$.subscribe(data => {
      if(data) meetingList = data
    })
    this.listOfMeetings = meetingData
    if(meetingList.length != 0) this.listOfMeetings =  meetingList;
    
   
    this.refreshDataTable(this.listOfMeetings);
    this.showLoaderSpinner = false;
   

  }
  ngAfterViewInit() {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort
    }
  }




  applyFilter(event) {

    if(event.keyCode == 13 && event?.target?.value.length != 0){
      let filterValue = event?.target?.value.trim(); // Remove whitespace
      this.searchKey = filterValue;

     
    }

    if(event?.target?.value.length == 0){
      let filterValue = event?.target?.value; // Remove whitespace
      this.searchKey = filterValue;

     
    }

  }


  refreshDataTable(filterdData) {


    let data = []
    data = filterdData

    if (data && data.length == 0) {
      this.showNoRecordsAvailbleMessage = true;
      this.dataSource = new MatTableDataSource(data);

      this.dataSource.filterPredicate = (data, filter: string) => {
        const accumulator = (currentTerm, key) => {
          return this.nestedFilterCheck(currentTerm, data, key);
        };
        const dataStr = Object.keys(data).reduce(accumulator, '').toLowerCase();

        const transformedFilter = filter.trim().toLowerCase();
        return dataStr.indexOf(transformedFilter) !== -1;
      }
      this.dataSource.paginator = this.paginator;
      setTimeout(() => {
        this.dataSource.paginator = this.paginator;
      });
      this.dataSource.sort = this.sort;
      this.searchKey = "";



    }
    if (data && data.length != 0) {
      data = [...data]
      this.showNoRecordsAvailbleMessage = false;
      this.dataSource = new MatTableDataSource(data);

      this.dataSource.filterPredicate = (data, filter: string) => {
        const accumulator = (currentTerm, key) => {
          return this.nestedFilterCheck(currentTerm, data, key);
        };
        const dataStr = Object.keys(data).reduce(accumulator, '').toLowerCase();
        // Transform the filter by converting it to lowercase and removing whitespace.
        const transformedFilter = filter.trim().toLowerCase();
        return dataStr.indexOf(transformedFilter) !== -1;
      }

      this.dataSource.paginator = this.paginator;
      setTimeout(() => {
        this.dataSource.paginator = this.paginator;
      });
      this.dataSource.sort = this.sort;

      this.searchKey = "";
    }
  }



  nestedFilterCheck(search, data, key) {

    switch (key) {

      case 'meetingSummary': {
        return search + data.meetingSummary;
        break;
      }
      default: {
        return search + data[key]
      }
    }

  }

  getUserNames(list){
    let names = list?.map(ele => ele.name) + "";
    return names
  }


  
}



