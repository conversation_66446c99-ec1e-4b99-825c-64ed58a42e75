<div class="actionBtnsContainer createMeeting" fxLayout="row wrap" fxLayoutGap="4px" >
  <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="50%">
    <button mat-raised-button class="actionBtns green" (click)="createMeeting()">CREATE
      MEETING</button>
  </div>
  <div fxFlex="29%" fxFlex.md="29%" fxFlex.xs="90%" fxFlex.sm="49%">
    <mat-form-field class="searchInput font-weight-500 width-100" >
      <mat-icon matSuffix>search</mat-icon>
      <input matInput (keyup)="applyFilter($event)" [(ngModel)]="searchKey" placeholder="Search Ic Meetings" #input>
    </mat-form-field>
  </div>



</div>






<div fxLayout="row wrap" class="mb-3" >
  <div  fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="height-auto">
    <mat-card appearance="outlined" class="mat-card-top-border mb-5">
      
     
      <mat-card-content>
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="task-table-container  mat-elevation-z0  mat-table-width ">

          <table mat-table [dataSource]="dataSource " matSort fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
            class="  mat-elevation-z0 mat-table-width" >



            <!--  Column -->
            <ng-container matColumnDef="meetingSummary">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-30"> Summary </th>
              <td mat-cell class="pointer hyperlinkColor" *matCellDef="let row">
                <span (click)="navigateToSummaryPage(row)" class="customDescription">
                  <p [matTooltip]="row?.meetingSummary">
                    {{stringWithEllipsis( row?.meetingSummary)}}</p>

                </span>
              </td>
            </ng-container>



            <!--  Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Status </th>
              <td mat-cell class=" " *matCellDef="let row"> <span class="">{{row.status}}</span>
              </td>
            </ng-container>
            <!--  Column -->
            <ng-container matColumnDef="assignedTo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> People </th>
              <td mat-cell class=" " *matCellDef="let row" > <span class="" [matTooltip]="getUserNames(row.userList)">{{stringWithEllipsis(getUserNames(row.userList))}}</span>
              </td>
            </ng-container>
            <!--  Column -->
            <ng-container matColumnDef="type">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-29"> Type </th>
              <td mat-cell class=" " *matCellDef="let row"> <span class="">{{row.type}}</span>
              </td>
            </ng-container>

            <!--  Column -->
            <ng-container matColumnDef="dueDate">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-18"> Due Date </th>
              <td mat-cell class=" " *matCellDef="let row"> <span class="">{{row.dueDate | date}}</span>
              </td>
            </ng-container>

            <!--  Column -->
            <ng-container matColumnDef="createdDate">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22"> Creation Date </th>
              <td mat-cell class=" " *matCellDef="let row"> {{row.createdDate | date}} </td>
            </ng-container>

            <!-- Column -->
            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="w-12  "> </th>
              <td class=" " mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">


              </td>
            </ng-container>

            <tr mat-header-row class="textAlign" *matHeaderRowDef="displayedColumns;"></tr>
            <tr mat-row class="textAlign"
              *matRowDef="let row; columns: displayedColumns; let i = index; " [class.task-row__alternate]="i % 2"></tr>
            <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
              <tr class="mat-row width100" *matNoDataRow>
                <td class="mat-cell" colspan="4">No data matching the filter</td>
              </tr>
            </div>
          </table>


          <div class="no-records-found" *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner">
            <!-- <mat-card appearance="outlined" class="no-record-card mat-elevation-z0"> No records found </mat-card> -->
          </div>
          <div *ngIf="showLoaderSpinner">
            <mat-spinner class="no-record-card ShowLoader" > No records found </mat-spinner>
          </div>
        </div>

      </mat-card-content>
      <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
        <mat-paginator class="" [pageSizeOptions]="[8, 25,50, 100]" [pageSize]="50"></mat-paginator>
      </div>

    </mat-card>
  </div>
</div>