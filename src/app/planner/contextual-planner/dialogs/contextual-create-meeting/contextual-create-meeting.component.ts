import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormControlName, UntypedFormControl } from '@angular/forms';
import { startWith, map, filter } from 'rxjs/operators';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { Observable } from 'rxjs';
import { DealService } from 'src/app/shared-service/deal.service';

@Component({
  selector: 'app-contextual-create-meeting',
  templateUrl: './contextual-create-meeting.component.html',
  styleUrls: ['./contextual-create-meeting.component.scss']
})
export class ContextualCreateMeetingComponent implements OnInit {

  dealListControl = new UntypedFormControl('')
  dealList: any = [];
  selectedDealList: any = [];
  finalSourceList : any = []
  filteredDealList: Observable<any[]>;
  generalDetail: any[] = [];
  constructor(public dealService: DealService) { }

  ngOnInit() {
    this.getDealList()
    this.filteredDealList = this.dealListControl.valueChanges
      .pipe(
        startWith(null),
        map((tag: string | null) => tag ? this.filter(tag) : this.dealList ?.slice())
      );
  }





  visible: boolean = true;
  selectable: boolean = true;
  removable: boolean = true;
  addOnBlur: boolean = false;

  separatorKeysCodes = [ENTER, COMMA];



  @ViewChild('dealInput') dealInput: ElementRef;

  addDeal(event: MatChipInputEvent): void {

    const input = event.input;
    const value = event.value;
    if (!this.dealList.includes(value.trim())) {
      // this.notificationMessage.error("Please select correct labels.")
    }
    if (this.dealList.includes(value.trim())) {
      if ((value || '').trim()) {
        if (!this.selectedDealList.includes(value.trim())) {
          this.selectedDealList.push(value);
        }

      }
    }

    if (input) {
      input.value = '';
    }
    this.dealListControl.setValue(null);
  }

  remove(tag: any): void {
    const index = this.selectedDealList.indexOf(tag);
    if (index >= 0) {
      this.selectedDealList.splice(index, 1);
    }

  }

  private filter(value: any): any[] {
    let filteredArr = [];

    filteredArr = this.dealList.filter(tag => tag.dealIdentifier ?.toLowerCase().includes(value ?.toString() ?.toLowerCase()));
    // filteredArr.length == 0 ? this.labelError = true : false;

    return filteredArr
  }

  selected(event: MatAutocompleteSelectedEvent, trigger: MatAutocompleteTrigger): void {

    if (!this.selectedDealList.includes(event.option.value)) {
      let sourceData = event.option.value
    
      this.selectedDealList.push(event.option.value)
      let data = {
        'name': sourceData.dealIdentifier,
        'lead': this.getSourceLead(event.option.value),
        'meetingMinutes': [],
        'meetingAgendaDetail': []
      }
      this.finalSourceList.push(data);
      this.generalDetail = this.finalSourceList
    } else {
      // this.notificationMessage.error(`Label '${event.option.value ? event.option.value.labelName : ""}' already selected`)
    }


    this.dealInput.nativeElement.value = '';
    this.dealListControl.setValue(null);


  }

  getSourceLead(data){
    let sourceData = data;
    let leadData = sourceData.dealTeamList?.filter(ele => ele.isTeamLead)[0] ;
    return leadData?.teamName
  }
  onChangeOfInput(event: Event, trigger: MatAutocompleteTrigger) {

    // setTimeout(function () {
    //   trigger.openPanel();
    // }, 1);
  }



  getDealList() {
    this.dealService.getAllDealList().subscribe(res => {
      if (res) {
        this.dealList = res
        // this.setStagesSelectedBusinessProcess(this.dealListFromParent)
      }
    })
  }



}
