<app-create-meeting [generalDetail]="generalDetail">
  <div generalDetails  class="userListCss width-100">
  <mat-form-field class="width-100"  fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" >
    <mat-label>Deal <span class="red">*</span></mat-label>

    <mat-chip-grid #dealList class="font-12">
      <mat-chip-row class="font-12 font-weight-500  button"  *ngFor="let deal of selectedDealList"
          [removable]="removable" 
         (removed)="remove(deal)">
        {{deal.dealIdentifier}} 
        <mat-icon matChipRemove *ngIf="removable" >cancel</mat-icon>
      </mat-chip-row>
      <input #dealInput required [matAutocomplete]="auto" (focus)="onChangeOfInput($event, trigger)"
        [formControl]="dealListControl" [matChipInputFor]="dealList"
        [matChipInputSeparatorKeyCodes]="separatorKeysCodes" #trigger="matAutocompleteTrigger"
        [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addDeal($event)" />
    </mat-chip-grid>
    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event, trigger)">
      <mat-option *ngFor="let deal of filteredDealList | async" [value]="deal">
        <span class="selectedRole">{{ deal.dealIdentifier }}</span>

      </mat-option>
    </mat-autocomplete>

  </mat-form-field>
</div>
</app-create-meeting>
