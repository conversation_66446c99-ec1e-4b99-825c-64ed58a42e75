import { Component, OnInit } from '@angular/core';
import { DataSharingService } from '../common/dataSharing.service';

@Component({
  selector: 'app-planner',
  templateUrl: './planner.component.html',
  styleUrls: ['./planner.component.scss']
})
export class PlannerComponent implements OnInit {
  plannerTabs : any = [
    { index: 0, id: 1, label: 'Tasks', icon: 'peaple', link: 'tasks'},
    { index: 1, id: 2, label: 'Notifications', icon: 'peaple', link: 'notifications'},
    // { index: 2, id: 3, label: 'Meeting', icon: 'peaple', link: 'meetings'}
  ]
  constructor(private dataSharingService : DataSharingService) { }

  ngOnInit() {
  }


  getSidebarItembyName(itemName){
    if(this.dataSharingService.getSidebarItembyName(itemName)){
      let item = this.dataSharingService.getSidebarItembyName(itemName)[0] ;
      return item?.displayName;
     }
  }

}
