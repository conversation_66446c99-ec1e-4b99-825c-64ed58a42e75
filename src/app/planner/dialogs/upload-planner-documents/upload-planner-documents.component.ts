import { Component, OnInit, Inject } from '@angular/core';
import { Subject } from 'rxjs';
import { listOfDocumentTypes } from 'src/app/application-summary/static-data';
import { ThemePalette } from '@angular/material/core';
import { ProgressBarMode } from '@angular/material/progress-bar';
import { FormControl, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { DownloadFileService } from 'src/app/shared-service/download-file.service';

@Component({
  selector: 'app-upload-planner-documents',
  templateUrl: './upload-planner-documents.component.html',
  styleUrls: ['./upload-planner-documents.component.scss']
})
export class UploadPlannerDocumentsComponent implements OnInit {

  private unsubscribe$ = new Subject();


  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage: boolean = false;
  color: ThemePalette = 'primary';
  mode: ProgressBarMode = 'indeterminate';
  value = 50;
  bufferValue = 75;
  maxDocFileSize;
  fileSize: any;

  constructor(
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<UploadPlannerDocumentsComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationMessage: ToasterService,
    private downloadFileService: DownloadFileService
  ) {

    this.dataFromParentComponent = data;



  }

  ngOnInit() {
    this.downloadFileService.getFileSizeLimitFromCache().subscribe(limit => this.maxDocFileSize = limit);
  }

  ngOnDestroy() {

    this.unsubscribe$.next('');
    this.unsubscribe$.complete();
  }

  fileUpload(file) {

    // this.taskService.isFileUploaded = true;
    if (file) {
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 104857600) {
        this.showFileSizeErrorMessage = true;
      }
    }

  }

  closeDialog() {
    this.dialogRef.close(false)
  }


  onUpload() {


    if (!(this.selectedFileName)) {
      this.notificationMessage.error("Please fill in all the required fields with valid data.")
      return
    }
    if (this.showFileSizeErrorMessage) {
      return
    }
    let result = {

      status: true,
      file: this.selectedFile,

      selectedFileName: this.selectedFileName
    }
    this.dialogRef.close(result)

  }

}

