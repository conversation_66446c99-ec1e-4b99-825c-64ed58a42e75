<mat-dialog-content class="mat-dialog-content-custom-css">
    <div fxLayout="row wrap">
      <div fxFlex="40%" fxFlex.md="20%" fxFlex.xs="80%" fxFlex.sm="40%"  >
        <h2>Upload Document</h2>
      </div>
      <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="80%" fxFlex.sm="40%" class="ml43">
        <mat-icon (click)="closeDialog()" class="pointer">close</mat-icon>
      </div>
  
  
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="uploadDocumentsInputs">
        <mat-form-field class="width-100 font-6" appFileDragNDrop (filesChangeEmiter)="fileUpload($event)">
          <button (click)="fileDropRef.click()" mat-button class="green">Choose file</button>
          <span class="font-12">{{this.selectedFileName}}</span>
          <span *ngIf= "!this.selectedFileName" class="fileDropRef"> or Drop it here!</span>
          <input matInput class="displayInput">
          <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
            (change)="fileUpload($event.target.files[0])" accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic" />
  
        </mat-form-field>
        <section class="example-section" *ngIf="showFileProgressBar">
          <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
            [bufferValue]="bufferValue">
          </mat-progress-bar>
          
        </section>
        <mat-hint class="noteForFile">Note : Select document in .doc,  .pdf,.pptx,.jpeg,.jpg,.xlsx ,.HEIC,.MOV  format upto {{maxDocFileSize}} of size.
        </mat-hint>
        <small *ngIf="showFileSizeErrorMessage" class="font-10">
          File Size Should be less than <strong>{{maxDocFileSize}}</strong>
        </small>
  
      </div>
  
  
  
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="uploadBtnContainerCss">
        <button mat-raised-button class="green " (click)="onUpload()" [disabled]="showFileProgressBar">UPLOAD
          DOCUMENT</button>
      </div>
    </div>
  
  
  </mat-dialog-content>
