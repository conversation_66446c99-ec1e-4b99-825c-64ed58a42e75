import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { ToasterService } from 'src/app/common/toaster.service';
import { PlannerService } from '../../service/planner.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ValidationErrorMessageService } from 'src/app/shared-service/validation-error-message.service';
import { Router } from '@angular/router';
import { startWith, map } from 'rxjs/operators';
import { OnCancelWarningDialogComponent } from 'src/app/task/on-cancel-warning-dialog/on-cancel-warning-dialog.component';
import { FileUploadDialogComponent } from 'src/app/task/file-upload-dialog/file-upload-dialog.component';
import { IdentityService } from 'src/app/shared-service/identity.service';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { RequireMatch as RequireMatch } from '../../requiredMatch'
import { meetingData } from '../../dummy-data';
import JsonData from 'src/assets/data.json';
import { DownloadFileService } from 'src/app/shared-service/download-file.service';
@Component({
  selector: 'app-create-meeting',
  templateUrl: './create-meeting.component.html',
  styleUrls: ['./create-meeting.component.scss']
})
export class CreateMeetingComponent implements OnInit {

  // @ViewChild(TaskComponent) taskComponent: TaskComponent;
  @Input() generalDetail: any
  agendaList : any = []
  addMeetingForm: UntypedFormGroup;
  selectedUserList: any = []
  JsonData:any
  todayDate: Date = new Date();
  showDateRangePicker: boolean = false;
  userList: any = [];
  filteredUserList: Observable<any[]>;
  selectedType="IC Meeting"
  investmentList: any = []
  allModules: any;
  projectList: any;
  files: any = [];
  loggedInUser: string;
  userNotFound: boolean;
  fileData: any = new FormData()
  selectedFile: any = null;
  fileSize: any = 0;
  selectedFileName: any;
  showFileSizeErrorMessage: boolean;
  meetingSourceList: any;
  maxDocFileSize;

  constructor(public fb: UntypedFormBuilder,
    private notification: ToasterService,
    // @Inject(MAT_DIALOG_DATA) public data,
    public plannerService: PlannerService,
    public matDialog: MatDialog,
    private errorMessageService: ValidationErrorMessageService,
    public dialogRef: MatDialogRef<CreateMeetingComponent>,
    public identityService : IdentityService,
    private router: Router,
    private downloadFileService: DownloadFileService
  ) {
    this.generateaddMeetingForm();
  }

  ngOnInit() {
    this.getAllUsers();
    this.loggedInUser = localStorage.getItem('user')
    this.filteredUserList = this.addMeetingForm.get('assigneeDetail').valueChanges
      .pipe(
        startWith(null),
        map((tag: string | null) => tag ? this.filter(tag) : this.userList?.slice())
      );
    this.downloadFileService.getFileSizeLimitFromCache().subscribe(limit => this.maxDocFileSize = limit);
  }

  ngOnDestroy() {
  }

  generateaddMeetingForm() {
    this.addMeetingForm = this.fb.group({
      meetingSummary: ['', [Validators.required]],
      createdDate: ['' ],
      type: [''],
      assigneeDetail: [''],
      dueDate : ['' ],
      description: [''],
      meetingAgenda: [''],
      status : []
    })
  }

  fileUpload(file) {
    
    // this.taskService.isFileUploaded = true;
   if(file){
    this.selectedFile = file;
    this.selectedFileName = file.name;
    this.fileSize = file.size;
    this.showFileSizeErrorMessage = false;
  
    if (this.fileSize >= 104857600) {
      this.showFileSizeErrorMessage = true; 
    }
   }
    
  }


  onCreate() {
    this.addMeetingForm.patchValue({
      'createdDate' : new Date(),
      'status' : 'In Progress'

    })  
    if (this.addMeetingForm.invalid) {
      this.notification.error('Please fill in all the required fields with valid data.')
      
      this.addMeetingForm.markAllAsTouched();
      return
    }
    if ( this.selectedUserList.length == 0) {
      this.notification.error('Atleast one assignee is required.')
      return
    }
    if ( this.generalDetail.length == 0) {
      this.notification.error('Atleast one deal is required.')
      return
    }
    if ( this.generalDetail.length > 5) {
      this.notification.error('Select upto 5 deals for meeting.')
      return
    }

    if (typeof this.addMeetingForm.get('assigneeDetail').value != 'object') {
      this.notification.error('Assignee is not found.')
      return
    }
    if (this.fileSize >= 10485760) {
      this.notification.error('File size can not be more than 10MB')
      return
    }
   



    // const assignees = this.addMeetingForm.get('assigneeDetail').value.firstName + " " + this.addMeetingForm.get('assigneeDetail').value.lastName




    let data = this.addMeetingForm.value;
    
 
    if (this.generalDetail && this.generalDetail.length != 0) {
       this.meetingSourceList = this.generalDetail;
      data.meetingSource = this.generalDetail;
    }else{
      data.meetingSource = []
    }
  

    this.agendaWithSourceList(this.agendaList)
    this.getFormattedUserList(this.selectedUserList)
    let payload =  {
      id: meetingData.length + 1,
      createdDate: data.createdDate,
      meetingSource: this.meetingSourceList,
      description: data?.description,
      dueDate: data.dueDate,
      status: "In Progress",
      meetingSummary: data.meetingSummary,
      type: data.type,
      createdBy: "",
      userList: this.selectedUserList,
      meetingMode:"Virtual Meeting",
      chairsReport: "",
  }

    this.notification.success(JsonData["label.success.CreateMeeting"]);
    this.dialogRef.close(payload);
    // console.log(payload)
    // delete data['assigneeDetail'],


    // this.fileData = new FormData();
    // this.fileData.append('meeting', JSON.stringify(data));
    // if (this.selectedFile == null) {
    //   this.fileData.append('file', JSON.stringify(this.selectedFile));
    // } else {
    //   this.fileData.append('file', this.selectedFile);
    // }



    // this.plannerService.addMeeting(this.fileData).subscribe(res => {

    //   if (this.router.url === '/monitor/tasks') {
    //     this.plannerService.refreshAllTasks.emit(true);
    //     this.router.navigate(['/monitor/tasks']);
    //   }
    //   this.notification.success('Meeting created successfully.');
    //   this.plannerService.isFileUploaded = false;
    //   this.dialogRef.close(true);
    // }, (error) => {
    //   let errMsgs = [];
    //   if (error.error && error.error.length > 0) {
    //     error.error.forEach((e, ei) => {
    //       if (e.message) {
    //         let msg = e.fieldName + ":" + e.message
    //         errMsgs.push(msg);
    //       }
    //     })
    //   }
    //   this.notification.error(errMsgs);

    // })
  }

  agendaWithSourceList(agendaList){

    this.meetingSourceList.forEach(element => {
    element.meetingAgendaDetail = agendaList
      
    });
  }

  onClear() {
    this.addMeetingForm.reset();
  }

  get f() { return this.addMeetingForm.controls; }

  private _filterUser(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.userList.filter(option => (option.identifier).toLowerCase().includes(filterValue));
  }


  getFormattedUserList(userList){
    userList.forEach(element => {
      element.name = element.firstName + ' ' + element.lastName ;
      element.isAttended = true;
    });
  }




  AutoCompleteDisplay(item: any): string {
    if (item == undefined) return 
    return item.firstName + ' ' + item.lastName;
  }



  fileBrowseHandler(file) {
    if (file) {
      this.files = [file.name];
    }
  }






  getErrorMessage(formName, controlName, customValidation?: any) {
    return this.errorMessageService.getErrorMessage(this, formName, controlName, customValidation);
  }



 







  
  getAllUsers() {
    this.identityService.getAllUser().subscribe(res => {
      if (res) {
       this.userList = res;
      }
    }, (error) => {
      // console.log(error);
    })
  }






















  visible: boolean = true;
  selectable: boolean = true;
  removable: boolean = true;
  addOnBlur: boolean = false;

  separatorKeysCodes = [ENTER, COMMA];



  @ViewChild('userInput') userInput: ElementRef;

  addUser(event: MatChipInputEvent): void {

    const input = event.input;
    const value = event.value;
    if (!this.userList.includes(value.trim())) {
      // this.notificationMessage.error("Please select correct labels.")
    }
    if (this.userList.includes(value.trim())) {
      if ((value || '').trim()) {
        if (!this.selectedUserList.includes(value.trim())) {
          this.selectedUserList.push(value);
        }

      }
    }

    if (input) {
      input.value = '';
    }
    this.addMeetingForm.get('assigneeDetail').setValue(null);
  }

  remove(tag: any): void {
    const index = this.selectedUserList.indexOf(tag);
    if (index >= 0) {
      this.selectedUserList.splice(index, 1);
    }

  }

  private filter(value: any): any[] {
    let filteredArr = [];
  
    filteredArr = this.userList.filter(tag => tag.firstName?.toLowerCase().includes(value?.toString()?.toLowerCase()) || tag.lastName?.toLowerCase().includes(value.toString().toLowerCase()));
    // filteredArr.length == 0 ? this.labelError = true : false;
    return filteredArr
  }

  selected(event: MatAutocompleteSelectedEvent, trigger: MatAutocompleteTrigger): void {

    if (!this.selectedUserList.includes(event.option.value)) {
      this.selectedUserList.push(event.option.value);
    } else {
      // this.notificationMessage.error(`Label '${event.option.value ? event.option.value.labelName : ""}' already selected`)
    }


    this.userInput.nativeElement.value = '';
    this.addMeetingForm.get('assigneeDetail').setValue(null);
    setTimeout(function () {
      trigger.openPanel();
    }, 1);

  }
  onChangeOfInput(event: Event, trigger: MatAutocompleteTrigger) {

    // setTimeout(function () {
    //   trigger.openPanel();
    // }, 1);
  }


  closeDialog() {
    this.dialogRef.close()
  }

 removeAgenda( agenda ) {

      this.agendaList.splice(agenda, 1);
 }
  addAgenda(){
 

    let agenda = this.agendaList.find(agenda => agenda.agendaTitle == this.addMeetingForm.value.meetingAgenda.trim());
    if (agenda && agenda) {
      // this.notificationMessage.error("agenda " + this.addMeetingForm.value.agenda + " already exists ");
      return;
    }
    let agendaData = {
      agendaTitle : this.addMeetingForm.value.meetingAgenda,
      agendaDetails : []
    }
    this.agendaList.push(agendaData)
   
    this.addMeetingForm.controls['meetingAgenda'].reset();
  }


}
