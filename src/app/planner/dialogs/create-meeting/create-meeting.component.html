<mat-dialog-content class="mat-dialog-content-form-custom-css">

  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">


        <div fxFlex="90%" fxFlex.md="90%" fxFlex.xs="80%" fxFlex.sm="90%">

          <h2>Create Meeting</h2>


        </div>
        <div fxFlex="9%" fxFlex.md="9%" fxFlex.xs="16%" fxFlex.sm="9%">

          <button mat-button class="close-icon" (click)="closeDialog()" [mat-dialog-close]="true">
            <mat-icon>close</mat-icon>
          </button>


        </div>
      </div>
    </div>
  </div>

  <div fxLayout="row wrap" fxLayoutGap="4px">
    <form [formGroup]="addMeetingForm" novalidate  fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
      fxFlex.sm="100%" class="display-flex">

      <div fxLayout="row wrap" fxLayoutGap="4px">

        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-label>Type <span class="red">*</span></mat-label>
          <mat-select formControlName="type" [(ngModel)]="selectedType">
            <mat-option value="IC Meeting" >IC Meeting</mat-option>
            <mat-option value=" Meeting" >Meeting</mat-option>
          </mat-select>
          <mat-error *ngIf="
                    addMeetingForm.controls.type.touched &&
                    addMeetingForm.controls.type.errors?.required
                  ">
            Select Type.
          </mat-error>
        </mat-form-field>
        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-label>Date <span class="red">*</span></mat-label>
          <input class="width-80"  matInput formControlName="dueDate" [matDatepicker]="i"
            
           />
          <mat-datepicker-toggle class="width-80" matSuffix [for]="i">
          </mat-datepicker-toggle>
          <mat-datepicker class="width-80" #i></mat-datepicker>
        </mat-form-field>




        <mat-form-field class="width-100 userListCss"  fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" >
          <mat-label>People <span class="red">*</span></mat-label>

          <mat-chip-grid #userList class="font-12">
            <mat-chip-row class="font-12 font-weight-500 button"  *ngFor="let user of selectedUserList"
                [removable]="removable" 
               (removed)="remove(user)">
              {{user.firstName}} {{ user.lastName }}
              <mat-icon matChipRemove *ngIf="removable" >cancel</mat-icon>
            </mat-chip-row>
            <input #userInput type="text"  [matAutocomplete]="auto" (focus)="onChangeOfInput($event, trigger)"
              formControlName="assigneeDetail" [matChipInputFor]="userList"
              [matChipInputSeparatorKeyCodes]="separatorKeysCodes" #trigger="matAutocompleteTrigger"
              [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addUser($event)"  />
          </mat-chip-grid>
          <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event, trigger)">
            <mat-option *ngFor="let user of filteredUserList | async" [value]="user">
              <span class="selectedRole">{{ user.firstName }} {{ user.lastName }}</span>

            </mat-option>
          </mat-autocomplete>
          <!-- <mat-error *ngIf="addMeetingForm.controls['assigneeDetail'].hasError('required')">
              Please enter a assignee.
          </mat-error> -->

        </mat-form-field>


         <!--************************* Additional/optional information  **************************************-->

        <ng-content select="[generalDetails]" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%"></ng-content>
      
        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-label>Subject <span class="red">*</span> </mat-label>
          <input class="width-100"  formControlName="meetingSummary" name="meetingSummary" matInput
          autocomplete="off" />
        <mat-error *ngIf="
              addMeetingForm.controls.meetingSummary.touched &&
              addMeetingForm.controls.meetingSummary.errors?.required
            ">
          Enter  meeting Summary.
        </mat-error>
        </mat-form-field>

        <!-- <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-label>Description</mat-label>
          <textarea class="width-100"  formControlName="description" name="description" matInput
          autocomplete="off" ></textarea>

        </mat-form-field> -->

        <mat-form-field class="width-100 display-flex" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-label>Agenda</mat-label>
          <input class="width-100 display-flex"  formControlName="meetingAgenda" name="meetingAgenda" matInput
          autocomplete="off" >

          <button mat-raised-button class="green" type="submit"
          [disabled]="(!this.addMeetingForm.controls.meetingAgenda.value) || (addMeetingForm.controls.meetingAgenda && addMeetingForm.controls.meetingAgenda.value && addMeetingForm.controls.meetingAgenda.value.length == 0)"
          (click)="addAgenda()">Add</button>
        </mat-form-field>


        <mat-list role="list" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
            <mat-list-item *ngFor="let agenda of agendaList  let i = index" role="listitem">

              <div class="width-100" fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="88%" fxFlex.md="88%" fxFlex.xs="80%" fxFlex.sm="88%">{{agenda.agendaTitle}} </div>
                <div fxFlex="10%" fxFlex.md="10%" fxFlex.xs="15%" fxFlex.sm="10%">
                 
                  <button mat-icon-button (click)="removeAgenda(i)" class="mat-icon-buttons-in-action-column red">
                      <mat-icon class="pointer ">delete</mat-icon></button>
                </div>
              </div>
            </mat-list-item>

          </mat-list>



        <div class="uploadDocumentsInputs" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%">
          <mat-form-field class="width-100 font-6" appFileDragNDrop (filesChangeEmiter)="fileUpload($event)">
            <button (click)="fileDropRef.click()" type="button" mat-button class="green">Choose file</button>
            <span class="font-12">{{this.selectedFileName}}</span>
            <span class="font-16 infoIconColor mt-1" *ngIf= "!this.selectedFileName" > or Drop it here!</span>
            <input matInput class="displayInput">
            <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
              (change)="fileUpload($event.target.files[0])" accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic" />
    
          </mat-form-field>
          <!-- <section class="example-section" *ngIf="showFileProgressBar"> -->
            <!-- <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
              [bufferValue]="bufferValue">
            </mat-progress-bar> -->
            
          <!-- </section> -->
          <mat-hint class="noteForFile">Note : Select document in .doc,  .pdf,.pptx,.jpeg,.jpg,.xlsx ,.HEIC,.MOV  format upto {{maxDocFileSize}} of size.
          </mat-hint>
          <small *ngIf="showFileSizeErrorMessage" class="font-10">
            File Size Should be less than <strong>{{maxDocFileSize}}</strong>
          </small>
    
        </div>

      </div>

      <div class="createBtnContainerCss" fxLayout="row wrap" fxLayoutGap="4px">
        <button mat-raised-button type="button" (click)="onCreate()" class="green" >Create</button>
      </div>
    </form>
  </div>

</mat-dialog-content>