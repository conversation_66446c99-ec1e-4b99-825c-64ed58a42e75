.mat-mdc-form-field{
    width: 100%
}

/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-custom-css{
  // padding:  0 !important;
  // margin: 0  !important
}
/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-form-custom-css {
  min-height: 50vh;
  max-height: 80vh !important;
}

.uploadBtnContainerCss{
  justify-content: center;
  display: flex;
  margin-top: 5%;
  margin-bottom: 1%
}

.noteForFile{
  display: flex;
  font-size: 11px;

  justify-content: flex-end;
}

.uploadDocumentsInputs{
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-underline {
      bottom: 0 !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important; 
  }
}



.createBtnContainerCss{
  justify-content: center;
  display: flex;
  margin-top: 5%;
  margin-bottom: 1%
}


.button-row{
  text-align: center;
}


.display-flex{
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-mdc-form-field .mat-form-field-infix {

  display:flex !important;
}
}

::ng-deep .mat-mdc-list-base .mat-mdc-list-item .mat-list-item-content, .mat-list-base .mat-list-option .mat-list-item-content {

  padding: 0 !important;
}

.width-100 { ::ng-deep button {
  line-height: 26px !important;
}
}

.centerd {
 display: block !important;
 margin: 0 auto;
}


.userListCss{
  ::ng-deep .mat-mdc-chip.mat-mdc-standard-chip {
    border: #c7c7c7 solid 1px !important;
    background: none !important;
    // color:#757575
  }
}
