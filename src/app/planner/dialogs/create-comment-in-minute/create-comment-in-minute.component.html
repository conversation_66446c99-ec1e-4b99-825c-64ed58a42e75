<div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="94%" fxFlex.md="85%" fxFlex.xs="80%" fxFlex.sm="85%">
          <h2>Add Comment</h2>
        </div>
        <div fxFlex="5%" fxFlex.md="12%" fxFlex.xs="16%" fxFlex.sm="12%">
          <!-- <button mat-button class="close-icon" > -->
          <mat-icon class="pointer" (click)="closeDialog()">close</mat-icon>
          <!-- </button> -->
        </div>
      </div>
    </div>
  </div>
  
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div class="matTextareaInMeeting" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <mat-form-field class="width-100" appearance="outline">
        <mat-label>New comment</mat-label>
        <!-- [matAutocomplete]="autoName"  -->
        <textarea matInput required (input)="onInput($event)" [formControl]='description'></textarea>
        <!-- <mat-autocomplete #autoName="matAutocomplete">

            <mat-option *ngFor="let user of userListInMeeting"
                        [value]="user.name"
                        (click)="$event.stopPropagation();">{{user.name}}</mat-option>
    
        </mat-autocomplete> -->
      </mat-form-field>
    </div>
  </div>
 

  
  <div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="button-row">
      <button mat-raised-button class="green" (click)="onAddComment()">CREATE</button>
    </div>
  
  </div>