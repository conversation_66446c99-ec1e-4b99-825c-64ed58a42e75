import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { UploadPlannerDocumentsComponent } from '../upload-planner-documents/upload-planner-documents.component';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';

@Component({
  selector: 'app-create-comment-in-minute',
  templateUrl: './create-comment-in-minute.component.html',
  styleUrls: ['./create-comment-in-minute.component.scss']
})
export class CreateCommentInMinuteComponent implements OnInit {
  @ViewChild(MatAutocompleteTrigger, {read: MatAutocompleteTrigger}) inputAutoComplete: MatAutocompleteTrigger;
  selectedAssignee : any;
  isCreateMinute : boolean = false;
  showAssignedToInput: boolean = false;
  dataFromParentComponent : any;
  description = new UntypedFormControl();
  documentsList = [];
  userListInMeeting : any = []
  constructor(
    
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<CreateCommentInMinuteComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationMessage: ToasterService,
  ) {
    this.dataFromParentComponent = data;
    this.userListInMeeting = this.dataFromParentComponent.userList
  }

  ngOnInit() {
  }

  openUploadDialog(){
    const matDialogRef = this.matDialog.open(UploadPlannerDocumentsComponent, {
      autoFocus: false,
      width: '35%',
      data: {
        module: 'actionData',
      },
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {
       
        this.documentsList.push({name : result.selectedFileName})
      }
    })
  }

  closeDialog(){
    this.dialogRef.close()
  }

  showAssigneeDialog(){
    this.showAssignedToInput = !this.showAssignedToInput
  }

  onSubmitAssignee(){
    this.showAssignedToInput = false;
    
  }

  onAddComment(){
    if(this.description.invalid){
      return
    }
    let data =   {
   
      commentDescription  :  this.description.value ,
      createdBy : localStorage.getItem('user'),
      createdDate : new Date()
    }


    this.dialogRef.close(data)
  }

  onInput(event){
    
    
  }
}
