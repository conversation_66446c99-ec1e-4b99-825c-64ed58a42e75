import { Component, OnInit, Inject } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { UploadPlannerDocumentsComponent } from '../upload-planner-documents/upload-planner-documents.component';
import { UntypedFormControl } from '@angular/forms';

@Component({
  selector: 'app-createAction',
  templateUrl: './createAction.component.html',
  styleUrls: ['./createAction.component.scss']
})
export class CreateActionComponent implements OnInit {
  selectedAssignee : any;
  isCreateMinute : boolean = false;
  showAssignedToInput: boolean = false;
  dataFromParentComponent : any;
  description = new UntypedFormControl();
  documentsList = [];
  userListInMeeting : any = []
  constructor(
    
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<CreateActionComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationMessage: ToasterService,
  ) {
    this.dataFromParentComponent = data;
    this.userListInMeeting = this.dataFromParentComponent.userList
  }

  ngOnInit() {
  }

  openUploadDialog(){
    const matDialogRef = this.matDialog.open(UploadPlannerDocumentsComponent, {
      autoFocus: false,
      width: '35%',
      data: {
        module: 'actionData',
      },
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {
       
        this.documentsList.push({name : result.selectedFileName})
      }
    })
  }

  closeDialog(){
    this.dialogRef.close()
  }

  showAssigneeDialog(){
    this.showAssignedToInput = !this.showAssignedToInput
  }

  onSubmitAssignee(){
    this.showAssignedToInput = false;
    
  }

  onCreateAction(){
    if(this.description.invalid){
      return
    }
    let data =   {
      description : this.description.value,
      documents : this.documentsList,
      assignedTo :this.selectedAssignee,
      isCreateMinute : this.isCreateMinute,
    }

    this.dialogRef.close(data)
  }
}
