<div fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="94%" fxFlex.md="85%" fxFlex.xs="80%" fxFlex.sm="85%">
        <h2>Create Action</h2>
      </div>
      <div fxFlex="5%" fxFlex.md="12%" fxFlex.xs="16%" fxFlex.sm="12%">
        <!-- <button mat-button class="close-icon" > -->
        <mat-icon class="pointer" (click)="closeDialog()">close</mat-icon>
        <!-- </button> -->
      </div>
    </div>
  </div>
</div>

<div fxLayout="row wrap" fxLayoutGap="4px">
  <div class="matTextareaInMeeting" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <mat-form-field class="width-100" appearance="outline">
      <mat-label>New action</mat-label>
      <textarea matInput required [formControl]='description'></textarea>
    </mat-form-field>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="20%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">

    <button mat-icon-button class="black  ">
      <mat-icon class="pointer icon-white" (click)="openUploadDialog()">
        publish</mat-icon>


    </button>
    <button mat-icon-button class=" black " (click)="showAssigneeDialog()">
      <mat-icon class="pointer icon-white">
        person_add</mat-icon>

    </button>
  </div>
  <div class="customDescription userActionButtons"  fxFlex="53%" fxFlex.md="56%" fxFlex.xs="20%" fxFlex.sm="20%">
    <ng-container *ngIf="selectedAssignee && !showAssignedToInput">
      <mat-chip-option class="documentListContainer">
        {{selectedAssignee}}
        <mat-icon class="displayInput" matChipRemove>close</mat-icon>
      </mat-chip-option>
    </ng-container>
    <ng-container *ngIf="showAssignedToInput ">
    
      <mat-form-field class="width-80">

        <mat-select [(ngModel)]='selectedAssignee' [ngModelOptions]="{standalone: true}">
          <ng-container *ngFor="let user of userListInMeeting">
            <mat-option [value]="user.name">{{user.name}} </mat-option>
          </ng-container>

        </mat-select>
      </mat-form-field>
    </ng-container>
  </div>
 
</div>

<div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
    <div fxFlex="15%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
      </div>
  <div fxFlex="60%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="customDescription">
    
    <ng-container *ngIf="documentsList?.length != 0">
      <mat-chip-listbox *ngFor="let document of documentsList" class="doumentsChipList">
        <mat-chip-option class="documentListContainer">
          <mat-icon>attachment</mat-icon>
          {{document.name}}
          <mat-icon matChipRemove>close</mat-icon>
        </mat-chip-option>
      </mat-chip-listbox>
    </ng-container>
  </div>
</div>

<div fxLayout="row wrap" fxLayoutGap="4px" class="minuteButtons">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="button-row">
    <button mat-raised-button class="green" (click)="onCreateAction()">CREATE</button>
  </div>

</div>