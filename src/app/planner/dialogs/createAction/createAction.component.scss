.matIconsInMeeting{
    ::ng-deep .mat-icon {
      height: 22px !important;
      width: 22px !important;
    }
  }
  
  
  .matTextareaInMeeting{
      /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
      ::ng-deep .mat-form-field-wrapper {
          padding-bottom: 0.34375em !important; 
      }
      /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
      .mat-form-field-appearance-outline .mat-form-field-wrapper {
        margin: 0 !important;
    }
  }
  
  .documentListContainer{
    padding: 5% 15%;
    font-size: 12px;
    background: no-repeat;
    border: 1px solid darkgray;
  }
  
  .doumentsChipList{
    /* TODO(mdc-migration): The following rule targets internal classes of chips that may no longer apply for the MDC version. */
    ::ng-deep .mat-chip-list-wrapper {
      margin : 0 !important
    }
  }