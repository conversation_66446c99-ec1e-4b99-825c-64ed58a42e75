import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TasksComponent } from './tasks.component';
import { TasksRoutes } from './tasks.routing';
import { TableViewTasksComponent } from './table-view-tasks/table-view-tasks.component';
import { SharedModuleModule } from 'src/app/shared-module/shared-module.module';
import { TaskDetailsComponent } from './task-details/task-details.component';

@NgModule({
  imports: [
    CommonModule,
    TasksRoutes,
    SharedModuleModule,

  ],
  declarations: [TasksComponent,
    TableViewTasksComponent,
    TaskDetailsComponent
  ],

  exports: [
    TasksComponent,
    TableViewTasksComponent,
    TaskDetailsComponent
  ]
})
export class TasksModule { }
