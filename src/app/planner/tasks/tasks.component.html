<ng-container *ngIf="selectedView == 'Kanban view'">
  <!-- *ngIf="showTaskSpinner" -->
  <div class="taskSpinner" [hidden]="!showTaskSpinner">
    <mat-spinner mode="indeterminate" class="spinner-mat"></mat-spinner>
  </div>
  <!-- <app-kanban-view [hidden]="showTaskSpinner" #kanbanUI [taskList]="allTasks"
   (valueChange)='onChangesReceived($event)'></app-kanban-view> -->

</ng-container>


<ng-container *ngIf='selectedView == "Split view"'>
  <!-- <app-split-view #splitUI [taskList]="allTasks" (valueChange)='onChangesReceived($event)'></app-split-view> -->
</ng-container>


<!-- 
<ng-container *ngIf='selectedView == "List view"'>
    <app-table-view-tasks  [taskList]="allTasks" (valueChange)='onChangesReceived($event)'>
      </app-table-view-tasks>
</ng-container> -->
