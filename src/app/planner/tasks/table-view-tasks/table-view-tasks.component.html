<div class="taskTableView">

  <div class="SelectedTaskEmpty" fxLayout="row wrap" fxLayoutGap="4px" *ngIf="isSelectedTaskEmpty">
    <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="50%">
      <!-- <button mat-raised-button class="actionBtns green" (click)="openUploadTemplate(type)">CREATE DOCUMENT
              TEMPLATE</button> -->
    </div>
    <div fxFlex="29%" fxFlex.md="29%" fxFlex.xs="90%" fxFlex.sm="49%">
      <mat-form-field class="searchInput searchBox" >
        <mat-icon matSuffix>search</mat-icon>
        <input matInput [(ngModel)]="serchText" (keyup)="applyFilter($event.target.value)" placeholder="Search" #input>
      </mat-form-field>
    </div>



  </div>
  <ng-container>
    <mat-card appearance="outlined" class="mat-elevation-z0" *ngIf="isSelectedTaskEmpty">


      <div class="task-table-container mat-elevation-z0  mat-table-width task-table-container">

        <table mat-table [dataSource]="dataSource " matSort class="alignCenter mat-elevation-z0  mat-table-width">

          <!--  Column -->
          <ng-container matColumnDef="priorityAndUserType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter width-3-5" > </th>
            <td mat-cell class="alignCenter" *matCellDef="let row">
              <mat-icon *ngIf="row.taskPriority != 'Low'" matTooltip="{{row.taskPriority}}" [ngClass]="{
                  'highColor' : row.taskPriority === 'High',
                  'lowColor' : row.taskPriority === 'Low',
                  'mediumColor' : row.taskPriority === 'Medium'}" [inline]="true">priority_high</mat-icon>
            </td>

          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="taskName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-35"> Task Summary </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskName}} </td>
          </ng-container>



          <!--  Column -->
          <ng-container matColumnDef="assigneeName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Assigned to </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.assigneeName}} </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="taskDueDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Task due date </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskDueDate | date}} </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="taskOwner">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-15"> Task owner </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskOwner }} </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="taskReminder">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-15"> Task reminder </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskReminder }} </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="taskPriority">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Task priority </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskPriority }} </td>
          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="assigneeType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Assignee type </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.assigneeType }} </td>
          </ng-container>

          <!-- Column -->
          <ng-container matColumnDef="taskStatus">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Task status </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.taskStatus }} </td>
          </ng-container>
          <!-- Column -->
          <ng-container matColumnDef="taskCreationDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter w-10"> Task creation date </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.createdDate | date }} </td>
          </ng-container>



          <!-- Column -->
          <ng-container matColumnDef="delegatedTo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter"> Delegated to </th>
            <td mat-cell class="alignCenter" *matCellDef="let row"> {{row.delegatedTo }} </td>
          </ng-container>

          <!-- Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="alignCenter "> Action </th>
            <td class="alignCenter" mat-cell *matCellDef="let element" (click)="$event.stopPropagation()">

              <button mat-icon-button class="mat-icon-buttons-in-action-column blue">
                <mat-icon class="pointer icon-white" (click)="onChange('editTask' , element)" matTooltip="Edit task">
                  edit
                </mat-icon>
              </button>

              <button mat-icon-button class="mat-icon-buttons-in-action-column red">
                <mat-icon class="pointer icon-white" (click)="onChange('deleteTask' , element)"
                  matTooltip="Delete task">
                  delete</mat-icon>
              </button>


            </td>
          </ng-container>

          <tr mat-header-row class="textAlign" *matHeaderRowDef="displayedColumns;"></tr>
          <tr mat-row class="pointer textAlign" 
            *matRowDef="let row; columns: displayedColumns; let i = index; " (click)="viewTask(row)"
            [class.task-row__alternate]="i % 2"></tr>
          <tr class="mat-row" *matNoDataRow [hidden]="showNoRecordsAvailbleMessage">
            <td class="mat-cell" colspan="4">No data matching the filter</td>
          </tr>
        </table>



        <div [hidden]="showNoRecordsAvailbleMessage">
          <mat-paginator [pageSizeOptions]="[10, 25,50, 100]"></mat-paginator>
        </div>
        <div class="no-records-found" [hidden]="!showNoRecordsAvailbleMessage">
          <!-- <mat-card appearance="outlined" class="no-record-card mat-elevation-z0"> No records found </mat-card> -->
        </div>
      </div>













    </mat-card>

    <ng-container *ngIf="!isSelectedTaskEmpty">
      <!-- ********** This is For simple basic task view without  tags *************- -->
      <ng-container>
        <app-task-details [taskData]="selectedTask" (valueChange)='onChangesReceived($event)'>
          <div backButton>
            <button class="addTaskBtn" mat-button matTooltip="Close task details"
              (click)="isSelectedTaskEmpty = !isSelectedTaskEmpty">

              <ng-container>Close </ng-container>
            </button>
          </div>
        </app-task-details>
      </ng-container>

      <!-- ************* This is For contextual task view with  tags *************- -->
      <!-- <app-view-task-dialog [parentName]="selectedView" [taskData]="selectedTask"
        (valueChange)='onChangesReceived($event)'>

      </app-view-task-dialog> -->
    </ng-container>


  </ng-container>
</div>