import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { TaskService } from '../../service/task.service';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { tasksTableColumnList, tasksStatusList } from '../../dummy-data';
import { Subscription, Observable } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { startWith, map } from 'rxjs/operators';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import { UntypedFormControl } from '@angular/forms';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

@Component({
  selector: 'app-table-view-tasks',
  templateUrl: './table-view-tasks.component.html',
  styleUrls: ['./table-view-tasks.component.scss']
})
export class TableViewTasksComponent implements OnInit {

  clickEventsubscription: Subscription;
  showNoRecordsAvailbleMessage: boolean = true;
  showTaskSpinner: boolean;
  displayedColumns = [];
  allColumnsList = tasksTableColumnList;
  dataSource: MatTableDataSource<any>;
  selectedStatus: any = 'Recently viewed';
  allTasks = [];
  selectedView: any = 'List view';
  taskStatus = tasksStatusList;
  allTagList: any = [];
  tags: any = [];
  @Input() taskList: any;
  @Output() valueChange = new EventEmitter();
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  updateTaskId: any;
  isDeletedId: any;
  isSelectedTaskEmpty: boolean = true;
  selectedTask: any;
  serchText: string;

  constructor(private matDialog: MatDialog, public taskService: TaskService, private router: Router, private dataSharingService: DataSharingService ) {
    this.displayedColumns = this.allColumnsList.map(item => {
      if (item.isSelected) {
        return item.value
      }
    }).filter(ele => ele != undefined && ele != null)


    this.filteredTags = this.tagCtrl.valueChanges.pipe(
      startWith(null),
      map((tag: string | null) => tag ? this.filter(tag) : this.allTagList.slice()));
  }

  ngOnInit() {
    this.allTasks = this.taskList;
    this.selectedStatus = this.taskService.selectedFilter;
    this.onChangeStatus(this.selectedStatus);
    this.getTagList(this.taskList)
       
    if (!this.dataSharingService.data) {
      // this.tags.push(this.dataSharingService.data.investmentIdentifier);
      this.onChangeTag(this.tags)
     } 
  }

  ngOnChanges() {
    this.allTasks = this.taskList;
    this.selectedStatus = this.taskService.selectedFilter;
    this.onChangeStatus(this.selectedStatus);
    // this.applyFilter("")
    this.serchText = ""

    if (this.updateTaskId) {
      this.selectedTask = this.allTasks.filter(ele => ele.taskId === this.updateTaskId)[0];
      this.updateTaskId = null;
    }
    if (this.isDeletedId) {
      if (this.allTasks.filter(ele => ele.taskId === this.isDeletedId).length === 0) {
        this.selectedTask = {};
        this.isSelectedTaskEmpty = true;
      }
    }
    this.getTagList(this.taskList);
    if (this.tags.length != 0) {
      this.onChangeTag(this.tags)
    }
  }


  applyFilter(filterValue: string) {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  ngAfterViewInit() {
    this.selectedStatus = this.taskService.selectedFilter;
    this.onChangeStatus(this.selectedStatus);
  }

  openColumnSelectorDialog() {
    // //window.scroll(0,0)
    // const matDialogRef = this.matDialog.open(ColumnSelectorDialogComponent, {
    //   width: '40%',
    //   // height: '80vh',
    //   panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //     list: this.allColumnsList,
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result.status) {
    //     this.displayedColumns = result.data.map(item => {
    //       if (item.isSelected) {
    //         return item.value
    //       }
    //     }).filter(ele => ele != undefined && ele != null)
    //   }
    // })

  }



  onChangeStatus(event) {
    this.selectedStatus = event;
    if (event === "all") {
      let filterdData = []
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData);
    }

    if (event === "Open" || event === "In progress" || event === "In review") {
      let filterdData = []
      filterdData = this.allTasks.filter(item => item.taskStatus === event);
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData)
    }

    if (event === "Todays tasks") {
      let filterdData = []
      filterdData = this.allTasks.filter(item => item.taskDueDate == this.taskService.getDateFormatInPayload(new Date()) && item.taskStatus != 'Completed');
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData)
    }

    if (event === "Recently created") {
      let filterdData = []
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      let data = filterdData.sort(function (a, b) {
        return <any>new Date(b.taskCreationDate) - <any>new Date(a.taskCreationDate);
      });
      this.refreshDataTable(data)
    }

    if (event === "Recently viewed") {
      let filterdData = []
      filterdData = this.allTasks;
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      let data = filterdData.sort(function (a, b) {
        return <any>new Date(b.recentViewDate) - <any>new Date(a.recentViewDate);
      });
      this.refreshDataTable(data)
    }


    if (event === "Delegated tasks") {
      let filterdData = []
      filterdData = this.allTasks.filter(item => {
        if (item.delegatedTo && item.delegatedTo.trim().length > 0) {
          return item
        }
      });
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData)
    }

    if (event === "Recently completed") {
      let filterdData = []
      let today = new Date();
      let lastDayOfWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      filterdData = this.allTasks.filter(item => {
        if (item.taskStatus === "Completed" && new Date(item.modifiedDate) <= today && new Date(item.modifiedDate) > lastDayOfWeek) {
          return item
        }
      }
      );
      this.showNoRecordsAvailbleMessage = filterdData.length != 0 ? false : true;
      this.refreshDataTable(filterdData)
    }

  }

  refreshDataTable(filterdData) {
    let data = filterdData;
    data = [...data]
    this.dataSource = new MatTableDataSource(data);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }


  onChange(actionName, value: any) {
    let emitData = { actionName: actionName, data: value }
    if (actionName === 'changeDisplayUI') {
      this.selectedView = value;
      emitData = { actionName: actionName, data: this.selectedView }
    }

    if (actionName === 'editTask') {
      let data = value;
      this.updateTaskId = data.taskId;
      emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'deleteTask') {
      let data = value;
      this.isDeletedId = data.taskId;
      emitData = { actionName: actionName, data: data },
        this.isSelectedTaskEmpty = true;
    }

    if (actionName === 'updateTaskStatus') {
      let data = value;

      this.updateTaskId = data.taskId;
      emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'updateViewTime') {
      let data = value;
      emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'closeViewPage') {
      this.isSelectedTaskEmpty = true
      // let data = value;
      // emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'createEmail') {
      //this.openEmailDialog();
    }

    this.valueChange.emit(emitData);
  }

  openEmailDialog(){
    // const matDialogRef = this.matDialog.open(EmailDialogComponent, {
    //   width: '75%',
    //   // height: '80vh',
    //   panelClass: 'mat-dialog-container-custom-css',
    //   data: {
    //     module: 'tasks',
    //   },
    // })
    // matDialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     // this.getAllTaskList();
    //   }
    // })
  }
  

  viewTask(data) {
    this.selectedTask = data
    this.isSelectedTaskEmpty = false;
    this.onChange('updateViewTime', data)
  }

  onChangesReceived(data: any) {
    this.onChange(data.actionName, data.data)
  }


  getTagList(taskData) {
    this.allTagList = taskData.slice().map(element => {
      return element.taskTagList
    })

    this.allTagList = this.allTagList.flat(Infinity).filter((tag, index) => {
      delete tag.tagId
      const singleTag = JSON.stringify(tag);
      return index === this.allTagList.flat(Infinity).findIndex(obj => {
        return JSON.stringify(obj) === singleTag;
      });
    })
  }










  visible: boolean = true;
  selectable: boolean = true;
  removable: boolean = true;
  addOnBlur: boolean = false;

  separatorKeysCodes = [ENTER, COMMA];

  tagCtrl = new UntypedFormControl();

  filteredTags: Observable<any[]>;

  @ViewChild('tagInput') tagInput: ElementRef;

  add(event: MatChipInputEvent): void {
    const input = event.input;
    const value = event.value;
    if ((value || '').trim()) {
      this.tags.push(value);
    }
    if (input) {
      input.value = '';
    }
    this.tagCtrl.setValue(null);
  }

  remove(tag: any): void {
    const index = this.tags.indexOf(tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
    }
    this.onChangeTag(this.tags)
  }

  private filter(value: any): any[] {
    return this.allTagList.filter(tag => tag.value.toLowerCase().includes(value.toString().toLowerCase()));
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    this.tags.push(event.option.value);
    this.tagInput.nativeElement.value = '';
    this.tagCtrl.setValue(null);
    this.onChangeTag(this.tags)
  }




  onChangeTag(tag) {
    let data = [];
    let investmentData: any = [];
    let finalData = []
    if (tag.length === 0) {
      data = this.allTasks;
      this.dataSource = new MatTableDataSource(data);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
    if (tag.length != 0) {
      let count = 0;
      let numberOfinvestments = tag.filter(ele => ele.name.toLowerCase() === 'investment').length;
      let numberOfModules = tag.filter(ele => ele.name.toLowerCase() === 'module').length
      this.allTasks.filter((item, index) => {
        count++;
        tag.forEach((oneTag) => {
          if (numberOfinvestments != 0) {
            if (item.taskTagList.filter(e => e.name.toLowerCase() === "investment" && "investment" === oneTag.name.toLowerCase() && e.value.toLowerCase() === oneTag.value.toLowerCase()).length != 0) {
              data.push(item);
              investmentData = data;
            }
          } else {
            if (item.taskTagList.filter(e => e.name.toLowerCase() === "module" && "module" === oneTag.name.toLowerCase() && e.value.toLowerCase() === oneTag.value.toLowerCase()).length != 0) {
              data.push(item);

              finalData = data;
              this.dataSource = new MatTableDataSource(finalData);
              this.dataSource.paginator = this.paginator;
              this.dataSource.sort = this.sort;

            }
          }


        });
      })

      if (numberOfModules == 0 && numberOfinvestments != 0) {
        finalData = investmentData;

        this.dataSource = new MatTableDataSource(finalData);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }

      if (numberOfModules != 0 && numberOfinvestments != 0) {
        investmentData.forEach(filteredTask => {
          tag.forEach(oneTag => {

            if (filteredTask.taskTagList.filter(e => e.name.toLowerCase() === "module" && "module" === oneTag.name.toLowerCase() && e.value.toLowerCase() === oneTag.value.toLowerCase()).length != 0) {
              finalData.push(filteredTask);

              this.dataSource = new MatTableDataSource(finalData);
              this.dataSource.paginator = this.paginator;
              this.dataSource.sort = this.sort;
              if (finalData.length == 0) {
                let emptyData = []
                this.dataSource = new MatTableDataSource(emptyData);
                this.dataSource.paginator = this.paginator;
                this.dataSource.sort = this.sort;
              }
            }
          });
        });
      }
    }
  }



  getColor(value){ 
    
    if(value.toLowerCase() != "investment"){
      return 'accent';
    }else{
      return "warn"
    }
  }

  onCancelFromEditDeleteAction() {
    this.isDeletedId = null;
    this.updateTaskId = null;
  }

}
