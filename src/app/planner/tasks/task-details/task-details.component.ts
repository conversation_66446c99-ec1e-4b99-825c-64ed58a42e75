import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { saveAs } from 'file-saver';
import { TaskService } from '../../service/task.service';
import { MatDialog } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import JsonData from 'src/assets/data.json';
@Component({
  selector: 'app-task-details',
  templateUrl: './task-details.component.html',
  styleUrls: ['./task-details.component.scss']
})
export class TaskDetailsComponent implements OnInit {
  @Input() taskData: any;
  @Output() valueChange = new EventEmitter();
  hide : boolean = false;
  selectedTask: any = {
    assigneeEmailId: "",
    assigneeName: "",
    assigneeType: "",
    createdBy: "",
    createdDate: "",
    dateRange: "",
    daysAfterReminder: "",
    delegatedEmailId: null,
    delegatedTo: null,
    deleted: false,
    investmentName: null,
    modifiedBy: "",
    modifiedDate: "",
    moduleName: null,
    projectName: null,
    taskAttachement: null,
    taskCreationDate: "",
    taskDescription: "",
    taskDueDate: "",
    taskId: 0,
    taskName: "",
    taskOwner: "",
    taskPriority: "",
    taskReminder: "",
    taskSerialNumber: 0,
    taskStatus: ""
  };
  selectedView: any;
  updateTaskId: any;
  isDeletedId: any;
  taskCommentList: any = 0;
  JsonData:any;
  constructor(private taskService: TaskService, 
    private matDialog: MatDialog, private notification: ToasterService, ) { }

  ngOnInit() {
    this.selectedTask = this.taskData;
    if(this.selectedTask.taskCommentList){
      this.taskCommentList = this.selectedTask.taskCommentList;
    }
  }

  ngOnChanges() {
    this.selectedTask = this.taskData;
    if(this.selectedTask.taskCommentList){
      this.taskCommentList = this.selectedTask.taskCommentList;
    }
  }

  onMarkAsComplete(selectedTask){
    let data = {taskStatus : 'Completed' , taskId : selectedTask.taskId}
    this.onChange('updateTaskStatus' , data)
  }

  onChange(actionName, value: any) {
    let emitData = { actionName: actionName, data: value }
    if (actionName === 'editTask') {
      let data = value;
      this.updateTaskId = data.taskId;
      emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'updateTaskStatus') {
      let data = value;
      this.updateTaskId = data.taskId;
      emitData = { actionName: actionName, data: data }
    }

    if (actionName === 'deleteTask') {
      let data = value;
      this.isDeletedId = data.taskId;
      emitData = { actionName: actionName, data: data }
    }
    this.valueChange.emit(emitData);
  }

  /**
   * Follwing function is for downloading a file which is attached to a task.
   * @param filename : name of the file which needs to be download.
   * @param taskId : taskId for a refernce of tasks.
   */

  downloadFile(filename, taskId) {
    this.taskService.downloadFile(taskId).subscribe((res: any) => {
      const blob = new Blob([res], { type: 'application/octet-stream' });
      const file = new File([blob], filename, { type: 'application/octet-stream' });
      saveAs(file);
      this.notification.success(JsonData["label.success.DownloadFile"])
    })
  }

  /**
   * Following function is to give options to select a status while reopening a task.
   * @param selectedTask : task data whose status needs to be updated
   */
  onReopen(selectedTask){
      // const matDialogRef = this.matDialog.open(ReopenTaskDialogComponent, {
      //   // width: '40%',
      //   // panelClass: 'mat-dialog-container-custom-css',
      //   data : selectedTask
      // })
      // matDialogRef.afterClosed().subscribe(result => {
      //   if (result) {
      //     this.onChange('updateTaskStatus' , result)
      //   }
      // }) 
  }
}
