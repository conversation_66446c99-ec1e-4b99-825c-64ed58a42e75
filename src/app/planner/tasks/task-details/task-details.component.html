<mat-card appearance="outlined" *ngIf="selectedTask">
  <div fxLayout="row wrap">
    <div fxFlex="75%" fxFlex.md="75%" fxFlex.xs="80%" fxFlex.sm="80%"   class="widgetsIcon">
      <mat-icon>widgets</mat-icon>
     <h2 class="carbon-fix-margin-top-title selectedTaskName" >{{selectedTask.taskName}}</h2>
       
    </div>
    <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%" class="actionBtns ">
      <div class="backButton">
        <ng-content select="[backButton]"></ng-content>
      </div>
      <div class="taskCompletedBtn">

        <button class="addTaskBtn" *ngIf="selectedTask.taskStatus != 'Completed'" matTooltip="Mark as complete"
          (click)="onMarkAsComplete(selectedTask)" mat-raised-button class="green">
          <ng-container *ngIf="selectedTask.taskStatus != 'Completed'">Complete </ng-container>

        </button>
        <button class="addTaskBtn" *ngIf="selectedTask.taskStatus === 'Completed'" matTooltip="Reopen task"
          (click)="onReopen(selectedTask)" mat-raised-button color="primary">

          <ng-container>Reopen </ng-container>
        </button>
      </div>
      <div class="display-flex">

       
      </div>
    </div>
    <div fxFlex="75%" fxFlex.md="75%" fxFlex.xs="80%" fxFlex.sm="80%"  class="top-bar-buttons" >
        <ng-content select="[monitorTags]"></ng-content>
    </div>
  </div>

  <mat-card appearance="outlined" class="mat-elevation-z0 ">
    <div fxLayout="row wrap" class="taskOwner" >
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <span class="label-span-header"> Task owner </span>
      </div>
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <span class="label-span-header"> Creation date </span>
      </div>
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <span class="label-span-header">Due date </span>
      </div>
    </div>
    <div fxLayout="row wrap">
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <p class="labelValue"> {{selectedTask.taskOwner}} </p>
      </div>
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <p class="labelValue"> {{selectedTask.taskCreationDate | date}}</p>
      </div>
      <div fxFlex="25%" fxFlex.md="30%" fxFlex.xs="50%" fxFlex.sm="50%">
        <p class="labelValue">{{selectedTask.taskDueDate | date}}</p>
      </div>
    </div>
  </mat-card>
</mat-card>

<mat-card appearance="outlined" class="taskDetailsContainer">

  <mat-tab-group class="detailsTab" [dynamicHeight]='true'>

    <mat-tab label="Details">
      <div fxLayout="row wrap" >
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="taskDetails" >
          <!-- <mat-list>
              <mat-list-item> -->
          <div>
            <span class="label-span-header"> Task description </span>
          </div>

          <!-- 
              </mat-list-item>
            </mat-list> -->
          <div class="listDescripation taskDescription" >
            <p class="labelValueInDetailContainer taskDescriptionContainer" >
              {{selectedTask.taskDescription}}
            </p>
          </div>

          <mat-divider class="insideDivider"></mat-divider>
        </div>

        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <div fxLayout="row wrap">
            <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
              <mat-list>
                <mat-list-item>
                  <div mat-line>
                    <span class="label-span-header"> Task priority </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.taskPriority}}</p>
                  </div>
                </mat-list-item>
                <mat-divider mat-line class="leftHalfColoumnDivider"></mat-divider>
                <mat-list-item>
                  <div mat-line>
                    <span class="label-span-header"> Assignee Type </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.assigneeType}}</p>
                  </div>
                </mat-list-item>
                <mat-divider class="leftHalfColoumnDivider"></mat-divider>

                <mat-list-item>
                  <div mat-line>
                    <span class="label-span-header"> Reminder </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.taskReminder}}</p>
                  </div>
                </mat-list-item>
                <mat-divider class="leftHalfColoumnDivider"></mat-divider>

                <mat-list-item *ngIf="selectedTask.taskAttachement">
                  <div mat-line>
                    <span class="label-span-header"> Attachment </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer pointer" matTooltip="Download a file"
                      (click)="downloadFile(selectedTask.taskAttachement , selectedTask.taskId)">
                      {{selectedTask.taskAttachement}}</p>
                  </div>
                </mat-list-item>
                <mat-divider *ngIf="selectedTask.taskAttachement" class="leftHalfColoumnDivider">
                </mat-divider>

               
              </mat-list>
            </div>





            <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
              <mat-list>
                <mat-list-item>
                  <div mat-line>
                    <span class="label-span-header"> Task status </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.taskStatus}}</p>
                  </div>
                </mat-list-item>
                <mat-divider class="rightHalfColoumnDivider"></mat-divider>
                <mat-list-item>
                  <div mat-line>
                    <span class="label-span-header"> Assignee Name </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.assigneeName}}</p>
                  </div>
                </mat-list-item>
                <mat-divider class="rightHalfColoumnDivider"></mat-divider>
                <mat-list-item *ngIf='selectedTask.dateRange'>
                  <div mat-line>
                    <span class="label-span-header"> Remind on </span>
                  </div>
                  <div mat-line>
                    <p class="labelValueInDetailContainer"> {{selectedTask.dateRange | date}}</p>
                  </div>
                </mat-list-item>
                <mat-divider *ngIf='selectedTask.dateRange' class="rightHalfColoumnDivider"></mat-divider>

              
              </mat-list>
            </div>


            <ng-container *ngIf="taskCommentList.length != 0 && hide" >

              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
               
                <p class="label-span-header taskComments" >Comments</p>
                
                <div *ngFor="let comment of taskCommentList">


                  <mat-card appearance="outlined" class="example-card mat-elevation-z0 ">
                    <div>

                      
                      <p>
                        <span class="font-weight-500 selectedRole" >{{comment.createdBy}}</span> <span> added a comment -
                          {{comment.createdDate | date}}</span>
                      </p>
                    </div>

                    <mat-card-content>
                      <p>
                        {{comment.commentDescription}}
                      </p>
                    </mat-card-content>
                  </mat-card>
                  <mat-divider class="taskdivider"></mat-divider>
                </div>
              </div>

              
            </ng-container>


          </div>
        </div>
      </div>
    </mat-tab>

  </mat-tab-group>
</mat-card>
