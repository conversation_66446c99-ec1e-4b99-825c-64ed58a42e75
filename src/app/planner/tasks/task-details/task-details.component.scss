.taskList {
    padding: 0.7% 1.3%;
  }
  .listCard {
    // height: 100%;
    overflow-y: auto
  }
  
  .viewTask {
    padding: 0.3%;
  }
  
  .taskSplitView {
    margin-top: 5%;
    height: 100%;
  }
  
  .selectStatusCss {
    margin-top: -4%;
    margin-left: 8%;
    font-weight: 500;
    /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
    ::ng-deep .mat-select-value {
      max-width: fit-content;
      width: fit-content;
    }
  }
  .addTaskContainer {
    right: 0;
    position: absolute;
  }
  .addTaskBtn {
    margin-top: -4%;
  }
  
  .displayUiIcon {
    right: 1%;
    position: absolute;
    margin-top: -2%;
  }
  
  .px-0 {
    padding: 16px 0 !important;
  }
  
  .my-1 {
    margin-top: 1%;
    margin-bottom: 1%;
  }
  .py-1 {
    padding: 1% 0;
  }
  
  .taskName {
    margin: 0 !important;
  }
  
  .taskName::first-letter {
    // font-weight: bold;
    text-transform: uppercase;
  }
  .viewTaskName {
    margin: 0.5% 2% !important;
    font-weight: 500;
  }
  
  .viewTaskName::first-letter {
    // font-weight: bold;
    text-transform: uppercase;
  }
  
  .label-span {
    font-weight: 300 !important;
    font-size: 13px !important;
  }
  
  .createdAndDueDate {
    right: 0;
    bottom: 28%;
    margin: 0;
    position: absolute;
  }
  
  .insideDivider {
    margin: -0.5% 0;
  }
  
  .editDeleteBtn {
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    ::ng-deep .mat-button-toggle-checked {
      background: none !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    ::ng-deep
      .mat-button-toggle-appearance-standard
      .mat-button-toggle-label-content {
      line-height: 33px;
      padding: 0 12px;
    }
  }
  
  .actionBtns {
    display: inline-flex;
    // right: 0;
    // position: absolute;
    padding-right: 2%;
  }
  
  .taskCompletedBtn {
    margin-right: 3%;
    margin-bottom: 1%;
  }
  
  .priorityIcon {
    font-size: 14px;
  }
  
  .highColor {
    font-size: 20px;
    color: #ff0000 !important;
  }
  .mediumColor {
    font-size: 20px;
    color: #ffff00 !important;
  }
  // .mediumColor {
  //   font-size: 20px;
  //   color: #f57e00e8 !important;
  // }
  
  .label-span-header {
    font-weight: 300 !important;
    font-size: 14px !important;
  }
  
  .labelValue {
    margin: 0;
    font-weight: 400;
    text-transform: capitalize;
    font-size: 15px;
  }
  
  .labelValueInDetailContainer {
    margin: 2% 0;
    font-weight: 400;
    text-transform: capitalize;
    font-size: 15px;
  }
  
  .taskDetailsContainer {
    margin: 1% 0;
    min-height: 60%;
    max-height: 60%;
    overflow: auto;
  }
  
  .rightHalfColoumnDivider {
    margin: -0.5% 0;
    width: 72% !important;
    left: auto !important;
  }
  
  .leftHalfColoumnDivider {
    margin: -0.5% 7% -0.5% 0%;
    width: 45% !important;
  }
  
  .noDataToView {
    position: absolute;
    
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  ::ng-deep .listDescripation .mat-mdc-list-base .mat-mdc-list-item .mat-line {
    white-space: none !important;
  }
  
  .relatedToLabel {
    font-size: 12px;
    text-transform: uppercase;
  }

  .backButton{
    margin-top: 0.8%;
    margin-right: 3%;
  }
  .widgetsIcon{
    padding: 0.2% 1% !important;
    display: inline-flex;
  }
  .selectedTaskName{
    font-weight: 400 !important;
     margin-left:1% !important;
  }
  .display-flex{
    display: flex;
  }
  .taskOwner{
    margin: 1% 0 1% 0 !important; 
  }
  .taskDescription{
    white-space:none !important
  }
  .taskDescriptionContainer{
    margin : 1% 0 2% 0 !important
  }
  .taskComments{
    margin-bottom: 0 !important;
    margin-left: 2% !important;
  }
  .taskdivider{
    margin: -0.5% !important
  }
  .taskDetails{
    padding: 16px 16px 0 16px !important
  }