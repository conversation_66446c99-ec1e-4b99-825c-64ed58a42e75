import { Injectable, EventEmitter, Inject } from '@angular/core';
import { Subject, Subscription, BehaviorSubject, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TaskService {
  isTaskAdded: boolean;
  invokeGetAllTaskList: Subject<any> = new Subject();
  subscribeTasks: Subscription;
  refreshAllTasks = new EventEmitter<boolean>()
  isFileUploaded: boolean;
  selectedFilter : string = "Recently viewed"
  public isTaskStatusUpdated = new BehaviorSubject<boolean>(false);

  setBooleanToTrue(valueChange: boolean) {
      this.isTaskStatusUpdated.next(valueChange);
  }

  getBooleanValue() {
      return this.isTaskStatusUpdated.asObservable();
  }

  constructor(private http: HttpClient, @Inject('tasksBaseUrl') private baseUrl: string, private dataShareService: DataSharingService) { }

  addTask(taskData) {

      return this.http.post(`${this.baseUrl}/tasks`, taskData)
          .pipe(map(responseData => {
              responseData;
          }),
              // catchError(errorResponse => {
              //     const errormsg = errorResponse.message;
              //     return throwError(errormsg);
              // })
          );
  }



  updateTask(taskData, taskId) {

      return this.http.put(`${this.baseUrl}/tasks/` + taskId, taskData)
          .pipe(map(responseData => {
              return responseData;
          }),
              // catchError(errorResponse => {
              //     const errormsg = errorResponse;
              //     return throwError(errormsg);
              // })
          );
  }

  updateTaskStatus(taskStatus, taskId) {
      let taskData = {}

      return this.http.put(`${this.baseUrl}/tasks/` + taskStatus + '/' + taskId, taskData)
          .pipe(map(responseData => {
              return responseData;
          }),
              catchError(errorResponse => {
                  const errormsg = errorResponse;
                  return throwError(errormsg);
              })
          );
  }


  updateViewTime(taskId) {
      let taskData = {}

      return this.http.put(`${this.baseUrl}/tasks/recentTask/` + taskId, taskData)
          .pipe(map(responseData => {
              return responseData;
          }),
              catchError(errorResponse => {
                  const errormsg = errorResponse;
                  return throwError(errormsg);
              })
          );
  }

  getTask() {

      return this.http.get(`${this.baseUrl}/tasks`, { withCredentials: true })
          .pipe(map(responseData => {
              return this.dataShareService.responseToJson(responseData);
          }),
              catchError(errorResponse => {
                  const errormsg = errorResponse.message;
                  return throwError(errormsg);
              })
          );
  }


  deleteTask(taskId) {

      return this.http.delete(`${this.baseUrl}/tasks/` + taskId, { observe: 'response' })
          .pipe(map(responseData => {
              return this.dataShareService.responseToJson(responseData);
          }),
              catchError(errorResponse => {
                  const errormsg = errorResponse;
                  return throwError(errormsg);
              })
          );
  }




  OnAddingTaskGetUpdatedTaskList() {
      this.invokeGetAllTaskList.next('taskAdded');
  }


  getDateFormatInPayload(newdate) {
      if (newdate) {
          let startDateFull = new Date(newdate);
          let startDateDay = '' + startDateFull.getDate();
          let startDateMonth: any = startDateFull.getMonth() + 1;
          let startDateYear = startDateFull.getFullYear();
          if (startDateMonth.toString().length < 2)
              startDateMonth = '0' + startDateMonth.toString();
          if (startDateDay.length < 2)
              startDateDay = '0' + startDateDay;

          let result = [startDateYear, startDateMonth, startDateDay].join('-');
          return result;
      }
      else {
          return
      }
  }



  uploadTemplateConfiguration() {

  }


  downloadFile(taskId) {
      return this.http.get(`${this.baseUrl}/tasks/downloadFile/` + taskId, { responseType: 'blob' })
          .pipe(map(responseData => {

              return responseData
          }),
              catchError(errorResponse => {
                  const errormsg = errorResponse.message;
                  return throwError(errormsg);
              })
          );

  }



  days_between(date2) {
      const dt1 = new Date();
      const dt2 = new Date(date2);
      const numberOfDays = Math.floor((Date.UTC(dt2.getFullYear(), dt2.getMonth(), dt2.getDate()) - Date.UTC(dt1.getFullYear(), dt1.getMonth(), dt1.getDate())) / (1000 * 60 * 60 * 24));

      if (numberOfDays < 0) {
          if (numberOfDays != -1) {

              return "Running late by " + numberOfDays.toString().replace('-', "") + " days";
          } else {
              return "Running late by " + numberOfDays.toString().replace('-', "") + " day";
          }
      } else {
          if (numberOfDays != 1) {
              return numberOfDays + " " + "days remaining";
          } else {
              return numberOfDays + " " + "day remaining";
          }
      }
  }



}
