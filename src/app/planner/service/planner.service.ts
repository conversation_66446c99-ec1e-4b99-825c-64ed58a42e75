import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PlannerService {

  constructor() { }



  private allMeetingListSource = new Subject<any>();

  allMeetingDataChangeEmitted$ = this.allMeetingListSource.asObservable();

  emitChangesOfSelectedAllMeetingData(data) {
   
    this.allMeetingListSource.next(data);
  }



  private oneMeetingListSource = new Subject<any>();

  oneMeetingDataChangeEmitted$ = this.oneMeetingListSource.asObservable();

  emitChangesOfSelectedOneMeetingData(data) {
    this.oneMeetingListSource.next(data);
  }

 
}
