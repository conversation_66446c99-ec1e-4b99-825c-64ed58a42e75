export const meetingData = [
    {
        id: 1,
        status: "In progress",
        createdDate: "Thu Sep 9 2021 00:00:00 GMT+0530",
        meetingSource: [
            {
                'name': 'VC-11',
                'lead': '<PERSON><PERSON>',
                'meetingMinutes': [{}],
                'meetingAgendaDetail': [
                    {
                        agendaTitle: "Declaration Of Interest by Committee Members",
                        agendaDetails: [
                            {
                                comment: "Given that IC meeting needs its own meeting details, notes and minutes , and there will be actions and tasks created during the meeting as Originate FE engineer I want to develop the meeting module UI ",
                                documents: [
                                    { name: "8_IC Meeting_Meeting details.txt" }
                                ],
                                assignedTo: "<PERSON><PERSON> se<PERSON>",
                                isCreateMinute: true,
                                minutesComments : [
                                    { 
                                        commentDescription  :  "First comment" ,
                                        createdBy : "Operator",
                                        createdDate : "9/23/2021"
                                 
                                     }
                                ],
                                actions: [
                                    {
                                        description: "Add a button to convert a meeting note to minutes - 'Create Minute' only those go to minutes tab",
                                        documents: [
                                            { name: "IC Meeting_Minutes_comment.txt" }
                                        ],
                                        assignedTo: "<PERSON><PERSON><PERSON> varma",
                                        isCreateMinute: true,

                                    },
                                    {
                                        description: "Meeting notes and Minutes - would be separate tabs - since not all meeting notes turn into minutes ",
                                        documents: [],
                                        assignedTo: "",
                                        isCreateMinute: true,

                                    },
                                ]
                            }
                        ]
                    },
                    {
                        agendaTitle: "Plan Creating  Notes in Minutes",
                        agendaDetails: [
                            {
                                comment: "Minutes - is read only page where the users can view what has been discussed and by who and then",
                                documents: [],
                                assignedTo: "Supriya Joshi",
                                isCreateMinute: true,
                                minutesComments : [
                                  
                                 ],
                                actions: [
                                    {
                                        description: "Things need to be IN MINUTES PAGE: all the things in note page and Need to add comment option on discussion.",
                                        documents: [{ name: 'IC Meeting_Minutes.txt' }],
                                        assignedTo: "",
                                        isCreateMinute: true,

                                    },

                                ]
                            }
                        ]
                    }
                ],

            },
            {
                'name': 'VC-14',
                'lead': 'Supriya Patil',
                'meetingMinutes': [{}],
                'meetingAgendaDetail': [
                    {
                        agendaTitle: "Declaration Of Interest by Committee Members",
                        agendaDetails: [
                            {
                                comment: "Given that IC meeting needs its own meeting details, notes and minutes , and there will be actions and tasks created during the meeting as Originate FE engineer I want to develop the meeting module UI ",
                                documents: [
                                    { name: "8_IC Meeting_Meeting details.txt" }
                                ],
                                assignedTo: "Rajat sehgal",
                                isCreateMinute: true,
                                minutesComments : [
                                    
                                 ],
                                actions: [
                                    {
                                        description: "Add a button to convert a meeting note to minutes - 'Create Minute' only those go to minutes tab",
                                        documents: [
                                            { name: "IC Meeting_Minutes_comment.txt" }
                                        ],
                                        assignedTo: "Rakesh varma",
                                        isCreateMinute: true,

                                    },
                                    {
                                        description: "Meeting notes and Minutes - would be separate tabs - since not all meeting notes turn into minutes ",
                                        documents: [],
                                        assignedTo: "",
                                        isCreateMinute: true,

                                    },
                                ]
                            }
                        ]
                    },
                    {
                        agendaTitle: "Plan Summary of Deals in discussion",
                        agendaDetails: [
                            {
                                comment: "Minutes - is read only page where the users can view what has been discussed and by who and then",
                                documents: [],
                                assignedTo: "Supriya Joshi",
                                isCreateMinute: true,
                                actions: [],
                                minutesComments : [
                                  
                                ]
                            }
                        ]
                    },


                ],

            }
        ],
        description: "",
        dueDate: "Thu Sep 16 2021 00:00:00 GMT+0530",
        meetingMode: "Virtual Meeting",
        chairsReport: "",
        meetingSummary: "IC review meeting for Multiple deals",
        type: "Ic Meeting",
        createdBy: "Supriya Patil",
        userList: [
            {
                countryCode: "+91",
                createdDate: "2021-08-30 08:29:78",
                name: "Karan Teckchandani",
                identifier: "operator",

                mailId: "<EMAIL>",
                phoneNumber: "9823441330",
                role: "administrator",
                isAttended: true,
            },
            {
                countryCode: "+91",
                createdDate: "2021-08-30 08:29:78",
                name: "Rakesh Varma",
                identifier: "operator",

                mailId: "<EMAIL>",
                phoneNumber: "9823441330",
                role: "administrator",
                isAttended: true,
            },
            {
                countryCode: "+91",
                createdDate: "2021-08-30 08:29:78",
                name: "Supriya Joshi",
                identifier: "operator",

                mailId: "<EMAIL>",
                phoneNumber: "9823441330",
                role: "administrator",
                isAttended: true,
            },
            {
                countryCode: "+91",
                createdDate: "2021-08-30 08:29:78",
                name: "Rajat sehgal",
                identifier: "operator",

                mailId: "<EMAIL>",
                phoneNumber: "9823441330",
                role: "administrator",
                isAttended: true,
            },
            {
                countryCode: "+91",
                createdDate: "2021-08-30 08:29:78",
                name: "Supriya Patil",
                identifier: "operator",

                mailId: "<EMAIL>",
                phoneNumber: "9823441330",
                role: "administrator",
                isAttended: false,
            },
        ]
    }
]




export const meetingSummaryTabs = [
    { index: 0, id: 1, label: 'Meeting notes', icon: 'graphic_eq', isDisable: false, link: 'notes' },
    { index: 1, id: 2, label: 'Details', icon: 'assignment', isDisable: false, link: 'details' },
    { index: 2, id: 3, label: 'Minutes', icon: 'assignment', isDisable: false, link: 'minutes' },
    { index: 3, id: 4, label: 'Documents', icon: 'file_copy', isDisable: false, link: 'documents' },
    { index: 4, id: 5, label: 'Tasks', icon: 'file_copy', isDisable: false, link: 'tasks' },

]



export const doucmentList = [

        {
            "documentId": 40,
            "dmsId": 208,
            "workflowId": 136,
            "fileName": "IC Meeting_Minutes_comment.txt ",
            "size": 19,
            "type": "TEMPLATE",
            "referenceList": [
                "Originate",
                "Check Monitor POC",
                "136"
            ],
            "createdBy": "operator",
            "createdDate": "2021-09-23",
            "modifiedBy": null,
            "modifiedDate": null
        },

        {
            "documentId": 40,
            "dmsId": 208,
            "workflowId": 136,
            "fileName": "8_IC Meeting_Meeting details.txt ",
            "size": 19,
            "type": "TEMPLATE",
            "referenceList": [
                "Originate",
                "Check Monitor POC",
                "136"
            ],
            "createdBy": "operator",
            "createdDate": "2021-09-23",
            "modifiedBy": null,
            "modifiedDate": null
        },


        {
            "documentId": 40,
            "dmsId": 208,
            "workflowId": 136,
            "fileName": "IC Meeting_Minutes.txt",
            "size": 19,
            "type": "TEMPLATE",
            "referenceList": [
                "Originate",
                "Check Monitor POC",
                "136"
            ],
            "createdBy": "operator",
            "createdDate": "2021-09-23",
            "modifiedBy": null,
            "modifiedDate": null
        },

        {
            "documentId": 40,
            "dmsId": 208,
            "workflowId": 136,
            "fileName": "IC Meeting_Minutes.txt",
            "size": 19,
            "type": "TEMPLATE",
            "referenceList": [
                "Originate",
                "Check Monitor POC",
                "136"
            ],
            "createdBy": "operator",
            "createdDate": "2021-09-23",
            "modifiedBy": null,
            "modifiedDate": null
        },
    
]



export const tasksTableColumnList = [
    { index: 0,  viewValue: 'Priority icon', value: 'priorityAndUserType' ,isSelected :true },
    { index: 1,  viewValue: 'Task summary', value: 'taskName' , isSelected :true },
    { index: 2,  viewValue: 'Task status', value: 'taskStatus' , isSelected :true},
    { index: 3,  viewValue: 'Task Priority', value: 'taskPriority' , isSelected :false},
    { index: 4,  viewValue: 'Created date', value: 'taskCreationDate' , isSelected :true},
    { index: 5,  viewValue: 'Due Date', value: 'taskDueDate', isSelected :true },
    { index: 6,  viewValue: 'Assigned to', value: 'assigneeName', isSelected :true },
    { index: 7,  viewValue: 'Assigned type', value: 'assigneeType', isSelected :false },
    { index: 8,  viewValue: 'Reminder', value: 'taskReminder' , isSelected :false},
    { index: 9,  viewValue: 'Task Owner', value: 'taskOwner', isSelected :false },
    { index: 10,  viewValue: 'Action', value: 'action' , isSelected :false},
  ];


  export const tasksStatusList = [
    // { value: 'all', viewValue: 'All' },
    { value: 'Open', viewValue: 'Open' },
    { value: 'In progress', viewValue: 'In progress' },
    { value: 'In review', viewValue: 'In review' },
    { value: 'Recently viewed', viewValue: 'Recently viewed' },
    { value: 'Recently completed', viewValue: 'Recently completed' },
    { value: 'Recently created', viewValue: 'Recently created' },
    // { value: 'Recurring tasks', viewValue: 'Recurring tasks' },
    { value: "Todays tasks", viewValue: "Today's tasks" },
    // { value: 'Unscheduled tasks', viewValue: 'Unscheduled tasks' }
  ];


  export const tasksStatusListForKanbanUI = [
    // { value: 'all', viewValue: 'All' },
    // { value: 'Open', viewValue: 'Open' },
    // { value: 'In progress', viewValue: 'In progress' },
    // { value: 'In review', viewValue: 'In review' },
    { value: 'Recently viewed', viewValue: 'Recently viewed' },
    { value: 'Recently completed', viewValue: 'Recently completed' },
    { value: 'Recently created', viewValue: 'Recently created' },
    // { value: 'Recurring tasks', viewValue: 'Recurring tasks' },
    { value: "Todays tasks", viewValue: "Today's tasks" },
    // { value: 'Unscheduled tasks', viewValue: 'Unscheduled tasks' }
  ];



  export const customerData = {
    "companyId": null,
    "customerDetails": {
        "entityDetail": [
            {
                "role": {
                    "name": "Role",
                    "value": "",
                    "inputType": "Picklist",
                    "displayProperty": {
                        "validation": "",
                        "displayName": "Role",
                        "defaultValues": "Director,Promotor,Signing Authority,Beneficiary,Share Holder",
                        "isForFormView": true,
                        "isForListView": true
                    }
                }
            },
            {
                "company": {
                    "name": "Company",
                    "value": "",
                    "inputType": "Picklist",
                    "displayProperty": {
                        "validation": "",
                        "displayName": "Company",
                        "defaultValues": "Director,Promotor,Signing Authority,Beneficiary,Share Holder",
                        "isForFormView": true,
                        "isForListView": true
                    }
                }
            },
            {
                "contactEmail": {
                    "name": "Contact Email",
                    "value": "",
                    "inputType": "Email",
                    "displayProperty": {
                        "validation": "",
                        "displayName": "Contact Email",
                        "defaultValues": "",
                        "isForFormView": true,
                        "isForListView": true
                    }
                }
            },
            {
                "contactPhone": {
                    "name": "Contact Phone",
                    "value": "",
                    "inputType": "Number",
                    "displayProperty": {
                        "validation": "",
                        "displayName": "Contact Phone",
                        "defaultValues": "",
                        "isForFormView": true,
                        "isForListView": true
                    }
                }
            },
            {
                "contactMobile": {
                    "name": "Contact Mobile",
                    "value": "",
                    "inputType": "Number",
                    "displayName": "Contact Mobile",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "Y",
                        "validation": "",
                        "displayName": "Contact Mobile",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "pan": {
                    "name": "PAN",
                    "value": "",
                    "inputType": "Alphanumeric",
                    "displayName": "PAN",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "validation": "",
                        "displayName": "PAN",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "annualIncome": {
                    "name": "Annual Income",
                    "value": "",
                    "inputType": "Currency",
                    "displayName": "Annual Income",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "validation": "",
                        "displayName": "Annual Income",
                        "defaultValues": " ",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "creditScore": {
                    "name": "Credit Score",
                    "value": "",
                    "inputType": "Number",
                    "displayName": "Credit Score",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "Credit Score",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "country": {
                    "name": "Country",
                    "value": "",
                    "inputType": "Picklist",
                    "displayName": "Country",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "Country",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "state": {
                    "name": "State",
                    "value": "",
                    "inputType": "Picklist",
                    "displayName": "State",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "State",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "city": {
                    "name": "City",
                    "value": "",
                    "inputType": "Picklist",
                    "displayName": "City",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "City",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "pin": {
                    "name": "Pin",
                    "value": "",
                    "inputType": "Number",
                    "displayName": "Pin",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "Pin",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            },
            {
                "aadhar": {
                    "name": "Aadhar",
                    "value": "",
                    "inputType": "Alphanumeric",
                    "displayName": "Aadhar",
                    "displayProperty": {
                        "isDefault": "Yes",
                        "mandatory": "N",
                        "validation": "",
                        "displayName": "Aadhar",
                        "defaultValues": "",
                        "isForFormView": false,
                        "isForListView": false
                    }
                }
            }
        ]
    },
    "entityId": 9,
    "entityLinkageList": [],
    "entityType": "Person"
  }
  
  export const businessProcessDetails = {
    "currency": "INR",
    "currentStatus": "In progress",
    "currentStageName": "Onboarding",
    "dealAmount": null,
    "estimatedCloseDate": "2023-12-16",
    "businessProcessDetail": { },
    "dealCustomerList": [],
    "dealLinkageDetailsList": [],
    "dealLabelList": [],
    "dealIdentifier": "",
    "dealAsset": {
        "dealAssetItem": []
    }
  }
  
  export const defaultStepperConfig = [
      {
          type: 'horizontal-stepper',
          fieldGroup: []
      }
  ]
  
  export const defaultVerticalTabConfig = [
      {
          type: 'vertical-tabs',
          fieldGroup: []
      }
  ]