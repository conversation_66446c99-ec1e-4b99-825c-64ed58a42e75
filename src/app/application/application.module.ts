import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ApplicationComponent } from "./application/application.component";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { NewCustomerComponent } from "./application/new-customer/new-customer.component";
import { ApplicationRoutes } from "./application.routing";
import { KanbanViewComponent } from "./application/kanban-view/kanban-view.component";
import { TableViewComponent } from "./application/table-view/table-view.component";
import { UploadFileQDEComponent } from "./application/upload-file-QDE/upload-file-QDE.component";
import {
  NgxMatDatetimePickerModule,
  NgxMatTimepickerModule,
  NgxMatNativeDateModule,
} from "@angular-material-components/datetime-picker";

@NgModule({
  declarations: [
    ApplicationComponent,
    NewCustomerComponent,
    UploadFileQDEComponent,
    KanbanViewComponent,
    TableViewComponent,
  ],
  imports: [
    CommonModule,
    SharedModuleModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatNativeDateModule,
    ApplicationRoutes,
  ],
})
export class ApplicationModule {}
