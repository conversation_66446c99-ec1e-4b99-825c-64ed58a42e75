import {
  UntypedFormGroup,
  UntypedFormBuilder,
  FormControl,
  Validators,
} from "@angular/forms";
import {
  Component,
  OnInit,
  Inject,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
  AfterContentChecked,
} from "@angular/core";
import { DealService } from "../../../shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { catchError, takeUntil } from "rxjs/operators";
import { IdentityService } from "../../../shared-service/identity.service";
import { DatePipe } from "@angular/common";
import { HttpResponse } from "@angular/common/http";
import { DataSharingService } from "../../../common/dataSharing.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import { CurrencySymbolPipe } from "src/app/common/currency/currency-symbol.pipe";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { Router } from "@angular/router";
import { EMPTY, Subject } from "rxjs";
import {
  FormSources,
  SourceInfo,
  ZcpDataTypes,
} from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
@Component({
  selector: "app-new-customer",
  templateUrl: "./new-customer.component.html",
  styleUrls: ["./new-customer.component.scss"],
  providers: [CurrencySymbolPipe],
})
export class NewCustomerComponent implements OnInit, AfterContentChecked {
  @ViewChild("searchEntityInput") searchEntityInput: ElementRef;
  @ViewChild("entityNameSelect") entityNameSelect;
  @ViewChild("searchBoxNameSelect") searchBoxNameSelect;

  businessProcessEntityDefinitionList: any[] = [];
  stgName =
    this.dataSharingService?.getDataById?.businessProcessStageList[0]?.name ??
    "QDE";

  fileData: any = new FormData();
  searchText: any;
  showspinnerinlist: any = false;
  showNoDataMessage: any = false;
  opened = false;
  QDEForm: UntypedFormGroup = new UntypedFormGroup({});
  today = new Date();
  allLabelList = [];
  businessProcessList = [];
  businessProcessDetail: any;
  defaultDealAssets = [];
  selectedUser = "operator";
  usersList: any = [];
  selectedPrimaryActorDetails: any;
  selectedBusinessProcessInNewDeal: any;
  defualtStageForDeal: any;
  QDEStageDetails = [];
  formFieldsWithDetails = [];
  formFieldsWithDetailsPayload: any;
  showQDEForm = false;
  additionalFieldsFromQDEStage = [];
  extentionList = [];
  dealUserDetails = [];
  mode = "async";
  selectedPrimaryActorFromList: any = null;
  private unsubscribe$ = new Subject();
  disableCreateBtn = true;
  showCreateOption: boolean;
  teamLeadValue: any;
  JsonData: any;
  dealNameFormControl: FormControl;

  parentConfigList: any = {};
  childConfigList: any = {};
  searchedBP = "";
  useNewThemeUI: boolean;
  docArray: any = [];
  sourceInfo: SourceInfo;
  // nameRegex: RegExp;
  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.sourceInfo = <SourceInfo>{ type: FormSources.Deal };
    this.dealNameFormControl = new FormControl("", [
      Validators.pattern(this.errorMessageService.nameRegex),
    ]);
  }

  constructor(
    private errorService: ErrorService,
    private fb: UntypedFormBuilder,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    public service: ApplicationLabelService,
    public identityService: IdentityService,
    private businessProcessService: BusinessProcessService,
    public matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data,
    public dataSharingService: DataSharingService,
    public dialogRef: MatDialogRef<NewCustomerComponent>,
    private entityService: EntityService,
    private errorMessageService: ValidationErrorMessageService,
    private changeDetector: ChangeDetectorRef,
    public datepipe: DatePipe,
    public themeService: ThemeService,
    public router: Router,
    private dataTypesUtils: DataTypesUtilsService
  ) {
    this.getUserList();
    this.selectedUser = localStorage.getItem("user");
    this.getAllLabelColors();

    // Equanimity requirement

    this.selectedBusinessProcessInNewDeal = data.selectedBusinessProcess;
    this.selectedPrimaryActorDetails = data.selectedPrimaryActorDetails;
    this.today.setDate(this.today.getDate());
    this.businessProcessList = this.businessProcessService.businessProcessList;
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });

    this.onSelectOfBusinessProcess(this.selectedBusinessProcessInNewDeal);
  }

  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }

  filterData($event) {
    this.searchedBP = $event;
  }

  getList(list) {
    if (this.searchedBP) {
      return list
        .slice()
        .filter((list) =>
          list.name.toLowerCase().includes(this.searchedBP.toLowerCase())
        );
    } else {
      return list;
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }

  getPrimaryContactDetails(selectedBuisnessProcess, name) {
    return this.selectedPrimaryActorFromList
      ? this.selectedPrimaryActorFromList[name]
      : null;
  }

  checkTeamLeadValue() {
    this.dataSharingService.getDataById.assetItems?.forEach((e) => {
      if (
        this.getPropertyName(e) == "teamLead" &&
        e[this.getPropertyName(e)]?.value == ""
      ) {
        this.checkTeamMemberValue();
        if (this.teamLeadValue) {
          e[this.getPropertyName(e)].value = this.teamLeadValue;
          this.formFieldsWithDetailsPayload.push(e);
        }
      }
    });
  }

  checkTeamMemberValue() {
    this.dataSharingService.getDataById.assetItems.forEach((element) => {
      if (
        this.getPropertyName(element) == "teamMembers" &&
        element[this.getPropertyName(element)]?.value.length
      ) {
        element[this.getPropertyName(element)]?.value.forEach((ele) => {
          if (ele.id == this.selectedUser) {
            this.teamLeadValue = ele;
          }
        });
      }
    });
  }

  onCreate() {
    if (this.QDEForm.get("bussinessProcess")) {
      this.QDEForm.patchValue({
        bussinessProcess: this.selectedBusinessProcessInNewDeal,
      });
    }

    this.QDEForm.markAllAsTouched();

    if (
      this.dataSharingService.getDataById?.businessProcessEntityDefinition &&
      !(
        this.selectedPrimaryActorDetails &&
        this.selectedPrimaryActorDetails?.name !== undefined &&
        this.selectedPrimaryActorDetails?.name != null
      )
    ) {
      this.notificationMessage.error(
        `Please select a value for ${this.getPrimaryContactDetails(
          this.selectedBusinessProcessInNewDeal,
          "entityName"
        )}.`
      );
      return;
    }

    if (this.QDEForm.invalid || this.dealNameFormControl.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    this.getDealTeamFormmated();
    this.addDocuments();
    // this.getFormattedStageItemForQDE(this.QDEForm.value);
    this.formFieldsWithDetailsPayload = [...this.formFieldsWithDetails];
    this.checkTeamLeadValue();
    this.mode = "sync";
    const closeDate = new Date(
      new Date().setFullYear(new Date().getFullYear() + 1)
    ); // next year's date

    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(
        this.formFieldsWithDetailsPayload,
        this.QDEForm,
        true
      );
    const payload = {
      currency: "INR",
      dealIdentifier: this.dealNameFormControl.value
        ? this.dealNameFormControl.value
        : "",
      currentStatus: "In progress",
      currentStageName: this.defualtStageForDeal?.name,

      startDate: this.dataSharingService.getDateFormatInPayload(
        this.QDEForm.value["startDate"]
      )
        ? this.dataSharingService.getDateFormatInPayload(
            this.QDEForm.value["startDate"]
          )
        : this.dataSharingService.getDateFormatInPayload(
            this.QDEForm.value["Deal received on"]
          ),

      estimatedCloseDate: this.dataSharingService.getDateFormatInPayload(
        this.QDEForm.value["estimatedCloseDate"]
      )
        ? this.dataSharingService.getDateFormatInPayload(
            this.QDEForm.value["estimatedCloseDate"]
          )
        : this.dataSharingService.getDateFormatInPayload(closeDate),

      businessProcessDetail: {
        name: this.businessProcessDetail.name,
        version: this.businessProcessDetail.version,
        assetTypeName: this.businessProcessDetail.assetTypeName,
      },

      dealCustomerList: this.dataSharingService.getDataById
        ?.businessProcessEntityDefinition
        ? [
            {
              companyFlag:
                this.getPrimaryContactDetails(
                  this.selectedBusinessProcessInNewDeal,
                  "entityType"
                ) == "Person"
                  ? false
                  : true,
              customerName: this.selectedPrimaryActorDetails?.name,
              customerType: this.getPrimaryContactDetails(
                this.selectedBusinessProcessInNewDeal,
                "entityName"
              ),
              coApplicantFlag: false,
              entityId: this.selectedPrimaryActorDetails?.customerId
                ? this.selectedPrimaryActorDetails?.customerId
                : 0,
            },
          ]
        : [],
      dealLinkageDetailsList: updatedLinkage,
      dealAsset: {
        dealAssetItem: updatedVals,
      },
    };

    // Only Push Coapplicant Value If User enters coaplicant

    this.dealService
      .addExistingDeal(payload, this.mode)
      .pipe(
        catchError((error) => {
          const errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
          return EMPTY;
        })
      )
      .subscribe((res: any) => {
        const data = {
          success: true,
          selectedBusinessProcess: this.selectedBusinessProcessInNewDeal,
        };
        if (res) {
          if (this.docArray !== null) {
            if (this.docArray.length == 0) {
              const successData = {
                success: true,
                selectedBusinessProcess: this.selectedBusinessProcessInNewDeal,
              };
              this.notificationMessage.success(
                JsonData["label.success.Createdeal"]
              );
              this.dialogRef.close(successData);
            }

            this.docArray.forEach((element) => {
              const dealCreateResp: { id: number; currentStageName: string } =
                res;
              this.uploadApiCall(element, dealCreateResp);
            });
          } else if (res && res?.infoList?.length > 0) {
            this.notificationMessage.infoList(
              "Warning:\n• " + res.infoList.join("\n• "),
              true
            );
            return;
          } else {
            this.notificationMessage.success(
              `${this.getSidebarItembyName("Deal")} ` +
                JsonData["label.success.Create"]
            );
            this.dialogRef.close(data);
          }
        }

        this.QDEForm.reset();
        this.router.navigate(["/application-summary/details/" + btoa(res?.id)]);
      });
  }

  addDocuments() {
    this.formFieldsWithDetails.forEach((ele) => {
      const fieldValue = this.QDEForm.value?.[this.getPropertyName(ele)];
      if (
        ele[this.getPropertyName(ele)].inputType === ZcpDataTypes.DOCUMENT &&
        fieldValue
      ) {
        ele[this.getPropertyName(ele)].value = fieldValue;
        this.docArray.push(ele);
      }
    });
  }

  //get stage releated Items
  getStageFileds(element) {
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) => stage.stageName == this.stgName
    );
    return stage;
  }

  getAllLabelColors() {
    this.service.getLabelColors().subscribe((res: any) => {
      this.allLabelList = res;
    });
  }

  systemUsersList: any = [];

  getUserList() {
    this.identityService.getAllUser().subscribe((res) => {
      this.usersList = res;
      this.systemUsersList = [];
      this.systemUsersList = res.slice().map((ele) => {
        return {
          id: ele.identifier,
          name: ele?.firstName + " " + ele?.lastName,
        };
      });
    });
  }

  onSelectOfBusinessProcess(value) {
    if (value == undefined) {
      this.dataSharingService.DealFromCompany = true;
    }
    if (this.data?.dealFrom != "company") {
      this.businessProcessEntityDefinitionList = [];
      this.selectedPrimaryActorFromList = null;
      this.selectedPrimaryActorDetails = null;
    }

    const item = this.businessProcessService.businessProcessList?.find(
      (item) => item.name === this.selectedBusinessProcessInNewDeal
    );
    this.businessProcessService
      .getBusinessProcessById(item.id)
      .subscribe((data) => {
        this.dataSharingService.getDataById = data;
        this.stgName = data.businessProcessStageList[0].name;

        this.businessProcessEntityDefinitionList =
          data.businessProcessEntityDefinitionList;
        if (this.businessProcessEntityDefinitionList?.length == 1) {
          this.onselectPrimaryEntity(
            this.businessProcessEntityDefinitionList[0]
          );
        }
        this.formFieldsWithDetails = [];
        this.QDEForm = this.fb.group({});
        if (
          this.businessProcessService.businessProcessList &&
          this.dataSharingService.getDataById.businessProcessStageList.length !=
            0
        ) {
          this.businessProcessDetail =
            this.businessProcessService.businessProcessList.filter(
              (item) => item.name === value
            )[0];
          if (
            this.businessProcessDetail &&
            this.dataSharingService.getDataById.businessProcessStageList
              .length != 0
          ) {
            this.QDEStageDetails =
              this.dataSharingService.getDataById.assetItems.filter((ele) => {
                if (
                  ele[this.getPropertyName(ele)].stages?.find(
                    (stage) =>
                      stage.stageName == this.stgName && stage.isSelected
                  )
                )
                  return true;
              });

            this.loadExtentionsAsPercustomerType("Company", "customerEntity");

            this.dealNameFormControl.setValue(undefined);
            this.reset();
            if (this.QDEStageDetails) {
              this.formFieldsWithDetails = this.QDEStageDetails;

              this.sortAnFormFieldsAsPerSections(this.formFieldsWithDetails);
              this.generateReactiveForm(this.formFieldsWithDetails);
              const dealAseest =
                this.dataSharingService.getDataById.assetItems.filter((ele) => {
                  if (
                    ele[this.getPropertyName(ele)].stages?.find(
                      (stage) => stage.isSelected
                    )
                  )
                    return true;
                });

              this.defaultDealAssets = dealAseest;
              this.defualtStageForDeal =
                this.dataSharingService.getDataById.businessProcessStageList
                  .sort(function (a, b) {
                    return a.order - b.order;
                  })
                  .filter(
                    (item) =>
                      item.name != this.stgName &&
                      (item.display == "Active" || item.display == "Optional")
                  )[0];
              this.reset();
            }
          }

          if (
            this.defualtStageForDeal &&
            this.defualtStageForDeal.stageItems &&
            this.defualtStageForDeal.stageItems.length != 0
          ) {
            const dealAseest =
              this.dataSharingService.getDataById.assetItems.filter((ele) => {
                if (
                  ele[this.getPropertyName(ele)].stages?.find(
                    (stage) => stage.isSelected
                  )
                )
                  return true;
              });
            this.defaultDealAssets = dealAseest;
          }
        }
        this.getOptions(this.businessProcessDetail);
      });
  }

  getOptions(businessProcessDetail) {
    const BPname = this.businessProcessDetail?.name;
    const PrimayActorname = this.selectedPrimaryActorDetails?.name
      ? this.selectedPrimaryActorDetails?.name
      : "";
    PrimayActorname ? PrimayActorname : "";
    const dealIdentifierConfiguration =
      this.dataSharingService.getDataById?.dealIdentifierConfiguration;
    if (PrimayActorname == "") {
      return;
    }
    if (
      dealIdentifierConfiguration == "Business Process Name" ||
      dealIdentifierConfiguration == "User Entered" ||
      dealIdentifierConfiguration == "Custom"
    ) {
      //this.dealNameFormControl.setValue(BPname);
    }
    if (dealIdentifierConfiguration == "Primary Actor Name") {
      this.dealNameFormControl.setValue(PrimayActorname);
    }
    if (
      dealIdentifierConfiguration ==
      "Business Process Name + Primary Actor Name"
    )
      this.dealNameFormControl.setValue(BPname + "-" + PrimayActorname);
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  generateReactiveForm(data) {
    if (data && data.length != 0) {
      this.QDEForm = this.createGroup(data);
      this.getValidations(data);
    }
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      const isReadOnly = this.getStageFileds(control)?.isReadOnly === "Y";
      group.addControl(
        this.getPropertyName(control),
        this.fb.control({
          value: this.getDefaultValues(control),
          disabled: isReadOnly,
        })
      );
    });
    return group;
  }

  getDefaultValues(item) {
    if (
      item[this.getPropertyName(item)].inputType == "Date" &&
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues ==
        "Today"
    ) {
      return new Date();
    }
    if (
      item[this.getPropertyName(item)].inputType == "Date And Time" &&
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues ==
        "Today With Current Time"
    ) {
      const newtime = this.datepipe.transform(
        new Date(),
        "yyyy-MM-ddTHH:mm:ss"
      );
      return newtime;
    }
    if (
      item[this.getPropertyName(item)]?.inputType == "Configuration" &&
      item[this.getPropertyName(item)].value
    ) {
      this.getConfigDetails(true, item, item[this.getPropertyName(item)].value);
    } else {
      return item[this.getPropertyName(item)].value
        ? item[this.getPropertyName(item)].value
        : "";
    }
  }

  sortAnFormFieldsAsPerSections(currentData) {
    if (currentData && currentData.length != 0) {
      currentData.forEach((element) => {
        element[this.getPropertyName(element)].displayProperty.sectionOrder = 4;

        if (
          element[this.getPropertyName(element)].stages.find(
            (stage) =>
              stage.stageName == this.stgName &&
              stage.section[0]?.section == "Investment Information"
          )
        ) {
          element[
            this.getPropertyName(element)
          ].displayProperty.sectionOrder = 2;
        }
        if (
          element[this.getPropertyName(element)].stages.find(
            (stage) =>
              stage.stageName == this.stgName &&
              stage.section[0]?.section == "Customer Information"
          )
        ) {
          element[
            this.getPropertyName(element)
          ].displayProperty.sectionOrder = 1;
        }
        if (
          element[this.getPropertyName(element)].stages.find(
            (stage) =>
              stage.stageName == this.stgName &&
              stage.section == "Additional Information"
          )
        ) {
          element[
            this.getPropertyName(element)
          ].displayProperty.sectionOrder = 3;
        }
      });
    }
    this.formFieldsWithDetails = this.getSortedArray(currentData);

    this.reset();
  }

  reset() {
    this.showQDEForm = false;
    setTimeout(() => {
      this.showQDEForm = true;
    }, 800);
    if (this.formFieldsWithDetails.length == 0) {
      this.showNoDataMessage = true;
      this.showQDEForm = false;
    } else {
      this.showNoDataMessage = false;
    }
  }

  getSortedArray(formFieldsWithDetails) {
    const sortedArray = [];
    const sections = [
      "Customer Information",
      "Investment Information",
      "Additional Information",
      "Section 1",
      "Section 2",
      "Section 3",
      "Section",
    ];
    sections.forEach((section) => {
      const customerInfo = formFieldsWithDetails.filter((element) => {
        const stageIndex = element[this.getPropertyName(element)][
          "stages"
        ]?.findIndex((stage) => stage.stageName == this.stgName);
        const sectionName =
          element[this.getPropertyName(element)].stages[stageIndex]?.section[0]
            ?.section;
        if (sectionName == section) {
          return true;
        }
        return false;
      });
      const customerInfosort = customerInfo.sort((a, b) => {
        return (
          a[this.getPropertyName(a)].stages[0]?.section[0]?.order -
          b[this.getPropertyName(b)].stages[0]?.section[0]?.order
        );
      });
      sortedArray.push(...customerInfosort);
    });
    return sortedArray;
  }

  onSelectValue(value, stageItem) {
    const dependentFieldKey =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues?.split("|")?.[1];
    if (dependentFieldKey) {
      this.childConfigList[dependentFieldKey] = this.parentConfigList[
        this.getPropertyName(stageItem)
      ]?.find((e) => e?.[this.getPropertyName(stageItem)] == value)?.[
        dependentFieldKey
      ];
      this.QDEForm.get(dependentFieldKey)?.setValue("");
    }
  }

  getConfigDetails(opened: boolean, stageItem, val?) {
    const identifier =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues.split("|")[0];
    if (!identifier) return;

    if (opened && !this.parentConfigList?.[this.getPropertyName(stageItem)]) {
      this.showspinnerinlist = true;

      this.dealService
        .getConfigurationDetailsByIdentifier(identifier)
        .subscribe(
          (resp: any) => {
            this.parentConfigList[this.getPropertyName(stageItem)] =
              resp.configDetails;
            this.showspinnerinlist = false;
            if (val) this.onSelectValue(val, stageItem);
          },
          (err) => {
            this.showspinnerinlist = false;
          }
        );
    }
  }

  getValidations(data) {
    if (data && data.length != 0) {
      data.forEach((element) => {
        const countryCode =
          element[this.getPropertyName(element)].displayProperty.defaultValues
            .countryCode; // needed for phone number validation
        const validations = this.errorMessageService.getValidation(
          this.getStageFileds(element).isMandatory,
          element[this.getPropertyName(element)]?.inputType,
          element[this.getPropertyName(element)]?.displayProperty.validation,
          countryCode
        );
        if (
          element &&
          element[this.getPropertyName(element)].inputType !== "formly" &&
          validations
        ) {
          this.QDEForm.controls[this.getPropertyName(element)].addValidators(
            validations
          );
          this.QDEForm.controls[
            this.getPropertyName(element)
          ].updateValueAndValidity({ emitEvent: false });
        }
      });
    }
  }

  showOptions(fieldName, option) {
    return true;
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }

  onCheckEvent(event, fieldName): void {
    if (fieldName == "customerEntity") {
      this.loadExtentionsAsPercustomerType(event, "customerEntity");
    }
  }

  loadExtentionsAsPercustomerType(event, fieldName) {
    if (this.QDEStageDetails && this.QDEStageDetails) {
      const extensions = this.dataSharingService.getDataById.entities.find(
        (item) => this.getPropertyName(item) == event
      )[event]?.extensions;
      if (fieldName == "customerEntity") {
        this.extentionList = extensions ? extensions : [];
      }
    }
  }

  getDealTeamFormmated() {
    const userArray = [];

    if (userArray?.length != 0) {
      const index = userArray?.indexOf(this.QDEForm.get("dealLead")?.value);

      if (index !== -1) {
        userArray?.splice(index, 1);
      }

      this.dealUserDetails = userArray?.map((obj) => ({
        teamName: obj,
        isTeamLead: false,
      }));
    }
    // Equanimity requirement
    if (this.QDEForm.get("dealLead")?.value) {
      this.dealUserDetails?.unshift({
        teamName: this.QDEForm.get("dealLead").value,
        isTeamLead: true,
      });
    }
  }

  disableTeamLeamInMemberList(user) {
    if (this.QDEForm.get("dealLead")?.value === user) {
      return false;
    } else {
      return true;
    }
  }

  uploadApiCall(
    element,
    dealCreateResp: { id: number; currentStageName: string }
  ) {
    const data = {
      type: "UPLOAD",
      dealId: dealCreateResp.id,
      documentTitle:
        element[this.getPropertyName(element)]?.displayProperty?.defaultValues,
      referenceList: ["Originate", dealCreateResp?.id.toString()],
      inLineDocumentName: this.getPropertyName(element),
      workflowName: this.getStageFileds(element).workflowName,
    };

    this.fileData = new FormData();
    this.fileData.append("document", JSON.stringify(data));
    if (element[this.getPropertyName(element)]?.value?.file == null) {
      this.fileData.append(
        "file",
        JSON.stringify(element[this.getPropertyName(element)]?.value?.file)
      );
    } else {
      this.fileData.append(
        "file",
        element[this.getPropertyName(element)]?.value?.file
      );
      this.fileData.append("mode", "async");
    }
    this.dealService
      .uploadDocumentForDeal(this.fileData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event) => {
          if (event instanceof HttpResponse) {
            const successData = {
              success: true,
              selectedBusinessProcess: this.selectedBusinessProcessInNewDeal,
            };
            this.notificationMessage.success(
              JsonData["label.success.CreatedealOnDocUpload"]
            );
            this.dialogRef.close(successData);
          }
          this.QDEForm.reset();
        },
        () => {
          this.notificationMessage.error(
            "Deal created successfully. Document upload failed. Please try upload document again"
          );
          const successData = {
            success: true,
            selectedBusinessProcess: this.selectedBusinessProcessInNewDeal,
          };
          this.dialogRef.close(successData);
        }
      );
  }

  filterList() {
    this.disableCreateBtn = true;
    this.showspinnerinlist = false;
    this.getCustomersAsPerSearchText();
  }

  allCustomers = [];
  filteredCustomersList: any = [];
  filterCustomers(user: any) {
    return this.allCustomers.filter((option) => {
      return option.name.toLowerCase().includes(user.toLowerCase());
    });
  }

  openedChange(opened: boolean) {
    if (opened) {
      this.searchText = "";
      // this.addForm.get('searchEntity').reset();
      this.searchEntityInput.nativeElement.focus();
      this.filteredCustomersList = [];
    } else {
      this.getOptions(this.businessProcessDetail);
    }
  }

  getCustomersAsPerSearchText() {
    this.showCreateOption = false;
    this.showspinnerinlist = true;
    this.filteredCustomersList = [];
    const extentionType = this.getPrimaryContactDetails(
      this.selectedBusinessProcessInNewDeal,
      "entityName"
    );
    const selectedSubType = this.getPrimaryContactDetails(
      this.selectedBusinessProcessInNewDeal,
      "entityType"
    );

    if (selectedSubType && extentionType && this.searchText) {
      this.entityService
        .getCustomersListForSearcher(
          selectedSubType,
          extentionType,
          this.searchText
        )
        .subscribe(
          (res: any) => {
            this.filteredCustomersList = res;
            this.disableCreateBtn = false;
            this.showspinnerinlist = false;
            if (this.filteredCustomersList.length == 0) {
              this.showCreateOption = true;
            }
          },
          (error) => {
            this.showCreateOption = true;
            this.disableCreateBtn = false;
            this.showspinnerinlist = false;
          }
        );
    }
  }

  handleInput(event: KeyboardEvent): void {
    if (event.key === "Tab") {
      event.stopPropagation();
    }
  }

  newEntity() {
    this.openEntityDialog();
    this.entityNameSelect.close();
  }

  newEntityCreate() {
    const payload = {
      entityName: this.getPrimaryContactDetails(
        this.selectedBusinessProcessInNewDeal,
        "entityName"
      ),
      entityType: this.getPrimaryContactDetails(
        this.selectedBusinessProcessInNewDeal,
        "entityType"
      ),
      subType: this.getPrimaryContactDetails(
        this.selectedBusinessProcessInNewDeal,
        "subType"
      ),
      customerName: this.searchText.slice(),
    };

    this.entityService.newEntityCreate(payload).subscribe((res) => {
      this.filteredCustomersList.push(res);
      this.selectedPrimaryActorDetails = res;

      this.entityNameSelect.close();
    });
  }

  ngDoCheck() {
    const widthButton = document.getElementById("searchIcon")?.offsetWidth;

    const elems: any = document.getElementsByClassName("searchedInput");
    for (let i = 0; i < elems.length; i++) {
      elems[i].style["min-width"] = widthButton + "px";
      elems[i].style.width = widthButton * 2 + "px";
    }
  }

  searchedData: any = {};
  allPersonDetails: any = {};
  searcherKey: any = {};

  getSearchedList(formControlName) {
    if (
      this.searchedData[formControlName] &&
      this.searchedData[formControlName]?.length != 0
    ) {
      return this.searchedData[formControlName];
    } else {
      return undefined;
    }
  }

  selectedValue(formControlName, value) {
    this.QDEForm.get(formControlName).reset();
    this.QDEForm.value[formControlName] = value;
    this.QDEForm.get(formControlName).setValue(value);
  }

  getListViewEntityDetails(entityDetail) {
    if (entityDetail) {
      const filteredDetails = entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getFilteredArray(searchResult, itemValue) {
    if (searchResult && itemValue) {
      return searchResult.filter(
        (item) => !itemValue.find((e) => e.id == item.id)
      );
    } else if (searchResult) {
      return searchResult;
    }
  }

  getValue(list, nodeName) {
    const filteredPersonDetails = this.allPersonDetails.filter(
      (e) => e.customerId == list.id
    );

    const item = filteredPersonDetails[0]?.customerDetails?.entityDetail.find(
      (item) => this.getPropertyName(item) == nodeName
    );
    if (item) {
      return item[this.getPropertyName(item)]?.value || "";
    }
    return "";
  }

  setOptionValue(list, nodeName) {
    const obj = {
      id: list.id,
      name: list.name,
      sourceType: this.getValue(list, nodeName),
    };

    return obj;
  }

  clearQDEForm() {
    this.QDEForm.reset();
  }

  openEntityDialog() {
    const matDialogRef = this.matDialog.open(CreatePersonComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
      data: {
        selectedPersonExtensionName: this.getPrimaryContactDetails(
          this.selectedBusinessProcessInNewDeal,
          "entityName"
        ),
        isFromQDE: true,
      },
    });
    matDialogRef.afterClosed().subscribe((res) => {
      if (res) {
        this.filteredCustomersList.push(res);
        this.selectedPrimaryActorDetails = res;
        this.getOptions(this.businessProcessDetail);
      }
    });
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  getStageDetails(element, stageName) {
    return element[this.getPropertyName(element)]["stages"][
      this.getStageIndex(element, stageName)
    ]
      ? element[this.getPropertyName(element)]["stages"][
          this.getStageIndex(element, stageName)
        ]
      : [];
  }

  getSectionObject(element, stageName) {
    if (
      typeof this.getStageDetails(element, stageName)["section"] == "object"
    ) {
      return this.getStageDetails(element, stageName)["section"][0];
    }
  }

  /* Function is get index of current stage */
  getStageIndex(element, stageName) {
    return element[this.getPropertyName(element)]["stages"]?.findIndex(
      (stage) => stage.stageName == stageName
    );
  }

  getLabel(stageItem) {
    return this.getSectionObject(stageItem, this.stgName)?.displayName
      ? this.getSectionObject(stageItem, this.stgName).displayName
      : stageItem[this.getPropertyName(stageItem)]?.displayProperty
          ?.displayName;
  }

  public objectComparisonFunction = function (option, value): boolean {
    return option.name === value.name;
  };

  onselectPrimaryEntity(value) {
    this.selectedPrimaryActorFromList = value;
    this.dataSharingService.getDataById.businessProcessEntityDefinition =
      this.selectedPrimaryActorFromList;
  }
}
