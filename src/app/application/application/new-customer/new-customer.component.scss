.width-95 {
  width: 95%
}


/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
.mat-dialog-content-form-custom-css {
  min-height: fit-content;
  max-height: 80vh !important;
}

.labelChipsinForm {
  display: inline-block;
  margin-top: 0.5%;

  .mat-mdc-standard-chip {
    padding: 10px 5px !important;
    min-height: 25px !important;
  }
}

.noDataMessage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}


.createDealInputs {

  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-underline {
    bottom: 1% !important;
  }

  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 1% !important;
  }
}



.radioBtns {
  .mat-mdc-radio-button~.mat-mdc-radio-button {
    margin-left: 16px;
  }
}

.searchedInput {
  ::ng-deep .mat-mdc-menu-panel {
    padding: 0 10px !important;
    min-width: 613px !important;
  }
}

.cdk-overlay-pane {
  width: 100% !important;
}

.mat-mdc-menu-panel {
  max-width: 100% !important;
  min-width: 64px !important;
}


::ng-deep .mat-pseudo-checkbox-disabled {
  display: none !important;
}

::ng-deep .disbledOption .mat-mdc-option:first-child .mat-pseudo-checkbox {
  display: none;
}

.noteForFile {
  display: flex;
  font-size: 10px;
  justify-content: flex-end;
  margin-left: auto;
}

.example-viewport {
  height: 150px;
  // width: 835px;
  // border: 1px solid black;
}

::ng-deep .mat-mdc-select-panel {
  max-height: 310px !important;
}

.expansionToggleHide {
  .mat-expansion-panel-header {
    padding: 0 0px;
  }

  .mat-mdc-option {
    width: 100% !important;
  }

  ::ng-deep.mat-content.mat-content-hide-toggle {
    margin-right: 0 !important;
  }

}

.expansionToggleShow {
  .mat-expansion-panel-header {
    padding-left: 0px
  }

  .mat-mdc-option {
    width: 99% !important;
  }
}


.businessProcessInputArea {
  width: 100%;
}

.businessProcessContent {
  margin-top: 12px;
}

.searchEntityField {
  width: 95%;
  padding: 2%;
}

.searchEntityCreateNewButton {
  float: right;
  margin-right: 2%;
  margin-bottom: 10px;
}

.processNameInputArea {
  width: 100%;
  margin-top: 12px;
}

.loaderStyle {
  height: 360px;
}

.appendedContent {
  grid-gap: 3%;
}

.appendedContentExpansionHeader {
  padding-left: 0;
}

.appendedContentExpansionHeaderOption {
  width: 99% !important;
}

.optionDisabled {
  display: none;
}

.loaderInside {
  padding: 15px;
}

.appendedContentInput {
  width: 90%;
  padding: 2%;
}

.appendedContentList {
  width: 100%;
  min-height: 48px;
  line-height: normal;
  height: auto;
}

.appendedRadioSelSection {
  padding-bottom: 1%;
  padding-top: 1%;
}

.radioButtonCont {
  margin-right: 5%;
}

.matChipText {
  font-size: 12px;
}

.matChipTextRow {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.matChipTextTransform {
  text-transform: capitalize;
}

.contentFooter {
  display: flex;
  justify-content: center;
  position: sticky;
  padding: 5px;
}

.ml-70 {
  margin: auto;
}

.qde-spinner {
  display: flex;
  justify-content: center;
}

.font-16 {
  font-size: 16px !important;
}

.hideInput {
  display: none !important;
}

.btnColor {
  color: green;
}

.dark-blue {
  background-color: var(--clear-btn-colour) !important;
  color: var(--surface-color) !important;
}

.dark-blue:disabled {
  color:  var(--boolean-font-color) !important;
  background-color: var(--surface-color) !important;
  border: solid 1px var(--disabled-button-border-color);
}


.margin-right-14 {
  margin-right: 14% !important
}

.margin-right-2 {
  margin-right: 2% !important
}

.margin-left-45 {
  margin-left: 45% !important
}



.search-box-margin {
  margin: 1% 4%;
}

//New UI Boolean Field Style start
.margin-for-boolean {
  margin-top: 2%;
}

.boolean-background {
  background-color: var(--boolean-background-color) !important;
  border-radius: 6px !important;
  margin-bottom: 4% !important;
}

.font-size-boolean {
  font-weight: 600 !important;
  color: var(--boolean-font-color) !important;
  font-size: 16px !important;
  margin: 21px 0px 10px 15px !important;
}

.boolean-background:hover {
  background-color: var(--boolean-background-color-hover) !important;
  border-radius: 6px !important;
}

//New UI Boolean Field Style end

.table-spinner {
  margin-top: 5%;
  display: flex;
  justify-content: center;
}

.disabled-icon {
  color: rgba(0, 0, 0, 0.38);
  /* Typical disabled text color */
  opacity: 0.5;
  /* Reduced opacity to give a 'disabled' look */
  pointer-events: none;
  /* Prevents mouse events like click */
  cursor: not-allowed;
  /* Shows a 'not-allowed' cursor on hover */
}

.form-container {
  max-height: 500px;
  overflow-y: auto;
}

 ::ng-deep .mat-datepicker-content-container {
    width: 296px;
    height: 380px;
  }
