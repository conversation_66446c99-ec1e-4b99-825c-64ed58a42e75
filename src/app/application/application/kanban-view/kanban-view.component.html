<!---***********Action row*************-->
<div fxLayout="row wrap" fxLayoutGap="4px" class="md-2">
  <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="80%" fxFlex.sm="28%">
    <button class="green width-100"  [disabled]="businessProcessList && businessProcessList.length === 0" mat-raised-button
      (click)="newCustomer()">NEW  {{ this.getSidebarItembyName('Deal')}}</button>
    <!-- <mat-menu #menu="matMenu">
      <button class="green" mat-menu-item >New Company</button>

      <button class="green" mat-menu-item (click)="existingCustomer()">Existing Company</button>
    </mat-menu> -->
  </div>
  <div fxFlex="17%" fxFlex.md="19%" fxFlex.xs="80%" fxFlex.sm="28%">
    <p  class="inputLabelsInDeal SelectBP"> Select Business Process : </p>
  </div>
  <div fxFlex="20%" fxFlex.md="19%" fxFlex.xs="80%" fxFlex.sm="28%">
    <div class=" businessProcessListContainer bulkMovementButton" >
      <mat-form-field class="width95">
        <mat-select (selectionChange)="onSelectOfBusinessProcess($event.value)" [(ngModel)]="selectedBusinessProcess"
          [ngModelOptions]="{standalone: true}">

          <mat-option *ngFor="let type of businessProcessList" [value]="type.name">{{type.name}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <div fxFlex="15%" fxFlex.md="14%" fxFlex.xs="80%" fxFlex.sm="28%">
    <p  class="inputLabelsInDeal SelectBP">  Search Deals :  </p>
  </div>
  <div fxFlex="20%" fxFlex.md="19%" fxFlex.xs="80%" fxFlex.sm="28%">
    <div class="businessProcessListContainer">
      <mat-form-field class="searchBox" >
        <mat-icon matSuffix>search</mat-icon>
        <input matInput #input [(ngModel)]="searchText">
      </mat-form-field>
    </div>
  </div>
  <div fxFlex="9%" fxFlex.md="9%" fxFlex.xs="80%" fxFlex.sm="28%">
    <div  class="viewChangeBtns height-auto closeButton" >
      <mat-button-toggle-group #group="matButtonToggleGroup" name="fontStyle" aria-label="Font Style"
        [value]="selectedView" (click)="onViewChange(group.value , 'viewChange')">
        <mat-button-toggle value="Kanban view" (click)="selectedView == 'Kanban view'">
          <mat-icon>view_module</mat-icon>
        </mat-button-toggle>
        <mat-button-toggle value="Table view" (click)="selectedView == 'Table view'">
          <mat-icon>view_list</mat-icon>
        </mat-button-toggle>
        
  
      </mat-button-toggle-group>
    </div>
  </div>
</div>






























































<!---************DEAL LIST*************-->

<div class="loaderInSideDeal " fxFlex="100%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" *ngIf="showLoader && !showNoDataMessage">
  <mat-spinner></mat-spinner>
</div>
<div *ngIf="showNoDataMessage && businessProcessStagesWithDeals && businessProcessStagesWithDeals.length === 0">
  <p class="NoDataCss">{{noDataMessage}}</p>
</div>
<div class="kanbanView" *ngIf="!showLoader">
  <ng-container *ngFor="let stage of businessProcessStagesWithDeals ; let i = index">
    <mat-card appearance="outlined" cdkDropListGroup class="mainKanbanCards mat-card-top-border" *ngIf="stage.order !== 1"
      [ngStyle]="{width : getWidth((businessProcessStagesWithDeals.length - 1))}">
      <mat-card-header class="justify-content">
        <p class="mainCardTitle">{{stage.name}}</p>

      </mat-card-header>
      <mat-card-header class="justify-content">
        <!-- <small class="mainCardSubTitle"> {{stage.numberOfDeals}} for 
       
           &nbsp;{{getCurrencySymbol(selectedCurrency)}}&nbsp;
           {{this.getCurrencyInShorterFormat(stage.totalAmount , selectedCurrency)}}
        </small> -->
      </mat-card-header>
      <mat-divider></mat-divider>
      <mat-card-content class="showSubDealDetails" id={{stage.id}} cdkDropList [cdkDropListData]="stage.dealList"
        (cdkDropListDropped)="dropItem($event)" [cdkDropListConnectedTo]="getConnectedList()"
        (cdkDropEntered)="entered($event)">

        <!-- cdkDropList id="Open"
        [cdkDropListData]="allTasksInNotStarted"
        (cdkDropListDropped)="drop($event , 'allTasksInNotStarted')" cdkDropListConnectedTo="all"
        (cdkDropEntered)="entered($event)" -->


        <ng-container *ngFor="let deal of getDealList(stage.dealList) | FilterArrayWithNamePipe : searchText : 'deal';">
          <mat-card appearance="outlined" cdkDrag [cdkDragData]="deal" class="subCardsInKanbanView   subCardsInKanbanViewColor">
            <mat-card-header>
              <p (click)="navigateToSummaryPage(deal)" class="pointer subValueTitle">
                {{getCustomerName(deal.dealCustomerList)}}

              </p>
            </mat-card-header>
            <mat-card-content class="mb-4">
              <div class="flex-container"
                [ngStyle]="{  'flex-direction':  getWidth((businessProcessStagesWithDeals.length-1)) <= '15%' ? 'column' : 'row'}">
                <!-- <div class="flex-item-left"> -->
                  
                  <!-- &nbsp;{{getCurrencySymbol(selectedCurrency)}}&nbsp;
                  {{this.getCurrencyInShorterFormat(deal.dealAmount , selectedCurrency)}} -->
                 <!-- </div> -->
                <!-- <div class="flex-item-right"> -->
                  {{deal.startDate | date}}
                <!-- </div> -->
              </div>
            </mat-card-content>

            <mat-divider></mat-divider>

            <mat-card-content class="matCardView">


              <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="deal.dealLabelList.length != 0">
                <div class="labelChipsInKanban" fxFlex="100%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" >
                  <mat-chip-listbox #chipList class="font-12">
                    <mat-chip-option *ngFor="let label of deal.dealLabelList; let i = index"
                      [style.background]='label.colorName' [style.color]="getcolor(label.colorName)"
                      class="labelName">
                      <span>{{label.labelName}}</span>
                    </mat-chip-option>
                  </mat-chip-listbox>
                </div>
              </div>

            </mat-card-content>
          </mat-card>
        </ng-container>
      </mat-card-content>
    </mat-card>
  </ng-container>
</div>