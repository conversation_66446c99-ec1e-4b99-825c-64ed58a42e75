import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ApplicationLabelService } from 'src/app/shared-service/application-label.service';
import { BusinessProcessService } from 'src/app/shared-service/businessProcess.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { CdkDragDrop, moveItemInArray, transferArrayItem, CdkDragEnter } from '@angular/cdk/drag-drop';
import { NewCustomerComponent } from '../new-customer/new-customer.component';
import { CurrencyUnitService } from 'src/app/shared-service/currency-unit.service';
import JsonData  from 'src/assets/data.json';

@Component({
  selector: 'app-kanban-view',
  templateUrl: './kanban-view.component.html',
  styleUrls: ['./kanban-view.component.scss']
})
export class KanbanViewComponent implements OnInit {
  @Output() viewEvent = new EventEmitter<any>();
  @Input() businessProcessListFromParent: any;
  @Input() dealListFromParent: any;
  @Input() selecteBusinessProcessFromParent : any;
  selectedCurrency = ""
  selectedView = "Kanban view"
  businessProcessStagesWithDeals: any[] = [];
  legalDocumentation: any;
  searchText: any = "";
  showLoader: any = true;
  groups: { id: number; title: string; items: { name: string; }[]; }[];
  selectedBusinessProcess = "";
  businessProcessList: any = []
  data: any;
  counter: number = 0;
  listOfDeals: any = [];
  showNoDataMessage: any = false;
  noDataMessage: string;
  // selectedCurrency : any;
  constructor(private dialog: MatDialog,
    public router: Router,
    public dataSharingService: DataSharingService,
    public applicationLabelService: ApplicationLabelService,
    private businessProcessService: BusinessProcessService,
    public currencyUnitService : CurrencyUnitService,
    private dealService: DealService) {
    // this.getAllDeals()
this.selectedCurrency = localStorage.getItem('currency')
  }


  ngOnChanges() {
    this.listOfDeals = this.dataSharingService.dealList
    this.getBusinessProcessList()
  }
  ngOnInit(): void {
    this.data = [
  
    ]
    this.getBusinessProcessList()
    this.selectedCurrency = localStorage.getItem('currency')

    // this.businessProcessStagesWithDeals = this.data;
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex);
    }
  }

  getCustomerName(data){
    
    let customer = data.filter(ele => ele.coApplicantFlag == false)
    if(customer.length != 0){
      return customer[0]?.customerName
    }else{
      return data[0]?.customerName
    }
  }

  getConnectedList1(): any[] {
    return this.businessProcessStagesWithDeals.map(x => `${x.name}`);
  }
  dropItem(event: CdkDragDrop<string[]>) {

    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex);
    }
  }

  getConnectedList(): any[] {
    return this.businessProcessStagesWithDeals.map(x => `${x.id}`);
  }
  entered(event: CdkDragEnter<string[]>) {
  }

  dropGroup(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.groups, event.previousIndex, event.currentIndex);
  }


  newCustomer() {



    const matDialogRef = this.dialog.open(NewCustomerComponent, {
      autoFocus: false,
      width: '45%',
      disableClose: true ,
      //  height : '90vh',
      data: {
        "selectedBusinessProcess": this.selectedBusinessProcess
      }
    })
    matDialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (this.selectedBusinessProcess != result.selectedBusinessProcess) {
          this.selectedBusinessProcess = result.selectedBusinessProcess;
          this.businessProcessStagesWithDeals = [];
          this.showLoader = true;
          this.showNoDataMessage = false;
        }
        // this.onViewChange(null , 'addCustomer')
        this.getAllDeals(true)
      }
    })

  }

  onViewChange (value, eventName) {
    let data = {
      value : value,
      eventName : eventName
    }
    this.viewEvent.emit(data);
  }

 



  navigateToSummaryPage(data) {
       // Equanimity requirement
    let name = this.getCustomerName(data?.dealCustomerList) + ' ' + data?.purpose ; 

    // this.dataSharingService.newSubPageNameValue(name);
   
    this.dataSharingService.dealDataBackup = data;
    this.dataSharingService.selectedApplicationData = data
    this.dataSharingService.emitChangesOfSelectedApplicationData(data);

    this.router.navigate(['/application-summary/details/'+btoa(data.id)])

  }


  getWidth(length) {
    let w = 100 / length;
    return w.toFixed(2) + "%"
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }


  getBusinessProcessList() {
    // this.businessProcessService.businessProcessList = listOfBusinessProcess;
    // this.businessProcessList = this.businessProcessService.businessProcessList;
    // this.getAllDeals()

      this.businessProcessService.businessProcessList = this.businessProcessListFromParent;
      this.businessProcessList = this.businessProcessService.businessProcessList;
      if ( this.businessProcessList && this.businessProcessList.length != 0) {
        this.selectedBusinessProcess = this.businessProcessList[0].name;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(this.businessProcessList[0])
        this.getAllDeals(true)
      } else {
        this.showNoDataMessage = true;
        this.noDataMessage = "No data available."

      }
  
  }

  getAllDeals(reloadApi) {
    if (!reloadApi) {
      if (this.dealListFromParent) {
        this.listOfDeals = this.dataSharingService.dealList;
        this.generateKanbanViewAsPerSelectedBusinessProcess(this.dealListFromParent)
      }

    } else {
      this.dealService.getAllDealList().subscribe(res => {
        if (res) {
          this.showLoader = true;
          this.dataSharingService.dealList = res
          this.listOfDeals = this.dataSharingService.dealList;
          
          this.generateKanbanViewAsPerSelectedBusinessProcess(res)
        }
      })
    }
   
  }



  generateKanbanViewAsPerSelectedBusinessProcess(dealData) {

    dealData = dealData.filter(deal => deal.workFlowDetail.name == this.selectedBusinessProcess)

    let selectedBusinessProcessDetails = this.businessProcessList.filter(item => item.name.toLowerCase() === this.selectedBusinessProcess.toLowerCase())[0]
    
    if (selectedBusinessProcessDetails && selectedBusinessProcessDetails.businessProcessStageList.length != 0) {
      let numberOfDeals = 0;
      let rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData['label.button.rejectedStatus'],
        order: selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      }
      if(!(selectedBusinessProcessDetails.businessProcessStageList.some(stage => stage.name == JsonData['label.button.rejectedStatus']))) {
        selectedBusinessProcessDetails.businessProcessStageList.push(rejectionObj)
      }
      
      selectedBusinessProcessDetails.businessProcessStageList = selectedBusinessProcessDetails.businessProcessStageList.filter((item)=>item.display == "Active" || item.display == "Optional");
      selectedBusinessProcessDetails.businessProcessStageList.forEach((element, index) => {

        if (element.name && element.name.toLowerCase() === "Rejected" + " " + this.getSidebarItembyName('Deal')) {
          element.numberOfDeals
          element.dealList = dealData.filter(deal => deal.currentStatus.toLowerCase() === ('Rejected').toLowerCase());
          element.numberOfDeals = element.dealList.length;
          element.totalAmount = element.dealList.reduce((a, b) => a + b.dealAmount, 0);
        } else {
          element.numberOfDeals
          element.dealList = dealData.filter(deal =>    (deal.currentStatus.toLowerCase() === ('In progress').toLowerCase() || deal.currentStatus.toLowerCase() === ('Approved').toLowerCase()) &&   deal.currentStageName.toLowerCase() === element.name.toLowerCase());
          element.numberOfDeals = element.dealList.length;
          element.totalAmount = element.dealList.reduce((a, b) => a + b.dealAmount, 0);
        }

        if (selectedBusinessProcessDetails.businessProcessStageList.length == (index + 1)) {
          
          let finalData = selectedBusinessProcessDetails.businessProcessStageList.sort(function (a, b) { return a.order - b.order })
          this.dataSharingService.selectedBusinessProcessWithStagedetails = finalData
          this.dataSharingService.emitChangesOfSelectedBusinessProcessData(finalData);
          
          this.businessProcessStagesWithDeals = finalData;
          if (this.businessProcessStagesWithDeals && this.businessProcessStagesWithDeals.length != 0) {
            this.showLoader = false;
          }
          // this.addRejectionStageInKanban(finalData)
        }

      });
    }
  }




  onSelectOfBusinessProcess(value) {
    this.selectedBusinessProcess = value;
    this.generateKanbanViewAsPerSelectedBusinessProcess(this.listOfDeals)
  }


  getDealList(array) {
    // return array
    return array.sort((a, b) => {
      let firstKeyName = a.modifiedDate ? 'modifiedDate' : 'createdDate'
      let secondKeyName = b.modifiedDate ? 'modifiedDate' : 'createdDate'

      const sortedArray = b[secondKeyName].localeCompare(a[firstKeyName]);
      return sortedArray
    });

  }



  getCurrencyInShorterFormat(amount, currency) {
    // amount = this.removeCommas(amount);
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  getCurrencySymbol(currency) {

    let data = this.currencyUnitService.getCurrencySymbol(currency);

    return data[0].symbol;


  }

  getSidebarItembyName(itemName){
    if(this.dataSharingService.getSidebarItembyName(itemName)){
      let item = this.dataSharingService.getSidebarItembyName(itemName)[0] ;
      return item?.displayName;
     }
  }

}


