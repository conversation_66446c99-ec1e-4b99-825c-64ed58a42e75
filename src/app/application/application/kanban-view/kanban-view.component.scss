.board {
  min-height: 100vh;
  display: flex;
}

.column {
  flex: 1;
}

cdk-drop {
  display: block;
  min-height: 100vh;
}

.cdk-drop-dragging .cdk-drag {
  transition: transform 500ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-animating {
  transition: transform 550ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.kanbanView {
  display: inline-flex;
  width: 100%;
  overflow-x: auto;
  margin-bottom: 3%;
}

.justify-content {
  justify-content: center;
}

.mainKanbanCards {
  margin: 1% 0.1%;
  padding: 0;
  min-width: 163.21px !important ;
  /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
  ::ng-deep .mat-card-header-text {
    margin: 0 !important;
  }
}

.mainCardTitle {
  margin: 3% 5%;
  font-size: 15px;
  font-weight: 500;
  text-transform: capitalize;
}

.mainCardSubTitle {
  font-size: 13px;
  margin: 0 5% 2%;
  font-weight: 400;
}

.subCardsInKanbanView {
  margin: 3% 0;
  padding: 5%;
  /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
  ::ng-deep .mat-card-header-text {
    margin: 0 !important;
  }
}

.flex-container {
  display: flex;
  // flex-direction: row;
  // text-align: center;
}

.flex-item-left {
  flex: 50%;
}

.flex-item-right {
  flex: 50%;
}

.labelChipsCSS {
  font-size: 12px;
  padding: 2%;
}

@media (max-width: 1220px) {
  .flex-container {
    flex-direction: column;
  }
}
.subValueTitle {
  font-weight: 500;
}

.subValueTitle:hover {
  text-decoration: underline;
}

.labelChipsInKanban {
  .mat-mdc-standard-chip {
    padding: 2px 5px !important;
    min-height: 22px !important;
    height: auto !important;
  }
}

.businessProcessListContainer {
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
    border-radius: 4px 4px 0 0;
    padding: 0.25em 0.25em 0 0.25em;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-infix {
    // padding: .5em 0;
    border-top: 0.54375em solid transparent !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-underline {
    bottom: 0 !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0 !important;
  }
}

.inputLabelsInDeal {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  font-weight: 500;
}

.quickDataEntryContainer {
  align-items: center;
  display: flex;
}

.loaderInSideDeal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.NoDataCss {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.viewChangeBtns {
  /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
  ::ng-deep
    .mat-button-toggle-appearance-standard
    .mat-button-toggle-label-content {
    line-height: 35px !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
  ::ng-deep
    .mat-button-toggle-appearance-standard
    .mat-button-toggle-label-content {
    padding: 0px 9px !important;
  }
}
.SelectBP {
  margin: 4% 0 !important
}
.width95{
  width : 95% !important
}
.mb-4{
  margin-bottom: 4% !important
}
.matCardView{
  margin: 8% 0 0 !important
}
.labelName{
  font-size: 12px !important;
  text-transform: uppercase;
}