import { Component, OnInit, Inject } from '@angular/core';
import { Subject } from 'rxjs';
import { listOfDocumentTypes } from 'src/app/application-summary/static-data';
import { ThemePalette } from '@angular/material/core';
import { ProgressBarMode } from '@angular/material/progress-bar';
import { UntypedFormControl, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DealService } from 'src/app/shared-service/deal.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { takeUntil } from 'rxjs/operators';
import { HttpEventType, HttpResponse } from '@angular/common/http';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { DownloadFileService } from 'src/app/shared-service/download-file.service';

@Component({
  selector: 'app-upload-file-QDE',
  templateUrl: './upload-file-QDE.component.html',
  styleUrls: ['./upload-file-QDE.component.scss']
})
export class UploadFileQDEComponent implements OnInit {

  private unsubscribe$ = new Subject();
  documentTypeList: any = listOfDocumentTypes;
  selectedDocumentType: any ;
  fileData: any = new FormData();
  selectedFile: any = null;
  selectedFileName: any = null;
  dataFromParentComponent: any;
  filePercentage: any;
  showFileProgressBar: boolean = false;
  showFileSizeErrorMessage : boolean = false;
  color: ThemePalette = 'primary';
  mode: ProgressBarMode = 'indeterminate';
  value = 50;
  bufferValue = 75;
  maxDocFileSize;


  documentType = new UntypedFormControl("" ,[Validators.required])
  fileSize: any;

  constructor(
    public dataSharingService: DataSharingService,
    public matDialog: MatDialog,
    public dialogRef: MatDialogRef<UploadFileQDEComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    private downloadFileService: DownloadFileService
  ) {
    
    this.dataFromParentComponent = data;
    
    

  }

  ngOnInit() {
    this.getAllTheDocumentTypeList();
    if(this.dataFromParentComponent.documentType){
      this.selectedDocumentType = this.dataFromParentComponent.documentType;
      this.documentType.disable()
    }
    this.downloadFileService.getFileSizeLimitFromCache().subscribe(limit => this.maxDocFileSize = limit);
  }

  ngOnDestroy() {

    this.unsubscribe$.next('');
    this.unsubscribe$.complete();
  }

  fileUpload(file) {
    
    // this.taskService.isFileUploaded = true;
   if(file){
    this.selectedFile = file;
    this.selectedFileName = file.name;
    this.fileSize = file.size;
    this.showFileSizeErrorMessage = false;
  
    if (this.fileSize >= 104857600) {
      this.showFileSizeErrorMessage = true; 
    }
   }
    
  }

  closeDialog() {
    this.dialogRef.close(false)
  }


  onUpload() {
    this.documentType.markAsTouched()
   
    if(this.documentType.invalid || !(this.selectedFileName)){
      this.notificationMessage.error("Please fill in all the required fields with valid data.")
     return
    }
    if(this.showFileSizeErrorMessage){
      return
    }
  let result = {
    
    status : true,
    file : this.selectedFile,
     selectedDocumentType : this.selectedDocumentType,
     selectedFileName : this.selectedFileName
  }
    this.dialogRef.close(result)
    
  }
  getAllTheDocumentTypeList() {
    this.dealService.getAllDocumentTypeList().subscribe((res:any) => {
    let data = res;
    let docList = data.filter(doc => doc.workFlowDetail.name == this.dataSharingService.selectedApplicationData.workFlowDetail.name);
    if(docList && docList.length > 0) {
    this.documentTypeList = docList[0].documentList;
    }
    })
    }
}
