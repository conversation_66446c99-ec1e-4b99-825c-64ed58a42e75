<mat-dialog-content class="mat-dialog-content-custom-css">
  <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <h2>Upload Document</h2>
    </div>
    <div class="floatRight ml-btn">
      <!-- <button mat-button  (click)="closeDialog()"> -->
      <mat-icon (click)="closeDialog()" class="pointer">close</mat-icon>
      <!-- </button> -->
    </div>


    <div fxLayout="row wrap">
      <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
        class="width-100">
        <mat-label>Document Type</mat-label>
        <mat-select required [formControl]="documentType" [(ngModel)]="selectedDocumentType">
          <mat-option *ngFor="let type of documentTypeList" [value]="type.name || type">
            {{type.name ? type.name: type }}</mat-option>


          <!--
            <mat-option *ngFor="let type of documentTypeList" [value]="type.name">{{type}}</mat-option> -->
        </mat-select>
        <mat-error *ngIf="documentType.errors?.required">
          {{"label.materror.updateQDE"|literal}}
        </mat-error>
      </mat-form-field>
    </div>

    <div fxLayout="row wrap" class=" uploadDocumentsInputs">
      <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
        class="width-100 font-6" appFileDragNDrop (filesChangeEmiter)="fileUpload($event)">
        <button (click)="fileDropRef.click()" mat-button class="green">Choose file</button>
        <span class="font-12">{{this.selectedFileName}}</span>
        <span *ngIf="!this.selectedFileName" class="selectedFileName custom-input-info-text"> or
          Drop it
          here!</span>
        <input matInput class="displayInput">
        <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
          (change)="fileUpload($event.target.files[0])"
          accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic" />

      </mat-form-field>
      <section class="example-section" *ngIf="showFileProgressBar">
        <mat-progress-bar class="example-margin" [color]="color" [mode]="mode"
          [value]="filePercentage" [bufferValue]="bufferValue">
        </mat-progress-bar>

      </section>
      <mat-hint class="noteForFile">Note : Select document in .doc, .pdf,.pptx,.jpeg,.jpg,.xlsx
        ,.HEIC,.MOV format upto {{maxDocFileSize}} of size.
      </mat-hint>
      <small *ngIf="showFileSizeErrorMessage" class="font-10">
        File Size Should be less than <strong>{{maxDocFileSize}}</strong>
      </small>

    </div>



    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
      class=" uploadBtnContainerCss">
      <button mat-raised-button class="green " (click)="onUpload()"
        [disabled]="showFileProgressBar">UPLOAD
        DOCUMENT</button>
    </div>
  </div>


</mat-dialog-content>
