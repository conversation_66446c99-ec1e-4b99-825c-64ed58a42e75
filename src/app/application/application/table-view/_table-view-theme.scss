@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);
.mainContainer{
    .sub-container-1{
      .bp-select:hover{
        mat-form-field{
          .mat-mdc-text-field-wrapper{
            background-color: mat.get-color-from-palette($primary-palette,100);
          }

          .mat-mdc-select-value{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }

          .mat-mdc-select-arrow{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }
        }
      }
    }
    .sub-container-2{
        .activeFilter {
            font-weight: bold;
            background-color: $primary-color !important;
            color: $primary-contrast;
          }

          .filter-buttons {
            background-color: var(--container-color);
          }

          background-color: var(--container-color);

    }

    .sub-container-4{
      th{
        background-color: var(--container-color);
      }
     .delete-icon:hover{
        color: mat.get-color-from-palette($warn-palette, 400) !important;
      }
    }

   
}

}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .mainContainer{
    .sub-container-1{      
      .bp-select{
        mat-form-field{
          mat-select{
            @include mat.typography-level($typography-config, 'headline-6');
          }
       }
      
    }
  }

  .sub-container-3{
    .selected-filter{
      @include mat.typography-level($typography-config, 'headline-5');

    }
  }

}
}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}