import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import { Router } from '@angular/router';
import { DataSharingService } from '../../common/dataSharing.service';
import { ApplicationLabelService } from 'src/app/shared-service/application-label.service';
import { BusinessProcessService } from 'src/app/shared-service/businessProcess.service';
import { DealService } from '../../shared-service/deal.service';

@Component({
  selector: 'app-application',
  templateUrl: './application.component.html',
  styleUrls: ['./application.component.scss']
})
export class ApplicationComponent implements OnInit {
  selectedView = "Table view"    // Equanimity requirement
  businessProcessStagesWithDeals: any[] = [];
  legalDocumentation: any;
  searchText: any = "";
  showLoader: any = true;
  groups: { id: number; title: string; items: { name: string; }[]; }[];
  selectedBusinessProcess = "";
  businessProcessList: any = []
  data: any;
  counter: number = 0;
  listOfDeals: any = [];
  showNoDataMessage: any = false;
  noDataMessage: string;
  constructor(private dialog: MatDialog,
    public router: Router,
    public dataSharingService: DataSharingService,
    public applicationLabelService: ApplicationLabelService,
    private businessProcessService: BusinessProcessService,
    private dealService: DealService) {

    this.getBusinessProcessList()
  }

  ngOnInit(): void {
    this.data = [];
  }

  navigateToSummaryPage(data) {
    this.dataSharingService.selectedApplicationData = data
    this.dataSharingService.emitChangesOfSelectedApplicationData(data);
    this.router.navigate(['/application-summary'])
  }
  getWidth(length) {
    let w = 100 / length;
    return w.toFixed(2) + "%"
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }

  getBusinessProcessList() {
    this.businessProcessService.getAllBusinessProcessList().subscribe(response => {
      this.businessProcessService.businessProcessList = response;
      this.businessProcessList = this.businessProcessService.businessProcessList;
      if (this.businessProcessList.length != 0) {
        this.selectedBusinessProcess = this.businessProcessList[0].name;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(this.businessProcessList[0])
        this.showLoader = false;
      } else {
        this.showNoDataMessage = true;
        this.noDataMessage = "No data available."
      }
    }, (error) => {
      this.showNoDataMessage = true;
      this.showLoader = false;
      this.noDataMessage = "No data available."
    })
  }

  changeViewHandler(event: any) {
    if(event.eventName ==  'viewChange'){
      this.selectedView = event.value;   
    } 
  }

  getSidebarItembyName(itemName){
    if(this.dataSharingService.getSidebarItembyName(itemName)){
      let item = this.dataSharingService.getSidebarItembyName(itemName)[0] ;
      return item?.displayName;
     }
  }
}

