import {
  AbstractControl,
  FormControl,
  FormGroup,
  ValidatorFn,
} from "@angular/forms";
import {
  evalStringExpression,
  formStringExpression,
  isValidJSON,
} from "./utils";
import { ZcpDataTypes } from "../zcp-data-types/data-types.model";

export class FormUtils {
  public static clearAllValidators(
    formGroup: FormGroup,
    assetFields?: [{ [key: string]: { inputType: string } }]
  ) {
    if (assetFields && assetFields.length > 0) {
      assetFields.forEach((field) => {
        const key = Object.entries(field)[0][0];
        const inputType = field[key].inputType;
        const control = formGroup.controls[key];
        this.clearFormControlValidator(control, inputType);
      });
    } else {
      const controls = Object.values(formGroup.controls);
      controls.forEach((control) => {
        control.clearValidators();
        control.updateValueAndValidity({ emitEvent: false });
      });
    }
  }

  public static clearFormControlValidator(
    control: AbstractControl,
    inputType: string
  ) {
    if (
      inputType === ZcpDataTypes.DATE ||
      inputType === ZcpDataTypes.DATE_TIME
    ) {
      return;
    } else {
      control.clearValidators();
    }
    control.updateValueAndValidity({ emitEvent: false });
  }

  public static cloneFormGroup(formGroup: FormGroup): FormGroup {
    const controls: { [key: string]: any } = {};

    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);

      if (control instanceof FormGroup) {
        controls[key] = this.cloneFormGroup(control);
      } else if (control instanceof FormControl) {
        controls[key] = new FormControl(
          control.value,
          control.validator,
          control.asyncValidator
        );
      }
    });

    return new FormGroup(controls);
  }
}

export class UniqueRecordValidation {
  public static checkDuplicateRecordAsPerUniqueKey(
    entry,
    list,
    keys,
    rowIndex
  ) {
    if (!(keys && keys?.length > 0)) {
      return false;
    }
    return list?.data?.some(
      (item, i) =>
        rowIndex !== i &&
        keys?.every((key) => {
          if (typeof entry[key] == "string" || typeof entry[key] == "object") {
            return (
              JSON.stringify(item[key])?.toLowerCase() ===
              JSON.stringify(entry[key])?.toLowerCase()
            );
          }
          return item[key] === entry[key];
        })
    );
  }
}

export class RuleExecutor {
  key: string;

  constructor(
    private _form: FormGroup,
    private allAssetItems: any,
    private field: any,
    private sectionDetails: { isHide: boolean }
  ) {
    this.key = Object.entries(field)[0][0];
  }

  executeValueRule(rule: string, thisRef) {
    try {
      if (!rule) return;
      const evaluatedValue = this.evaluateValueRule(rule, thisRef);
      if (evaluatedValue !== "") {
        this._form.controls[this.key]?.setValue(evaluatedValue, {
          emitEvent: false,
        });
        this.field[this.key].value = evaluatedValue;
      } else {
        this.field[this.key].value = "";
        this._form.controls[this.key]?.setValue("", {
          emitEvent: false,
        });
      }
    } catch (error) {
      console.error(error);
    }
  }

  evaluateValueRule(rule: string, thisRef) {
    try {
      if (!rule) return;
      const exper = formStringExpression(rule, ["controls", "asset"]);
      const evaluatedValue = evalStringExpression(exper, thisRef, [
        this._form?.controls,
        this.allAssetItems,
      ]);
      return evaluatedValue;
    } catch (error) {
      console.error(error);
    }
  }

  executeReadOnlyRule(
    rule: string,
    thisRef,
    priorityDisable: boolean,
    subSectionRule?,
    sectionRule?
  ) {
    try {
      if (!rule && !priorityDisable && !subSectionRule && !sectionRule) return;
      const exper = formStringExpression(rule, ["controls", "asset"]);
      const experForSubSectionRule = formStringExpression(subSectionRule, [
        "controls",
        "asset",
      ]);
      const experForSectionRule = formStringExpression(sectionRule, [
        "controls",
        "asset",
      ]);

      let evalExper = evalStringExpression(experForSectionRule, thisRef, [
        this._form.controls,
        this.allAssetItems,
      ])
        ? evalStringExpression(experForSectionRule, thisRef, [
            this._form.controls,
            this.allAssetItems,
          ])
        : evalStringExpression(experForSubSectionRule, thisRef, [
            this._form.controls,
            this.allAssetItems,
          ])
        ? evalStringExpression(experForSubSectionRule, thisRef, [
            this._form.controls,
            this.allAssetItems,
          ])
        : evalStringExpression(exper, thisRef, [
            this._form.controls,
            this.allAssetItems,
          ]);
      const evaluatedExp = priorityDisable || evalExper;

      if (evaluatedExp) {
        this._form.controls[this.key]?.disable({ emitEvent: false });
        this.field[this.key]["disabledUsingValueRule"] = evaluatedExp;
      } else {
        this._form.controls[this.key]?.enable({ emitEvent: false });
      }
    } catch (error) {
      console.error(error);
    }
  }

  executeHideRule(rule: string, thisRef) {
    try {
      if (!rule) return;
      const exper = formStringExpression(rule, ["controls", "asset"]);
      this.sectionDetails.isHide = evalStringExpression(exper, thisRef, [
        this._form.controls,
        this.allAssetItems,
      ]);
    } catch (error) {
      console.error(error);
    }
  }

  executeValidateRule(
    inputType: string,
    rule: string,
    thisRef,
    validationsGetter: (vals, regex, field, compData) => ValidatorFn[]
  ) {
    try {
      let vals = null;
      if (!rule) return;
      // let parsedData = JSON.parse(rule)
      if (isValidJSON(rule)) {
        // Need this below code... when FE rule is  written  using TEXT view mode with filter component ex. [{"required": "(controls.isSameAsCurrentAddress.value ? false:true)"}]

        let parsedData = JSON.parse(rule);

        parsedData.forEach((element) => {
          let key = Object.keys(element);

          let exper = formStringExpression(element[key[0]], [
            "controls",
            "asset",
          ]);
          element[key[0]] = evalStringExpression(exper, thisRef, [
            this._form.controls,
            this.allAssetItems,
          ]);
        });
        vals = parsedData;
      } else {
        if (typeof rule != "string") rule = JSON.stringify(rule);
        // Need this below code .. when FE rule is written  using editor ex. [{required: controls.isSameAsCurrentAddress.value ? false:true}]
        const exper = formStringExpression(rule, ["controls", "asset"]);
        vals = evalStringExpression(exper, thisRef, [
          this._form.controls,
          this.allAssetItems,
        ]);
      }

      const componentData = {
        formControls: this._form.controls,
        this: thisRef,
        assets: this.allAssetItems,
      };

      const regexPatternFromAsset =
        this.field[this.key]?.displayProperty?.validation;
      const validations = validationsGetter(
        vals,
        regexPatternFromAsset,
        this.field,
        componentData
      );
      if (
        inputType === ZcpDataTypes.DATE ||
        inputType === ZcpDataTypes.DATE_TIME
      ) {
        this._form.controls[this.key].removeValidators(validations);
      } else {
        this._form.controls[this.key].clearValidators();
      }
      this._form.controls[this.key].updateValueAndValidity({
        emitEvent: false,
      });
      this._form.controls[this.key].addValidators(validations);
      this._form.controls[this.key].updateValueAndValidity({
        emitEvent: false,
      });
    } catch (error) {
      console.error(error);
    }
  }

  executeDefaultValueRule(
    rule: string,
    thisRef,
    selectedBusinessProcessDetails
  ) {
    try {
      if (!rule) return;
      const evaluatedValue = this.evaluateValueRule(rule, thisRef);

      if (evaluatedValue !== "") {
        this.valueRuleForPicklist(
          this.field,
          evaluatedValue,
          selectedBusinessProcessDetails
        );
      }
    } catch (error) {
      console.error(error);
    }
  }

  valueRuleForPicklist(
    element,
    evaluatedValue,
    selectedBusinessProcessDetails
  ) {
    const bpNode = selectedBusinessProcessDetails?.assetItems?.find(
      (item) =>
        item[this.getPropertyName(item)].name ==
        element[this.getPropertyName(element)].name
    );

    const nestedBPNode =
      selectedBusinessProcessDetails?.dealAsset?.dealAssetItem?.find((item) => {
        if (
          item[this.getPropertyName(item)].inputType == "Repetitive Section" ||
          item[this.getPropertyName(item)].inputType == "Table" ||
          item[this.getPropertyName(item)].inputType == "Advance Table"
        ) {
          const defaultValues =
            item[this.getPropertyName(item)].displayProperty.defaultValues;
          if (defaultValues && Array.isArray(defaultValues)) {
            return defaultValues.some((value) => {
              const keyNames = Object.keys(value);
              return keyNames.some(
                (key) =>
                  key === element[this.getPropertyName(element)].name ||
                  value[key]?.name ===
                    element[this.getPropertyName(element)].name
              );
            });
          }
        }
        return false;
      });

    const defaultValues = bpNode
      ? bpNode[this.getPropertyName(bpNode)]?.displayProperty?.defaultValues
      : nestedBPNode
      ? (() => {
          const defaultValuesArray =
            nestedBPNode[this.getPropertyName(nestedBPNode)]?.displayProperty
              ?.defaultValues;

          if (Array.isArray(defaultValuesArray)) {
            const evaluatedNode = defaultValuesArray.find((value) =>
              Object.values(value).some(
                (item: any) =>
                  item?.name === element[this.getPropertyName(element)].name
              )
            );
            return (
              evaluatedNode?.[element[this.getPropertyName(element)].name]
                ?.displayProperty?.defaultValues || ""
            );
          }
          return "";
        })()
      : "";
    const evalArray = evaluatedValue.split(",");
    const defaultValArray = defaultValues.split(",");
    const containsAll = evalArray.every((val) => defaultValArray.includes(val));
    if (containsAll) {
      element[this.getPropertyName(element)].displayProperty.defaultValues =
        evaluatedValue;
    } else {
      element[this.getPropertyName(element)].displayProperty.defaultValues =
        defaultValues;
    }
  }
  2449392;
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }
}

export class styleChange {
  constructor(
    private _form: FormGroup,
    private rule: string,
    private allAssetItems: any
  ) {}
  executeColorIndicationRule(thisRef) {
    const exper = formStringExpression(this.rule, ["controls", "asset"]);
    const flatAssetItem = Object.assign({}, ...this.allAssetItems);
    const evaluatedValue = evalStringExpression(exper, thisRef, [
      this._form.controls,
      flatAssetItem,
    ]);
    return evaluatedValue || null;
  }
}
