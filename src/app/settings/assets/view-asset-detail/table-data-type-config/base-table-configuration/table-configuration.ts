import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { FormBuilder } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { MatTableDataSource } from "@angular/material/table";
import { ToasterService } from "src/app/common/toaster.service";
import { Utils } from "src/app/helpers/utils";
import { SetRulesTableItemComponent } from "./set-rules-table-item/set-rules-table-item.component";
import { ColumnConfiguration } from "./column-configuration";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

export class TableConfiguration {
  private data_source: MatTableDataSource<ColumnConfiguration>;
  private add_column: ColumnConfiguration;
  private active_column: ColumnConfiguration;
  public display_columns = [
    "description",
    "dataType",
    "defaultValue",
    "actions",
  ];
  private nestedTableConfiguration: TableConfiguration;
  public table_error: string;
  public readonly = false;
  constructor(
    private readonly columns_data: tableColumn[],
    public readonly parent_data_type: string,
    private readonly fb: FormBuilder,
    private readonly dialog: MatDialog,
    private readonly notification: ToasterService
  ) {
    if (parent_data_type == "Table") {
      this.display_columns = [
        "uniqueKey",
        "description",
        "dataType",
        "defaultValue",
        "actions",
      ];
    }
    const column_configuration_data = [];
    columns_data.forEach((col) => {
      const column_obj = new ColumnConfiguration(
        this.parent_data_type,
        this.fb,
        this.notification
      );
      column_obj.patchFormValues(col);
      column_obj.updateColumnValues();
      column_configuration_data.push(column_obj);
    });
    this.data_source = new MatTableDataSource(column_configuration_data);
    this.add_column = new ColumnConfiguration(
      this.parent_data_type,
      this.fb,
      this.notification
    );
  }

  public get _add_column(): ColumnConfiguration {
    return this.add_column;
  }

  public get _active_column(): ColumnConfiguration {
    return this.active_column;
  }

  public get _data_source(): MatTableDataSource<ColumnConfiguration> {
    return this.data_source;
  }

  public get _nested_table(): TableConfiguration {
    return this.nestedTableConfiguration;
  }

  /**
   *
   * @param column
   */
  public set_nested_table(column: ColumnConfiguration) {
    this.active_column = column;
    this.nestedTableConfiguration = new TableConfiguration(
      column._form.value.defaultValues ? column._form.value.defaultValues : [],
      column._data_type,
      this.fb,
      this.dialog,
      this.notification
    );
    if (!column._display_form) {
      this.nestedTableConfiguration.readonly = true;
      this.nestedTableConfiguration.display_columns.pop();
    }
  }

  reset_nested_table() {
    this.nestedTableConfiguration = new TableConfiguration(
      [],
      this.nestedTableConfiguration.parent_data_type,
      this.fb,
      this.dialog,
      this.notification
    );
  }

  /**
   *
   * @param tableConfig
   * @returns
   */
  public mapTableToJSON(tableConfig?: TableConfiguration) {
    const finalColumnData = [];
    const dataSource = tableConfig ? tableConfig.data_source : this.data_source;
    dataSource.data.forEach((col: ColumnConfiguration, index: number) => {
      col._order = index;
      finalColumnData.push(col.mapColumnToJSON());
    });
    return finalColumnData;
  }

  /**
   *
   * @param column
   */
  addNewColumn(column: ColumnConfiguration) {
    const currentTableData = this.data_source.data;
    column._name = Utils.keyOf(column._form.controls.displayName.value);
    column._form.markAllAsTouched();
    if (column._form.invalid) {
      this.notification.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.isDuplicateColumnName(column._name)) {
      this.notification.error("Column with same name already exists.");
      return;
    }
    if (
      column._data_type == "Table" &&
      !(column._form.controls.defaultValues.value?.length > 0)
    ) {
      this.notification.error("Please set valid configuration");
      return;
    }
    if (
      column._data_type === ZcpDataTypes.FETCH_AND_MAP &&
      !column._is_Valid_Fetch_Map_Config
    ) {
      this.notification.error("Please set valid configuration");
      return;
    }
    column.toggleFormDisplay();

    currentTableData.push(column);
    this.data_source = new MatTableDataSource(currentTableData);
    this.add_column = new ColumnConfiguration(
      this.parent_data_type,
      this.fb,
      this.notification
    );
  }

  /**
   *
   * @param column_name
   * @returns whether column with name is present or not
   */
  isDuplicateColumnName(column_name: string) {
    return this.data_source.data.some((col) => col._name === column_name);
  }

  /**
   *
   * @param column_name
   * @param index
   */
  removeColumn(column_name: string, index?: number) {
    if (index >= 0) {
      this.data_source.data.splice(index, 1);
    } else {
      const col_index = this.data_source.data.findIndex(
        (col) => col._name == column_name
      );
      this.data_source.data.splice(col_index, 1);
    }

    this.data_source = new MatTableDataSource(this.data_source.data); //to refresh table
  }

  /**
   *
   * @param column_name
   * @param new_column_data
   */
  updateColumn(column_name: string, new_column_data: tableColumn) {
    const column_to_update = this.data_source.data.find(
      (col) => col._name === column_name
    );
    if (column_to_update) {
      Object.assign(column_to_update, new_column_data);
    }
    this.data_source = new MatTableDataSource(this.data_source.data); //to refresh table
  }

  /**
   *
   * @param column_name
   */
  updateAdvanceColumnConfig(column_name: string) {
    const column_to_update = this.data_source.data.find(
      (col) => col._name === column_name
    );
    const matDialogRef = this.dialog.open(SetRulesTableItemComponent, {
      width: "45%",
      data: {
        data: column_to_update.mapColumnToJSON(),
        dataType: this.parent_data_type,
      },
    });
    matDialogRef.afterClosed().subscribe((new_column_data: tableColumn) => {
      if (new_column_data) {
        column_to_update.updateColumnValuesFromRulesDialog(new_column_data);
      }
      this.data_source = new MatTableDataSource(this.data_source.data); //to refresh table
    });
  }

  restoreColumnConfig(column: ColumnConfiguration) {
    column.toggleFormDisplay();
    column.patchFormValues(column.mapColumnToJSON());
  }

  /**
   *
   * @param event
   */
  updateColumnPosition(event: CdkDragDrop<[]>): void {
    moveItemInArray(
      this.data_source.filteredData,
      event.previousIndex,
      event.currentIndex
    );
    this.data_source = new MatTableDataSource(this.data_source.data);
  }

  markAsUniquekey(column_name: string, value) {
    const column_to_update = this.data_source.data.find(
      (col) => col._name === column_name
    );
    column_to_update.markAsUniqueKey(value);
  }
}

type tableColumn = {
  [key: string]: {
    name: string;
    inputType: string;
    value: string;
    displayName?: string; //to handle older records
    displayProperty: {
      order: number;
      validation: string;
      displayName: string;
      defaultValues: any;
      totalOfColumnsRow?: "Y" | "N";
      enableInlineEditor?: "Y" | "N";
      tableSize?: "full" | "half";
    };
    ruleDetails: tableColumnRules;
  };
};

type tableColumnRules = {
  isMandatory?: "Y" | "N";
  isReadOnly?: "Y" | "N";
  _hide?: string;
  _validate?: string;
  _readonly?: string;
  _value?: string;
};
