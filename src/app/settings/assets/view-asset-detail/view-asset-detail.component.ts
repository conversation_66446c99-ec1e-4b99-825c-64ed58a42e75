import { EditItemDialogComponent } from "./../edit-item-dialog/edit-item-dialog.component";
import { EditAssetDialogComponent } from "./../../edit-asset-dialog/edit-asset-dialog.component";
import { AssetServiceService } from "./../../../shared-service/asset-service.service";
import { AddItemTemplateComponent } from "./add-item-template/add-item-template.component";
import { Component, OnInit, ViewChild, ChangeDetectorRef } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { ActivatedRoute, Router } from "@angular/router";
import { ToasterService } from "src/app/common/toaster.service";
import { MatTable, MatTableDataSource } from "@angular/material/table";
import {
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { DataFormatterForTableService } from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
import { ActionsCellComponent } from "src/app/shared-module/ag-grid-table/actions-cell/actions-cell.component";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import { finalize } from "rxjs";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { DM_UPDATE_ACTIONS } from "../../bussiness-process-config/data-models.model";
export interface AddItemElement {
  description: string;
  dataType: string;
  defaultValue: string;
  edit: string;
  actions: string;
}

@Component({
  selector: "app-view-asset-detail",
  templateUrl: "./view-asset-detail.component.html",
  styleUrls: ["./view-asset-detail.component.scss"],
})
export class ViewAssetDetailComponent implements OnInit {
  dragDisabled = true;
  totalNumberOfRecords = 0;
  editAction = { iconName: "edit", className: "blue", name: "edit" };
  deleteAction = { iconName: "delete", className: "red", name: "delete" };
  deletedItems = [];

  actions = [];
  originalValues: any[];
  dispalyColoumnsForAgGridTable: any = [
    {
      field: "descripation",
      headerName: "Descripation",
      filter: "agTextColumnFilter",
    },
    { field: "name", headerName: "Name", filter: "agTextColumnFilter" },
    {
      field: "dataType",
      headerName: "Data type",
      filter: "agTextColumnFilter",
    },
    {
      field: "action",
      headerName: "Actions",
      width: 200,
      pinned: "right",
      cellRenderer: ActionsCellComponent,
      cellRendererParams: {
        customParam: { actions: [this.editAction, this.deleteAction] },
      },
    },
  ];

  listViewData: any;

  checked = false;
  indeterminate = false;
  labelPosition: "before" | "after" = "after";
  disabled = false;
  asset;
  assetId;
  version;
  activePageDataChunk: any;
  showNoRecordsAvailbleMessage = false;
  pageEvent: PageEvent;
  displayedColumns: string[] = ["description", "name", "dataType", "edit"];
  displayedColumnsnew: string[] = [
    "dragDrop",
    "description",
    "name",
    "dataType",
    "edit",
  ];
  dataSource: any;
  pagelength;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  loading = false;
  JsonData: any;

  get ASSET_RESOURCE() {
    return ConfigurationResources.Asset_Def;
  }

  constructor(
    protected errorService: ErrorService,
    private readonly router: Router,
    private readonly dialog: MatDialog,
    public notificationMessage: ToasterService,
    public assetService: AssetServiceService,
    private readonly activeRoute: ActivatedRoute,
    public assetservice: AssetServiceService,
    private readonly dataFormatterForTableService: DataFormatterForTableService,
    public themeService: ThemeService,
    private readonly dataTypesUtils: DataTypesUtilsService
  ) {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };

    this.asset = state ? state : "";
    this.assetId = state ? state["id"] : "";
    this.version = state ? state["version"] : "";
    this.showNoRecordsAvailbleMessage = false;
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  ngOnInit(): void {
    if (this.assetId && this.version) {
      // Init value with API values
      this.getAssetDetailsById();
    } else {
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("id")) {
          this.assetId = atob(params.get("id"));
          this.getAssetDetailsById();
        }
      });
    }

    this.assetService.addItemObj.subscribe((response) => {
      let data = [];

      data = this.assetService.assetConfigurations;

      this.dataSource = new MatTableDataSource(
        this.assetService.assetConfigurations
      );
      this.listViewData =
        this.dataFormatterForTableService.getFormattedDataForAgTable(
          data,
          "assetItemList"
        );
      this.dataSource.paginator = this.paginator;
      this.totalNumberOfRecords = this.listViewData?.length;

      if (this.assetService.assetConfigurations.length == 0) {
        this.showNoRecordsAvailbleMessage = true;
      } else {
        this.showNoRecordsAvailbleMessage = false;
      }
    });
  }

  getAssetDetailsById() {
    this.loading = true;
    this.assetService.getAssetById(this.assetId).subscribe(
      (data) => {
        this.originalValues = structuredClone(data.assetConfigurations);
        this.deletedItems = [];
        this.dataSource = new MatTableDataSource(data.assetConfigurations);
        this.assetService.assetConfigurations = data.assetConfigurations;
        this.listViewData =
          this.dataFormatterForTableService.getFormattedDataForAgTable(
            data.assetConfigurations,
            "assetItemList"
          );
        this.totalNumberOfRecords = this.listViewData?.length;

        this.asset = data;

        this.dataSource.paginator = this.paginator;
        this.loading = false;
        if (this.dataSource.data.length == 0)
          this.showNoRecordsAvailbleMessage = true;
        else this.showNoRecordsAvailbleMessage = false;
      },
      () => {
        this.showNoRecordsAvailbleMessage = true;
        this.loading = false;
      }
    );
  }

  onPageChanged(e) {
    const firstCut = e.pageIndex * e.pageSize;
    const secondCut = firstCut + e.pageSize;
  }

  onAddItem() {
    this.dialog.open(AddItemTemplateComponent, {
      width: "75vw",
      disableClose: true,
    });
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  editAsset(row) {
    const dialogRef = this.dialog.open(EditAssetDialogComponent, {
      disableClose: true,
      data: { asset: row },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.assetTypeName) this.asset = result;
    });
  }
  // assetTypeName
  editRows(param: any, i: number) {
    const dialogRef = this.dialog.open(EditItemDialogComponent, {
      width: "65%",
      disableClose: true,
      data: { item: param, index: i },
    });

    dialogRef.afterClosed().subscribe((result) => {
      // Update data source with updated data
      this.loading = true;
      this.dataSource = new MatTableDataSource(
        this.assetService.assetConfigurations
      );
      this.listViewData =
        this.dataFormatterForTableService.getFormattedDataForAgTable(
          this.assetService.assetConfigurations,
          "assetItemList"
        );
      this.totalNumberOfRecords = this.listViewData?.length;
      this.dataSource.paginator = this.paginator;
      this.loading = false;
    });
  }

  deleteRowOption(row) {
    return true;
  }

  removeRows(row) {
    const index = this.assetService.assetConfigurations.findIndex((item) => {
      return Object.entries(item)[0][0] === Object.entries(row)[0][0];
    });

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = this.asset.isBusinessProcessLinked
      ? JsonData["label.warning.dataModelLinkedBP"]
      : JsonData["label.warning.delete.confirmation"];
    const additionalMessage = this.asset.isBusinessProcessLinked
      ? JsonData["label.warning.impacts"]
      : "";
    const infoMessage = this.asset.isBusinessProcessLinked
      ? JsonData["label.warning.delete.confirmation"]
      : "";
    const icon = this.asset.isBusinessProcessLinked
      ? "crisis_alert"
      : "warning";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        additionalMessage: additionalMessage,
        buttonList: buttonList,
        icon: icon,
        infoMessage: infoMessage,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loading = true;
        const currentConfig = this.assetService.assetConfigurations[index];
        currentConfig[Object.keys(currentConfig)[0]]["action"] =
          DM_UPDATE_ACTIONS.DELETE;
        this.deletedItems.push(currentConfig);
        this.assetService.assetConfigurations.splice(index, 1);
        this.dataSource = new MatTableDataSource(
          this.assetService.assetConfigurations
        );
        this.listViewData =
          this.dataFormatterForTableService.getFormattedDataForAgTable(
            this.assetService.assetConfigurations,
            "assetItemList"
          );
        this.totalNumberOfRecords = this.listViewData?.length;
        this.dataSource.paginator = this.paginator;
        this.notificationMessage.success(JsonData["label.success.DeleteItem"]);
        this.loading = false;
        if (this.dataSource.data.length == 0)
          this.showNoRecordsAvailbleMessage = true;
        else this.showNoRecordsAvailbleMessage = false;
      }
    });
  }

  applyFilter(filterValue: string) {
    if (this.dataSource) {
      filterValue = filterValue.trim(); // Remove whitespace
      filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches

      this.dataSource.data = this.assetService.assetConfigurations.filter(
        (item) =>
          (item[this.getPropertyName(item)]?.displayProperty?.displayName)
            .toLowerCase()
            .includes(filterValue)
      );
    }
  }

  // Update the data
  saveDetails() {
    // Edit
    const orderedPayload = structuredClone(
      this.assetService.assetConfigurations.map((item, idx) => {
        item[Object.keys(item)[0]]["dataModelOrder"] = idx;
        return item;
      })
    );

    if (this.assetId) {
      const assetConfigDiff = this.dataTypesUtils.getObjDiff(
        this.originalValues,
        orderedPayload
      );
      const assetData = {
        assetConfigurations: [...assetConfigDiff, ...this.deletedItems],
      };

      this.assetService
        .updateAssetDetails(assetData, this.assetId, this.version)
        .pipe(
          finalize(() => {
            this.loading = false;
          })
        )
        .subscribe((data) => {
          this.loading = true;
          if (data) {
            this.notificationMessage.success(
              "Data Model " +
                this.asset.assetTypeName +
                " " +
                JsonData["label.success.Update"]
            );
            this.loading = false;
          }
          this.getAssetDetailsById();
        });
    }

    // Save
    else {
      const assetData = {
        id: this.assetId,
        version: this.version,
        assetTypeName: this.asset.assetTypeName?.trim(),
        description: this.asset.description,
        assetConfigurations: orderedPayload,
      };
      this.assetService.createAssetDetails(assetData).subscribe(
        (data) => {
          this.notificationMessage.success(
            "Data Model " + data + " " + JsonData["label.success.Create"]
          );
          this.assetService.getAssetByName(data).subscribe((data) => {
            this.originalValues = data.assetConfigurations;
            this.assetId = data.id;
            this.asset = data;
            this.version = data.version;
          });
        },
        (error) => {
          this.loading = false;
        }
      );
    }
  }

  // to drag and drop table rows
  @ViewChild("table2") table2: MatTable<any>;
  @ViewChild("list1") list1: CdkDropList;

  drop(event: CdkDragDrop<any[]>, assetPaginator?) {
    this.dragDisabled = true;
    const previousIndex = this.dataSource?.data?.findIndex(
      (d) => d === event.item.data
    );

    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        previousIndex,
        assetPaginator.pageIndex * assetPaginator.pageSize + event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        previousIndex,
        assetPaginator.pageIndex * assetPaginator.pageSize + event.currentIndex
      );
    }
    this.dataSource.data = [...this.dataSource.data];
    this.assetService.assetConfigurations = this.dataSource.data;
  }

  onChangesReceived(data) {
    if (data.eventName == "delete") {
      this.removeRows(data.data);
    }
    if (data.eventName == "edit") {
      const index = this.assetService.assetConfigurations.findIndex((item) => {
        return item[this.getPropertyName(item)]?.name === data?.data?.name;
      });
      const rowData = this.assetService.assetConfigurations[index];
      this.editRows(rowData, index);
    }
  }
}
