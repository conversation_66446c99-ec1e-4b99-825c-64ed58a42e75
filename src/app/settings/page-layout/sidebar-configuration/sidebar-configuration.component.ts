import { Component } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { AddButtonRulesComponent } from "src/app/dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import { ErrorService } from "src/app/shared-service/error.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import JsonData from "src/assets/data.json";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-sidebar-configuration",
  templateUrl: "./sidebar-configuration.component.html",
  styleUrls: ["./sidebar-configuration.component.scss"],
})
export class SidebarConfigurationComponent {
  sidebarConfigDetails: any = undefined;
  enableEdit: any = false;
  loading = true;
  JsonData: any;
  displayName: string;
  editIndex: any = -1;
  sidenavList = [
    { name: "Dashboard", icon: "dashboard", url: "/home" },
    { name: "Deal", icon: "work", url: "/application" },
    { name: "Entity", icon: "group", url: "" },
    { name: "Task", icon: "assignment_turned_in", url: "/task" },
    { name: "Planner", icon: "event", url: "/planner" },
    { name: "Reporting", icon: "leaderboard", url: "/report" },
    { name: "Configuration", icon: "settings", url: "" },
    { name: "Bulk Stage Move", icon: "fast_forward", url: "/home" },
    { name: "User Guide", icon: "help_outline", url: "" },
    { name: "Theme Toggle", icon: "toggle_off", url: "/home" },
  ];

  get UTILITIES_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  constructor(
    private errorService: ErrorService,
    public pageLayoutService: PageLayoutService,
    public notificationMessage: ToasterService,
    public dialog: MatDialog,
    public dataSharingService: DataSharingService,
    public themeService: ThemeService
  ) {
    // this.getAllConfigurationDetails();
    this.getConfigurationDetailsByIdentifier("SIDE_BAR");
  }

  sidebarItemsList: any = [];
  getConfigurationDetailsByIdentifier(identifier) {
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .subscribe((res: any) => {
        this.sidebarConfigDetails = Object.assign({}, res);
        this.sidebarItemsList =
          this.sidebarConfigDetails?.configDetails?.slice();

        const data = JSON.parse(JSON.stringify(this.sidebarConfigDetails));
        this.dataSharingService.setSidebarItems(data);
        this.loading = false;
      });
  }

  isValid: any = false;
  updateAPICall() {
    this.getValidate(this.sidebarItemsList?.slice());
    const id = this.sidebarConfigDetails?.id;
    this.sidebarConfigDetails.configDetails = this.sidebarItemsList?.slice();

    if (!this.isValid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    const data = Object.assign({}, this.sidebarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe(
        () => {
          this.dataSharingService.setSidebarItems(data);
          localStorage.setItem("sidebarItems", JSON.stringify(data));
          this.getConfigurationDetailsByIdentifier("SIDE_BAR");

          this.notificationMessage.success(JsonData["label.success.Sidebar"]);
          this.loading = false;
        },
        (error) => {
          this.loading = false;

          this.getConfigurationDetailsByIdentifier("SIDE_BAR");
        }
      );
  }

  getValidate(configDetails) {
    const arr = configDetails
      .map((ele) => ele.displayName)
      ?.filter((ele) => ele);
    if (arr?.length != configDetails?.length) {
      this.isValid = false;
    } else {
      this.isValid = true;
    }
  }

  getIconName(item) {
    const sidebarItem = this.sidenavList.filter(
      (ele) => ele.name == item.name
    )[0];
    return sidebarItem?.icon;
  }
  onCancel() {
    this.getConfigurationDetailsByIdentifier("SIDE_BAR");
  }
  addRules(ele, index) {
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "45vw",
      disableClose: true,
      data: {
        buttonRules: ele?.rules,
        translationContext: "Set Hide Rules",
        enableSuggestions: true,
        parentName: "sidebarLiterals",
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result && typeof result == "object") {
        ele.rules = result.rules;
        this.sidebarConfigDetails.configDetails[index] = ele;
        this.updateAPICall();
      }
    });
  }
  onEdit(index) {
    this.editIndex = index;
    this.displayName =
      this.sidebarConfigDetails.configDetails[index].displayName;
  }
  editLiterals(index, name) {
    this.sidebarConfigDetails.configDetails[index].displayName = name;
    this.editIndex = -1;
    this.updateAPICall();
  }
  discardLiterals() {
    this.editIndex = -1;
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 35);
  }
}
