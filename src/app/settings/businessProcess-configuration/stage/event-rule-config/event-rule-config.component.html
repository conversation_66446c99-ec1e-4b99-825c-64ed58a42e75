<div fxLayout="row" class="w100 mb1">
  <div fxFlex="100%" fxLayoutAlign="end center" fxLayoutGap="10">
    <mat-slide-toggle class="mr1 " matTooltipClass="accent-tooltip" [checked]="true"
      matTooltip="Switch to old Configuration UI" labelPosition="before"
      (change)="switchToOldConfiguration($event.checked)" color="primary">
      <span class="font-color">{{ "label.button.switchToOldConfiguration" |
        literal}}</span>
    </mat-slide-toggle>
    <button (click)="valueChange.emit(false)" class=" large-icon-button outlined-icon-button"
      mat-icon-button aria-label="save-btn-app-events"
      *ifHasPermission="BP_RESOURCE.Business_Process; scope:'CHANGE'" matTooltipPosition="above"
      matTooltipClass="accent-tooltip" matTooltip="Cancal">
      <span class="material-symbols-outlined">close</span>
    </button>
    <ng-container *ngIf="showSaveButton">
      <button class="colored-icon-button large-icon-button" mat-icon-button
        aria-label="save-btn-app-events" (click)="onChangesReceived()"
        *ifHasPermission="BP_RESOURCE.Business_Process; scope:'CHANGE'" matTooltipPosition="above"
        matTooltipClass="accent-tooltip" matTooltip="Save">
        <span class="material-symbols-outlined">check</span>
      </button>
    </ng-container>

  </div>
</div>
<div fxLayout="row" class="w100 mb-3">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

    <table style="height: 500px; overflow: auto;" mat-table [dataSource]="tableData"
      class="mat-elevation-z8 uniform-row-table">
      <ng-container matColumnDef="{{ column }}" *ngFor="let column of displayedColumns;let i=index">
        <th [style.width]="getDataFromColoumnName(column , true)" mat-header-cell *matHeaderCellDef>
          {{ getDataFromColoumnName(column , false) }}
        </th>
        <ng-container *matCellDef="let element">
          <td [class.buttonView]="column ==='buttonName'" mat-cell
            *ngIf="column!='buttonName' || element.rowspan!=-1"
            [attr.rowspan]="column=='buttonName' && element.rowspan?element.rowspan:null"
            [style.padding-left.px]="i>0?0:null" [style.width.px]="column=='buttonName'?90:null">

            <ng-container *ngIf="column =='eventName'">
              <mat-icon *ngIf="element?.eventIcon">{{element?.eventIcon}}</mat-icon> {{
              element[column] }}
            </ng-container>

            <ng-container *ngIf="column ==='buttonName'">
              <button [class]="element['buttonColor']" *ngIf="element?.buttonType ==='matRaised'"
                mat-raised-button>
                {{element.eventName}}
                <span *ngIf="element?.buttonIcon">
                  <mat-icon style="margin-bottom: -7px;">{{element?.buttonIcon}}</mat-icon>
                </span>

              </button>
              <button *ngIf="element?.buttonType ==='matIcon'" class="outlined-icon-button"
                mat-icon-button>
                <span class="material-symbols-outlined">{{element?.buttonIcon}}</span>
              </button>
              <button style="margin-left:1%"
                *ngIf="element?.buttonType ==='matIcon' && element?.subIcon"
                class="outlined-icon-button" mat-icon-button>
                <span class="material-symbols-outlined">{{element?.subIcon}}</span>
              </button>

              <mat-icon *ngIf="element?.buttonType ==='icon'">{{element?.buttonIcon}}</mat-icon>


            </ng-container>

            <ng-container *ngIf="column ==='action'">

              <button (click)="openRuleDialog(element , 'add')" *ngIf="!element['isRulePresent']"
                attr.aria-label="add-icon-{{element.buttonName}}-event" mat-icon-button
                class="blue margin-left-7">
                <span class="material-symbols-outlined">add</span>

              </button>
              <ng-container *ngIf="element['isRulePresent']">


                <button (click)="openRuleDialog(element , 'edit')"
                  attr.aria-label="view-icon-{{element.buttonName}}-event" mat-icon-button
                  class="blue margin-left-7">
                  <span class="material-symbols-outlined">remove_red_eye</span>

                </button>

                <button (click)="openDeleteRuleDialog(element , 'delete')"
                  attr.aria-label="delete-icon-{{element.buttonName}}-event" mat-icon-button>
                  <span class="material-symbols-outlined">delete </span>
                </button>
              </ng-container>
            </ng-container>

          </td>
        </ng-container>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>

      <tr mat-row *matRowDef="let row; columns: displayedColumns" class="example-detail-row"></tr>
    </table>
  </div>
</div>
