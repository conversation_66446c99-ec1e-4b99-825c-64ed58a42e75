import { CommonModule } from "@angular/common";
import {
  Component,
  EventEmitter,
  Input,
  input,
  OnInit,
  Output,
  SimpleChanges,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";

import { ConfigurationResources } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { EventRuleDialogComponent } from "./event-rule-dialog/event-rule-dialog.component";
import { eventElement, eventList } from "./event";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";
import JsonData from "src/assets/data.json";
import { ToasterService } from "src/app/common/toaster.service";
import { EventRuleService } from "./event-rule.service";
const EVENT_DATA: eventElement[] = eventList;

@Component({
  selector: "app-event-rule-config",
  templateUrl: "./event-rule-config.component.html",
  styleUrls: ["./event-rule-config.component.css"],
  standalone: true,
  imports: [SharedModuleModule, CommonModule],
})
export class EventRuleConfigComponent implements OnInit {
  JsonData;
  @Input() eventRules: any;
  @Input() eventRuleConfigData: any;
  @Input() userRoles: any;
  @Output() valueChange = new EventEmitter();
  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }
  showSaveButton = false;
  eventsDetail = EVENT_DATA;

  _eventRules: any;
  _eventRuleConfigData: any;
  displayedColumns: string[];
  constructor(
    public themeService: ThemeService,
    public dialog: MatDialog,
    public businessProcessService: BusinessProcessService,
    private notificationMessage: ToasterService,
    private eventRuleService: EventRuleService
  ) {
    this.displayedColumns = ["buttonName", "eventName", "action"];
  }
  ngOnInit(): void {
    this._eventRules = JSON.parse(
      JSON.stringify(this.eventRules ? this.eventRules : {})
    );
    this._eventRuleConfigData = this.eventRuleConfigData
      ? this.eventRuleConfigData
      : {};
    if (this._eventRules) this.addActionIcons();
    this.formatQueryData(
      this.businessProcessService.data.assetItems,
      this.userRoles
    );
  }
  ngOnChanges(changes: SimpleChanges): void {
    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    //Add '${implements OnChanges}' to the class.
    let eventRulesData = changes.eventRules?.currentValue
      ? changes.eventRules?.currentValue
      : this.eventRules
      ? this.eventRules
      : {};
    this._eventRules = JSON.parse(JSON.stringify(eventRulesData));
    this._eventRuleConfigData = changes.eventRuleConfigData?.currentValue
      ? changes.eventRuleConfigData?.currentValue
      : this.eventRuleConfigData
      ? this.eventRuleConfigData
      : {};

    if (this._eventRules) this.addActionIcons();
  }

  onChangesReceived() {
    let resData = {
      _eventRules: this._eventRules,
      eventName: "save",
      _eventRuleConfigData: this._eventRuleConfigData,
    };
    this.valueChange.emit(resData);
  }

  getDataFromColoumnName(column, isWidthClass) {
    switch (column) {
      case "buttonName":
        return isWidthClass ? "30%" : "CTA";
      case "eventName":
        return isWidthClass ? "50%" : "Event";
      case "action":
        return isWidthClass ? "10%" : "Action";
    }
  }
  loadUI = false;
  tableData: any;
  addActionIcons() {
    this.eventsDetail.forEach((event, index) => {
      if (
        this._eventRules[event.ruleName] !== undefined &&
        this._eventRules[event.ruleName] !== ""
      ) {
        this.eventsDetail[index]["isRulePresent"] = true;
      } else {
        this.eventsDetail[index]["isRulePresent"] = false;
      }
    });
    this.tableData = this.eventsDetail.map((ele, i) => ({
      ...ele,
      rowspan:
        i == 0 || this.eventsDetail[i - 1].buttonName != ele.buttonName
          ? this.eventsDetail.filter((f) => f.buttonName == ele.buttonName)
              .length
          : -1,
    }));
  }

  openDeleteRuleDialog(ele, editOrAdd) {
    this.eventRuleService.deleteRuleDialog().subscribe((result) => {
      if (result) {
        delete this._eventRules[ele.ruleName];
        let index = this._eventRuleConfigData.findIndex(
          (item) => item.ruleName == ele.ruleName
        );

        if (
          index > -1 &&
          this._eventRuleConfigData &&
          this._eventRuleConfigData[index]
        ) {
          this._eventRuleConfigData[index] =
            this.eventRuleService.removeValuesKeysFromObject(
              this._eventRuleConfigData[index],
              ["queryMetaData", "rule"]
            );
        }
        ele.isRulePresent = false;
        this.showSaveButton = true;
        this.notificationMessage.success(JsonData["label.success.eventRule"]);
      }
    });
  }
  openRuleDialog(ele, editOrAdd) {
    let ruleConfig = this._eventRuleConfigData?.filter(
      (item) => item.ruleName == ele.ruleName
    )[0];
    ruleConfig["rule"] = this._eventRules[ele.ruleName]
      ? this._eventRules[ele.ruleName]
      : "";
    let ruledata = {
      rule: this._eventRules[ele?.ruleName],
      element: ele,
      buttonRuleFilterConfigData: ruleConfig,
    };
    const matDialogRef = this.dialog.open(EventRuleDialogComponent, {
      width: "85vw",
      maxWidth: "100%",
      height: "max-content",
      maxHeight: "100vh",
      disableClose: true,
      data: {
        assetItems: this.itemIlistForQueryBuilder,
        buttonRule: ruledata["rule"],
        buttonDetails: ele,
        buttonRuleFilterConfigData: ruledata["buttonRuleFilterConfigData"],
        translationContext: "Set Rules Json",
      },
    });
    matDialogRef.afterClosed().subscribe(async (result) => {
      if (result) {
        this.showSaveButton = true;
        await this._eventRuleConfigData?.forEach((item, index) => {
          if (item.ruleName == result.data.ruleName) {
            item = { ...result.data };
            this._eventRuleConfigData[index] = { ...result.data };

            this._eventRules[item.ruleName] = result?.data?.rule;
            if (this._eventRules[item.ruleName]) {
              ele.isRulePresent = true;
            } else {
              ele.isRulePresent = false;
            }
            let msg =
              editOrAdd == "add"
                ? JsonData["label.success.eventRuleAdded"]
                : JsonData["label.success.eventRuleUpdated"];
            this.notificationMessage.success(msg);
          }
        });
      }
    });
  }

  async switchToOldConfiguration(event) {
    await this._eventRuleConfigData.forEach((item, index) => {
      this._eventRuleConfigData[index]["view"] = "oldEditor";
    });
    if (!event) {
      let resData = {
        eventName: "switchToOld",
        _eventRules: this._eventRules,
        _eventRuleConfigData: this._eventRuleConfigData,
      };
      this.valueChange.emit(resData);
    }
  }

  itemIlistForQueryBuilder: any = [];

  /** Following  function is for to form a assetItems data in a required sturcture for a
   * query builder
   */
  formatQueryData(assetItems, userRoles) {
    let complexDataType = [
      "Table",
      "Advance Table",
      "Nested Table",
      "Repetitive Section",
      "Address",
    ];
    let roleObj = {
      fieldName: "userRole",
      inputType: "Multiple Static Picklist",
      displayName: "User roles",
      options: userRoles?.join(","),
      fieldRule: "this.userRoles",
    };
    let userObj = {
      fieldName: "user",
      inputType: "Text",
      displayName: "User",
      options: "",
      fieldRule: "this.user",
    };
    let assetData = JSON.parse(JSON.stringify(assetItems ? assetItems : {}));
    assetData = assetData
      ?.map((ele) => {
        if (
          !complexDataType.includes(ele[this.getPropertyName(ele)]?.inputType)
        ) {
          return {
            fieldName: this.getPropertyName(ele),
            inputType: ele[this.getPropertyName(ele)]?.inputType,
            displayName:
              ele[this.getPropertyName(ele)]?.displayProperty?.displayName,
            options:
              ele[this.getPropertyName(ele)]?.displayProperty?.defaultValues,
            fieldRule: `asset.${this.getPropertyName(ele)}?.value`,
          };
        }
      })
      .filter(Boolean)
      ?.sort((a, b) =>
        (a.displayName || "").localeCompare(b.displayName || "", {
          sensitivity: "accent",
        })
      );
    this.itemIlistForQueryBuilder = assetData;
    this.itemIlistForQueryBuilder.push(roleObj);
    this.itemIlistForQueryBuilder.push(userObj);
  }
  // get the property Name
  getPropertyName(element) {
    return element ? Object.entries(element)[0][0] : null;
  }
}
