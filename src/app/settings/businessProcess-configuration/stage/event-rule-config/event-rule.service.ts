import { Injectable } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";

@Injectable({
  providedIn: "root",
})
export class EventRuleService {
  constructor(public themeService: ThemeService, public dialog: MatDialog) {}

  deleteRuleDialog() {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = `Are you sure you want to delete this Rule ?`;
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    return matDialogRef.afterClosed();
  }

  removeValuesKeysFromObject(obj, keys) {
    keys.forEach((ele) => {
      obj[ele] = "";
    });
    return { ...obj };
  }
}
