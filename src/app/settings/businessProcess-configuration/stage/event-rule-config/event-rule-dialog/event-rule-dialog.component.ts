import { CommonModule } from "@angular/common";
import { Component, Inject, OnInit, Optional } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialog,
} from "@angular/material/dialog";
import { eventNames } from "process";
import { ToasterService } from "src/app/common/toaster.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { CustomQueryModule } from "src/app/shared-module/custom-query-filter/custom-query.module";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { ThemeService } from "src/app/theme.service";
import { EventRuleService } from "../event-rule.service";

@Component({
  selector: "app-event-rule-dialog",
  templateUrl: "./event-rule-dialog.component.html",
  styleUrls: ["./event-rule-dialog.component.css"],
  standalone: true,
  imports: [SharedModuleModule, CommonModule, CustomQueryModule],
})
export class EventRuleDialogComponent implements OnInit {
  buttonDetails: any;
  ruleDetails: any;
  sideBarItemRuleKeys = [
    "__report__",
    "__topMenu__",
    "__subMenu__",
    "__dataModel__",
    "__businessProcess__",
    "__dashboard__",
    "__entityDefinition__",
    "__reports__",
    "__rolesActions__",
    "__themeToggle__",
    "__userGuide__",
    "__deal__",
    "__planner__",
    "__bulkStageMove__",
    "__personDetails__",
    "__companyDetails__",
  ];
  assetItems = [];
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<EventRuleDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private data: any,
    public notificationMessage: ToasterService,
    public themeService: ThemeService,
    private eventRuleService: EventRuleService
  ) {
    this.buttonDetails = JSON.parse(
      JSON.stringify(data.buttonDetails ? data.buttonDetails : {})
    );
    this.ruleDetails = JSON.parse(
      JSON.stringify(
        data.buttonRuleFilterConfigData ? data.buttonRuleFilterConfigData : {}
      )
    );
    this.assetItems = data.assetItems;
  }

  ngOnInit() {}
  onBackBtn() {
    this.dialogRef.close(false);
  }
  ngOnDestroy(): void {
    this.customProvider?.dispose();
  }
  customProvider: any;
  editorOptions = {
    theme: "vs-dark",
    language: "javascript",
    autoIndent: "full",
  };
  getFields() {}

  getQuerybuilderData() {
    return;
  }

  updatedQuery(event) {
    this.ruleDetails.queryMetaData = event.data;
    this.ruleDetails.rule = this.ruleDetails.queryMetaData.conditionRule;
  }

  getViewMode() {
    return this.ruleDetails["view"] == "editor" ? true : false;
  }

  getQueryData() {
    let data = this.ruleDetails["queryMetaData"];
    if (data) data["rule"] = this.ruleDetails["rule"];
    return data;
  }
  provideCompletionItems() {
    let fieldItems = [];
    // Implementing custom logic to provide completion items here
    if (!window["monaco"]) return;
    this.customProvider = window[
      "monaco"
    ].languages.registerCompletionItemProvider("javascript", {
      provideCompletionItems: (model, position) => {
        const match = true;
        if (!match) {
          return { suggestions: [] };
        }
        const word = model.getWordUntilPosition(position);
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        };
        return {
          suggestions: createDependencyProposals(range, fieldItems),
        };
      },
    });

    function createDependencyProposals(range, assetItems) {
      const suggestions = [];
      assetItems.forEach((item) => {
        const key = Object.entries(item)[0][0];
        suggestions.push({
          label: item[key].displayProperty.displayName,
          kind: window["monaco"].languages.CompletionItemKind.Snippet,
          documentation: `This will insert snippet in controls.${key}.value format`,
          insertText: "controls." + key + ".value",
          detail: "controls value",
          range: range,
        });

        suggestions.push({
          label: item[key].displayProperty.displayName,
          kind: window["monaco"].languages.CompletionItemKind.Snippet,
          documentation: `This will insert snippet in asset.${key}.value format`,
          insertText: "asset." + key + ".value",
          detail: "asset value",
          range: range,
        });
      });

      return [...suggestions];
    }
  }

  updateRulesConfig() {
    let res = {
      data: this.ruleDetails,
      eventName: "save",
    };
    this.dialogRef.close(res);
  }

  openConfirmation(event) {
    //open confirmation dialog only if rule value is present and view is switching from editor to filter

    if (this.ruleDetails.rule && !event.checked) {
      this.eventRuleService.deleteRuleDialog().subscribe((result) => {
        if (result) {
          this.ruleDetails.view = "filter";

          this.ruleDetails = this.eventRuleService.removeValuesKeysFromObject(
            this.ruleDetails,
            ["queryMetaData", "rule"]
          );

          event.source.checked = !event.checked;
        }
      });
      return;
    } else {
      this.ruleDetails.view = event.checked ? "editor" : "filter";
    }
  }
}
