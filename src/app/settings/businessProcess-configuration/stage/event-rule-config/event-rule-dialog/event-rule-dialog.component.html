<mat-dialog-content class="mat-dialog-content-form-custom-css">
  <div *ngIf="themeService.useNewTheme">
    <div fxLayout="row">
      <div fxFlex="92%" fxLayoutAlign="start center" fxLayoutGap="10">
        <!-- <mat-icon class="mr0">more_horiz</mat-icon> -->
        <h2 class="mv1">{{'label.title.FErulesConfiguration' | literal}}</h2>
      </div>
      <div fxFlex="7%" fxLayoutAlign="end center" fxLayoutGap="10">
        <button mat-icon-button (click)="onBackBtn()">
          <span class="material-symbols-outlined">close</span>
        </button>
      </div>
    </div>
    <div fxLayout="row">
      <div class="buttonView " fxFlex="100%" fxLayoutAlign="start center" fxLayoutGap="10">
        <div class="border-right">
          <button [class]="this.buttonDetails['buttonColor']"
            *ngIf="this.buttonDetails?.buttonType ==='matRaised'" mat-raised-button>
            {{this.buttonDetails.buttonName}}
            <span *ngIf="this.buttonDetails?.buttonIcon">
              <mat-icon style="margin-bottom: -7px;">{{this.buttonDetails?.buttonIcon}}</mat-icon>
            </span>

          </button>
          <button *ngIf="this.buttonDetails?.buttonType ==='matIcon'" class="outlined-icon-button"
            mat-icon-button>
            <span class="material-symbols-outlined">{{this.buttonDetails?.buttonIcon}}</span>
          </button>
          <button style="margin-left:1%"
            *ngIf="this.buttonDetails?.buttonType ==='matIcon' && this.buttonDetails?.subIcon"
            class="outlined-icon-button" mat-icon-button>
            <span class="material-symbols-outlined">{{this.buttonDetails?.subIcon}}</span>
          </button>

          <mat-icon
            *ngIf="this.buttonDetails?.buttonType ==='icon'">{{this.buttonDetails?.buttonIcon}}</mat-icon>


        </div>
        <div class="buttonView ">

          <h3> <mat-icon *ngIf="buttonDetails?.eventIcon">{{buttonDetails?.eventIcon}}</mat-icon>
            {{buttonDetails?.eventName }}</h3>
        </div>

      </div>


    </div>
    <div fxLayout="row">
      <div fxFlex="100%" fxLayoutAlign="start center" fxLayoutGap="10">
        <hr width="100%">
      </div>
    </div>

    <div fxLayout="row" *ngIf="this.ruleDetails['view'] == 'editor'">
      <div fxFlex="100%" fxLayoutAlign=" center" fxLayoutGap="10">
        <div style="width:100%">
          <ngx-monaco-editor (onInit)="provideCompletionItems()" [options]="editorOptions"
            [(ngModel)]="this.ruleDetails['rule']"></ngx-monaco-editor>
        </div>

      </div>
    </div>
    <div fxLayout="row" *ngIf="this.ruleDetails['view'] != 'editor'">
      <div fxFlex="100%">
        <app-custom-query-filter [assetFields]="assetItems" [queryBuilderType]="'Basic'"
          [queryDetails]="getQueryData()"
          (updatedQuery)="updatedQuery($event)"></app-custom-query-filter>
      </div>
    </div>

    <div fxLayout="row">
      <div fxFlex="100%" fxLayoutAlign="start center" fxLayoutGap="10" class="isEditorblock">
        Use code editor <mat-slide-toggle color="primary" (change)="openConfirmation($event)"
          [checked]="getViewMode()"></mat-slide-toggle>
      </div>


    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="center">

  <button color="primary" mat-raised-button type="submit" (click)="updateRulesConfig()">
    {{"label.button.save" | literal}}
  </button>
</mat-dialog-actions>
