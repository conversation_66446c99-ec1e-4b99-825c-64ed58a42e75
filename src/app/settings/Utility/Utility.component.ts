import { Component } from "@angular/core";
import { utilityTabs } from "./utility-static-data";
import { ThemeService } from "src/app/theme.service";
import { UtilitiesService } from "../utilities/utilities.service";

@Component({
  selector: "app-utility",
  templateUrl: "./Utility.component.html",
  styleUrls: ["./Utility.component.scss"],
})
export class UtilityComponent {
  utilityTabs = utilityTabs;
  newUtilitesUI = true;

  constructor(
    public themeService: ThemeService,
    public utilitiesService: UtilitiesService
  ) {}

  ngOnInit() {
    this.utilitiesService.newUtilitesUI$.subscribe((enabled) => {
      this.newUtilitesUI = enabled;
    });
  }
}
