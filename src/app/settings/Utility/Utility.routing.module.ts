import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { AuthGuard } from "src/app/guard/auth.guard";

import { CreateUserComponent } from "./dialogs/create-user/create-user.component";
import { UpdateUserComponent } from "./dialogs/update-user/update-user.component";
import { UsersComponent } from "./users/users.component";

const routes: Routes = [
  { path: '', component: UsersComponent,
  children : [
    {path: '', redirectTo: 'users', pathMatch: "full",}, 
    { path: 'create-user',canActivate: [AuthGuard], component: CreateUserComponent },
    { path: 'update-user',canActivate: [AuthGuard], component: UpdateUserComponent },
   
   

  ]
},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UtilityRoutingModule { }
