import { Component, OnInit, Inject } from '@angular/core';
import { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { IdentityService } from 'src/app/shared-service/identity.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { ErrorService } from 'src/app/shared-service/error.service';
import JsonData from 'src/assets/data.json';
import { ThemeService } from 'src/app/theme.service';
@Component({
  selector: 'app-update-user',
  templateUrl: './update-user.component.html',
  styleUrls: ['./update-user.component.scss']
})
export class UpdateUserComponent implements OnInit {
  JsonData:any;
  isExpanded_UserDetails: any;
  roles: { value: string; }[];
  updateUser: UntypedFormGroup;
  text: string;
  error: boolean;
  selectedRole: any = 'administrator';
  selectedCountryCode: any = '+91'
  countryCodes: any = [{ value: "+91" }];
  username: any;
  showUserNameErrorOnPattern = false;
  patternInfo = `Starts with alphabet, no special characters or space allowed.`;
  selectedUserDetail: any
  constructor(
    private errorService :ErrorService,
    public notificationMessage : ToasterService ,
    public dialogRef: MatDialogRef<UpdateUserComponent>,
    public identityService: IdentityService,
    private notification: ToasterService,
    public matDialog: MatDialog,
    public themeService : ThemeService,
    @Inject(MAT_DIALOG_DATA) public data: any) {

    this.selectedUserDetail = JSON.parse(JSON.stringify(data ?.selectedUserDetail))
  }

  ngOnInit() {
    this.updateUser = new UntypedFormGroup({
      firstName: new UntypedFormControl('', [Validators.required, Validators.pattern('^[a-zA-z]*$'), Validators.minLength(2)]),
      lastName: new UntypedFormControl('', [Validators.required, Validators.pattern('^[a-zA-z]*$'), Validators.minLength(2)]),
      mailId: new UntypedFormControl('', [Validators.required, Validators.email]),
      role: new UntypedFormControl({value: '', disabled: true}),
      phoneNumber: new UntypedFormControl('', [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10)]),
      identifier: new UntypedFormControl(''),
      countryCode: new UntypedFormControl()
    });
    this.roles = [{ value: 'Super user' }, { value: 'User' }, { value: 'Guest' }, { value: 'administrator' }]
    this.isExpanded_UserDetails = true;
    this.text = 'Please enter a value'
  }

  closeDialog() {
    this.dialogRef.close()
  }
  getErrorMessage(formName, controlName,labelName, customValidation?: any) {
    this.showUserNameErrorOnPattern = false;
    if (this[formName].controls[controlName].errors && this[formName].controls[controlName].errors.required) {
      return `${labelName} is required.`
    }

    if (this[formName].controls[controlName].errors && this[formName].controls[controlName].errors.pattern) {
      if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^[0-9]*$") {
        return "Only numbers allowed"
      }
      else if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^[a-zA-z ]*$") {
        return "Only alphabets allowed"
      }
      else if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^[a-zA-z]*$") {
        return "Only alphabets allowed"
      }

      else if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^[0-9a-zA-z]*$") {
        return "Only alphanumeric characters allowed"
      }

      else if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^[0-9a-zA-z]*$") {
        return "Only alphanumeric characters allowed"
      }

      else if (this[formName].controls[controlName].errors.pattern.requiredPattern == "^\d*[a-zA-Z][a-zA-Z0-9]*$") {

        this.showUserNameErrorOnPattern = true;

        return `Please give a valid username.`
      }

      this.showUserNameErrorOnPattern = false;
    }


  }

  submit() {

  

    if (this.updateUser.invalid) {
      this.updateUser.markAllAsTouched();
      this.notification.error("Please fill in all the required fields with valid data.");
      return;
    }
    if (this.updateUser.get('phoneNumber').value < 6000000000) {
      this.notification.error("Please provide a valid phone number.")
      return;
    }
    const payload = this.updateUser.value;
    payload.identifier = this.selectedUserDetail ?.identifier;


    this.identityService.updateUser(payload).subscribe(res => {
      this.dialogRef.close(true);
      this.notification.success(JsonData["label.success.UpdateUser"]);
    }, (error) => {

     
      const errors = this.errorService.ErrorHandling(error)
      this.notificationMessage.error(errors);
     
    })

  }

  get f() { return this.updateUser.controls; }




  checkUsername(str) {
    if (str.length < 3) {
      this.error = true;
      return ("Password should contain min 3 characters required");
    } else if (str.length > 50) {
      this.error = true;
      return ("Password should contain max 8 characters required");
    } else if (str.search(/[a-zA-Z]/) == -1) {
      this.error = true;
      return ("Password should contain atleast 1 alphabet");
    } else {
      this.error = false;
    }
  }


  createUserName(event) {
    // this.username = (this.updateUser.get('firstName').value ? this.updateUser.get('firstName').value : "")  + (this.updateUser.get('lastName').value ? this.updateUser.get('lastName').value[0] : "")
  }
}
