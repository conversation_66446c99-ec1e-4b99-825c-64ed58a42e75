<ng-container *ngIf="!themeService.useNewTheme;else newUIHtml">
  <mat-dialog-content>

    <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
      <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%" >
       
        <h2>{{"label.header.updateUser" | literal}}</h2>
      </div>
      <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml-50">
        <button mat-button (click)="closeDialog()">
        <mat-icon aria-label="close-dialog-box-icon" class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>
  
    <div class="user-fields">
      <form fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" [formGroup]="updateUser" id="createInvestment" autocomplete="off">
        <div fxLayout="row wrap" fxLayoutGap="4px" >
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
  
  
            <div fxLayout="row wrap" fxLayoutGap="4px" >
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Firstname" | literal}}</mat-label>
                  <input aria-label="first-name-input-field" matInput (input)="createUserName($event.target.value)" [(ngModel)]="selectedUserDetail.firstName"
                    formControlName="firstName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'firstName' , 'First name')}}
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.minlength">
                    {{"label.error.charLong" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
  
  
              </div>
  
  
  
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Lastname" | literal}}</mat-label>
                  <input aria-label="last-name-input-field" matInput (input)="createUserName($event.target.value)" [(ngModel)]="selectedUserDetail.lastName"
                    formControlName="lastName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'lastName' , 'Last name')}}
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.minlength">
                   {{"label.error.charLong"| literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
            </div>
  
            <div fxLayout="row wrap" fxLayoutGap="4px" >
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Mailid" | literal}}</mat-label>
                  <input aria-label="mail-id-input-field" matInput formControlName="mailId" [(ngModel)]="selectedUserDetail.mailId">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'mailId' , 'Mail id')}}
                  </mat-error>
                  <mat-error *ngIf="
                      updateUser.get('mailId').hasError('email')
                      &&!updateUser.get('mailId').hasError('required')">
                    {{"label.error.validEmail" | literal}}
                  </mat-error>
                  <mat-error *ngIf="f.mailId.touched && f.mailId.errors?.required">
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
  
  
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Roles" | literal}}<span class="requiredField">&nbsp;*</span></mat-label>
                  <mat-select class="selectedRole"  [(ngModel)]="selectedUserDetail.role" disabled
                    formControlName="role">
                    <mat-option *ngFor="let roleType of roles" [value]="roleType.value">
                      {{roleType.value}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="f.role.touched && f.role.errors?.required">
                  </mat-error>
                  <mat-hint align="end"></mat-hint>
                </mat-form-field>
  
  
              </div>
  
  
  
            </div>
  
  
  
            <div fxLayout="row wrap" fxLayoutGap="4px" >
  
              <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" >
  
  
                <mat-form-field floatLabel="never" class="full-width">
                  <mat-select class="" disabled formControlName="countryCode" [ngModel]="selectedCountryCode">
                    <mat-option *ngFor="let code of countryCodes" [value]="code.value">
                      {{code.value}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
  
  
              </div>
              <div class="width78">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Phonenumber" | literal}}</mat-label>
                  <input aria-label="phone-number-input-field" matInput formControlName="phoneNumber" maxlength="10"
                    [(ngModel)]="selectedUserDetail.phoneNumber">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'phoneNumber' , 'Phone number')}}
                  </mat-error>
                  <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.minlength">
                    {{"label.error.validPhNum" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
  
  
  
            </div>
  
  
          </div>
        </div>
      </form>
    </div>
  
  </mat-dialog-content>
  
  
  <mat-card-actions align="center" class="userActionButtons">
    <button  aria-label="update-user-btn" mat-raised-button (click)="submit()" class="green ml-43">{{"label.button.update" | literal}}</button>
  </mat-card-actions>
</ng-container>

<ng-template #newUIHtml>
  <mat-dialog-content>

    <div fxLayout="row" fxLayoutAlign="space-between center" class="p-percent-2">
      <h2 class="no-m">{{"label.header.updateUser" | literal}}</h2>
   
      <button mat-icon-button (click)="closeDialog()">
        <mat-icon aria-label="close-dialog-box-icon">close</mat-icon>
      </button>
    </div>
  
      <form [formGroup]="updateUser" autocomplete="off">

  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Firstname" | literal}}</mat-label>
                  <input aria-label="first-name-input-field" matInput (input)="createUserName($event.target.value)" [(ngModel)]="selectedUserDetail.firstName"
                    formControlName="firstName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'firstName' , 'First name')}}
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.minlength">
                    {{"label.error.charLong" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Lastname" | literal}}</mat-label>
                  <input aria-label="last-name-input-field" matInput (input)="createUserName($event.target.value)" [(ngModel)]="selectedUserDetail.lastName"
                    formControlName="lastName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'lastName' , 'Last name')}}
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.minlength">
                   {{"label.error.charLong"| literal}}
                  </mat-error>
  
                </mat-form-field>
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Mailid" | literal}}</mat-label>
                  <input aria-label="mail-id-input-field" matInput formControlName="mailId" [(ngModel)]="selectedUserDetail.mailId">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('updateUser', 'mailId' , 'Mail id')}}
                  </mat-error>
                  <mat-error *ngIf="
                      updateUser.get('mailId').hasError('email')
                      &&!updateUser.get('mailId').hasError('required')">
                    {{"label.error.validEmail" | literal}}
                  </mat-error>
                  <mat-error *ngIf="f.mailId.touched && f.mailId.errors?.required">
                  </mat-error>
  
                </mat-form-field>
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Roles" | literal}}<span class="requiredField">&nbsp;*</span></mat-label>
                  <mat-select class="selectedRole"  [(ngModel)]="selectedUserDetail.role" disabled
                    formControlName="role">
                    <mat-option *ngFor="let roleType of roles" [value]="roleType.value">
                      {{roleType.value}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="f.role.touched && f.role.errors?.required">
                  </mat-error>
                  <mat-hint align="end"></mat-hint>
                </mat-form-field>
  
  
                <div fxLayout="row" fxLayoutGap="4px" >
                  <div fxFlex="20">
                    <mat-form-field floatLabel="never" class="full-width dense">
                      <mat-select class="" disabled formControlName="countryCode" [ngModel]="selectedCountryCode">
                        <mat-option *ngFor="let code of countryCodes" [value]="code.value">
                          {{code.value}}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                  <div fxFlex>
                    <mat-form-field class="full-width">
                      <mat-label>{{"label.field.Phonenumber" | literal}}</mat-label>
                      <input aria-label="phone-number-input-field" matInput formControlName="phoneNumber"  maxlength="10">
                      <mat-hint></mat-hint>
                      <mat-error>
                        {{getErrorMessage('updateUser', 'phoneNumber' , 'Phone number')}}
                      </mat-error>
                      <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.required">
                      </mat-error>
                      <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.minlength">
                        {{"label.error.validPhNum" | literal}}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
  
      </form>
  
  </mat-dialog-content>
  
  
  <mat-card-actions class="dialog-button">
    <button aria-label="update-user-btn" mat-raised-button (click)="submit()" color="primary">
      {{"label.button.update" | literal}}
    </button>
  </mat-card-actions>
</ng-template>