<ng-container *ngIf="!themeService.useNewTheme;else newUIHtml">
  <mat-dialog-content>

    <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
      <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%" >
        <h2>{{"label.header.addUser" | literal}}</h2>
      </div>
     
        <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml-50">
          <button mat-button (click)="closeDialog()">
          <mat-icon aria-label="close-dialog-box-icon" class="close-icon">close</mat-icon>
        </button>
        </div>
    </div>
  
    <div class="user-fields">
      <form fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" [formGroup]="addUser" id="createInvestment" autocomplete="off">
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
  
  
            <div fxLayout="row wrap" fxLayoutGap="4px" >
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Firstname" | literal}}</mat-label>
                  <input aria-label="first-name-input-field" matInput (input)="createUserName($event.target.value)" formControlName="firstName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('addUser', 'firstName' , 'First name')}}
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.minlength">
                    {{"label.error.charLong" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
  
  
              </div>
  
  
  
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Lastname" | literal}}</mat-label>
                  <input aria-label="last-name-input-field" matInput (input)="createUserName($event.target.value)" formControlName="lastName">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('addUser', 'lastName' , 'Last name')}}
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.minlength">
                    {{"label.error.charLong" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
            </div>
  
            <div fxLayout="row wrap" fxLayoutGap="4px">
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Mailid" | literal}}</mat-label>
                  <input aria-label="mail-id-input-field" matInput formControlName="mailId">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('addUser', 'mailId' , 'Mail id')}}
                  </mat-error>
                  <mat-error *ngIf="
                      addUser.get('mailId').hasError('email')
                      &&!addUser.get('mailId').hasError('required')">
                    {{"label.error.validEmail" | literal}}
                  </mat-error>
                  <mat-error *ngIf="f.mailId.touched && f.mailId.errors?.required">
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
  
  
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Roles" | literal}}</mat-label>
                  <mat-select class="selectedRole"  disabled formControlName="role"
                    [ngModel]="selectedRole">
                    <mat-option *ngFor="let roleType of roles" [value]="roleType.value">
                      {{roleType.value}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="f.role.touched && f.role.errors?.required">
                  </mat-error>
                  <mat-hint align="end"></mat-hint>
                </mat-form-field>
  
  
              </div>
  
  
  
            </div>
  
  
  
            <div fxLayout="row wrap" fxLayoutGap="4px" >
  
              <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" >
  
  
                <mat-form-field floatLabel="never" class="full-width">
                  <mat-select class="" disabled formControlName="countryCode" [ngModel]="selectedCountryCode">
                    <mat-option *ngFor="let code of countryCodes" [value]="code.value">
                      {{code.value}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
  
  
              </div>
              <div class="width78">
  
  
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.Phonenumber" | literal}}</mat-label>
                  <input aria-label="phone-number-input-field" matInput formControlName="phoneNumber"  maxlength="10">
                  <mat-hint></mat-hint>
                  <mat-error>
                    {{getErrorMessage('addUser', 'phoneNumber' , 'Phone number')}}
                  </mat-error>
                  <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.required">
                  </mat-error>
                  <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.minlength">
                    {{"label.error.validPhNum" | literal}}
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
  
  
  
  
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
  
                <mat-form-field floatLabel="never" class="full-width">
                  <mat-label>{{"label.field.Username" | literal}}</mat-label>
                  <mat-icon  class="pointer" color="primary" matSuffix
                    [matTooltip]="tooltipForUsername" [matTooltipClass]="'info-tooltip'">info</mat-icon>
                  <input matInput formControlName="identifier" [(ngModel)]="username">
                  <mat-hint align="end" class="noteColor">{{"label.note" | literal}}: {{patternInfo}}</mat-hint>
                  <mat-error>
                    {{getErrorMessage('addUser', 'identifier' , 'Username')}}
  
  
                  </mat-error>
                  <mat-error *ngIf="f.identifier.touched && f.identifier.errors?.required">
                  </mat-error>
  
                </mat-form-field>
  
  
              </div>
            </div>
  
            <div fxLayout="row wrap" fxLayoutGap="4px" class="createEntity" >
              <div fxFlex="25%" fxFlex.md="25%" fxFlex.xs="25%" fxFlex.sm="25%" class="padding-2" >
              <mat-checkbox color="primary"  (change)="isCheckedOrNot($event)"> Create Entity</mat-checkbox>
              </div>
              
            </div>
            <div fxLayout="row wrap" fxLayoutGap="4px" >
              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="padding-1">
              <mat-form-field class="full-width" *ngIf="isChecked">
                  
                <mat-label>{{"label.field.entityTypes" | literal}}<span>&nbsp;*</span></mat-label>
                <mat-select  formControlName="entityId" [ngModel]="selectedEntity">
                  <mat-option> <ngx-mat-select-search  placeholderLabel="Search List" 
                    noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterData($event)" [ngModelOptions]="{standalone: true}"
                    ></ngx-mat-select-search>
                  </mat-option>
                  <ng-container *ngFor="let entity of getPicklist(personEntityList)">
                  <mat-option  [value]="entity">
                    {{ entity.entityName }}
                  </mat-option>
                </ng-container>
                
              </mat-select>
              </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  
  </mat-dialog-content>
  
  
  <mat-card-actions align="center" class="userActionButtons">
    
    <button  aria-label="add-user-btn" mat-raised-button (click)="submit()" class="green ml-43">{{"label.button.create" | literal}}</button>
  
  </mat-card-actions>
</ng-container>

<ng-template #newUIHtml>
  <mat-dialog-content>

    <div fxLayout="row" fxLayoutAlign="space-between center" class="p-percent-2">
        <h2 class="no-m">{{"label.header.addUser" | literal}}</h2>
     
        <button mat-icon-button (click)="closeDialog()">
          <mat-icon aria-label="close-dialog-box-icon">close</mat-icon>
        </button>
    </div>
  
      <form [formGroup]="addUser" id="createInvestment" autocomplete="off">
  
          <mat-form-field class="full-width">
            <mat-label>{{"label.field.Firstname" | literal}}</mat-label>
            <input aria-label="first-name-input-field" matInput (input)="createUserName($event.target.value)" formControlName="firstName">
            <mat-hint></mat-hint>
            <mat-error>
              {{getErrorMessage('addUser', 'firstName' , 'First name')}}
            </mat-error>
            <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.required">
            </mat-error>
            <mat-error *ngIf="f.firstName.touched && f.firstName.errors?.minlength">
              {{"label.error.charLong" | literal}}
            </mat-error>

          </mat-form-field>
  
  
          <mat-form-field class="full-width">
            <mat-label>{{"label.field.Lastname" | literal}}</mat-label>
            <input aria-label="last-name-input-field" matInput (input)="createUserName($event.target.value)" formControlName="lastName">
            <mat-hint></mat-hint>
            <mat-error>
              {{getErrorMessage('addUser', 'lastName' , 'Last name')}}
            </mat-error>
            <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.required">
            </mat-error>
            <mat-error *ngIf="f.lastName.touched && f.lastName.errors?.minlength">
              {{"label.error.charLong" | literal}}
            </mat-error>

          </mat-form-field>

          <mat-form-field class="full-width">
            <mat-label>{{"label.field.Mailid" | literal}}</mat-label>
            <input aria-label="mail-id-input-field" matInput formControlName="mailId">
            <mat-hint></mat-hint>
            <mat-error>
              {{getErrorMessage('addUser', 'mailId' , 'Mail id')}}
            </mat-error>
            <mat-error *ngIf="
                addUser.get('mailId').hasError('email')
                &&!addUser.get('mailId').hasError('required')">
              {{"label.error.validEmail" | literal}}
            </mat-error>
            <mat-error *ngIf="f.mailId.touched && f.mailId.errors?.required">
            </mat-error>

          </mat-form-field>


          <mat-form-field class="full-width">
            <mat-label>{{"label.field.Roles" | literal}}</mat-label>
            <mat-select class="selectedRole"  disabled formControlName="role"
              [ngModel]="selectedRole">
              <mat-option *ngFor="let roleType of roles" [value]="roleType.value">
                {{roleType.value}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="f.role.touched && f.role.errors?.required">
            </mat-error>
          </mat-form-field>
  

          <div fxLayout="row" fxLayoutGap="4px" >
            <div fxFlex="20">
              <mat-form-field floatLabel="never" class="full-width dense">
                <mat-select class="" disabled formControlName="countryCode" [ngModel]="selectedCountryCode">
                  <mat-option *ngFor="let code of countryCodes" [value]="code.value">
                    {{code.value}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxFlex>
              <mat-form-field class="full-width">
                <mat-label>{{"label.field.Phonenumber" | literal}}</mat-label>
                <input aria-label="phone-number-input-field" matInput formControlName="phoneNumber"  maxlength="10">
                <mat-hint></mat-hint>
                <mat-error>
                  {{getErrorMessage('addUser', 'phoneNumber' , 'Phone number')}}
                </mat-error>
                <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.required">
                </mat-error>
                <mat-error *ngIf="f.phoneNumber.touched && f.phoneNumber.errors?.minlength">
                  {{"label.error.validPhNum" | literal}}
                </mat-error>
              </mat-form-field>
            </div>

          </div>
  
  
          <mat-form-field floatLabel="never" class="full-width">
            <mat-label>{{"label.field.Username" | literal}}</mat-label>
            <mat-icon  class="pointer" color="primary" matSuffix
              [matTooltip]="tooltipForUsername" [matTooltipClass]="'accent-tooltip'">info</mat-icon>
            <input matInput formControlName="identifier" [(ngModel)]="username">
            <mat-hint align="end" class="hint">{{"label.note" | literal}}: {{patternInfo}}</mat-hint>
            <mat-error>
              {{getErrorMessage('addUser', 'identifier' , 'Username')}}


            </mat-error>
            <mat-error *ngIf="f.identifier.touched && f.identifier.errors?.required">
            </mat-error>

          </mat-form-field>
  
  
        <div fxLayout="row" fxLayoutAlign="end center" class="p-percent-2" >
          <mat-checkbox color="accent"  (change)="isCheckedOrNot($event)"> Create Entity</mat-checkbox>
        </div>
        <mat-form-field class="full-width" *ngIf="isChecked">
          <mat-label>{{"label.field.entityTypes" | literal}}<span>&nbsp;*</span></mat-label>
          <mat-select  formControlName="entityId" [ngModel]="selectedEntity">
            <mat-option> <ngx-mat-select-search  placeholderLabel="Search List" 
              noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterData($event)" [ngModelOptions]="{standalone: true}"
              ></ngx-mat-select-search>
            </mat-option>
            <ng-container *ngFor="let entity of getPicklist(personEntityList)">
            <mat-option  [value]="entity">
              {{ entity.entityName }}
            </mat-option>
          </ng-container>
        </mat-select>
        </mat-form-field>
      </form>
  
  </mat-dialog-content>
  
  
  <mat-card-actions class="dialog-button">
    
    <button  aria-label="add-user-btn" mat-raised-button (click)="submit()" color="primary">
      {{"label.button.create" | literal}}
    </button>
  
  </mat-card-actions>
</ng-template>