import { Component, OnInit } from "@angular/core";
import { ToasterService } from "../../../../common/toaster.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import {
  UntypedFormGroup,
  UntypedFormControl,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MatDialog } from "@angular/material/dialog";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { EntityService } from "src/app/shared-service/entity.service";
import { DataApiService } from "src/app/shared-service/data.service";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-create-user",
  templateUrl: "./create-user.component.html",
  styleUrls: ["./create-user.component.scss"],
})
export class CreateUserComponent implements OnInit {
  JsonData: any;
  isExpanded_UserDetails: any;
  roles: { value: string }[];
  addUser: UntypedFormGroup;
  text: string;
  error: boolean;
  selectedRole: any = "administrator";
  tooltipForUsername = "Default password is same as username.";
  selectedCountryCode: any = "+91";
  countryCodes: any = [{ value: "+91" }];
  username: any;
  showUserNameErrorOnPattern: boolean = false;
  allEntities: any[] = [];
  selectedEntity: any = [];
  extentionList: any = [];
  personEntityList = [];
  isChecked: boolean = false;
  patternInfo = `Starts with alphabet, no special characters or space allowed.`;
  searchData: any;

  constructor(
    private errorService: ErrorService,
    public notificationMessage: ToasterService,
    public dialogRef: MatDialogRef<CreateUserComponent>,
    public identityService: IdentityService,
    private entityService: EntityService,
    private dataApiService: DataApiService,
    private notification: ToasterService,
    public matDialog: MatDialog,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.addUser = new UntypedFormGroup({
      firstName: new UntypedFormControl("", [
        Validators.required,
        Validators.pattern("^[a-zA-z]*$"),
        Validators.minLength(2),
      ]),
      lastName: new UntypedFormControl("", [
        Validators.required,
        Validators.pattern("^[a-zA-z]*$"),
        Validators.minLength(2),
      ]),
      mailId: new UntypedFormControl("", [
        Validators.required,
        Validators.email,
      ]),
      role: new UntypedFormControl({ value: "", disabled: true }),
      phoneNumber: new UntypedFormControl("", [
        Validators.required,
        Validators.pattern("[0-9]*"),
        Validators.minLength(10),
      ]),
      identifier: new UntypedFormControl("", [
        Validators.required,
        Validators.pattern("^d*[a-zA-Z][a-zA-Z0-9]*$"),
      ]),
      countryCode: new UntypedFormControl(),
      entityId: new UntypedFormControl(),
    });
    this.roles = [
      { value: "Super user" },
      { value: "User" },
      { value: "Guest" },
      { value: "administrator" },
    ];
    this.isExpanded_UserDetails = true;
    this.text = "Please enter a value";
    this.getExtentions();
  }
  getExtentions() {
    this.entityService.getExtentionsList().subscribe((res) => {
      this.extentionList = res;
      this.allEntities = [...this.extentionList];
      this.allEntities.forEach((element) => {
        if (element.entityType == "Person") {
          this.personEntityList.push(element);
        }
      });
    });
  }
  isCheckedOrNot(event: any) {
    this.isChecked = event.checked;
    this.addUser.controls.entityId?.setValue([]);
  }

  getErrorMessage(formName, controlName, labelName, customValidation?: any) {
    this.showUserNameErrorOnPattern = false;
    if (
      this[formName].controls[controlName].errors &&
      this[formName].controls[controlName].errors.required
    ) {
      return `${labelName} is required.`;
    }

    if (
      this[formName].controls[controlName].errors &&
      this[formName].controls[controlName].errors.pattern
    ) {
      if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^[0-9]*$"
      ) {
        return "Only numbers allowed";
      } else if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^[a-zA-z ]*$"
      ) {
        return "Only alphabets allowed";
      } else if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^[a-zA-z]*$"
      ) {
        return "Only alphabets allowed";
      } else if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^[0-9a-zA-z]*$"
      ) {
        return "Only alphanumeric characters allowed";
      } else if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^[0-9a-zA-z]*$"
      ) {
        return "Only alphanumeric characters allowed";
      } else if (
        this[formName].controls[controlName].errors.pattern.requiredPattern ==
        "^d*[a-zA-Z][a-zA-Z0-9]*$"
      ) {
        this.showUserNameErrorOnPattern = true;

        return `Please give a valid username.`;
      }

      this.showUserNameErrorOnPattern = false;
    }
  }
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }
  submit() {
    if (this.addUser.invalid) {
      this.addUser.markAllAsTouched();
      this.notification.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.addUser.get("phoneNumber").value < 6000000000) {
      this.notification.error("Please provide a valid phone number.");
      return;
    }
    if (this.isChecked && this.addUser.controls.entityId.value?.length == 0) {
      this.notification.error("Please select a entity type.");
      return;
    }

    let payload = this.addUser.value;
    payload.password = window.btoa(this.addUser.value.identifier);
    payload = Object.keys(payload)
      .filter((key) => key != "entityId")
      .reduce((acc, key) => {
        acc[key] = payload[key];
        return acc;
      }, {});

    let entitypayload = this.addUser.value;
    entitypayload.entityId = this.addUser.controls.entityId.value?.id;

    if (this.isChecked) {
      this.dataApiService.addEntityUser(entitypayload).subscribe((res) => {
        this.dialogRef.close(true);
        this.notificationMessage.success(JsonData["label.success.AddUser"]);
      });
    } else {
      this.identityService.addUser(payload).subscribe(
        (res) => {
          this.dialogRef.close(true);
          this.notification.success(JsonData["label.success.AddUser"]);
        },
        (error) => {
          let errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
        }
      );
    }
  }

  get f() {
    return this.addUser.controls;
  }

  checkUsername(str) {
    if (str.length < 3) {
      this.error = true;
      return "Password should contain min 3 characters required";
    } else if (str.length > 50) {
      this.error = true;
      return "Password should contain max 8 characters required";
    } else if (str.search(/[a-zA-Z]/) == -1) {
      this.error = true;
      return "Password should contain atleast 1 alphabet";
    } else {
      this.error = false;
    }
  }

  createUserName(event) {
    this.username =
      (this.addUser.get("firstName").value
        ? this.addUser.get("firstName").value
        : "") +
      (this.addUser.get("lastName").value
        ? this.addUser.get("lastName").value[0]
        : "");
  }
  closeDialog() {
    this.dialogRef.close();
  }

  filterData($event) {
    this.searchData = $event;
  }

  getPicklist(arr) {
    if (this.searchData) {
      return arr.filter((a) =>
        a.entityName.toLowerCase().includes(this.searchData.toLowerCase())
      );
    } else {
      return arr.sort((a, b) => {
        return a.entityName.toLowerCase() < b.entityName.toLowerCase()
          ? -1
          : a.entityName.toLowerCase() > b.entityName.toLowerCase()
          ? 1
          : 0;
      });
    }
  }
}
