import { Injectable } from "@angular/core";
import {
  chartTypes,
  dataTypes,
} from "./dashboard-config/classes-and-interfaces/dashboard-interfaces";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { DatePipe } from "@angular/common";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";

@Injectable({
  providedIn: "root",
})
export class DashboardUtilsService {
  constructor(
    private readonly datepipe: DatePipe,
    private readonly dateTimepipe: ZcpDateTimePipe
  ) {}

  isNumber(inputType: string) {
    return [
      dataTypes.Number,
      dataTypes.Perentage,
      dataTypes.Currency,
      dataTypes.Number_with_decimal,
    ].includes(<dataTypes>inputType);
  }

  generateDataForGraph(searchRespData, dashboard, chartType?) {
    let graphData = [];
    const accessNode =
      dashboard.queryType == "Entity" ? "entityDetail" : "dealAsset";
    const dateFormat =
      chartType === chartTypes.TimeLine_Bar ||
      chartType === chartTypes.TimeLine_Line
        ? "yyyy-MM-dd"
        : "";

    searchRespData.forEach((ele) => {
      const assetObj = ele[accessNode] ? ele[accessNode] : {};
      for (const key in assetObj) {
        const objValue = dashboard.fields.find((item) => item.name == key);
        if (objValue) {
          const assetKey = objValue.displayProperty?.displayName;
          const inputType = objValue.inputType;
          assetObj[assetKey] = assetObj[key];
          delete assetObj[key];
          if (assetObj.hasOwnProperty(assetKey)) {
            const value = assetObj[assetKey];
            if (value && Array.isArray(value)) {
              let valueArray = [];
              if (inputType === ZcpDataTypes.MULTIPLE_PICKLIST) {
                value?.forEach((ele) => valueArray.push(ele.name));
              } else {
                valueArray = [...value];
              }
              assetObj[assetKey] = valueArray.toString();
            } else if (typeof value === "object" && value !== null) {
              const newvalue = assetObj[assetKey].name;
              assetObj[assetKey] = newvalue;
            }

            if (inputType === ZcpDataTypes.DATE && value != "null") {
              assetObj[assetKey] = this.datepipe.transform(value, dateFormat);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              assetObj[assetKey] = this.dateTimepipe.transform(value + "Z");
            }
            if (this.isNumber(inputType) && value === "null") {
              assetObj[assetKey] = 0;
            }
          }
        }
      }
      assetObj["id"] = ele.id;
      assetObj["Description"] = ele.dealIdentifier;
      assetObj["Business Process"] = ele.businessProcessName;
      assetObj["Stage"] = ele.currentStageName;
      assetObj["Created Date"] = this.datepipe.transform(
        ele.createdDate,
        dateFormat
      );
      assetObj["Current Status"] = ele.currentStatus;
      assetObj["Name"] = ele?.name;
      assetObj["customerId"] = ele?.customerId;

      const entityObj = ele.dealEntity ? ele.dealEntity : {};

      for (const key in entityObj) {
        if (entityObj?.[key]) {
          const newEntityKey = "entity" + key;
          entityObj[newEntityKey] = entityObj[key];
          delete entityObj[key];
        }
        if (entityObj.hasOwnProperty(key)) {
          const value = entityObj[key];
          if (value && Array.isArray(value)) {
            const valueArray = [];
            value?.forEach((ele) => valueArray.push(ele.name));
            entityObj[key] = valueArray;
          } else if (typeof value === "object" && value !== null) {
            const newvalue = entityObj[key].name;
            entityObj[key] = newvalue;
          }
        }
      }
      const obj = { ...assetObj, ...entityObj };
      graphData.push(obj);
    });

    return graphData;
  }
}
