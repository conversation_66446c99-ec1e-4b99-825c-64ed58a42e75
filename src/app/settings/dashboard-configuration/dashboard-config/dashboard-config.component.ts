import { Component } from "@angular/core";
import { Activated<PERSON>oute, Router } from "@angular/router";
import {
  DashboardPayload,
  Field,
  chartProperties,
  dashboardData,
} from "./classes-and-interfaces/dashboard-interfaces";
import { Chart } from "./chart-config-form/chart-config-form.component";
import { QueryGenerator } from "./query-builder/query-builder.component";
import { ErrorService } from "src/app/shared-service/error.service";
import { ToasterService } from "src/app/common/toaster.service";
import JsonData from "src/assets/data.json";
import { DasboardConfigService } from "../dasboard-config.service";
import { MatDialog } from "@angular/material/dialog";
import { EditDashboardConfigComponent } from "./edit-dashboard-config/edit-dashboard-config.component";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { IdentityService } from "src/app/shared-service/identity.service";
import { MatSelectionListChange } from "@angular/material/list";
import { ThemeService } from "src/app/theme.service";
import { EntityService } from "src/app/shared-service/entity.service";

@Component({
  selector: "app-dashboard-config",
  templateUrl: "./dashboard-config.component.html",
  styleUrls: ["./dashboard-config.component.scss"],
})
export class DashboardConfigComponent {
  dashboardId: string;
  dataFromParent: any;
  dashboard: Dashboard | EntityDashboard;
  allUserRoles: string[] = [];
  filteredUserRoles: string[] = [];
  static DEFAULT_ALLOWED_ROLE = "super_admin";

  get DASHBOARD_RESOURCE() {
    return ConfigurationResources.Dashboard_Def;
  }

  constructor(
    private activeRoute: ActivatedRoute,
    private route: Router,
    private dashboardService: DasboardConfigService,
    private errorService: ErrorService,
    private notificationMessage: ToasterService,
    private dialog: MatDialog,
    private identityService: IdentityService,
    public themeService: ThemeService,
    public entityService: EntityService
  ) {
    this.getAllUserRoles();
    this.activeRoute.paramMap.subscribe((param) => {
      this.dashboardId = param.get("id") ? atob(param.get("id")) : "";
    });

    this.dataFromParent = this.route.getCurrentNavigation()?.extras?.state;

    if (this.dataFromParent) {
      const DashboardInstance =
        this.dataFromParent.dashboardDetails.queryType == "Entity"
          ? EntityDashboard
          : Dashboard;
      this.dashboard = new DashboardInstance(
        this.dataFromParent,
        dashboardService,
        route,
        errorService,
        notificationMessage,
        entityService
      );
      this.dashboard.setDashboardData();
      this.dashboard.setLinkageData();
    } else if (!this.dataFromParent && this.dashboardId) {
      this.getDashBoardById(this.dashboardId);
    } else {
      this.route.navigate(["dashboard-configuration"]);
    }
  }

  getAllUserRoles() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        this.allUserRoles = roles.map((role) => role.identifier);
        this.filteredUserRoles = this.allUserRoles;
      });
  }
  filterUserRoles = (searchKey: string) => {
    this.filteredUserRoles = this.allUserRoles.filter((role: string) =>
      role.includes(searchKey)
    );
  };

  getDashBoardById(dashboardId) {
    this.dashboardService
      .getDashboardById(dashboardId)
      .subscribe((resp: any) => {
        this.dataFromParent = resp;
        const DashboardInstance =
          this.dataFromParent.dashboardDetails.queryType == "Entity"
            ? EntityDashboard
            : Dashboard;
        this.dashboard = new DashboardInstance(
          this.dataFromParent,
          this.dashboardService,
          this.route,
          this.errorService,
          this.notificationMessage,
          this.entityService
        );
        this.dashboard.setDashboardData();
        this.dashboard.setLinkageData();
      });
  }

  actionEdit() {
    const matDialogRef = this.dialog.open(EditDashboardConfigComponent, {
      autoFocus: false,
      width: "35%",
      disableClose: true,
      data: this.dashboard,
    });
    matDialogRef.afterClosed().subscribe((result: any) => {
      if (result && result.newDashboardName) {
        this.dashboard.dashboardName = result.newDashboardName;
      }
    });
  }
}

export class Dashboard {
  dashboardName: string;
  dashboardOrder: number;
  dashboardType: string;
  linkageDetails: any;
  dealEntityDetails: any;
  queryType: string;
  fields: any[] = [];
  activeChart: Chart;
  query: QueryGenerator;
  addedCharts: Chart[] = [];
  allowedRoles: string[] = [];
  DEFAULT_ALLOWED_ROLE = "super_admin";

  //flags
  dashboardPreview = false;
  editingChartIndex: number | undefined = undefined;
  loading = false;

  constructor(
    protected dashboardData: dashboardData,
    protected dashboardService: DasboardConfigService,
    private route: Router,
    private errorService: ErrorService,
    private notificationMessage: ToasterService,
    public entityService: EntityService
  ) {}

  get dashboardId() {
    return this.dashboardData.id;
  }

  setDashboardData() {
    this.dashboardName = this.dashboardData.dashboardName;
    this.allowedRoles = this.dashboardData?.roles
      ? this.dashboardData.roles
      : [];
    const currentUserRole = localStorage.getItem("userRole").split(",");

    if (
      this.ifHasDefaultPermission(currentUserRole) &&
      !this.allowedRoles.includes(this.DEFAULT_ALLOWED_ROLE)
    ) {
      this.allowedRoles.push(this.DEFAULT_ALLOWED_ROLE);
    }
    this.queryType = this.dashboardData.dashboardDetails.queryType;
    this.dashboardData.dashboardDetails.chartsDetail.forEach(
      (chartProps: chartProperties) => {
        this.addedCharts.push(new Chart(chartProps));
      }
    );
  }
  ifHasDefaultPermission = (userRole): boolean => {
    if (typeof userRole === "string")
      return userRole === this.DEFAULT_ALLOWED_ROLE;
    else if (Array.isArray(userRole)) {
      return userRole.includes(this.DEFAULT_ALLOWED_ROLE);
    }
  };

  setLinkageData() {
    //fields data
    this.loading = true;
    this.dashboardService
      .getBusinessProcessById(this.dashboardData.dashboardDetails.linkageId)
      .subscribe((resp) => {
        this.linkageDetails = resp;
        // businessProcessEntityDefinition is deprecated
        if (
          this.linkageDetails.businessProcessEntityDefinition ||
          this.linkageDetails.businessProcessEntityDefinitionList
        ) {
          this.setLinkedEntityDetails(); // only needed for queryType BP
        } else {
          this.mapFields([]); //no primary actor details available
        }
      });
  }

  setLinkedEntityDetails() {
    // this.dealEntityDetails = this.linkageDetails.businessProcessEntityDefinitionList;
    this.entityService
      .getPrimaryEntityDetailsUsingBPId(
        this.dashboardData.dashboardDetails.linkageId
      )
      .subscribe((resp: any) => {
        const d = resp?.flatMap((item) =>
          item.entityDefinition.entityDetail.entityDetail.map((detail) => ({
            ...detail,
            entityNameTitle: item.entityName,
          }))
        );

        this.mapFields(d); // only needed for queryType BP
      });
  }

  mapFields(entityDefDetails: any) {
    this.fields = this.linkageDetails.assetItems
      .map((item) => ({
        displayProperty: {
          displayName:
            item[Object.entries(item)[0][0]].displayProperty.displayName,
        },
        inputType: item[Object.entries(item)[0][0]].inputType,
        type: "assetField",
        name: Object.entries(item)[0][0],
      }))
      .concat(
        entityDefDetails.map((item) => ({
          displayProperty: {
            displayName:
              item[Object.entries(item)[0][0]].displayProperty.displayName,
          },
          inputType: item[Object.entries(item)[0][0]].inputType,
          type: "entityField",
          name: Object.entries(item)[0][0],
          entityNameTitle: item.entityNameTitle,
        }))
      );

    //pushing harcoded columns here

    const createdDate: Field = {
      displayProperty: {
        displayName: "Created Date",
      },
      inputType: "Date",
      type: "createdDate",
      name: "createdDate",
      isMetaData: true,
    };

    const stages: Field = {
      displayProperty: {
        displayName: "Stage",
      },
      inputType: "stage",
      type: "stage",
      name: "currentStageName",
      isMetaData: true,
    };

    const description: Field = {
      displayProperty: {
        displayName: "Description",
      },
      inputType: "Text",
      type: "dealIdentifier",
      name: "dealIdentifier",
      isMetaData: true,
    };

    const currentStatus: Field = {
      displayProperty: {
        displayName: "Current Status",
      },
      inputType: "Text",
      type: "currentStatus",
      name: "currentStatus",
      isMetaData: true,
    };
    this.fields.push(description, createdDate, stages, currentStatus);
    this.loading = false;
  }

  addNewChartToDashboard(chart: Chart) {
    const chartCopy = Object.assign({}, chart);
    this.addedCharts.push(chartCopy);
  }

  updateExistingChart(chart: Chart, index: number) {
    this.addedCharts[index] = Object.assign({}, chart);
    this.editingChartIndex = undefined;
  }

  getStageList() {
    return this.linkageDetails.businessProcessStageList.map(
      (stage) => stage.name
    );
  }

  removeChartFromDashboard(index: number) {
    this.addedCharts.splice(index, 1);
  }

  updateAllowedRoles(selectionChange: MatSelectionListChange) {
    if (selectionChange.options[0].selected) {
      this.allowedRoles.push(selectionChange.options[0].value);
    } else {
      const changedOptionIndex = this.allowedRoles.indexOf(
        selectionChange.options[0].value
      );
      this.allowedRoles.splice(changedOptionIndex, 1);
    }
  }

  saveDashboard() {
    if (this.addedCharts.length === 0) {
      this.notificationMessage.error(JsonData["label.error.dashboardSave"]);
      return;
    }
    const dashboardPayload: DashboardPayload = {
      id: this.dashboardData?.id,
      dashboardName: this.dashboardName,
      dashboardOrder: 0,
      dashboardDetails: {
        linkageId: this.linkageDetails.id,
        chartsDetail: this.addedCharts.map((e) => e.chartProperties),
        dashboardType: this.dashboardType,
        queryType: this.queryType,
      },
      queryString: this.addedCharts[0]?.chartProperties.query,
      roles: this.allowedRoles,
    };
    const apiCall = this.dashboardData?.id
      ? "updateDashboard"
      : "saveDashboard";
    this.dashboardService[apiCall](
      dashboardPayload,
      this.dashboardData?.id
    ).subscribe(
      (res) => {
        this.notificationMessage.success(JsonData["label.success.Dashboard"]);

        if (!this.dashboardData?.id)
          this.route.navigate(["dashboard-configuration"]);
      },
      (err) => {
        const errors = this.errorService.ErrorHandling(err);
        this.notificationMessage.error(errors);
        this.loading = false;
      }
    );
  }
}

export class EntityDashboard extends Dashboard {
  setLinkageData(): void {
    this.dashboardService
      .getEntityDefinitionById(this.dashboardData.dashboardDetails.linkageId)
      .subscribe((resp) => {
        this.linkageDetails = resp;
        this.mapFields(this.linkageDetails.entityDetail.entityDetail);
      });
  }

  mapFields(entityDefDetails: any) {
    this.fields = entityDefDetails.map((item) => ({
      displayProperty: {
        displayName:
          item[Object.entries(item)[0][0]].displayProperty.displayName,
      },
      inputType: item[Object.entries(item)[0][0]].inputType,
      type: "entityField",
      name: Object.entries(item)[0][0],
    }));

    //pushing harcoded columns here

    const createdDate: Field = {
      displayProperty: {
        displayName: "Created Date",
      },
      inputType: "Date",
      type: "Date",
      name: "createdDate",
      isMetaData: true,
    };

    const name: Field = {
      displayProperty: {
        displayName: "Name",
      },
      inputType: "Text",
      type: "Text",
      name: "entityName",
      isMetaData: true,
    };
    this.fields.push(name, createdDate);
    this.linkageDetails.assetItems =
      this.linkageDetails.entityDetail.entityDetail;
  }
}
