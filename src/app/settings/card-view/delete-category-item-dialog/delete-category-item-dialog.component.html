<mat-dialog-content>
  <div fxLayout="column" class="action-confirmation-container">
    <div class="close-btn">
      <button mat-icon-button class="buttonPosition" (click)="close()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div fxLayoutAlign="center center" class="attention-img">
      <img width="200px" height="200px" src="../../../assets/imgs/attention.svg">
    </div>
    <div class="headline-4 m-b-10 warn-heading" fxLayoutAlign="center center">
      Delete Work Group?
    </div>

    <div class="subtitle-1 center m-b-20 subheading-container">
      <p>
        <strong>
          Please Note
        </strong>
        <br>
        Deleting a Work Group <strong>Will Not Delete Business Process(s) and/or
          Entity(s).</strong>They will be moved to the
        <strong> Uncategorized Section</strong>
      </p>
    </div>

    <div class="extended-content">
      <p>Are you Sure you <strong>Want to Proceed?</strong> This Action may impact existing User
        Access. It is Advisable to Verify the Role Assignment to a Business Process or Entity</p>
    </div>

    <div fxLayoutAlign="center center" fxLayoutGap="10" class="btn-container">
      <button aria-label="proceed-anyway" class="outlined-button" mat-raised-button
        (click)="deleteItem()">
        {{"label.button.proceed.anyway" | literal}}
      </button>
      <button aria-label="cancel" color="primary" mat-raised-button (click)="close()">
        {{"label.button.cancel" | literal}}
      </button>
    </div>
  </div>
</mat-dialog-content>
