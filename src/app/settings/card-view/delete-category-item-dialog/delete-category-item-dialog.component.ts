import { Component, Inject } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { CardViewApiService } from "../card-view.api.service";

@Component({
  selector: "app-delete-category-item-dialog",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./delete-category-item-dialog.component.html",
  styleUrl: "./delete-category-item-dialog.component.scss",
})
export class DeleteCategoryItemDialogComponent {
  constructor(
    private dialog: MatDialogRef<DeleteCategoryItemDialogComponent>,
    private cardViewApiService: CardViewApiService,
    @Inject(MAT_DIALOG_DATA) private data
  ) {}

  close() {
    this.dialog.close(false);
  }

  deleteItem() {
    this.cardViewApiService
      .deleteCardConfig(this.data.itemDetails.id)
      .pipe()
      .subscribe((res) => {
        if (res.status === 200) this.dialog.close(true);
      });
  }
}
