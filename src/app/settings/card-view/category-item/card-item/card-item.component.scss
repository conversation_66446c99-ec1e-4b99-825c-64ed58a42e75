.card-main-container {
  position: relative;
}

.workflow-cards {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.workflow-card {
  position: relative;
  width: 25rem;
  height: 5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  background-color: var(--container-color);
  border-radius: 4px;
}

.entity-indicator {
  position: absolute;
  background-color: var(--container-color);
  border-radius: 50%;
  padding: 1rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  top: -1rem;
  left: -1rem;
  border: solid 1px var(--input-border-color);
  z-index: 9;
}

.card-content {
  width: 100%;
  display: flex;
  align-items: center;
}

.add-icon-container {
  border-radius: 50%;
  cursor: pointer;
}

.add-icon {
  font-size: 1.75rem;
  padding: 0.2rem;

  &:hover {
    font-weight: 700;
    color: var(--primary-color);
  }
}

.workflow-icon {
  width: 4rem;
  height: 4rem;
  color: #FFF;
  font-weight: 700;
  font-size: 1rem;
  border-radius: 0.25rem;
  margin-right: 2rem;
}

.dark-font {
  color: #000;
}

.initials-container {
  display: flex;
  flex-direction: column;
  padding: 0.25rem;
  height: 100%;
  justify-content: flex-end;
}

.workflow-title {
  font-weight: 600;
  width: 15rem;
  max-height: 2rem;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
