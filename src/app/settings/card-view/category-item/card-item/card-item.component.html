<div class="card-main-container">
  <div matRipple class="workflow-card pointer" (click)="navigateToItem()">
    <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
      <div class="workflow-icon" [ngStyle]="{ 'background-color': bgColor }"
        [ngClass]="{ 'dark-font': darkFont }">
        <div class="initials-container">
          @for(initial of getInitials(); track initial) {
          <div>{{initial}}</div>
          }
        </div>
      </div>
      <div class="workflow-title" [matTooltip]="itemName" matTooltipClass="accent-tooltip">
        {{itemName}}</div>
      @if(user === USER_TYPE.USERS) {
      <div class="add-icon-container" matTooltip="Create" matTooltipClass="accent-tooltip"
        (click)="$event.stopPropagation(); $event.preventDefault(); onAddClick()">
        <mat-icon class="add-icon">add</mat-icon>
      </div>
      }
    </div>
  </div>

  @if(itemType===WORKSPACE_ITEM_TYPE.ENTITY && user===USER_TYPE.ADMIN) {
  <div class="entity-indicator">
    <span class="material-symbols-outlined">people</span>
  </div>
  }
</div>
