import { Component, Input } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import {
  DEFAULT_ICON_COLOR,
  EntityCategoryItem,
  ICON_COLOR_MAP,
  USER_TYPE,
  WORKSPACE_ITEM_TYPE,
} from "../../card-view.model";
import { Router } from "@angular/router";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  EntityDefinitionByNameResponse,
  EntityType,
} from "src/app/common/models/entity.model";
import { finalize } from "rxjs";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { LoaderService } from "src/app/shared-service/loader.service";
import { WorkspaceService } from "src/app/workspace/workspace.service";
import { WorkspaceRecentItem } from "src/app/workspace/workspace.model";
import { CreateCompanyComponent } from "src/app/entity/company-details/create-company/create-company.component";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";

@Component({
  selector: "app-card-item",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./card-item.component.html",
  styleUrl: "./card-item.component.scss",
})
export class CardItemComponent {
  @Input({ required: true }) item: string | EntityCategoryItem;
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;
  @Input({ required: true }) user: USER_TYPE;

  itemName = "";
  bgColor = DEFAULT_ICON_COLOR.bgColor;
  darkFont = DEFAULT_ICON_COLOR.darkFont;

  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;
  readonly USER_TYPE = USER_TYPE;

  // NOTE: workflow is an alias for business process

  constructor(
    private readonly router: Router,
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService,
    private readonly dialog: MatDialog,
    private readonly loader: LoaderService,
    private readonly workspaceService: WorkspaceService
  ) {}

  ngOnInit() {
    this.itemName = this.getItemName();
  }

  getItemName(): string {
    return this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW
      ? (this.item as string)
      : ((this.item as EntityCategoryItem).name as string);
  }

  getInitials(): string[] {
    const name = structuredClone(this.itemName);
    if (!name?.length) return ["#"];
    const words = name.trim().split(" ");
    const initials = words.map((word) => word[0]?.toUpperCase()).slice(0, 3);
    this.getColorByInitial(initials[0]);
    return initials;
  }

  getColorByInitial(initial: string) {
    if (!initial) return DEFAULT_ICON_COLOR;

    this.bgColor =
      ICON_COLOR_MAP[initial]?.bgColor ?? DEFAULT_ICON_COLOR.bgColor;
    this.darkFont =
      ICON_COLOR_MAP[initial]?.darkFont ?? DEFAULT_ICON_COLOR.darkFont;
  }

  onAddClick() {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.addEntity();
    } else this.addWorkflow();
  }

  addEntity() {
    this.loader.show();
    const item = this.item as EntityCategoryItem;

    if (item.type === EntityType.COMPANY) {
      this.addEntityCompany(item);
    } else if (item.type === EntityType.PERSON) {
      this.addEntityPerson(item);
    }
  }

  addEntityCompany(item: EntityCategoryItem) {
    this.entityService
      .getEntityDefinitionByName(item.name, item.type, item.subType)
      .pipe(
        finalize(() => {
          this.loader.hide();
        })
      )
      .subscribe((res: EntityDefinitionByNameResponse) => {
        const entityId = res.id;
        const matDialogRef = this.dialog.open(CreateCompanyComponent, {
          width: "45%",
          disableClose: true,
          data: {
            selectedCompanyExtensionId: entityId,
            isFromCard: true,
          },
        });

        matDialogRef.afterClosed().subscribe((res) => {
          if (res) {
            this.router.navigate([
              `/entity/viewcompany/detail/${btoa(
                this.workspaceService.createdEntityId
              )}`,
            ]);
            this.workspaceService.createdEntityId = undefined;
          }
        });
      });
  }

  addEntityPerson(item: EntityCategoryItem) {
    this.loader.hide();
    const matDialogRef = this.dialog.open(CreatePersonComponent, {
      width: "45%",
      disableClose: true,
      data: {
        selectedPersonExtensionName: item.name,
        isFromQDE: true,
      },
    });

    matDialogRef.afterClosed().subscribe((res) => {
      this.router.navigate([
        `/entity/viewperson/detail/${btoa(res.customerId)}`,
      ]);
    });
  }

  addWorkflow() {
    this.loader.show();
    let matDialogRef: MatDialogRef<NewCustomerComponent>;

    // NOTE: refactor existing new customer dialog to not rely on this fetched data
    this.businessProcessService
      .getAllBusinessProcessList()
      .pipe(
        finalize(() => {
          this.loader.hide();
        })
      )
      .subscribe((response) => {
        this.businessProcessService.businessProcessList = response;
        matDialogRef = this.dialog.open(NewCustomerComponent, {
          autoFocus: false,
          width: "45%",
          disableClose: true,
          data: {
            selectedBusinessProcess: this.item,
          },
        });
      });
  }

  navigateToItem() {
    this.updateRecentlyVisitedItems();

    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.navigateToEntity();
    } else this.navigateToWorkflow();
  }

  navigateToEntity() {
    this.loader.show();
    const item = this.item as EntityCategoryItem;

    if (this.user === USER_TYPE.ADMIN) {
      this.entityService
        .getEntityDefinitionByName(item.name, item.type, item.subType)
        .pipe(
          finalize(() => {
            this.loader.hide();
          })
        )
        .subscribe((res: EntityDefinitionByNameResponse) => {
          const entityId = res.id;
          this.router.navigate([`entity-details/${btoa(entityId)}`]);
        });
    } else if (this.user === USER_TYPE.USERS) {
      this.workspaceService.clickedItem = this.item;
      if (item.type === EntityType.COMPANY) {
        this.router.navigate([`entity/companies`]);
      } else if (item.type === EntityType.PERSON) {
        this.router.navigate([`entity/persons`]);
      }
      this.loader.hide();
    }
  }

  navigateToWorkflow() {
    this.loader.show();

    if (this.user === USER_TYPE.ADMIN) {
      this.businessProcessService
        .getBusinessProcessByName(this.item as string)
        .pipe(
          finalize(() => {
            this.loader.hide();
          })
        )
        .subscribe((res) => {
          const workflowId = res.id;
          this.router.navigate([`stage/${btoa(workflowId)}`]);
        });
    } else if (this.user === USER_TYPE.USERS) {
      this.workspaceService.clickedItem = this.item;
      this.loader.hide();
      this.router.navigate([`application`]);
    }
  }

  updateRecentlyVisitedItems() {
    if (this.user === USER_TYPE.USERS) {
      let recentItems =
        JSON.parse(localStorage.getItem("workspaceRecentItems")) ?? [];

      recentItems = recentItems.filter(
        (item) =>
          item.type !== this.itemType ||
          (item.type === this.itemType &&
            JSON.stringify(item.details) !== JSON.stringify(this.item))
      );

      const newItem: WorkspaceRecentItem = {
        type: this.itemType,
        details:
          this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW
            ? (this.item as string)
            : (this.item as EntityCategoryItem),
      };

      if (recentItems?.length === 3) {
        recentItems.pop();
        recentItems.unshift(newItem);
      } else if (recentItems?.length > 0) {
        recentItems.unshift(newItem);
      } else {
        recentItems = [newItem];
      }

      localStorage.setItem("workspaceRecentItems", JSON.stringify(recentItems));
      this.workspaceService.recentItemsUpdated$.next(true);
    } else return;
  }
}
