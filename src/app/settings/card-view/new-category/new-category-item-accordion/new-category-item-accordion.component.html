<mat-accordion multi>
  <mat-expansion-panel class="mat-expansion-panel-custom">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <h3 class="mat-expansion-header-text-custom">
          {{itemType === WORKSPACE_ITEM_TYPE.ENTITY ? "Entity(s)" : "Business Process(s)"}}
        </h3>
        @if(selectedItems.length) {
        <span class="selected-items-count">{{selectedItems.length}}</span>
        }
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="accordion-content">
      <mat-form-field appearance="outline" class="search-bar full-width">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput
          placeholder="{{itemType === WORKSPACE_ITEM_TYPE.ENTITY ? 'Search Entity' :'Search Business Process'}}"
          (input)="onSearchTextChange($event.target.value)" />
      </mat-form-field>

      @if(isItemLoading){
      <div class="spinner-center">
        <mat-spinner></mat-spinner>
      </div>
      } @else {
      <div class="checkbox-container">
        @for(item of sortedItemList; track item) {
        <mat-checkbox [value]="item" [checked]="checkItemIsSelected(item)"
          (change)="onCheckboxChange($event, item)" color="primary"><span
            class="checkbox-item-text">{{
            itemType=== "Entity" ?
            item.entityName :
            item.name}}</span></mat-checkbox>
        } @empty{
        No items found
        }
      </div>
      }
    </div>
  </mat-expansion-panel>
</mat-accordion>
