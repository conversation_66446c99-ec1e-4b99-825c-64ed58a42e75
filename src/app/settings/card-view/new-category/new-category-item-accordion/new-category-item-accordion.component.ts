import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { WORKSPACE_ITEM_TYPE } from "../../card-view.model";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  finalize,
  Subject,
  takeUntil,
} from "rxjs";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { EntityBasicDetailsResponse } from "src/app/common/models/entity.model";

@Component({
  selector: "app-new-category-item-accordion",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./new-category-item-accordion.component.html",
  styleUrl: "./new-category-item-accordion.component.scss",
})
export class NewCategoryItemAccordionComponent {
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;
  @Input() preSelectedItems: any[] = [];
  @Output() selectedItemsChange = new EventEmitter<any[]>();

  isItemLoading = false;
  itemList: any[] = [];
  filteredItemList: any[] = [];
  selectedItems: any[] = [];
  searchText$ = new BehaviorSubject<string>("");
  destroy$ = new Subject<void>();
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  get sortedItemList() {
    return this.filteredItemList.slice().sort((a, b) => {
      const aChecked = this.checkItemIsSelected(a);
      const bChecked = this.checkItemIsSelected(b);
      return Number(bChecked) - Number(aChecked); // checked items first
    });
  }

  constructor(
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  ngOnInit() {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.getAllEntities();
    } else if (this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW) {
      this.getAllWorkflows();
    }

    this.searchText$
      .pipe(takeUntil(this.destroy$), debounceTime(100), distinctUntilChanged())
      .subscribe((searchTerm) => {
        if (searchTerm) {
          this.filteredItemList =
            this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
              ? this.itemList.filter((item) =>
                  item.entityName
                    .toLowerCase()
                    .includes(searchTerm.toLowerCase())
                )
              : this.itemList.filter((item) =>
                  item.name.toLowerCase().includes(searchTerm.toLowerCase())
                );
        } else {
          this.filteredItemList = structuredClone(this.itemList);
        }
      });
  }

  getAllEntities() {
    this.isItemLoading = true;

    this.entityService
      .getEntityBasicDetails()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res: EntityBasicDetailsResponse) => {
        this.itemList = res;
        this.filteredItemList = structuredClone(this.itemList);

        // set selected items as checked for edit
        if (this.preSelectedItems.length) {
          // console.log("preseleced", this.preSelectedItems);
          this.selectedItems = structuredClone(
            this.preSelectedItems.map((item) => {
              // console.log(this.itemList);
              return this.itemList.find(
                (entity) =>
                  entity.entityName === item.entityName &&
                  entity.entityType === item.entityType &&
                  entity.subType === item.subType
              );
            })
          );
        }
      });
  }

  getAllWorkflows() {
    this.isItemLoading = true;

    this.businessProcessService
      .getAllBusinessProcessList()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res) => {
        this.itemList = res as any[];
        this.filteredItemList = structuredClone(this.itemList);
        // set selected items as checked for edit
        if (this.preSelectedItems.length) {
          // console.log("workflows", this.preSelectedItems);
          this.selectedItems = structuredClone(
            this.preSelectedItems.map((item) => {
              return this.itemList.find((workflow) => workflow.name === item);
            })
          );
        }
      });
  }

  checkItemIsSelected(currentItem: any) {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      // console.log(this.selectedItems);
      return this.selectedItems
        .map((item) => item.entityName)
        .includes(currentItem.entityName);
    } else {
      // console.log("workflow", currentItem);
      return this.selectedItems
        .map((item) => item.name)
        .includes(currentItem.name);
    }
  }

  onSearchTextChange(searchTerm: string) {
    this.searchText$.next(searchTerm);
  }

  onCheckboxChange(event: MatCheckboxChange, item: any) {
    // console.log(item);
    this.selectedItems = event.checked
      ? [...this.selectedItems, item]
      : this.selectedItems.filter((e) =>
          this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
            ? e.entityName !== item.entityName
            : e.name !== item.name
        );

    this.selectedItemsChange.emit(this.selectedItems);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
