<mat-dialog-content class="mat-dialog-content-form-custom-css">
  <div fxLayout="column" fxLayoutGap="4px" class="custom-form-field">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px" fxLayoutAlign="space-between center">
        <div fxFlex="85%" fxFlex.md="85%" fxFlex.xs="100%" fxFlex.sm="100%">
          <h2 class="form-header">{{data.mode === MODE.CREATE ? ('label.newCategoryName' | literal)
            : ('label.categoryOverview' | literal)}}</h2>
        </div>
        <div fxFlex="10%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml-btn">
          <button mat-icon-button (click)="closeDialog()">
            <mat-icon aria-label="create-deal-close-btn" class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <div class="form-container">
      <div class="createDealInputs ">
        <div fxLayout="row wrap" class="createDealInputs ">
          <mat-form-field class="custom-mat-input-style processNameInputArea full-width"
            fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <mat-label>{{"label.workGroupName" | literal}}</mat-label>
            <input required matInput name="categoryNameFormControl"
              [formControl]="categoryNameFormControl" aria-label="create-deal-input-deal-name">
            @if(categoryNameFormControl?.errors?.pattern) {
            <mat-error>
              {{"label.materror.nameValidation" |literal}}
            </mat-error>
            }
          </mat-form-field>

          <mat-form-field class="custom-mat-input-style processNameInputArea full-width"
            fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <mat-label>{{"label.workGroupDescription"| literal}}</mat-label>
            <textarea matInput [(ngModel)]="categoryDescriptionControl"
              aria-label="create-deal-input-deal-name" rows="4"></textarea>
          </mat-form-field>

          <mat-form-field class="custom-mat-input-style full-width">
            <mat-label>{{"label.selectRoles" | literal}}</mat-label>
            <mat-select [(value)]="selectedRoles" multiple>
              @for(role of filteredUserRoles; track role) {
              <mat-option [value]="role">
                {{ role }}
              </mat-option>
              }
            </mat-select>
          </mat-form-field>

        </div>

      </div>
    </div>
  </div>

  <div class="dialog-subheading">Assign Items</div>
  <div class="dialog-subheading-description">{{"label.workspace.subTitle.assignItems" | literal}}
  </div>

  <div class="accordion-container">
    <app-new-category-item-accordion [itemType]="WORKSPACE_ITEM_TYPE.WORKFLOW"
      [preSelectedItems]="selectedWorkflows"
      (selectedItemsChange)="onWorkflowSelected($event)"></app-new-category-item-accordion>

    <app-new-category-item-accordion [itemType]="WORKSPACE_ITEM_TYPE.ENTITY"
      [preSelectedItems]="selectedEntities"
      (selectedItemsChange)="onEntitySelected($event)"></app-new-category-item-accordion>
  </div>

  <div class="dialog-button-container">
    <button color="primary" aria-label="add-extension-btn" mat-raised-button type="submit"
      (click)="onClickBtn()" [disabled]="!isFormValid()">{{data.mode === MODE.CREATE ? "Create" :
      "Update"}}</button>
  </div>
</mat-dialog-content>
