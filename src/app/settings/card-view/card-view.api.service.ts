import { HttpClient, HttpParams } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import {
  CardConfigResponse,
  CategoriesItem,
  UncategorizedCardConfigResponse,
} from "./card-view.model";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class CardViewApiService {
  constructor(
    private readonly http: HttpClient,
    @Inject("originateBaseUrl") private readonly baseUrl: string
  ) {}

  getCardConfig(searchKey?: string): Observable<CardConfigResponse> {
    let params = new HttpParams();

    if (searchKey) params = params.set("searchKey", searchKey);

    return this.http.get<CardConfigResponse>(`${this.baseUrl}/card-config`, {
      params,
    });
  }

  getUncategorisedCardConfig(): Observable<UncategorizedCardConfigResponse> {
    return this.http.get<UncategorizedCardConfigResponse>(
      `${this.baseUrl}/card-config/uncategorized-card-configs`
    );
  }

  createCardConfig(payload: CategoriesItem) {
    return this.http.post(`${this.baseUrl}/card-config/create`, payload, {
      observe: "response",
    });
  }

  updateCardConfig(payload: CategoriesItem) {
    return this.http.put(`${this.baseUrl}/card-config/update`, payload, {
      observe: "response",
    });
  }

  deleteCardConfig(id: string) {
    const params = new HttpParams().set("id", id);

    return this.http.delete(`${this.baseUrl}/card-config/delete`, {
      params,
      observe: "response",
    });
  }
}
