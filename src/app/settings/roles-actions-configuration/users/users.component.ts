import { Component, OnInit, ViewChild, ChangeDetectorRef } from "@angular/core";
import { MatTableDataSource } from "@angular/material/table";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { DomSanitizer } from "@angular/platform-browser";
import { IdentityService } from "../../../shared-service/identity.service";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import { Subject } from "rxjs";
@Component({
  selector: "app-users",
  templateUrl: "./users.component.html",
  styleUrls: ["./users.component.scss"],
})
export class UsersComponent implements OnInit {
  type = "";
  selectedApplicationsData: any;
  dataSource: MatTableDataSource<any>;
  searchKey = "";
  businessProcessList = [];
  pdfFileBaseURL = "";
  imageBaseURL: any;
  private destroy$ = new Subject<void>();
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }

  get UTILITIES_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  @ViewChild(MatSort, { static: false }) sort: MatSort;
  displayedColumns: any[] = [
    "name",
    "mailId",
    "phoneNumber",
    "identifier",
    "role",
  ];
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  tableData = [];
  constructor(
    public identityService: IdentityService,
    private readonly cdr: ChangeDetectorRef,
    public dom: DomSanitizer,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.getUserList();
  }

  applyFilter(filterValue) {
    if (this.dataSource) {
      this.dataSource.filter = filterValue.trim().toLowerCase();
      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    }
  }

  refreshDataTable(filterdData) {
    let data = [];

    data = filterdData;

    this.dataSource = new MatTableDataSource(data);
    this.cdr.detectChanges();
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.searchKey = "";
    this.showNoRecordsAvailbleMessage = true;

    if (data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
      this.cdr.detectChanges();
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.dataSource.filterPredicate = function (
        data,
        filter: string
      ): boolean {
        return (
          data?.firstName?.toLowerCase().includes(filter) ||
          data?.lastName?.toLowerCase().includes(filter) ||
          data?.role?.toString().includes(filter)
        );
      };
      this.searchKey = "";
      this.showNoRecordsAvailbleMessage = false;
    }
  }

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res) => {
        this.showLoaderSpinner = false;
        this.refreshDataTable(res);
      },
      () => {
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
