@use '@angular/material' as mat;
@use 'src/styles/themes/themes.scss' as theme;
@use 'sass:map';

.mat-mdc-menu-item {
  @include mat.typography-level(theme.$custom-typography-config, subtitle-1);
}

.fab-div {

  padding: 1%;

  z-index: 1000;

  position: fixed;

  bottom: 3%;

  right: 3%;

}

.loanaccount {
  width: 800px;
}

.iconclass {
  height: 40px !important;
  width: 51px !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep .menu .mat-mdc-form-field .mat-form-field-infix {
  border: 1px solid #c7c7c7;
  border-radius: 4px;
  height: 23px;
  padding-top: 10px;
  padding-left: 6%;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
::ng-deep .menu .mat-mdc-form-field .mat-form-field-underline {
  display: none;
}

.menu-icon {
  position: relative;
  top: 9px;
  left: 2%;
  font-size: 20px;
}

.editmenu {
  margin-left: 40px;
}


.queryblock {
  margin-left: 17%;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}


.cdk-drag-preview .sbmenu-icon {
  display: none;
}


.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.add-button {
  float: right;
  margin-right: 3%;
}

.submenu {
  border-top: 1px solid rgb(0 0 0 / 24%) !important;
  padding: 6px 19px !important;
  width: 76%;
  margin-left: 1% !important;
  // height: 44px ;
  display: block;
}



/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.cdk-drag-preview ::ng-deep .mat-form-field-underline {
  display: none;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.cdk-drag-preview ::ng-deep .mat-form-field-infix {
  border: 1px solid #c7c7c7;
  border-radius: 4px;
  height: 23px;
  padding-top: 10px;
  padding-left: 6%;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.highlight ::ng-deep .mat-form-field-flex {
  border: 1px solid !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.menuborder ::ng-deep .mat-form-field-flex {
  border: 0px solid !important;
}

.width-26 {
  width: 26% !important;
}

.top-bar-card {
  margin-bottom: 2% !important;
  min-height: 350px !important
}

.cdkDragCard {
  margin-bottom: 1% !important;
  border-top: 1px solid rgb(0 0 0 / 24%) !important;
  display: block;
  padding: 2% !important;
}

.submenuCard {
  margin-left: 2.7% !important
}

.addMenuBtn {
  background-color: #15c4fe !important
}

.topMenuActions {
  float: right;
  position: relative;
  bottom: 54% !important;
}

.addBtnDisable {
  background-color: #c7c7c7 !important;
}

.borderValue {
  border: 1px solid black;
  width: 36px;
  height: 36px;
  text-align: center;
}

.margin-right {
  margin-right: 1vw;
}

.error-message {
  position: absolute;
  right: 1.5vw;
  color: red;
  width: 100%;
  text-align: end;
}

.newUI {

  .mar-R {
    margin-right: 8px;
  }

  .content-display {
    display: flex;
  }

  .edit-bttn {
    bottom: 1.5vh;
  }
}
