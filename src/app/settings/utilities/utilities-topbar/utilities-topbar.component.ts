import { Component, ElementRef, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import JsonData from "src/assets/data.json";
import { AddButtonRulesComponent } from "src/app/dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { AddSubmenuDialogComponent } from "../../page-layout/top-navigation-configuration/dialog/add-submenu-dialog/add-submenu-dialog.component";
import { EditQueryDialogComponent } from "../../page-layout/top-navigation-configuration/dialog/edit-query-dialog/edit-query-dialog.component";
import { AddQueryDialogComponent } from "../../page-layout/top-navigation-configuration/dialog/add-query-dialog/add-query-dialog.component";
import { AddMenuDialogComponent } from "../../page-layout/top-navigation-configuration/dialog/add-menu-dialog/add-menu-dialog.component";

@Component({
  selector: "app-utilities-topbar",
  standalone: false,
  templateUrl: "./utilities-topbar.component.html",
  styleUrl: "./utilities-topbar.component.scss",
})
export class UtilitiesTopbarComponent {
  menus: any = [];
  isEdit: any;
  isEditMenu: any;
  isMenu: any;
  dragInitiate = false;
  menubarConfigDetails: any;
  subMenuName: any;
  menuName: any;
  loading = false;
  rules: any;
  navBarMenuLimitation: number = 4;
  showStaticData: boolean = true;
  tenantConfigDetails: any;
  tenantConfigArr: any = [];

  @ViewChild("subMenu") subMenu: ElementRef;

  get UTILITIES_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  constructor(
    private readonly errorService: ErrorService,
    public notificationMessage: ToasterService,
    private readonly dialog: MatDialog,
    public pageLayoutService: PageLayoutService,
    public dataSharingService: DataSharingService
  ) {
    this.getConfigurationDetailsByIdentifier("MENU_BAR");
    this.getConfigurationDetailsByIdentifierTenant();
    this.loading = false;
  }

  getConfigurationDetailsByIdentifier(identifier) {
    this.loading = true;
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .subscribe((res: any) => {
        this.menubarConfigDetails = Object.assign({}, res);
        this.menus = this.menubarConfigDetails?.configDetails?.slice();
        const data = JSON.parse(JSON.stringify(this.menubarConfigDetails));
        this.dataSharingService.setMenubarItems(data);
        this.loading = false;
      });
  }

  getConfigurationDetailsByIdentifierTenant() {
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier("TENANT_CONFIGURATION")
      .subscribe({
        next: (res) => {
          this.tenantConfigDetails = Object.assign({}, res);
          this.tenantConfigArr = this.tenantConfigDetails?.configDetails;
          this.navBarMenuLimitation =
            this.tenantConfigDetails?.configDetails?.filter((x) =>
              x.hasOwnProperty("menuConfiguration")
            )[0]?.menuConfiguration || this.navBarMenuLimitation;
        },
        error: (err) => {
          throw err;
        },
      });
  }

  onEditInput() {
    this.showStaticData = false;
  }

  crossButtonClick() {
    this.showStaticData = true;
  }

  doneButtonClick() {
    this.showStaticData = true;
    this.addOrUpdateProperty(this.tenantConfigDetails?.configDetails);
    this.updateTenantConfigDetails();
  }

  addOrUpdateProperty(arr) {
    const hasProperty = arr.some((x) => x.hasOwnProperty("menuConfiguration"));

    if (!hasProperty) {
      arr.push({ menuConfiguration: this.navBarMenuLimitation });
    } else {
      arr.forEach((x) => {
        if (x.hasOwnProperty("menuConfiguration")) {
          x["menuConfiguration"] = this.navBarMenuLimitation;
        }
      });
    }
  }

  dropMenu(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.menus, event.previousIndex, event.currentIndex);
    const id = this.menubarConfigDetails?.id;
    this.menubarConfigDetails.configDetails = this.menus;
    const data = Object.assign({}, this.menubarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe(
        () => {
          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        },
        (error) => {
          this.loading = false;

          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        }
      );
  }

  isEditable(i, j, item) {
    return item ? this.isEdit == j && this.isMenu == i : false;
  }

  isEditableMenu(j, item) {
    return item ? this.isEditMenu == j : false;
  }

  getValue(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

  updateMenuName(menu, targetValue) {
    const filterMenu = this.menus.find((item) => item.menu == menu);
    if (targetValue == undefined) {
      filterMenu.menu = filterMenu.menu;
    } else {
      filterMenu.menu = targetValue;
    }
    const id = this.menubarConfigDetails?.id;
    this.menubarConfigDetails.configDetails = this.menus;
    const data = Object.assign({}, this.menubarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe(
        () => {
          this.getConfigurationDetailsByIdentifier("MENU_BAR");
          this.notificationMessage.success(JsonData["label.success.menuName"]);
          this.isEdit = null;
          this.isEditMenu = null;
        },
        (error) => {
          this.loading = false;

          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        }
      );
  }

  editMenuRules(rules, menu) {
    this.rules = rules;
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "45vw",
      disableClose: true,
      data: { buttonRules: rules, translationContext: "Set Hide Rules" },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      this.rules = result;
      const filterMenu = this.menus.find((item) => item.menu == menu);
      if (typeof this.rules == "object") {
        filterMenu.rules = this.rules;
        const id = this.menubarConfigDetails?.id;
        this.menubarConfigDetails.configDetails = this.menus;
        const data = Object.assign({}, this.menubarConfigDetails);
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, data)
          .subscribe(
            () => {
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.notificationMessage.success(
                JsonData["label.success.menuName"]
              );
            },
            (error) => {
              this.loading = false;

              this.getConfigurationDetailsByIdentifier("MENU_BAR");
            }
          );
      }
    });
  }

  editRulesSubMenu(rules, menu, subMenu, subMenuName) {
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "45vw",
      disableClose: true,
      data: { buttonRules: rules, translationContext: "Set Hide Rules" },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      this.rules = result;
      const filterMenu = this.menus.find((item) => item.menu == menu);
      const filterSubmenu = filterMenu.subMenus.find(
        (item) => item.subMenu == subMenu
      );
      if (typeof this.rules == "object") {
        filterSubmenu.rules = this.rules;
      }
      const id = this.menubarConfigDetails?.id;
      this.menubarConfigDetails.configDetails = this.menus;
      const data = Object.assign({}, this.menubarConfigDetails);
      this.pageLayoutService
        .updateConfigurationDetailsByIdentifier(id, data)
        .subscribe(
          () => {
            this.getConfigurationDetailsByIdentifier("MENU_BAR");
            this.notificationMessage.success(
              JsonData["label.success.menuName"]
            );
            this.isEdit = null;
            this.isMenu = null;
          },
          (error) => {
            this.loading = false;

            this.getConfigurationDetailsByIdentifier("MENU_BAR");
          }
        );
    });
  }

  delete(i, menu, name) {
    let message;
    if (name == "menu") {
      message = `Deleting Navigation menu will delete all related submenus,do you want to proceed?`;
    } else {
      message = `This action will delete the submenu filter,confirm?`;
    }
    let buttonList = [
      { value: true, label: "Yes,Delete" },
      { value: false, label: "Cancel" },
    ];

    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      let msg;
      if (result) {
        if (name == "menu") {
          this.menus.splice(i, 1);
          msg = JsonData["label.success.deleteMenu"];
        } else {
          const filterMenu = this.menus.find((item) => item.menu == menu);
          filterMenu.subMenus.splice(i, 1);
          msg = JsonData["label.success.deleteSubMenu"];
        }
        const id = this.menubarConfigDetails?.id;
        this.menubarConfigDetails.configDetails = this.menus;
        const data = Object.assign({}, this.menubarConfigDetails);
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, data)
          .subscribe(
            () => {
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.notificationMessage.success(msg);
            },
            (error) => {
              this.loading = false;

              this.getConfigurationDetailsByIdentifier("MENU_BAR");
            }
          );
      }
    });
  }

  addSubMenu(menu) {
    const filterMenu = this.menus.find((item) => item.menu == menu);
    const matDialogRef = this.dialog.open(AddSubmenuDialogComponent, {
      width: "50%",
      disableClose: true,
      data: { menu: menu, submenus: filterMenu.subMenus },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const filterMenu = this.menus.find((item) => item.menu == menu);
        filterMenu.subMenus = result.subMenus;
        const id = this.menubarConfigDetails?.id;
        this.menubarConfigDetails.configDetails = this.menus;
        const data = Object.assign({}, this.menubarConfigDetails);
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, data)
          .subscribe(
            () => {
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.notificationMessage.success(
                JsonData["label.success.addSubMenu"]
              );
            },
            (error) => {
              this.loading = false;

              this.getConfigurationDetailsByIdentifier("MENU_BAR");
            }
          );
      }
    });
  }

  drop(event: CdkDragDrop<string[]>, menu) {
    const filterMenu = this.menus.find((item) => item.menu == menu);
    moveItemInArray(
      filterMenu.subMenus,
      event.previousIndex,
      event.currentIndex
    );
    const id = this.menubarConfigDetails?.id;
    this.menubarConfigDetails.configDetails = this.menus;
    const data = Object.assign({}, this.menubarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe(
        () => {
          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        },
        (error) => {
          this.loading = false;

          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        }
      );
  }

  addMenu() {
    const matDialogRef = this.dialog.open(AddMenuDialogComponent, {
      width: "30%",
      disableClose: true,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      let data;
      if (result) {
        this.menus.push(result);
        if (!this.menubarConfigDetails) {
          data = {
            configIdentifier: "MENU_BAR",
            configDetails: this.menus,
          };
          this.pageLayoutService.addConfigurationDetail(data).subscribe(
            (data) => {
              this.notificationMessage.success(
                JsonData["label.success.addMenu"]
              );
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.loading = false;
            },
            (error) => {
              this.loading = false;
            }
          );
        } else {
          const id = this.menubarConfigDetails?.id;
          this.menubarConfigDetails.configDetails = this.menus;
          const data = Object.assign({}, this.menubarConfigDetails);
          this.pageLayoutService
            .updateConfigurationDetailsByIdentifier(id, data)
            .subscribe(
              (res: any) => {
                this.getConfigurationDetailsByIdentifier("MENU_BAR");
                this.notificationMessage.success(
                  JsonData["label.success.addMenu"]
                );
                this.loading = false;
              },
              (error) => {
                this.loading = false;

                this.getConfigurationDetailsByIdentifier("MENU_BAR");
              }
            );
        }
      }
    });
  }

  addQuery(j, subMenu, menu) {
    const matDialogRef = this.dialog.open(AddQueryDialogComponent, {
      width: "50%",
      disableClose: true,
      data: { name: subMenu },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const filterMenu = this.menus.find((item) => item.menu == menu);
        const filterSubmenu = filterMenu.subMenus.find(
          (item) => item.subMenu == subMenu
        );
        filterSubmenu.queryName = result.queryName;
        filterSubmenu.query = result.query;
        const id = this.menubarConfigDetails?.id;
        this.menubarConfigDetails.configDetails = this.menus;
        const data = Object.assign({}, this.menubarConfigDetails);
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, data)
          .subscribe(
            () => {
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.notificationMessage.success(
                JsonData["label.success.menuQuery"]
              );
            },
            (error) => {
              this.loading = false;

              this.getConfigurationDetailsByIdentifier("MENU_BAR");
            }
          );
      }
    });
  }

  editQuery(name, query, subMenu, menu) {
    const matDialogRef = this.dialog.open(EditQueryDialogComponent, {
      width: "50%",
      disableClose: true,
      data: { name: name, query: query, subMenu: subMenu },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const filterMenu = this.menus.find((item) => item.menu == menu);
        const filterSubmenu = filterMenu.subMenus.find(
          (item) => item.subMenu == subMenu
        );
        filterSubmenu.queryName = result.queryName;
        filterSubmenu.query = result.query;

        const id = this.menubarConfigDetails?.id;
        this.menubarConfigDetails.configDetails = this.menus;
        const data = Object.assign({}, this.menubarConfigDetails);
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, data)
          .subscribe(
            () => {
              this.getConfigurationDetailsByIdentifier("MENU_BAR");
              this.notificationMessage.success(
                JsonData["label.success.menuEditQuery"]
              );
            },
            (error) => {
              this.loading = false;

              this.getConfigurationDetailsByIdentifier("MENU_BAR");
            }
          );
      }
    });
  }

  updateSubMenuName(menu, subMenu, subMenuName) {
    const filterMenu = this.menus.find((item) => item.menu == menu);
    const filterSubmenu = filterMenu.subMenus.find(
      (item) => item.subMenu == subMenu
    );
    if (subMenuName == undefined) {
      filterSubmenu.subMenu = filterSubmenu.subMenu;
    } else {
      filterSubmenu.subMenu = subMenuName;
    }

    const id = this.menubarConfigDetails?.id;
    this.menubarConfigDetails.configDetails = this.menus;
    const data = Object.assign({}, this.menubarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe(
        () => {
          this.getConfigurationDetailsByIdentifier("MENU_BAR");
          this.notificationMessage.success(
            JsonData["label.success.subMenuName"]
          );
          this.isEdit = null;
          this.isMenu = null;
        },
        (error) => {
          this.loading = false;

          this.getConfigurationDetailsByIdentifier("MENU_BAR");
        }
      );
  }

  updateTenantConfigDetails() {
    let id = this.tenantConfigDetails?.id;
    let data = Object.assign({}, this.tenantConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .subscribe({
        next: (res) => {
          this.getConfigurationDetailsByIdentifierTenant();
          this.notificationMessage.success(
            JsonData["label.success.menuConfiguration"]
          );
        },
        error: (err) => {
          this.loading = false;
          this.getConfigurationDetailsByIdentifierTenant();
        },
      });
  }
}
