<div class="newUI">
  <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center">
    <div fxFlex="75%">
      <h2>Set Top Bar Navigation</h2>
    </div>
    <div fxFlex="25%" fxLayoutAlign="end end">
      <div *ngIf="showStaticData" class="content-display">

        <h2><span class="margin-right">Top Menu Count</span></h2>
        <h2 class="borderValue">{{navBarMenuLimitation}}</h2>
        <button mat-icon-button (click)="onEditInput()" class="edit-bttn edit-btn">
          <span class="material-symbols-outlined">edit</span>
        </button>

      </div>
      <div *ngIf="!showStaticData">
        <mat-form-field appearance="outline">
          <input matInput required type="number" [(ngModel)]="navBarMenuLimitation">
        </mat-form-field>
        <button mat-icon-button (click)="doneButtonClick()"
          [matTooltip]="navBarMenuLimitation < menus.length ? 'You cannot reduce the No. of Menus, as Menus are configured.' : null"
          [disabled]="navBarMenuLimitation < menus.length">
          <span class="material-symbols-outlined">check</span>
        </button>
        <button mat-icon-button (click)="crossButtonClick()">
          <span class="material-symbols-outlined">cancel</span>
        </button>
      </div>
    </div>
  </div>

  <div fxLayout="row">
    <div fxFlex="100%" fxLayout="column" fxLayoutAlign="center">
      <h5
        *ngIf="navBarMenuLimitation !== null && navBarMenuLimitation !== undefined && navBarMenuLimitation < menus.length"
        class="error-message">
        You cannot reduce the No. of Menus, as Menus are configured.
      </h5>
    </div>
  </div>

  <div *ngIf="!loading" cdkDropList (cdkDropListDropped)="dropMenu($event) ">
    <div *ngFor="let menu of menus; let i = index">
      <mat-card appearance="outlined" cdkDrag class="mat-card mat-focus-indicator cdkDragCard">

        <mat-form-field appearance="outline"
          [class]="!isEditableMenu(i,menu.menu)?'width-26 menuborder':'width-26 highlight'">
          <mat-icon matPrefix>more_vert</mat-icon>
          <input matInput [value]="menu.menu" [readonly]="!isEditableMenu(i,menu.menu)"
            (input)="menuName = getValue($event)"
            (keyup.enter)="updateMenuName(menu.menu,$event.target.value)" />
        </mat-form-field>
        <span>
          <button mat-icon-button (click)="isEditMenu=i" *ngIf="!isEditableMenu(i,menu.menu)"
            class="edit-btn">
            <span class="material-symbols-outlined">edit</span>
          </button>
          <button mat-icon-button (click)="updateMenuName(menu.menu,menuName)"
            *ngIf="isEditableMenu(i,menu.menu)">
            <span class="material-symbols-outlined">check</span>
          </button>
          <button mat-icon-button (click)="editMenuRules(menu.rules, menu.menu)">
            <span class="material-symbols-outlined">device_hub</span>
          </button>
          <button class="delete-bttn" mat-icon-button (click)="delete(i,menu.menu,'menu')">
            <span class="material-symbols-outlined">delete</span>
          </button>
          <button mat-icon-button class="add-button colored-icon-button large-icon-button"
            type="submit" (click)="addSubMenu(menu.menu)" matTooltipPosition="above"
            matTooltipClass="accent-tooltip" matTooltip="Add Sub Menu">
            <span class="material-symbols-outlined">add</span>
          </button>
        </span>

        <div cdkDropList (cdkDropListDropped)="drop($event,menu.menu) ">
          <div class="submenuCard" *ngFor="let subMenu of menu.subMenus; let j = index">
            <mat-card appearance="outlined" cdkDrag
              class="submenu-border mat-card mat-focus-indicator submenu" #subMenu>

              <mat-form-field appearance="outline"
                [class]="!isEditable(i,j,subMenu.subMenu)?'width-28 menuborder':'width-28 highlight'">
                <mat-icon matPrefix class="sbmenu-icon">more_vert</mat-icon>
                <input matInput [value]="subMenu.subMenu" (input)="subMenuName = getValue($event)"
                  [readonly]="!isEditable(i,j,subMenu.subMenu)" focusOnInit />
              </mat-form-field>

              <span *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'">
                <button mat-icon-button (click)="isEdit = j;isMenu=i;"
                  *ngIf="!isEditable(i,j,subMenu.subMenu)" class="edit-btn">
                  <span class="material-symbols-outlined">edit</span>
                </button>
                <button mat-icon-button
                  (click)="updateSubMenuName(menu.menu,subMenu.subMenu,subMenuName)"
                  *ngIf="isEditable(i,j,subMenu.subMenu)">
                  <span class="material-symbols-outlined">check</span>
                </button>
                <button mat-icon-button
                  (click)="editRulesSubMenu(subMenu.rules,menu.menu,subMenu.subMenu,subMenuName)">
                  <span class="material-symbols-outlined">device_hub</span>
                </button>
                <button class="delete-bttn" mat-icon-button (click)="delete(j,menu.menu,'submenu')">
                  <span class="material-symbols-outlined">delete</span>
                </button>
              </span>
              <mat-form-field appearance="outline" class="width-28 queryblock">
                <ng-container *ngIf="subMenu.query" matSuffix>
                  <button mat-icon-button class="blue pointer topMenuAction edit-btn"
                    matTooltipPosition="above" matTooltipClass="accent-tooltip"
                    matTooltip="Edit Query"
                    (click)="editQuery(subMenu.queryName,subMenu.query,subMenu.subMenu,menu.menu)"
                    *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'">
                    <span class="material-symbols-outlined">edit</span>
                  </button>
                </ng-container>
                <ng-container *ngIf="subMenu.query === ''" matSuffix>
                  <button mat-icon-button class=" pointer topMenuActions colored-icon-button mar-R"
                    matTooltipPosition="above" matTooltipClass="accent-tooltip"
                    matTooltip="Add Query" (click)="addQuery(j,subMenu.subMenu,menu.menu)"
                    *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'">
                    <span class="material-symbols-outlined">add</span>
                  </button>
                </ng-container>
                <input matInput [value]="subMenu.queryName" class="width-80" readonly="true" />
              </mat-form-field>
            </mat-card>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
  <div *ngIf="loading">
    <mat-spinner></mat-spinner>
  </div>
  <div class="fab-div" matTooltipPosition="above" matTooltipClass="accent-tooltip"
    matTooltip="You can add up to {{navBarMenuLimitation}} menus">
    <button aria-label="fab-dashboard" mat-fab class="fab-add addMenuBtnNewUi" title="quickActions"
      (click)="addMenu()" [disabled]="menus.length >= navBarMenuLimitation"
      [ngClass]="{ 'addBtnDisable' : menus.length >= navBarMenuLimitation }"
      *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'">
      <span class="material-symbols-outlined">add </span>
    </button>
  </div>
</div>
