<div cdkDropList (cdkDropListDropped)="drop($event)">
  <ng-container *ngFor="let item of sidebarItemsList; let j = index">
    <ng-container *ngIf="!utilitiesService.hiddenSidebarItems.includes(item.name)">
      <div cdkDrag [cdkDragDisabled]="" fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
        class="p-percent-1">
        <div class="full-width utilities-sidebar-item-container">
          <span>
            <mat-icon>
              {{getIconName(item)}}
            </mat-icon>
          </span>

          <div fxLayout="row" fxLayoutGap="10" fxLayoutAlign="start center" class="half-width">
            <mat-form-field subscriptSizing="dynamic" class="width-100 custom-mat-input-style">
              <mat-label>{{ item.name }} Label</mat-label>
              <input matInput required [(ngModel)]="item.displayName"
                [ngModelOptions]="{ standalone: true }"
                [disabled]="disableFields || item.name === SIDEBAR_ITEMS.USER_GUIDE || item.name === 'Theme Toggle'" />
            </mat-form-field>
          </div>

          <span
            [matTooltip]="((!item?.rules || disableFields) ? 'utilities.tooltip.toggle.hideRuleDisabled' : 'utilities.tooltip.toggle.hideRuleEnabled')  | literal"
            matTooltipClass="accent-tooltip" [matTooltipDisabled]="disableFields">
            <button mat-stroked-button class="icon-content-button" (click)="addRules(item,j)"
              [disabled]="!item?.rules || disableFields">
              <span class="material-symbols-outlined">
                device_hub
              </span>
            </button>
          </span>

          <button mat-stroked-button class="icon-content-button"
            [matMenuTriggerFor]="roleSelectionMenu"
            [matTooltip]="'utilities.tooltip.toggle.selectRoles'|literal"
            matTooltipClass="accent-tooltip" [disabled]="item?.rules">
            <span class="material-symbols-outlined">
              visibility
            </span>
            <span>{{(item?.roles?.length ?? allUserRoles.length) - 1}}</span>
          </button>

          <mat-menu #roleSelectionMenu="matMenu">
            <div class="rolesPanel">
              <mat-selection-list (selectionChange)="updateSelectedRoles($event, item.name)">
                <ng-container *ngFor="let role of filteredUserRoles">
                  <mat-list-option togglePosition="before" [value]="role"
                    (click)="$event.stopPropagation();" [selected]="item?.roles?.includes(role)"
                    [disabled]="disableFields">
                    {{ role }}
                  </mat-list-option>
                </ng-container>
              </mat-selection-list>
            </div>
          </mat-menu>

        </div>
      </div>
    </ng-container>
  </ng-container>
</div>
