<div class="search-based-queries-container">
  <div fxLayout="row" fxLayoutAlign="end center" class="search-field">
    <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
      <mat-icon matIconPrefix>search</mat-icon>
      <input aria-label="search-queries" matInput autocomplete="off" placeholder="Search" #input
        (keyup)="applyFilter($event.target.value)" />
    </mat-form-field>
  </div>

  <div class="queries-list-container">
    <div fxFlex="100%">
      <table mat-table [dataSource]="dataSource">

        <ng-container matColumnDef="queryName">
          <th class="width-80" mat-header-cell *matHeaderCellDef>{{"label.table.title.name" |
            literal}}
          </th>
          <td class="width-80" mat-cell *matCellDef="let element">
            {{ element.queryIdentifier }}
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th class="center width-20" mat-header-cell *matHeaderCellDef>{{"label.theader.Action" |
            literal}}
          </th>
          <td class="center width-20" mat-cell *matCellDef="let element ,let i = index">
            <button aria-label="edit-queries-btn" mat-icon-button matTooltip="Edit"
              matTooltipClass="accent-tooltip" class="primary-icon-button"
              (click)="editQuery(element,i)">
              <mat-icon class="material-symbols-outlined">edit</mat-icon>
            </button>
            <button aria-label="delete-queries-btn" mat-icon-button matTooltip="Delete"
              matTooltipClass="accent-tooltip" class="delete-icon"
              (click)="deleteQuery(element.id)">
              <mat-icon class="material-symbols-outlined">delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <span *ngIf="queryConfigDetails?.length === 0">
        <div fxLayout="row" fxLayoutAlign="center center" class="m-t-15 no-records-found"></div>
      </span>

      <br>
      <mat-paginator [pageSizeOptions]="[8, 10, 25, 50, 100]" [pageSize]="10">
      </mat-paginator>
    </div>
  </div>

</div>
