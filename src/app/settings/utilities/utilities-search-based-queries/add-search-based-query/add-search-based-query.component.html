<mat-dialog-content>
  <div fxLayout="row wrap">

    <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
      <div fxLayoutAlign="start center">
        <h2>{{"label.title.add.query" | literal}}</h2>
      </div>
      <div fxLayoutAlign="end center">
        <span>
          <button mat-icon-button [mat-dialog-close]="false">
            <mat-icon class="material-symbols-outlined">close</mat-icon>
          </button>
        </span>
      </div>
    </div>

    <form [formGroup]="addQueryForm" fxFlex="100%" class="m-t-15">

      <div fxLayout="row wrap">

        <mat-form-field fxFlex="100%">
          <mat-label matTooltip="Query Name" matTooltipClass="accent-tooltip"
            matTooltipShowDelay="1000" matTooltipPositionAtOrigin="true">{{"label.field.queryName" |
            literal}}
          </mat-label>
          <input attr.aria-label="query-name" matInput formControlName="queryIdentifier" />
          <mat-error *ngIf="addQueryForm.get('queryIdentifier')?.hasError('required')">
            Query Name is required.
          </mat-error>
          <mat-error *ngIf="addQueryForm.get('queryIdentifier')?.hasError('pattern')">
            Only letters and digits are allowed. No spaces, underscores, or special characters.
          </mat-error>
        </mat-form-field>

        <div fxFlex="100%">
          <ngx-monaco-editor [options]="editorOptions" formControlName="query"></ngx-monaco-editor>
        </div>

      </div>

      <div class="dialog-button" fxLayout="row wrap">
        <button [disabled]="this.addQueryForm.invalid" color="primary" aria-label="add-btn"
          mat-raised-button type="submit" (click)="onAdd()">{{"label.button.add" |
          literal}}</button>
      </div>

    </form>
  </div>

</mat-dialog-content>
