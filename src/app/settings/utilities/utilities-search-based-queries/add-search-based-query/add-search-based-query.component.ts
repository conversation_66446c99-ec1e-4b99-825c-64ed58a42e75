import { Component, Inject, OnInit } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { UtilitiesService } from "../../utilities.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { finalize } from "rxjs";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";

@Component({
  selector: "app-add-search-based-query",
  standalone: false,
  templateUrl: "./add-search-based-query.component.html",
  styleUrl: "./add-search-based-query.component.scss",
})
export class AddSearchBasedQueryComponent implements OnInit {
  addQueryForm: FormGroup;
  editorOptions = {
    theme: "vs-dark",
    language: "javascript",
    autoIndent: "full",
  };

  constructor(
    public dialogRef: MatDialogRef<AddSearchBasedQueryComponent>,
    public dialog: MatDialog,
    private _formBuilder: FormBuilder,
    public utilitiesService: UtilitiesService,
    public dataSharingService: DataSharingService,
    private validationService: ValidationErrorMessageService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  ngOnInit() {
    this.initForm();
  }

  initForm() {
    this.addQueryForm = this._formBuilder.group({
      queryIdentifier: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationService.strictAlphaNumericRegex),
        ],
      ],
      query: ["", [Validators.required]],
    });
  }

  onAdd() {
    const data = this.addQueryForm.getRawValue();
    this.dataSharingService.loaderSavePrompt = true;

    this.utilitiesService
      .createQuery(data)
      .pipe(finalize(() => (this.dataSharingService.loaderSavePrompt = false)))
      .subscribe({
        next: (res) => {
          if (res) {
            this.dialogRef.close(true);
          }
        },
        error: (err) => {
          throw err;
        },
      });
  }
}
