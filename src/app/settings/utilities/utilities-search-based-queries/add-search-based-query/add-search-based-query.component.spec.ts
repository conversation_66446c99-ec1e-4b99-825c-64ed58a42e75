import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddSearchBasedQueryComponent } from './add-search-based-query.component';

describe('AddSearchBasedQueryComponent', () => {
  let component: AddSearchBasedQueryComponent;
  let fixture: ComponentFixture<AddSearchBasedQueryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddSearchBasedQueryComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(AddSearchBasedQueryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
