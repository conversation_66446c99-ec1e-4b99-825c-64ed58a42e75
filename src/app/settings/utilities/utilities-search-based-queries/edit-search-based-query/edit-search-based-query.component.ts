import { Component, Inject, OnInit } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { UtilitiesService } from "../../utilities.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { finalize } from "rxjs";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";

@Component({
  selector: "app-edit-search-based-query",
  standalone: false,
  templateUrl: "./edit-search-based-query.component.html",
  styleUrl: "./edit-search-based-query.component.scss",
})
export class EditSearchBasedQueryComponent implements OnInit {
  editQueryForm: FormGroup;
  editorOptions = {
    theme: "vs-dark",
    language: "javascript",
    autoIndent: "full",
  };

  constructor(
    public dialogRef: MatDialogRef<EditSearchBasedQueryComponent>,
    public dialog: MatDialog,
    private _formBuilder: FormBuilder,
    public utilitiesService: UtilitiesService,
    public dataSharingService: DataSharingService,
    private validationService: ValidationErrorMessageService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.editQueryForm = this._formBuilder.group({
      queryIdentifier: [
        this.data.item?.queryIdentifier || "",
        [
          Validators.required,
          Validators.pattern(this.validationService.strictAlphaNumericRegex),
        ],
      ],
      query: [this.data.item?.query || "", [Validators.required]],
    });
  }

  update() {
    const data = {
      id: this.data.item.id,
      queryIdentifier: this.editQueryForm.value["queryIdentifier"],
      query: this.editQueryForm.value["query"],
    };
    this.dataSharingService.loaderSavePrompt = true;

    this.utilitiesService
      .updateQuery(data)
      .pipe(finalize(() => (this.dataSharingService.loaderSavePrompt = false)))
      .subscribe({
        next: (res) => {
          this.dialogRef.close(true);
        },
        error: (err) => {
          throw err;
        },
      });
  }
}
