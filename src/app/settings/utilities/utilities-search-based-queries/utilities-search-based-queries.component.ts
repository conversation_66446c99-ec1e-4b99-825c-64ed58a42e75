import { Component, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { ToasterService } from "src/app/common/toaster.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";
import JsonData from "src/assets/data.json";
import * as _ from "lodash";
import { EditSearchBasedQueryComponent } from "./edit-search-based-query/edit-search-based-query.component";
import { UtilitiesService } from "../utilities.service";
import { finalize } from "rxjs";

@Component({
  selector: "app-utilities-search-based-queries",
  standalone: false,
  templateUrl: "./utilities-search-based-queries.component.html",
  styleUrl: "./utilities-search-based-queries.component.scss",
})
export class UtilitiesSearchBasedQueriesComponent implements OnInit {
  queryConfigDetails: any = {};
  dataSource: MatTableDataSource<unknown>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  displayedColumns: string[] = ["queryName", "action"];

  constructor(
    private matDialog: MatDialog,
    private themeService: ThemeService,
    private notification: ToasterService,
    public utilitiesService: UtilitiesService
  ) {}

  ngOnInit(): void {
    this.getAllQueryList();

    this.utilitiesService.queryList$.subscribe(() => {
      this.getAllQueryList();
    });
  }

  getAllQueryList() {
    this.utilitiesService.isChildLoading$.next(true);
    this.utilitiesService
      .getAllQueryList()
      .pipe(
        finalize(() => {
          this.utilitiesService.isChildLoading$.next(false);
        })
      )
      .subscribe((res) => {
        this.queryConfigDetails = _.cloneDeep(res);
        this.dataSource = new MatTableDataSource(
          this.queryConfigDetails?.content
        );
        this.dataSource.paginator = this.paginator;
      });
  }

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  editQuery(element, i) {
    const dialogRef = this.matDialog.open(EditSearchBasedQueryComponent, {
      disableClose: true,
      width: "45%",
      hasBackdrop: true,
      data: { item: element, index: i },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getAllQueryList();
        this.notification.success(JsonData["label.update.query"]);
      }
    });
  }

  deleteQuery(id) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }

    const message = JsonData["label.warning.queryLinked"];
    const additionalMessage = JsonData["label.warning.query.impacts"];
    const infoMessage = JsonData["label.warning.delete.query"];

    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        additionalMessage: additionalMessage,
        infoMessage: infoMessage,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.utilitiesService.deleteQuery(id).subscribe((res) => {
          if (res) {
            this.getAllQueryList();
            this.notification.success(JsonData["label.delete.query"]);
          }
        });
      }
    });
  }
}
