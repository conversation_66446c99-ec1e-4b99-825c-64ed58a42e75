<mat-form-field class="full-width custom-mat-input-style" appFileDragNDrop
  (filesChangeEmiter)="fileUpload($event)" subscriptSizing="dynamic">
  <mat-label>{{displayName}}</mat-label>
  <input readonly [formControl]="control" matInput [hidden]="true">
  <input readonly [value]="fileName" matInput />

  <div fxLayout="row" matSuffix>
    <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5">
      <input matInput class="hidden">
      <input class="hidden" type="file" #fileDropRef id="fileDropRef"
        (change)="fileUpload($event.target.files[0])" accept=".pdf" />


      <button class="primary-icon-button" [disabled]="control.disabled"
        *ngIf="!control.disabled && !control.value?.fileName" (click)="fileDropRef.click()"
        mat-icon-button matTooltip="Upload document" matTooltipClass="accent-tooltip">
        <span class="material-symbols-outlined">upload</span>
      </button>
    </div>


    <!-- not showing actions when doc is not uploaded or it is getting uploaded on create/save -->
    <div *ngIf="control.value?.fileName">

      <button *ngIf="control.value" mat-icon-button class="primary-icon-button"
        (click)="downloadFile(control.value)" matTooltip="Download document"
        matTooltipClass="accent-tooltip">
        <span class="material-symbols-outlined">download</span>
      </button>

      <button *ngIf="control.value && !control.disabled" class="warn-icon-button"
        [disabled]="control.disabled" mat-icon-button (click)="openDeleteDialog()"
        matTooltip="Delete document" matTooltipClass="accent-tooltip">
        <span class="material-symbols-outlined">delete</span>
      </button>

      <button *ngIf="control.value" mat-icon-button class="primary-icon-button"
        (click)="previewFile()" matTooltip="Preview file" matTooltipClass="accent-tooltip">
        <span class="material-symbols-outlined">visibility</span>
      </button>
    </div>
  </div>

</mat-form-field>
