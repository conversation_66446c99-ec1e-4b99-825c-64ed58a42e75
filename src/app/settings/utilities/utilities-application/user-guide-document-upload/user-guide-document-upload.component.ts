import { Component, EventEmitter, Input, Output } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ToasterService } from "src/app/common/toaster.service";
import { LiteralPipe } from "src/app/pipe/literal.pipe";
import { DocumentService } from "src/app/shared-service/document.service";
import { saveAs } from "file-saver";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { catchError, EMPTY, throwError } from "rxjs";
import { FilePreviewComponent } from "src/app/dialogs/file-preview/file-preview.component";
import { MatDialog } from "@angular/material/dialog";

@Component({
  selector: "app-user-guide-document-upload",
  standalone: false,
  providers: [LiteralPipe],
  templateUrl: "./user-guide-document-upload.component.html",
  styleUrl: "./user-guide-document-upload.component.scss",
})
export class UserGuideDocumentUploadComponent {
  @Input({ required: true }) control: FormControl;
  @Input({ required: true }) displayName: string;
  @Output() updated = new EventEmitter();

  userGuideSrlNum: number;

  get fileName(): string {
    return this.control.value?.fileName ?? "";
  }

  selectedFile: File;

  constructor(
    private readonly notificationMessage: ToasterService,
    private readonly literal: LiteralPipe,
    private readonly documentService: DocumentService,
    private readonly matDialog: MatDialog
  ) {}

  uploadDocument() {
    const fileData = new FormData();
    fileData.append("file", this.selectedFile);

    this.documentService
      .uploadUtilitiesDocument(fileData)
      .pipe(
        catchError((err) => {
          this.control?.reset();
          if (err.status == 404) {
            this.notificationMessage.error(
              this.literal.transform("error.file.upload")
            );
            return EMPTY;
          }
          return throwError(() => err);
        })
      )
      .subscribe((res) => {
        if (res.status === 200) {
          this.notificationMessage.success(
            this.literal.transform("label.success.UploadDocument")
          );
          const value = {
            fileName: this.selectedFile.name,
          };
          this.control.setValue(value);
          this.control?.markAsDirty();
          this.updated.emit();
        }
      });
  }

  fileUpload(file: File) {
    if (file) {
      this.selectedFile = file;

      const fileExtension = this.selectedFile.name.split(".").at(-1);

      if (fileExtension !== "pdf") {
        this.control.reset();
        this.notificationMessage.error(
          this.literal.transform("error.message.userGuide.incorrectFileFormat")
        );
      } else {
        this.control.setValue({
          fileName: this.selectedFile.name,
          file: this.selectedFile,
        });

        this.uploadDocument();
      }
    }
  }

  downloadFile(filedata) {
    this.documentService
      .downloadFile(filedata?.randomSerialNumber)
      .pipe(
        catchError((err) => {
          if (err.status == 404) {
            this.notificationMessage.error(
              this.literal.transform(
                "label.success.PreviewDocumentNotAvailable"
              )
            );
            return EMPTY;
          }
          return throwError(() => err);
        })
      )
      .subscribe((res: any) => {
        const blob = new Blob([res], { type: "application/octet-stream" });
        const file = new File([blob], filedata?.fileName, {
          type: "application/octet-stream",
        });
        saveAs(file);
        this.notificationMessage.success(
          this.literal.transform("label.success.DownloadDocument")
        );
      });
  }

  openDeleteDialog() {
    const file = this.control.value;

    let buttonList = [
      { value: true, label: "Yes,Delete" },
      { value: false, label: "Cancel" },
    ];

    const message =
      "Deleting document here will remove the User Guide, do you want to proceed?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteDocument(file?.id);
      }
    });
  }

  deleteDocument(documentId) {
    this.documentService
      .deleteUtilityDocument(documentId)
      .pipe(
        catchError((err) => {
          if (err.status == 404) {
            this.notificationMessage.error(
              this.literal.transform("label.file.delete.error")
            );
            return EMPTY;
          } else return throwError(() => err);
        })
      )
      .subscribe((res) => {
        this.notificationMessage.success(res);
        this.control.reset();
        this.control?.markAsDirty();
        this.updated.emit();
      });
  }

  previewFile() {
    const file = this.control.value;
    this.documentService
      .filePreviewUrl(file?.randomSerialNumber)
      .pipe(
        catchError((err) => {
          this.notificationMessage.error(
            this.literal.transform("label.success.PreviewDocumentNotAvailable")
          );
          return EMPTY;
        })
      )
      .subscribe((res) => this.onFilePreview(res, file?.fileName));
  }

  onFilePreview(URL, fileName) {
    this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: "100vw",
      maxHeight: "100vh",
      height: "100%",
      width: "100%",
      disableClose: true,
      data: {
        previewURLString: URL,
        fileName: fileName,
      },
    });
  }
}
