<div class="utility-labels-container">

  <div class="p-percent-1" fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="4px">

    <div class="search-field">
      <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
        <mat-icon matIconPrefix>search</mat-icon>
        <input matInput aria-label="search-user-input-field"
          (keyup)="applyFilter($event.target.value)" autocomplete="off" placeholder="Search label"
          #input [(ngModel)]="searchKey" />
      </mat-form-field>
    </div>

    <button aria-label="create-user-btn" mat-icon-button type="button"
      class="colored-icon-button large-icon-button" (click)="onCreate()" matTooltipPosition="above"
      matTooltipClass="accent-tooltip" matTooltip="Create"
      *ifHasPermission="LABEL_RESOURCE.Lables; scope:'CHANGE'">
      <span class="material-symbols-outlined">add</span>
    </button>

  </div>

  <div class="labels-table-container">

    <table mat-table [dataSource]="dataSource" class="mat-mdc-table-wrap-text m-b-25">
      <!-- Label -->
      <ng-container matColumnDef="label">
        <th class="width-30" mat-header-cell *matHeaderCellDef>
          {{"label.theader.labelName" | literal}}
        </th>
        <td class="width-30" mat-cell *matCellDef="let element">
          <div class="label-name" [class.off]="getcolor(element.colorName)"
            [ngStyle]="{ 'background-color': element.colorName }">
            <span [class.default]="getdefault(element.colorName)">{{ element.labelName }}
            </span>
          </div>
        </td>
      </ng-container>

      <!-- created by -->
      <ng-container matColumnDef="created">
        <th class="width-30" mat-header-cell *matHeaderCellDef>
          {{"label.theader.createdDate" | literal}}
        </th>
        <td class="width-30" mat-cell *matCellDef="let element">
          {{ element.createdDate | date }} by {{ element.createdBy }}
        </td>
      </ng-container>

      <!-- date -->
      <ng-container matColumnDef="updated">
        <th class="width-20" mat-header-cell *matHeaderCellDef>
          {{"label.theader.updatedDate" | literal}}
        </th>
        <td class="width-20" mat-cell *matCellDef="let element">
          <span *ngIf="!element.modifiedDate"> - </span>
          <span *ngIf="element.modifiedDate"> {{ element.modifiedDate | date}} by {{
            element.modifiedBy }}
          </span>
        </td>
      </ng-container>

      <!-- edit -->
      <ng-container matColumnDef="edit">
        <th class="width-10" mat-header-cell *matHeaderCellDef></th>
        <td class="width-10" mat-cell *matCellDef="let row; let i = index">

          <button aria-label="edit-label-btn" mat-icon-button (click)="editRows(row)">
            <span class="material-symbols-outlined">edit</span>
          </button>

          <button aria-label="delete-label-btn" mat-icon-button class=""
            (click)="openDeleteDialog(row)">
            <span class="material-symbols-outlined warn-icon-button">delete</span>
          </button>

        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
  <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
    <mat-paginator [pageSizeOptions]="[8, 10, 25, 100]" [pageSize]="50">
    </mat-paginator>
  </div>

  <div class="no-records-found mt-1" *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner">
  </div>

</div>
