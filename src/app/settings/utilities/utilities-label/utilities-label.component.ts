import { Component, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { CreateLabelComponent } from "../../application-labels/create-label/create-label.component";
import { MatDialog } from "@angular/material/dialog";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { EditLabelComponent } from "../../application-labels/edit-label/edit-label.component";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ToasterService } from "src/app/common/toaster.service";
import { AccessControlService } from "../../roles-actions-configuration/access-control.service";
import JsonData from "src/assets/data.json";

@Component({
  selector: "app-utilities-label",
  standalone: false,
  templateUrl: "./utilities-label.component.html",
  styleUrl: "./utilities-label.component.scss",
})
export class UtilitiesLabelComponent {
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  dataSource: MatTableDataSource<any>;
  send_date = new Date();
  formattedDate: any;
  JsonData: any;

  displayedColumns: string[] = ["label", "created", "updated", "edit"];
  displayedColumnsResponsive: string[] = ["label", "edit"];

  get LABEL_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  pagelength;
  activePageDataChunk: MatTableDataSource<unknown>;

  pageEvent: PageEvent;
  searchKey: string;

  constructor(
    private readonly dialog: MatDialog,
    public service: ApplicationLabelService,
    public notificationMessage: ToasterService,
    private readonly accessControlService: AccessControlService
  ) {
    this.send_date.setMonth(this.send_date.getMonth() + 8);
    this.formattedDate = this.send_date.toISOString().slice(0, 10);
  }

  ngOnInit(): void {
    if (
      !this.accessControlService.havePermission(
        this.LABEL_RESOURCE.Lables,
        "CHANGE"
      )
    ) {
      this.displayedColumns.pop();
    }

    this.getAllLabelColors();
  }

  /**
   * on label create api call has done with this following function.
   *
   * @memberof ApplicationLabelsComponent
   */
  onCreate() {
    const matDialogRef = this.dialog.open(CreateLabelComponent, {
      autoFocus: false,
      disableClose: true,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getAllLabelColors();
      }
    });
  }

  refreshDataTable(filterdData) {
    let data = [];

    data = filterdData;

    this.dataSource = new MatTableDataSource(data);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.searchKey = "";
    this.showNoRecordsAvailbleMessage = true;

    if (data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.searchKey = "";
      this.showNoRecordsAvailbleMessage = false;
    }
  }

  getAllLabelColors() {
    this.service.getLabelColors().subscribe((res: any) => {
      this.service.arrayLabel = res;
      this.service.addLabelObj.subscribe(
        (response) => {
          this.showLoaderSpinner = false;

          this.refreshDataTable(this.service.arrayLabel);
        },
        (error) => {
          this.refreshDataTable([]);
          this.showLoaderSpinner = false;
          this.showNoRecordsAvailbleMessage = true;
        }
      );
    });
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return true;
    }
  }

  getdefault(color) {
    if (color == "") {
      return true;
    }
  }

  /**
   *
   *
   * @param {*} row
   * @memberof ApplicationLabelsComponent
   */
  editRows(row) {
    const matDialogRef = this.dialog.open(EditLabelComponent, {
      data: row,
      disableClose: true,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getAllLabelColors();
      }
    });
  }

  openDeleteDialog(row) {
    let buttonList;
    buttonList = [
      { value: true, label: "Yes,Delete" },
      { value: false, label: "Cancel" },
    ];

    const message = "Are you sure you want to delete this Label ?";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },

      // width: "25%",
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteRows(row);
      }
    });
  }

  deleteRows(row) {
    this.service.deleteLabelColor(row.labelId).subscribe(
      (res) => {
        this.getAllLabelColors();
        this.notificationMessage.success(JsonData["label.success.DeleteLabel"]);
      },
      (err) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.dataSource.filter = filterValue;
  }
}
