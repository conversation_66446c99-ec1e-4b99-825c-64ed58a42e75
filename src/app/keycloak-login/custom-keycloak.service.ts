import { HttpClient, HttpParams } from "@angular/common/http";
import { Inject, Injectable, OnDestroy } from "@angular/core";
import { KeycloakService } from "keycloak-angular";
import { map } from "rxjs/operators";
import { SessionTimeoutWarningToasterComponent } from "../shared-module/session-timeout-warning-toaster/session-timeout-warning-toaster.component";
import { MatSnackBar } from "@angular/material/snack-bar";

@Injectable({
  providedIn: "root",
})
export class CustomKeycloakService implements OnDestroy {
  sessionTimeOutTimer: NodeJS.Timeout;
  realm = localStorage.getItem("tenantIdentifier");

  constructor(
    private keycloak: KeycloakService,
    private snackBar: MatSnackBar,
    private http: HttpClient,
    @Inject("authUrl") private authUrl
  ) {}

  ngOnDestroy(): void {
    clearInterval(this.sessionTimeOutTimer);
  }

  startRefreshTimer() {
    const buffer_time_secs = 120;
    const configured_token_exp =
      this.keycloak.getKeycloakInstance().tokenParsed.exp;
    const exp_time = configured_token_exp * 1000 - new Date().getTime();
    const warning_trigger_time = exp_time - buffer_time_secs * 1000;

    if (warning_trigger_time > 0) {
      this.sessionTimeOutTimer = setTimeout(() => {
        const snackBarRef = this.snackBar.openFromComponent(
          SessionTimeoutWarningToasterComponent,
          {
            data: {
              message: "Session is going to expire in ",
              duration: buffer_time_secs,
            },
            horizontalPosition: "right",
            verticalPosition: "top",
            panelClass: ["warning", "infoList"],
          }
        );

        snackBarRef.afterDismissed().subscribe((resp) => {
          if (resp.dismissedByAction) {
            this.startRefreshTimer();
          }
        });
      }, warning_trigger_time);
    } else {
      this.keycloak.logout();
    }
  }

  makeAuthenticatedRequest() {
    const payload = new HttpParams()
      .set("grant_type", "urn:ietf:params:oauth:grant-type:uma-ticket")
      .set("audience", "identity");
    this.keycloakAuth(payload).subscribe({
      next: (res: any) => {
        localStorage.setItem("accessToken", `Bearer ${res["access_token"]}`);
      },
      error: () => {
        this.keycloak.clearToken();
      },
    });
  }

  keycloakAuth(payload) {
    return this.http
      .post(
        `${this.authUrl}/realms/` +
          this.realm +
          `/protocol/openid-connect/token`,
        payload,
        {}
      )
      .pipe(
        map((responseData: any) => {
          return responseData;
        })
      );
  }

  keycloakRefreshSession(payload) {
    return this.http
      .post(
        `${this.authUrl}/realms/` +
          this.realm +
          `/protocol/openid-connect/token`,
        payload,
        {}
      )
      .pipe(
        map((responseData: any) => {
          return responseData;
        })
      );
  }
}
