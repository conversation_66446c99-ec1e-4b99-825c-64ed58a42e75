import { Component, OnInit } from "@angular/core";

import * as keycloak from "keycloak-js";
import { KeycloakEventType, KeycloakService } from "keycloak-angular";

import { HttpParams } from "@angular/common/http";
import { Router } from "@angular/router";
import { CustomKeycloakService } from "./custom-keycloak.service";
import { IdentityService } from "../shared-service/identity.service";
import { DataSharingService } from "../common/dataSharing.service";
import { PageLayoutService } from "../shared-service/page-layout.service";
import { LoaderService } from "../shared-service/loader.service";
import { AccessControlService } from "../settings/roles-actions-configuration/access-control.service";
import { ThemeService } from "../theme.service";
import { NewUiConfirmationDialogComponent } from "../dialogs/new-ui-confirmation-dialog/new-ui-confirmation-dialog.component";
import { MatDialog } from "@angular/material/dialog";
import { AuthService } from "../guard/auth.service";
import { CopyConfigurationService } from "../shared-service/copy-configuration.service";

@Component({
  selector: "app-keycloak-login",
  templateUrl: "./keycloak-login.component.html",
  styleUrls: ["./keycloak-login.component.scss"],
})
export class KeycloakLoginComponent implements OnInit {
  public isLoggedIn = false;
  public userProfile: keycloak.KeycloakProfile | null = null;
  public userRoles: string[];
  public Username: string;
  private clearSessionData = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("accessTokenExpiration");
    localStorage.removeItem("user");
    localStorage.removeItem("userFullName");
  };

  constructor(
    private router: Router,
    private customKeycloakService: CustomKeycloakService,
    private dataSharingService: DataSharingService,
    private accessControlService: AccessControlService,
    private pageLayoutService: PageLayoutService,
    private loader: LoaderService,
    private identityService: IdentityService,
    private readonly keycloak: KeycloakService,
    private themeService: ThemeService,
    private dialog: MatDialog,
    private authenticationService: AuthService,
    private copyConfigurationService: CopyConfigurationService
  ) {
    this.clearSessionData();
  }

  async ngOnInit() {
    this.isLoggedIn = await this.keycloak.isLoggedIn();

    if (this.isLoggedIn) {
      this.loader.show();
      this.userProfile = await this.keycloak.loadUserProfile();
      this.makeAuthenticatedRequest();
    }
  }

  makeAuthenticatedRequest() {
    const payload = new HttpParams()
      .set("grant_type", "urn:ietf:params:oauth:grant-type:uma-ticket")
      .set("audience", "identity");

    this.customKeycloakService.keycloakAuth(payload).subscribe((res: any) => {
      localStorage.setItem("accessToken", `Bearer ${res["access_token"]}`);

      this.settingUpInitialData();

      this.keycloak.keycloakEvents$.subscribe({
        next: (event) => {
          if (
            event.type == KeycloakEventType.OnTokenExpired ||
            event.type === KeycloakEventType.OnAuthRefreshError
          ) {
            localStorage.removeItem("accessToken");
          } else if (event.type === KeycloakEventType.OnAuthLogout) {
            this.clearSessionData();
          } else if (event.type == KeycloakEventType.OnAuthRefreshSuccess) {
            this.customKeycloakService.makeAuthenticatedRequest();
          }
        },
      });
    });
  }

  settingUpInitialData() {
    this.dataSharingService.currentUserName = this.userProfile.username;

    localStorage.setItem("currency", "INR");
    localStorage.setItem("user", this.userProfile.username);
    localStorage.setItem(
      "userFullName",
      this.userProfile.firstName + " " + this.userProfile.lastName
    );
    this.getLoggedInUserDetails();
  }

  getLoggedInUserDetails() {
    const access_token = localStorage.getItem("accessToken");
    const parsed_token = JSON.parse(atob(access_token.split(".")[1]));
    localStorage.setItem("userRole", parsed_token.realm_access.roles);
    this.getAllConfigurationDetails();
    this.identityService.getAllUser().subscribe((res) => {
      const usersList = res;
      localStorage.setItem("userList", usersList);
      this.dataSharingService.setUserList(usersList);
    });
  }

  getAllConfigurationDetails() {
    this.loader.show();

    this.pageLayoutService
      .getAllConfigurationDetails()
      .subscribe((res: any) => {
        this.loader.hide();

        if (res) {
          const sidebarItems = res.find(
            (config) => config.configIdentifier == "SIDE_BAR"
          );
          this.dataSharingService.setSidebarItems(sidebarItems);
          localStorage.setItem("sidebarItems", JSON.stringify(sidebarItems));
          const sectorList = res.find(
            (config) => config.configIdentifier == "SECTOR_SUBSECTOR_LIST"
          );
          this.dataSharingService.configurablePickListData = sectorList;
          localStorage.setItem("sectorList", JSON.stringify(sectorList));

          const currencyList = res.find(
            (config) => config.configIdentifier == "CURRENCY_LIST"
          );
          this.dataSharingService.setcurrencyItems(currencyList);
          localStorage.setItem("currencyList", JSON.stringify(currencyList));

          const address = res.find(
            (config) => config.configIdentifier == "ADDRESS"
          );
          const ausurl = address?.configDetails?.find(
            (ausAdd) => ausAdd.country == "Australia"
          );
          localStorage.setItem("ausUrl", JSON.stringify(ausurl?.url));

          const uiLayoutConfig = res.find(
            (config) => config.configIdentifier == "UI_LAYOUT"
          );
          const useNewThemeFromConfig = uiLayoutConfig?.configDetails?.[0] || {
            useNewTheme: undefined,
          };
          localStorage.setItem(
            "useNewThemeFromConfig",
            JSON.stringify(useNewThemeFromConfig)
          );
          localStorage.setItem("closeUserToolTip", "true");

          const envProperties = res.find(
            (config) => config.configIdentifier == "ENV_PROPERTIES"
          );

          if (envProperties && envProperties.configDetails) {
            this.copyConfigurationService.setEnvConfigDetails(envProperties);
          }
        }
        this.accessControlService.updatePermissions.next("");
        if (!localStorage.getItem("user-theme")) {
          const matDialogRef = this.dialog.open(
            NewUiConfirmationDialogComponent,
            {
              disableClose: true,
              width: "70%",
              hasBackdrop: true,
              backdropClass: "custom-overlay-container",
            }
          );
          matDialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.themeService.useNewTheme = true;
            } else {
              this.themeService.useNewTheme = false;
            }
          });
        }
        if (this.authenticationService.redirectUrl) {
          this.router.navigateByUrl(this.authenticationService.redirectUrl);
        } else {
          this.router.navigate([`/dashboards/${btoa("0")}`], {
            state: { navigatedFromLogin: true },
          }); //passing 0 to naviagte on first tab by default
        }
      });
  }

  setConfigDetails(res) {
    const data = res?.find((ele) => ele.configIdentifier == "PROJECT_CONFIG");

    const projectTitle = data?.configDetails.find(
      (config) => config.configName == "BUSINESS_TITLE"
    );


    localStorage.setItem("projectTitle", projectTitle.displayName);
    this.dataSharingService.setProjectTitle(projectTitle.displayName);
    localStorage.setItem("projectLogo", projectTitle.logo);
    this.dataSharingService.setProjectLogo(projectTitle.logo);
  }
}
