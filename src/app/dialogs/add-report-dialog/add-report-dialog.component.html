<div *ngIf="!useNewThemeUI" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55" fxLayout="row wrap"
    fxLayoutGap="4px">
    <div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
      fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>{{ "label.header.addReport" | literal }}</h2>
        </div>
        <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">
          <button mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>


      <form autocomplete="off" [formGroup]="addReportForm" novalidate
        class="mt-30 split-form display mt-0" fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

          <div fxLayout="row wrap" fxLayoutGap="4px">
            <mat-form-field class="width-100">

              <mat-label>{{"label.field.selectType" | literal}}</mat-label>
              <mat-select formControlName="reportType" required
                (selectionChange)="selectList($event.value)">
                <ng-container *ngFor="let type of types">
                  <mat-option [value]="type">
                    {{ type }}
                  </mat-option>
                </ng-container>

              </mat-select>
            </mat-form-field>


            <mat-form-field class="width-100"
              *ngIf="dealOption === true && this.addReportForm.controls.reportType.value === 'Business process'">
              <mat-label> {{"label.label.businessProcessList" | literal}}</mat-label>
              <mat-select required (selectionChange)="checkList($event.value)" formControlName="id">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Search Business Process"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterBuisnessProcess($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let list of getList(id)"
                  [value]="list">{{list.name}}</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field class="width-100"
              *ngIf="dealOption === false && this.addReportForm.controls.reportType.value === 'Entity type'">
              <mat-label>{{"label.label.entityList" | literal}}</mat-label>

              <mat-select formControlName="id" required (selectionChange)="checkList($event.value)">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Search Entity"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterBuisnessProcess($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let list of getList(id)"
                  [value]="list">{{list.entityName}}</mat-option>
              </mat-select>
            </mat-form-field>




            <mat-form-field class="width-100">
              <mat-label>{{"label.header.reportName" | literal}}</mat-label>
              <input class="width-100" required formControlName="reportName" matInput
                autocomplete="off" />
              <mat-error
                *ngIf="addReportForm.controls.reportName.hasError('pattern')">{{"label.materror.nameValidation"
                |
                literal}}</mat-error>
            </mat-form-field>

            <mat-form-field *ngIf="this.addReportForm.controls.id.value"
              class="reportQuery width-100">
              <span matPrefix>{{reportQueryString}}</span>
              <textarea class="width-100" matInput formControlName="reportQuery">
            </textarea>
            </mat-form-field>
          </div>
        </div>

      </form>
    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button mat-raised-button class="green" type="submit"
        (click)="submit()">{{"label.button.create" |
        literal}}</button>
    </div>
  </mat-card-footer>
</div>

<div *ngIf="useNewThemeUI" class="newUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55" fxLayout="row wrap">

    <div class="add-report-dialog-container" fxFlex="100%">
      <div class="add-report-dialog-container-1" fxLayout="row wrap"
        fxLayoutAlign="space-between center">
        <div class="add-report-dialog-container-1-header" fxFlex="row wrap"
          fxLayoutAlign="start center">
          <span>{{ "label.header.addReport" | literal }}</span>
        </div>
        <div class="adv-search-report-container-1-close">
          <button mat-icon-button (click)="closeDialog()">
            <span class="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>

      <div class="add-report-dialog-container-2 m-t-30">
        <div class="add-report-dialog-sub-container">
          <form class="split-form" autocomplete="off" [formGroup]="addReportForm" novalidate
            fxLayout="row wrap">
            <div class="add-report-dialog-sub-container-main" fxFlex="100%">
              <div class="add-report-dialog-sub-container-select" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-form-field class="full-width">
                  <mat-label>{{"label.field.selectType" | literal}}</mat-label>
                  <mat-select formControlName="reportType" required
                    (selectionChange)="selectList($event.value)">
                    <ng-container *ngFor="let type of types">
                      <mat-option [value]="type">
                        {{ type }}
                      </mat-option>
                    </ng-container>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="add-report-dialog-sub-container-bp" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-form-field class="full-width"
                  *ngIf="dealOption === true && this.addReportForm.controls.reportType.value === 'Business process'">
                  <mat-label> {{"label.label.businessProcessList" | literal}}</mat-label>
                  <mat-select required (selectionChange)="checkList($event.value)"
                    formControlName="id">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="Search Business Process"
                        noEntriesFoundLabel="No matching found" ngModel
                        (ngModelChange)="filterBuisnessProcess($event)"
                        [ngModelOptions]="{standalone: true}">
                      </ngx-mat-select-search>
                    </mat-option>
                    <mat-option *ngFor="let list of getList(id)"
                      [value]="list">{{list.name}}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="add-report-dialog-sub-container-ent" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-form-field class="full-width"
                  *ngIf="dealOption === false && this.addReportForm.controls.reportType.value === 'Entity type'">
                  <mat-label>{{"label.label.entityList" | literal}}</mat-label>
                  <mat-select formControlName="id" required
                    (selectionChange)="checkList($event.value)">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="Search Entity"
                        noEntriesFoundLabel="No matching found" ngModel
                        (ngModelChange)="filterBuisnessProcess($event)"
                        [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option *ngFor="let list of getList(id)"
                      [value]="list">{{list.entityName}}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="add-report-dialog-sub-container-name" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-form-field class="full-width">
                  <mat-label>{{"label.header.reportName" | literal}}</mat-label>
                  <input required formControlName="reportName" matInput autocomplete="off" />
                  <mat-error
                    *ngIf="addReportForm.controls.reportName.hasError('pattern')">{{"label.materror.nameValidation"
                    |
                    literal}}</mat-error>
                </mat-form-field>
              </div>

              <div class="add-report-dialog-sub-container-id" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-form-field *ngIf="this.addReportForm.controls.id.value" class="full-width">
                  <span matPrefix>{{reportQueryString}}</span>
                  <textarea class="width-100" matInput formControlName="reportQuery">
                </textarea>
                </mat-form-field>
              </div>
            </div>
          </form>
        </div>
      </div>

    </div>

  </mat-dialog-content>

  <mat-card-footer>
    <div class="add-report-dialog-container-footer" fxLayout="row" fxLayoutAlign="center center">
      <button color="primary" mat-raised-button type="submit" (click)="submit()">
        {{"label.button.create" | literal}}
      </button>
    </div>
  </mat-card-footer>

</div>
