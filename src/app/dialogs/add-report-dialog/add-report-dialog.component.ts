import { Component, Inject, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { ReplaySubject } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import JsonData from "src/assets/data.json";
import { ConfirmationDialogComponent } from "../confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";

@Component({
  selector: "app-add-report-dialog",
  templateUrl: "./add-report-dialog.component.html",
  styleUrls: ["./add-report-dialog.component.scss"],
})
export class AddReportDialogComponent implements OnInit {
  addReportForm: UntypedFormGroup;
  selectedType: any;
  selectedTypeList: any;
  types: any[];
  id: any;
  entityList: any;
  dealOption: any;
  reportConfigDetails: any;
  reports: any;
  details: any;
  reportQueryString: any;
  selectedBusinessProcessId: any;
  selectedEntityId: any;
  useNewThemeUI: any;

  constructor(
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<AddReportDialogComponent>,
    private fb: UntypedFormBuilder,
    public notificationMessage: ToasterService,
    public errorService: ErrorService,
    public entityService: EntityService,
    public dataSharingService: DataSharingService,
    public pageLayoutService: PageLayoutService,
    public businessProcessService: BusinessProcessService,
    protected themeService: ThemeService,
    private validationService: ValidationErrorMessageService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.reportConfigDetails = data;

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.addReportForm = this.fb.group({
      reportType: ["", [Validators.required]],
      id: ["", [Validators.required]],
      reportName: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationService.nameRegex),
        ],
      ],
      reportQuery: [""],
    });
    this.types = ["Business process", "Entity type"];
  }
  closeDialog() {
    this.dialogRef.close(false);
  }
  selectList(event) {
    this.addReportForm.controls.reportType.disable();
    this.dealOption = undefined;
    this.reportQueryString = null;
    this.addReportForm.controls.reportQuery.setValue("");
    if (event == "Business process") {
      this.businessProcessService.getAllBusinessProcessList().subscribe(
        (response) => {
          this.id = response;
          this.dealOption = true;
          this.addReportForm.controls.reportType.enable();
        },
        (err) => {
          const errors = this.errorService.ErrorHandling(err);
          this.notificationMessage.error(errors);
        }
      );
    } else {
      this.dealOption = false;
      this.entityService.getExtentionsList().subscribe((res) => {
        this.entityList = res;
        this.id = [...this.entityList];
        this.addReportForm.controls.reportType.enable();
      });
    }
  }

  checkList(event) {
    if (this.dealOption) {
      this.reportQueryString = 'businessProcessName:"' + event.name + '"';
    } else {
      this.reportQueryString = 'entityType:"' + event.entityName + '"';
    }
  }

  submit() {
    this.details = this.addReportForm.value;
    this.details.reportQuery =
      this.reportQueryString + this.addReportForm.controls.reportQuery.value;
    if (this.addReportForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    const searchValue = this.addReportForm.controls.reportName.value.trim();
    const arrayOfObjects = this.reportConfigDetails.configDetails;

    if (Object.entries(this.reportConfigDetails).length !== 0) {
      const isValuePresent = arrayOfObjects.some(
        (obj) =>
          obj.reportName.toLowerCase().trim() === searchValue.toLowerCase()
      );
      if (isValuePresent) {
        this.notificationMessage.error(
          "The report name " + searchValue + " is already exist"
        );
        return;
      }
    }

    if (this.addReportForm.controls.reportType.value == "Entity type") {
      if (!this.addReportForm.controls.reportQuery.value.trim()) {
        let buttonList;
        if (this.themeService.useNewTheme) {
          buttonList = [
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ];
        } else {
          buttonList = [
            { value: true, label: "YES", color: "green" },
            { value: false, label: "NO", color: "red" },
          ];
        }

        const message =
          "Report query contains default criteria only, which can result in a large data set being returned, Do you want to continue? ";
        const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
          data: {
            message: message,
            buttonList: buttonList,
          },
        });
        matDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.createReport();
          } else {
            this.notificationMessage.error(
              "Please fill the required report query"
            );
            return;
          }
        });
      } else {
        this.createReport();
      }
    }

    if (this.addReportForm.controls.reportType.value == "Business process") {
      if (!this.addReportForm.controls.reportQuery.value.trim()) {
        let buttonList;
        if (this.themeService.useNewTheme) {
          buttonList = [
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ];
        } else {
          buttonList = [
            { value: true, label: "YES", color: "green" },
            { value: false, label: "NO", color: "red" },
          ];
        }
        const message =
          "Report query contains default criteria, which can result in a large data set being returned, Do you want to continue? ";
        const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
          data: {
            message: message,
            buttonList: buttonList,
          },
        });
        matDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.createReport();
          } else {
            this.notificationMessage.error(
              "Please fill the required report query"
            );
            return;
          }
        });
      } else {
        this.createReport();
      }
    }
  }

  createReport() {
    if (this.dealOption) {
      this.details.name = this.addReportForm.controls.id.value?.name;
    } else {
      this.details.name = this.addReportForm.controls.id.value?.entityName;
    }
    this.details.id = this.addReportForm.controls.id.value?.id;
    this.details.selectedColumns = [];
    if (Object.entries(this.reportConfigDetails).length !== 0) {
      const data = Object.assign({}, this.details);
      this.reportConfigDetails.configDetails.push(data);
      const id = this.reportConfigDetails?.id;

      this.pageLayoutService
        .updateConfigurationDetailsByIdentifier(id, this.reportConfigDetails)
        .subscribe(
          (res: any) => {
            this.notificationMessage.success(
              JsonData["label.success.addReport"]
            );
          },
          (error) => {
            const errors = this.errorService.ErrorHandling(error);
            this.notificationMessage.error(errors);
          }
        );
    } else {
      const data = Object.assign({}, this.details);

      const payload = {
        configIdentifier: "REPORT_CONFIG",
        selectedColumns: [],
        configDetails: [data],
      };
      this.pageLayoutService.addConfigurationDetail(payload).subscribe(
        (data) => {
          this.notificationMessage.success(JsonData["label.success.addReport"]);
        },
        (error) => {
          const errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
        }
      );
    }

    this.dialogRef.close(this.details);
  }

  public filteredBuisnessProcessList: ReplaySubject<any[]> = new ReplaySubject<
    any[]
  >(1);
  filteredBPList: any = [];
  searchedBP: any;

  filterBuisnessProcess(event) {
    this.searchedBP = event;
  }

  getList(list) {
    if (this.searchedBP && this.dealOption) {
      return this.id
        .slice()
        .filter((list) =>
          list.name.toLowerCase().includes(this.searchedBP.toLowerCase())
        );
    } else if (this.searchedBP && this.dealOption == false) {
      return this.id
        .slice()
        .filter((list) =>
          list.entityName.toLowerCase().includes(this.searchedBP.toLowerCase())
        );
    } else {
      return this.id;
    }
  }
}
