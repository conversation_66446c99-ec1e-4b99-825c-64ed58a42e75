<div>

  <div class="fullScreenEditor">
    <div class="previewTitle">

      <div class="formly-single-align-100">
        <h2>{{title}}</h2>
      </div>
      <div>

        <mat-icon class="pointer" (click)="close()">close</mat-icon>

      </div>

    </div>

    <div class="">
      <ckeditor class=" fullScreenEditor" [editor]="Editor" [config]="config" [(ngModel)]="content"
              [formControl]="contentDetails" ngDefaultControl></ckeditor>
    </div>

  </div>
  <div mat-dialog-actions align="center">

    <div>
      <button mat-raised-button type="button" class="green" (click)="onSave()">
        SAVE
      </button>
      <button mat-raised-button type="button" class="red" (click)="close()">
        CLOSE
      </button>
    </div>
  </div>
