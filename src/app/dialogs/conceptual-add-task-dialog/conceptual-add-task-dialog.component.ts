import { Component, OnInit } from '@angular/core';
import { DataSharingService } from 'src/app/common/dataSharing.service';
// import { scrollingTabs } from 'src/app/dummy-data';
import {MatSlideToggleChange} from '@angular/material/slide-toggle';


@Component({
  selector: 'app-conceptual-add-task-dialog',
  templateUrl: './conceptual-add-task-dialog.component.html',
  styleUrls: ['./conceptual-add-task-dialog.component.scss']
})
export class ConceptualAddTaskDialogComponent implements OnInit {
  selectedModuleName: any;
  allModules: any;
  generalDetail: any[] = [];
  router: any;
  addTaskComponentName: any;
  isGeneric: any;
  selectedInvestmentId: any;
  selectedModuleIndex: any;
  selectedApplicationsData:any;
  selectedDealName:any;


  constructor(private dataSharingService: DataSharingService, ) {

    this.selectedApplicationsData = this.dataSharingService.selectedApplicationData;

    this.selectedDealName = this.selectedApplicationsData?.dealCustomerList[0]?.customerName;
    this.selectedInvestmentId = this.selectedApplicationsData?.dealCustomerList[0]?.id;
    // this.allModules = this.dataSharingService.getAllScrollingTabList() ?
    //   this.dataSharingService.getAllScrollingTabList()
    //   : scrollingTabs;

    if (this.dataSharingService.data &&
      this.dataSharingService.data.selectedTabIndex
      && this.dataSharingService.data.selectedTabIndex != 0) {

      this.selectedModuleName = this.allModules.filter(item =>
        item.index === this.dataSharingService.data.selectedTabIndex
      )[0].label

      this.selectedModuleIndex = this.allModules.filter(item =>
        item.index === this.dataSharingService.data.selectedTabIndex
      )[0].index

    }

    if(this.selectedDealName){
      this.generalDetail[this.generalDetail.length] = {
        name: "DEAL",
        value: this.selectedDealName,
      }
      if (this.selectedModuleName) {
        this.generalDetail[this.generalDetail.length] = {
          name: "MODULE",
          value: this.selectedModuleName,
        }
      }
    }

  }

  ngOnInit() {
  }


  changeTaskForm(event: MatSlideToggleChange) {

    this.isGeneric = event.checked;
    if (this.isGeneric) {
      this.generalDetail = [];
      // console.log('this.isGeneric', this.generalDetail);
    } else {
      if(this.selectedDealName){
        this.generalDetail[0] = {
          name: "DEAL",
          value: this.selectedDealName,
        }
        if (this.selectedModuleName) {
          this.generalDetail[1] = {
            name: "MODULE",
            value: this.selectedModuleName,
          }
        }
      }

      // console.log('this.isGeneric', this.generalDetail);
    }
  }

}
