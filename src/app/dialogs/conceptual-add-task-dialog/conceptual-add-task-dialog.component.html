<app-basic-add-task [generalDetail]="generalDetail">
  <div toggleAddtaskForm>
    <mat-slide-toggle matTooltip="Switch to generic form" class="matBtn" [checked]="isGeneric"
      (change)="changeTaskForm($event)"></mat-slide-toggle>
  </div>
  <div generalDetails *ngIf="!isGeneric">
    <div fxLayout="row wrap" fxLayoutGap="4px" class="px-2">
      <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%">
        <p class="labelFont">{{"label.relatedTo"|literal}}</p>
      </div>
      <div fxFlex="50%" fxFlex.md="500%" fxFlex.xs="80%" fxFlex.sm="80%" class="padding-top-1" >

        <mat-chip-grid #chipList>
          <mat-chip-row class="relatedToLabel "  color="warn" selected >
            {{selectedDealName}}</mat-chip-row>
            <mat-chip-row *ngIf="selectedModuleName"  class="relatedToLabel " color="accent" selected>
              {{selectedModuleName}}
          </mat-chip-row>
          <input [matChipInputFor]="chipList">
        </mat-chip-grid>
      
      </div>
    </div>
  </div>
</app-basic-add-task>