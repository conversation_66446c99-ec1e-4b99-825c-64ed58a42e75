import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA , MatDialog} from '@angular/material/dialog';
import { DealService } from '../../shared-service/deal.service';
import { DataSharingService } from '../../common/dataSharing.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
@Component({
  selector: 'app-bulk-movement-details-dialog',
  templateUrl: './bulk-movement-details-dialog.component.html',
  styleUrls: ['./bulk-movement-details-dialog.component.scss']
})
export class BulkMovementDetailsDialogComponent implements OnInit {
  selectedDetails: any;
  bulkMovementTableDetails: any;
  displayedColumns = [ 'dealIdentifier', 'createdBy', 'createdDate', 'status', 'exceptionDetails' ]
  showTableSpinner: boolean;
  totalCountOflength: any;
  pageIndex: any = 0;
  pageSize: any = 25;
  sortBy: any ='desc'
  sortingKey : any = 'dealIdentifier';
  dataSource: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  sortDirection: any =  'desc';
  constructor(public dialogRef: MatDialogRef<BulkMovementDetailsDialogComponent>, private dealService: DealService,  @Inject(MAT_DIALOG_DATA) public data, public dialog: MatDialog,
  public dataSharingService: DataSharingService) { }

  ngOnInit() {
    
     this.selectedDetails = this.data.bulkMovementDetails
    this.fetchData();
 

  }

  fetchData(){
    this.showTableSpinner = true;
    this.dealService.fetchBulkMovementById(this.selectedDetails.id, this.pageIndex, this.pageSize,  this.sortBy, this.sortingKey).subscribe((res: any) => {
     

      this.bulkMovementTableDetails = res.dealEventDetailsList.content;
      this.totalCountOflength = res.dealEventDetailsList.totalElements
      this.dataList()
      this.showTableSpinner = false;
      this.selectedDetails = res;
  
    }, (err) => {
    })

  }


  dataList(){

    this.bulkMovementTableDetails = [...this.bulkMovementTableDetails]
    this.dataSource = this.bulkMovementTableDetails;
    this.dataSource.paginator = this.paginator;
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str,50);
  }

  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.fetchData()
  }

  sortData(event){

    console.log(event)
  }

  onPageRefresh(){
    this.selectedDetails = this.data.bulkMovementDetails;
    this.fetchData();
  }
}
