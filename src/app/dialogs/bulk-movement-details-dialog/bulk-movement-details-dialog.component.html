<div fxLayout="row wrap" fxLayoutGap="4px">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" *ngIf="!showTableSpinner">
    <mat-card appearance="outlined" class="overflow-auto">
      <mat-card-content>
        <mat-card appearance="outlined" class="mat-card-top-border margin-top-2 button-row">
          <mat-card-content>
            <div class="bold">
              {{selectedDetails.name}}
            </div>
            <mat-chip-option class="eligibleRecordsChip">Eligible Records :
              {{selectedDetails.recordSize}},
              Success : {{selectedDetails.success}}, Failed : {{selectedDetails.failed}}, In
              Progress : {{selectedDetails.inProgress?selectedDetails.inProgress:0
              }}</mat-chip-option>
            <br>
            <div class="mt-10">
              Selected query : {{selectedDetails.dashboardQuery.queryString}}
            </div>
          </mat-card-content>
        </mat-card>
      </mat-card-content>
    </mat-card>
  </div>
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="bulkMovementButton">
    <button mat-raised-button (click)="onPageRefresh()" type="button"
      class="blue ml-5 refreshBtnSpacing">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

    <table mat-table [dataSource]="dataSource"
      *ngIf="bulkMovementTableDetails?.length>0 && !showTableSpinner"
      class="mat-elevation-z8 width-100">
      <ng-container matColumnDef="dealIdentifier">
        <th mat-header-cell *matHeaderCellDef> Description </th>
        <td mat-cell class="" *matCellDef="let element">
          <p [matTooltip]="element?.dealIdentifier">
            {{stringWithEllipsis(element?.dealIdentifier)}}</p>
        </td>
      </ng-container>

      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef> Created By </th>
        <td mat-cell *matCellDef="let element"> {{element?.createdBy ? element?.createdBy:'-'}}</td>
      </ng-container>

      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef> Created Date </th>
        <td mat-cell *matCellDef="let element">{{ element?.createdDate| date }} </td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element">{{ element?.status ? element?.status : '-' }} </td>
      </ng-container>

      <ng-container matColumnDef="exceptionDetails">
        <th mat-header-cell *matHeaderCellDef> Exceptions </th>
        <td mat-cell *matCellDef="let element"> <span *ngIf="element?.exceptionDetails.length>0">
            {{element.exceptionDetails}} </span> </td>
        <td mat-cell *matCellDef="let element"> <span *ngIf="!element?.exceptionDetails"> -

          </span> </td>
      </ng-container>


      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mt-5"
    *ngIf="showTableSpinner">
    <mat-spinner [diameter]=65 mode="indeterminate"></mat-spinner>
  </div>
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <mat-paginator *ngIf="bulkMovementTableDetails && !showTableSpinner"
      [length]="totalCountOflength" class="" [pageSize]="pageSize" showFirstLastButtons="true"
      [pageIndex]="pageIndex" [pageSizeOptions]="[25,50, 100, 500, 1000]"
      (page)="onPaginationChanged($event)"></mat-paginator>
  </div>

  <mat-card-actions fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
    *ngIf="!showTableSpinner">

    <button class="red buttonCenter" mat-raised-button mat-dialog-close>Close</button>

  </mat-card-actions>
</div>
