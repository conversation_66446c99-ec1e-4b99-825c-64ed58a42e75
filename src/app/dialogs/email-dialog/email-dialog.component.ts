
import { UntypedFormControl, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';

import { Router, ActivatedRoute } from '@angular/router';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { DealService } from '../../shared-service/deal.service';

@Component({
  selector: 'app-email-dialog',
  templateUrl: './email-dialog.component.html',
  styleUrls: ['./email-dialog.component.scss']
})
export class EmailDialogComponent implements OnInit {
  selectedInvestment = "";
  investmentList = [];
  investmentName = new UntypedFormControl('', Validators.required)
  constructor( private router: Router, private dataSharingService : DataSharingService, private dealService:DealService ) { 
    this.getInvestmentList()
  }

  ngOnInit() {
    this.investmentName.markAsTouched()
  }



  onChangeInvestment(value){

  }


  getInvestmentList(){
    this.dealService.getAllDealList().subscribe((res: any) => {
      if(res && res.length > 0){
        this.investmentList = res.map(item => item.dealCustomerList[0].customerName);
        if (this.router.url && this.router.url.includes('/monitor/investment')) {
          this.investmentName.setValue(this.dataSharingService.data.investmentIdentifier)
        } 
      }
    })
  }
}
