<app-create-email [investmentList] = "investmentList" [selectedInvestment]='selectedInvestment'>
    <div investmentNameList >
        <div class="matInput">
            <mat-form-field class="emailDialogInputField" appearance="fill">
                <mat-label>{{"label.field.deals"|literal}} <span class="requiredField">&nbsp;*</span></mat-label>
                <mat-select [formControl]="investmentName" (selectionChange)="onChangeInvestment($event.value)"
                    class=" mat-custom-class input emailDialogMatSelectSec" [(ngModel)]="selectedInvestment">
                    <mat-option *ngFor="let list of investmentList" [value]="list">
                        {{list}}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="investmentName.errors?.required">
                    {{"label.materror.Investment"|literal}}

</mat-error>
                <!-- [ngModelOptions]="{standalone: true}"  -->
            </mat-form-field>
        </div>
    </div>
</app-create-email>