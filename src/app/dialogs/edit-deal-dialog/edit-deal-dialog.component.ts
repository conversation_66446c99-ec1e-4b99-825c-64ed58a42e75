import {
  Component,
  ElementRef,
  Inject,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ENTER, COMMA } from "@angular/cdk/keycodes";
import {
  MatAutocompleteTrigger,
  MatAutocompleteSelectedEvent,
} from "@angular/material/autocomplete";
import { MatChipInputEvent } from "@angular/material/chips";
import { Observable } from "rxjs";
import { startWith, map } from "rxjs/operators";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
@Component({
  selector: "app-edit-deal-dialog",
  templateUrl: "./edit-deal-dialog.component.html",
  styleUrls: ["./edit-deal-dialog.component.scss"],
})
export class EditDealDialogComponent implements OnInit {
  editDealForm: UntypedFormGroup;
  selectedDealIdentifier: any;
  allLabelList = [];
  labelList = [];
  JsonData: any;
  arrayLabel = [];
  separatorKeysCodes: number[] = [ENTER, COMMA];
  filteredLabels: Observable<string[]>;
  labels = [];
  labelError = false;

  @ViewChild("labelInput") labelInput: ElementRef<HTMLInputElement>;

  constructor(
    public fb: UntypedFormBuilder,
    private readonly validationService: ValidationErrorMessageService,
    public dialogRef: MatDialogRef<EditDealDialogComponent>,
    public matDialog: MatDialog,
    public service: ApplicationLabelService,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationservice: ToasterService,
    public dealservice: DealService,
    public dataSharingService: DataSharingService,
    public themeService: ThemeService
  ) {
    this.selectedDealIdentifier = this.data?.dealdata?.dealIdentifier;

    this.InitialFormValue();
    this.getAllLabelColors();
    this.filteredLabels =
      this.editDealForm.controls.dealLabel.valueChanges.pipe(
        startWith(null),
        map((label: string | null) =>
          label ? this._filter(label) : this.arrayLabel.slice()
        )
      );
  }
  ngOnInit() {
    this.dataSharingService.selectedApplicationData;
    this.labels = this.data.dealLabelName.slice();
  }

  getAllLabelColors() {
    this.service.getLabelColors().subscribe((res: any) => {
      if (res) {
        this.arrayLabel = res;
      }
    });
  }

  async saveDealEdit() {
    this.editDealForm.markAllAsTouched();

    if (this.editDealForm.invalid) {
      this.notificationservice.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.labels.length > 5) {
      this.notificationservice.error("Select upto 5 labels for a deal.");
      return;
    }

    for (let i = 0; i < this.labels.length; i++) {
      this.labelList.push({
        labelName: this.labels[i].labelName,
        colorName: this.labels[i].colorName,
      });
    }

    const payload = {
      dealIdentifier: this.selectedDealIdentifier,
      dealLabelList: this.labelList,
    };
    this.dealservice
      .updateDealIdentifier(this.data?.dealdata?.id, payload)
      .subscribe((res) => {
        const result = {
          newDealName: this.selectedDealIdentifier,
          dealLabels: this.labelList,
        };
        this.dialogRef.close(result);

        this.notificationservice.success(
          `${this.getSidebarItembyName("Deal")} ` +
            JsonData["label.success.UpdateDetails"]
        );
      });
  }

  closeDialog() {
    this.dialogRef.close();
  }

  InitialFormValue() {
    this.editDealForm = this.fb.group({
      dealName: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationService.nameRegex),
        ],
      ],
      dealLabel: [""],

      description: [""],
    });
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  add(event: MatChipInputEvent): void {
    const value = (event.value || "").trim();

    // Add our value
    if (value) {
      // default colour is violet

      const payload = {
        labelId: null,
        createdBy: null,
        createdDate: null,
        modifiedBy: null,
        modifiedDate: null,
        labelName: value,
        colorName: "#008000",
      };
      this.service.addLabelColors(payload).subscribe((res) => {
        this.labels.push({ labelName: value, colorName: "#800080" });
        this.getAllLabelColors();
      });
    }

    // Clear the input value
    event.chipInput?.clear();

    this.editDealForm.controls.dealLabel.setValue(null);
  }

  remove(label: string): void {
    const index = this.labels.indexOf(label);

    if (index >= 0) {
      this.labels.splice(index, 1);
    }
  }

  selected(
    event: MatAutocompleteSelectedEvent,
    trigger: MatAutocompleteTrigger
  ): void {
    if (
      this.labels.filter(
        (ele) => ele.labelName == event.option.value?.labelName
      )?.length == 0
    ) {
      this.labels.push(event.option.value);
    } else {
      this.notificationservice.error(
        `The label '${
          event.option.value ? event.option.value.labelName : ""
        }' is already selected`
      );
      return;
    }

    this.labelInput.nativeElement.value = "";
    this.editDealForm.controls.dealLabel.setValue(null);
    setTimeout(function () {
      trigger.openPanel();
    }, 1);
  }

  private _filter(value: string): string[] {
    return this.arrayLabel.filter((label) =>
      label.labelName.toLowerCase().includes(value)
    );
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
}
