@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .custom-dialog-header {
    @include mat.typography-level($typography-config, 'headline-5');
  }
}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
