import { Component, OnInit } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-add-automated-report-dialog",
  templateUrl: "./add-automated-report-dialog.component.html",
  styleUrls: ["./add-automated-report-dialog.component.scss"],
})
export class AddAutomatedReportDialogComponent implements OnInit {
  reportNameFormControl: FormControl;
  useNewThemeUI: boolean;

  constructor(
    public dialogRef: MatDialogRef<AddAutomatedReportDialogComponent>,
    private route: Router,
    protected themeService: ThemeService,
    private validationService: ValidationErrorMessageService
  ) {
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.reportNameFormControl = new FormControl("", [
      Validators.required,
      Validators.pattern(this.validationService.nameRegex),
    ]);
  }

  closeDialog() {
    this.dialogRef.close(false);
  }

  openReportPage() {
    if (this.reportNameFormControl.invalid) {
      this.reportNameFormControl.markAsTouched();
      return;
    }
    this.dialogRef.close(false);
    this.route.navigate([
      "/create-automated-report",
      this.reportNameFormControl.value,
    ]);
  }
}
