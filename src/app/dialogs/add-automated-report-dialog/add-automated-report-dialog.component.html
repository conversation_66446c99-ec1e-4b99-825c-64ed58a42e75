<div *ngIf="!useNewThemeUI" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>{{ "label.header.addReport" | literal }}</h2>
        </div>
        <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">
          <button mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutGap="4px">
        <mat-form-field class="width-100">
          <mat-label>{{"label.header.automatedReportName" | literal}}</mat-label>
          <input [(formControl)]="reportNameFormControl" class="width-100" required matInput
            autocomplete="off" />
          <mat-error
            *ngIf="reportNameFormControl.hasError('pattern')">{{"label.materror.nameValidation" |
            literal}}</mat-error>
        </mat-form-field>
      </div>

    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button mat-raised-button class="green" type="submit"
        (click)="openReportPage(); closeDialog()" [disabled]="!reportNameFormControl.value">
        {{"label.button.create" | literal}}
      </button>
    </div>
  </mat-card-footer>
</div>


<div *ngIf="useNewThemeUI" class="newUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55">
    <div class="add-query-report-dialog-container" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
      fxFlex.sm="100%">
      <div class="add-query-report-dialog-container-1" fxLayout="row wrap"
        fxLayoutAlign="space-between center">
        <div class="add-query-report-dialog-container-1-header" fxFlex="row wrap"
          fxLayoutAlign="start center">
          <span>{{ "label.header.addReport" | literal }}</span>
        </div>
        <div class="add-query-report-dialog-container-1-close">
          <button mat-icon-button (click)="closeDialog()">
            <span class="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>
      <div class="add-query-report-dialog-container-2">
        <div class="add-query-report-name" fxFlex="100%" fxLayout="row"
          fxLayoutAlign="center center">
          <mat-form-field class="full-width">
            <mat-label>{{"label.header.automatedReportName" | literal}}</mat-label>
            <input [(formControl)]="reportNameFormControl" class="width-100" required matInput
              autocomplete="off" />
            <mat-error
              *ngIf="reportNameFormControl.hasError('pattern')">{{"label.materror.nameValidation" |
              literal}}</mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="add-query-report-dialog-container-footer" fxLayout="row"
      fxLayoutAlign="center center">
      <button mat-raised-button color="primary" type="submit" (click)="openReportPage();"
        [disabled]="!reportNameFormControl.value">
        {{"label.button.create" | literal}}
      </button>
    </div>
  </mat-card-footer>
</div>
