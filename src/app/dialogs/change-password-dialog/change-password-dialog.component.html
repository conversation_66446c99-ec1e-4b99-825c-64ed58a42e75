<mat-dialog-content class="mat-dialog-change-password-form">


  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" >
      <div class="changePassword">

        <div class="formly-single-align-100">
          <h2>{{"label.title.changePassword"|literal}}</h2>
        </div>
        <div>

          <mat-icon class="pointer" (click)="close()">close</mat-icon>

        </div>

      </div>
    </div>

  </div>




    <div fxLayout="row wrap" fxLayoutGap="4px">


 

      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

        <mat-form-field class="width-100">
          <mat-icon matSuffix (click)="hide = !hide">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
          <input matInput [type]="hide ? 'password' : 'text'" placeholder="New password" [formControl]="newPassword" name="newpassword"
            autocomplete="new-password ">
            <mat-error *ngIf="newPassword.errors?.required" >
              {{"label.materror.NewPassword"|literal}}
          </mat-error>
          <mat-error *ngIf="newPassword.errors?.pattern" >
            {{"label.materror.pattern"|literal}}
        </mat-error>
        </mat-form-field>


      </div>

    </div>





    <div fxLayout="row wrap" fxLayoutGap="4px" class="mt-1" >



      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

        <mat-form-field class="width-100">
          <mat-icon matSuffix (click)="hide2 = !hide2">{{hide2 ? 'visibility_off' : 'visibility'}}</mat-icon>
          <input matInput [type]="hide2 ? 'password' : 'text'" (keyup)="checkMatchPwd()" placeholder="Re-enter new password"
            [formControl]="reEnterNewPassword" name="reenternewpassword" autocomplete="new-password">
            <mat-error *ngIf="reEnterNewPassword.errors?.required" >
              {{"label.materror.ReEnterPassword"|literal}}
          </mat-error>
        </mat-form-field>

        <mat-error class="change-password-error font-12"  *ngIf="reEnterNewPassword.touched">
          {{matchError}}
        </mat-error>


      </div>

    </div>


</mat-dialog-content>

<mat-dialog-actions align="center">



  <button mat-raised-button class="green margin-left-2"   (click)="change()"
    > {{"label.button.change"|literal}} </button>


</mat-dialog-actions>