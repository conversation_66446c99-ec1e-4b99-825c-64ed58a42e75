import { Component, OnInit } from '@angular/core';
import { IdentityService } from 'src/app/shared-service/identity.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { MatDialogRef, MatDialog } from '@angular/material/dialog';
import { UntypedFormControl, Validators } from '@angular/forms';
import JsonData from 'src/assets/data.json';
@Component({
  selector: 'app-change-password-dialog',
  templateUrl: './change-password-dialog.component.html',
  styleUrls: ['./change-password-dialog.component.scss']
})
export class ChangePasswordDialogComponent implements OnInit {
  error: boolean;
  matchError: string;
  hide : boolean = true;
  hide2 : boolean = true;
  JsonData :any;
  constructor(private dialog: MatDialog, private identityService: IdentityService, private notification: ToasterService , private dialogRef: MatDialogRef<ChangePasswordDialogComponent> ) { }
  newPassword = new UntypedFormControl('', [Validators.required,  Validators.pattern('((?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,30})')
]);
  reEnterNewPassword = new UntypedFormControl('', [Validators.required]);
 
  ngOnInit() {
      this.error=true;
  }
  close(){
    this.dialog.closeAll()
  }

  checkMatchPwd(){

    if(this.newPassword.value !== this.reEnterNewPassword.value){
      this.error = true;
      this.matchError = "Password does not match"  
    } else{
      this.matchError=""
      this.error = false;
    }
    
  }


  checkPwd(str) {
    if (str.length < 8) {
      this.error= true;
        return("Password should contain min 8 characters required");
    } else if (str.length > 50) {
      this.error= true;
        return("Password should contain max 8 characters required");
    } else if (str.search(/\d/) == -1) {
      this.error= true;
        return("Password should contain atleast 1 number");
    } else if (str.search(/[a-zA-Z]/) == -1) {
      this.error= true;
        return("Password should contain atleast 1 alphabet");
    } else if (str.search(/[^a-zA-Z0-9\!\@\#\$\%\^\&\*\(\)\_\+]/) != -1) {
      this.error= true;
        return("Password should contain atleast one speacial character");
    } else{
      if(this.newPassword.value == this.reEnterNewPassword.value){
        this.error = false;    
      }
      
    }
  
}


  change(){
    this.newPassword.markAsTouched();
    this.reEnterNewPassword.markAsTouched()
    if(this.newPassword.invalid && this.reEnterNewPassword.invalid){
       return
    }
    

    let userId = localStorage.getItem('user');

    if(this.newPassword.value == this.reEnterNewPassword.value){

      let password = window.btoa(this.newPassword.value);
      let payload = {password: password}

      this.identityService.forgetPassword(userId, payload).subscribe((res) => {

        this.notification.success(JsonData["label.success.Password"]);
        this.dialogRef.close();

      }, (err) => {
        
  
  
      })


    }  else {

      


    }
        
  


  }

}
