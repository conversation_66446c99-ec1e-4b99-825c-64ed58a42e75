import { Component, Inject, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import JsonData from "src/assets/data.json";
import { ConfirmationDialogComponent } from "../confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-edit-report-dialog",
  templateUrl: "./edit-report-dialog.component.html",
  styleUrls: ["./edit-report-dialog.component.scss"],
})
export class EditReportDialogComponent implements OnInit {
  addReportForm: UntypedFormGroup;
  selectedType: any;
  selectedTypeList: any;
  types: any[];
  id: any;
  entityList: any;
  dealOption: any;
  reportConfigDetails: any;
  report: any;
  details: any;
  reportQueryString: any;
  isInputDisabled: boolean = true;
  updateButton: boolean = true;
  useNewThemeUI: any;

  constructor(
    public dialogRef: MatDialogRef<EditReportDialogComponent>,
    private fb: UntypedFormBuilder,
    private matDialog: MatDialog,
    public notificationMessage: ToasterService,
    public errorService: ErrorService,
    public entityService: EntityService,
    public dataSharingService: DataSharingService,
    public pageLayoutService: PageLayoutService,
    public businessProcessService: BusinessProcessService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    protected themeService: ThemeService
  ) {
    this.report = data.data;

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.reportConfigDetails = this.data.reportConfigDetails;
    this.addReportForm = this.fb.group({
      reportType: ["", [Validators.required]],
      id: ["", [Validators.required]],
      reportName: [{ value: "", disabled: true }, [Validators.required]],
      reportQuery: [""],
    });
    this.types = ["Business process", "Entity type"];
    this.checkList();
    if (this.report) {
      this.selectedType = this.report.reportType;
      this.addReportForm.controls["reportName"].setValue(
        this.report.reportName
      );
    }
    if (this.report.reportType == "Business process") {
      this.dealOption = true;
      this.businessProcessService
        .getAllBusinessProcessList()
        .subscribe((response) => {
          this.id = response;
          let res = this.id.filter(
            (values) => values.name === this.report.name
          );
          this.selectedTypeList = res[this.getPropertyName(res)];
          this.updateButton = false;
        });
    }
    if (this.report.reportType == "Entity type") {
      this.dealOption = false;
      this.entityService.getExtentionsList().subscribe((response) => {
        this.entityList = response;
        this.id = [...this.entityList];
        let res = this.id.filter((item) => item.id == this.report.id);
        this.selectedTypeList = res[this.getPropertyName(res)];
        this.updateButton = false;
      });
    }
  }
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }
  closeDialog() {
    this.dialogRef.close(false);
  }
  selectList() {
    this.addReportForm.controls.reportQuery.setValue("");

    if (this.report.reportType == "Business process") {
      this.businessProcessService
        .getAllBusinessProcessList()
        .subscribe((response) => {
          this.id = response;
          let res = this.id.filter((values) => values.id === this.report.id);
          this.selectedTypeList = res[this.getPropertyName(res)];
          this.dealOption = true;
          this.updateButton = false;
        });
    }
    if (this.report.reportType == "Entity type") {
      this.entityService.getExtentionsList().subscribe((res) => {
        this.entityList = res;
        this.id = [...this.entityList];
        this.dealOption = false;
      });
    }
  }

  checkList() {
    if (this.report.reportType == "Business process") {
      this.reportQueryString = "businessProcessId:" + this.report.id + " ";
      let query = this.report.reportQuery.replace(
        "businessProcessId:" + this.report.id,
        ""
      );
      this.addReportForm.controls["reportQuery"].setValue(query);
    } else {
      this.reportQueryString = 'entityType:"' + this.report.name + ' "';
      let query = this.report.reportQuery.replace(
        'entityType:"' + this.report.name + '"',
        ""
      );
      this.addReportForm.controls["reportQuery"].setValue(query);
    }

    if (
      this.reportQueryString ===
      this.addReportForm.controls["reportQuery"].value
    ) {
      this.addReportForm.controls["reportQuery"].setValue("");
    }
  }

  submit() {
    this.details = this.addReportForm.value;
    this.details.reportQuery =
      this.reportQueryString + this.addReportForm.controls.reportQuery.value;
    if (this.addReportForm.controls.reportType.value == "Entity type") {
      if (!this.addReportForm.controls.reportQuery.value.trim()) {
        let buttonList;
        if (this.themeService.useNewTheme) {
          buttonList = [
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ];
        } else {
          buttonList = [
            { value: true, label: "YES", color: "green" },
            { value: false, label: "NO", color: "red" },
          ];
        }
        let message =
          "Report contains default criteria, which can result in a large data set being returned, Do you want to continue? ";
        const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
          data: {
            message: message,
            buttonList: buttonList,
          },
        });
        matDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.createReport();
          } else {
            this.notificationMessage.error(
              "Please fill the required report query"
            );
            return;
          }
        });
      } else {
        this.createReport();
      }
    }

    if (this.addReportForm.controls.reportType.value == "Business process") {
      if (!this.addReportForm.controls.reportQuery.value.trim()) {
        let buttonList;
        if (this.themeService.useNewTheme) {
          buttonList = [
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ];
        } else {
          buttonList = [
            { value: true, label: "YES", color: "green" },
            { value: false, label: "NO", color: "red" },
          ];
        }
        let message =
          "Report query contains default criteria, which can result in a large data set being returned, Do you want to continue? ";
        const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
          data: {
            message: message,
            buttonList: buttonList,
          },
        });
        matDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.createReport();
          } else {
            this.notificationMessage.error(
              "Please fill the required report query"
            );
            return;
          }
        });
      } else {
        this.createReport();
      }
    }
  }

  createReport() {
    if (this.addReportForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    this.details.reportName = this.report.reportName;
    if (this.dealOption) {
      this.details.name = this.addReportForm.controls.id.value?.name;
    } else {
      this.details.name = this.addReportForm.controls.id.value?.entityName;
    }
    this.details.id = this.addReportForm.controls.id.value?.id;
    this.details.selectedColumns = this.report.selectedColumns;
    let data = Object.assign({}, this.details);
    this.reportConfigDetails.configDetails[this.data.index] = data;
    let id = this.reportConfigDetails?.id;
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, this.reportConfigDetails)
      .subscribe((res: any) => {
        this.notificationMessage.success(
          JsonData["label.success.updateReport"]
        );
      });
    this.dialogRef.close(this.details);
  }
}
