<div *ngIf="!useNewThemeUI" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55 editReport "  fxLayout="row wrap"
  fxLayoutGap="4px" >
  <div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
        <h2>{{ "label.header.editReport" | literal }}</h2>
      </div>
      <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">
        <button mat-button (click)="closeDialog()">
        <mat-icon class="close-icon">close</mat-icon>
      </button>
      </div>
    </div>


    <form autocomplete="off" [formGroup]="addReportForm" novalidate class="mt-30 split-form display mt-0" fxLayout="row wrap"
      fxLayoutGap="4px" >
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

        <div fxLayout="row wrap" fxLayoutGap="4px">
          <mat-form-field class="width-100">

            <mat-label>{{"label.field.selectType" | literal}}</mat-label>
            <mat-select formControlName="reportType" [(ngModel)]="selectedType" required
              (selectionChange)="selectList()" [disabled]="isInputDisabled">
              <ng-container *ngFor="let type of types">
                <mat-option [value]="type">
                  {{ type }}
                </mat-option>
              </ng-container>

            </mat-select>
          </mat-form-field>
          <mat-form-field class="width-100">
            <mat-label *ngIf="dealOption">{{"label.label.businessProcessList" | literal}}</mat-label>
            <mat-label *ngIf="dealOption == false">{{"label.label.entityList" | literal}}</mat-label>
            <mat-select formControlName="id" [(ngModel)]="selectedTypeList" required [disabled]="isInputDisabled"
              (selectionChange)="checkList()">
              <ng-container *ngFor="let list of id">
                <mat-option *ngIf="dealOption" [value]="list">
                  {{ list.name }}
                </mat-option>
                <mat-option *ngIf="dealOption == false" [value]="list">
                  {{ list.entityName }}
                </mat-option>
              </ng-container>

            </mat-select>
          </mat-form-field>
          <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" >
            <mat-label>{{"label.header.reportName" | literal}}</mat-label>
            <input formControlName="reportName" class="width-100" matInput autocomplete="off">

          </mat-form-field>
      
          <mat-form-field  class="reportQuery width-100">
            <span matPrefix>{{reportQueryString}}</span>
            <textarea class="width-100" matInput  formControlName="reportQuery">
            </textarea>
          </mat-form-field>
        </div>
      </div>

    </form>
  </div>
</mat-dialog-content>
<mat-card-footer>
  <div class="addButton">
    <button mat-raised-button class="green" type="submit" (click)="submit()" [disabled]="updateButton"
    >{{"label.button.update" |
      literal}}</button>
  </div>
</mat-card-footer>
</div>

<div *ngIf="useNewThemeUI" class="newUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55 editReport "  fxLayout="row wrap">
    <div class="edit-report-dialog-container" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div class="edit-report-dialog-container-1" fxLayout="row wrap" fxLayoutAlign="space-between center">
        <div class="edit-report-dialog-container-1-header" fxFlex="row wrap" fxLayoutAlign="start center">
          <span>{{ "label.header.editReport" | literal }}</span>
        </div>
        <div class="edit-report-dialog-container-1-close">
          <button mat-icon-button (click)="closeDialog()">
            <span class="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>
      <div class="m-t-30" fxLayout="row" fxLayoutAlign="start">
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <form class="split-form" autocomplete="off" [formGroup]="addReportForm" novalidate>
            <div class="edit-report-dialog-container-2" fxLayout="row">
              <mat-form-field class="full-width">
                <mat-label>{{"label.field.selectType" | literal}}</mat-label>
                <mat-select formControlName="reportType" [(ngModel)]="selectedType" required (selectionChange)="selectList()" [disabled]="isInputDisabled">
                  <ng-container *ngFor="let type of types">
                    <mat-option [value]="type">
                      {{ type }}
                    </mat-option>
                  </ng-container>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="edit-report-dialog-container-3" fxLayout="row">
              <mat-form-field class="full-width">
                <mat-label *ngIf="dealOption">{{"label.label.businessProcessList" | literal}}</mat-label>
                <mat-label *ngIf="dealOption == false">{{"label.label.entityList" | literal}}</mat-label>
                <mat-select formControlName="id" [(ngModel)]="selectedTypeList" required [disabled]="isInputDisabled"
                  (selectionChange)="checkList()">
                  <ng-container *ngFor="let list of id">
                    <mat-option *ngIf="dealOption" [value]="list">
                      {{ list.name }}
                    </mat-option>
                    <mat-option *ngIf="dealOption == false" [value]="list">
                      {{ list.entityName }}
                    </mat-option>
                  </ng-container>
      
                </mat-select>
              </mat-form-field>
            </div>
            <div class="edit-report-dialog-container-4" fxLayout="row">
              <mat-form-field class="full-width" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%" fxFlex.sm="97%" >
                <mat-label>{{"label.header.reportName" | literal}}</mat-label>
                <input formControlName="reportName" class="width-100" matInput autocomplete="off">
              </mat-form-field>
            </div>
            <div class="edit-report-dialog-container-5" fxLayout="row">
              <mat-form-field  class="full-width custom-form-field">
                <span matPrefix>{{reportQueryString}}</span>
                <textarea matInput  formControlName="reportQuery">
                </textarea>
              </mat-form-field>
            </div>
            </form>
        </div>
      </div>
      
    </div>
  </mat-dialog-content>

  <mat-card-footer>
    <div class="edit-report-dialog-container-footer" fxLayout="row" fxLayoutAlign="center center">
      <button color="primary" mat-raised-button type="submit" (click)="submit()" [disabled]="updateButton">
        {{"label.button.update" | literal}}
      </button>
    </div>
  </mat-card-footer>
</div>
