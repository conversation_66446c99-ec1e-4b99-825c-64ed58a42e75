<mat-dialog-content cdkScrollable class="mat-dialog-content-form-custom-css max-height-66"
  fxLayout="row wrap" fxLayoutGap="4px">
  <div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
        <h2>Assign Rules</h2>
      </div>
      <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">
        <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>


    <form autocomplete="off" [formGroup]="rulesForm">
      <ng-container *ngIf="noRecordFlag && !showSpinner">
        No rules found.
      </ng-container>
      <div *ngIf="!noRecordFlag && ! showSpinner" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
        fxFlex.sm="100%" class="custom-dropdown  height-content">
        <table mat-table [dataSource]="dataSource" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
          fxFlex.sm="100%" class="mat-elevation-z0  mat-table-width list-container" matSort
          (matSortChange)="sortOrder($event)" cdkDropList [cdkDropListData]="dataSource"
          (cdkDropListDropped)="dropTable($event)">

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let i=index"> {{i+1}} </td>
          </ng-container>

          <ng-container matColumnDef="checkbox">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <mat-checkbox disableRipple class="example-margin"
                [checked]="getDefaultValues(element)"
                (change)="selectRule($event,element)"></mat-checkbox>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="ruleName">
            <th mat-header-cell *matHeaderCellDef> Rule Name </th>
            <td mat-cell *matCellDef="let element"> {{element.workflowName}} </td>
          </ng-container>

          <!-- Event Column -->
          <ng-container matColumnDef="event">
            <th mat-header-cell *matHeaderCellDef> Event when rule should execute </th>
            <td mat-cell *matCellDef="let element">
              <mat-form-field>
                <mat-label *ngIf=" !element.event || element?.event?.length == 0">Select
                  Event</mat-label>
                <mat-select [formControlName]="baseControlName+element.workflowName" disableRipple
                  multiple [(ngModel)]="element.event">
                  <!-- <mat-option value="">None</mat-option> -->
                  <mat-option *ngFor="let event of ruleEvents" [value]="event">
                    {{ event }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>


          <tr mat-header-row *matHeaderRowDef="displayedColumns" class="example-box"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="example-box" cdkDrag
            [cdkDragData]="row">
          </tr>
        </table>
      </div>
      <div *ngIf="showSpinner">
        <mat-spinner class="ShowLoader"></mat-spinner>
      </div>
    </form>
  </div>
</mat-dialog-content>
<mat-dialog-actions *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'CHANGE'">
  <div *ngIf="!noRecordFlag && !showSpinner" class="button-row centerd " fxFlex="100%"
    fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <button mat-raised-button class="green mat-primary" type="submit" (click)="setRules()">Apply
    </button>
  </div>
</mat-dialog-actions>
