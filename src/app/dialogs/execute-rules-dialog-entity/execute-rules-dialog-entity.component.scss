table th {
    // display:flex;
    font-size: large !important;
    font-weight: 500 !important;
    justify-content: center !important;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version.*/
  .mat-dialog-content-form-custom-css {
    min-height: 25vh;
    max-height: 80vh !important;
    margin-bottom: -24px !important;
  }

  .custom-dropdown{
    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-ripple .mat-ripple-element {
    opacity: 0.03 !important;
    background-color: #fff!important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-flex {
      padding:10px 0px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
    display: none;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-flex{
    height: 40px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-infix{
    border:1px solid #c7c7c7;
    border-radius: 4px;
    width: 250px;
    }
    // ::ng-deep .mat-select-arrow-wrapper{
    // display: block !important;
    // }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-infix{
    padding:8px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background,.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: green;
    }
    ::ng-deep .mat-mdc-input-element {
      padding-left: 10px !important;
    }
    ::ng-deep [mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,
    ::ng-deep [mat-sort-header].cdk-program-focused .mat-sort-header-container {
    border-bottom: none !important;
    }
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);


  }

  .mat-column-ruleName {
    width: 60%;
  }

  .mat-column-event {
    padding-left: 5%;
  }

  .centerd {
    display: block !important;
    margin: 0 auto;
    padding-top: 24px;
    text-align: center;
  }

  .example-box {
    flex-direction: row;
    justify-content: space-between;
    box-sizing: border-box;
    cursor: move;
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .cdk-drag-preview{
    // justify-content: space-between;
    // height: fit-content;
    // min-height: 48px !important;
    box-sizing: border-box;
    padding-top: 15px;
    display: flex;
    flex-direction: row;
    cursor: move;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-ripple .mat-ripple-element {
    opacity: 0.03 !important;
    background-color: #fff!important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
    display: none;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-flex{
    height: 40px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-mdc-form-field .mat-form-field-infix{
    border:1px solid #c7c7c7;
    border-radius: 4px;
    width: 250px;
    }
    // ::ng-deep .mat-select-arrow-wrapper{
    // display: block !important;
    // }
    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background,.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: green;
    }
    .list-container {
      height: 100%;
      min-height: 10vh !important;
      overflow-y: scroll !important;
    }
  }

  .ShowLoader{
    margin: 5% 50%;
  }
