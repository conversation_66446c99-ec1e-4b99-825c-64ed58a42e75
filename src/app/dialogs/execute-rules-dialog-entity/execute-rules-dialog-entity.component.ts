import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { Component, Inject, OnInit, Optional } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { Sort } from "@angular/material/sort";
import { ToasterService } from "src/app/common/toaster.service";
import { ConfigurationResources } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
//import { SetRulesComponent } from 'src/app/settings/workflow-configuration/stage/set-rules/set-rules.component';
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { WorkflowEngineService } from "src/app/shared-service/workflow-engine.service";
import JsonData from "src/assets/data.json";

@Component({
  selector: "app-execute-rules-dialog-entity",
  templateUrl: "./execute-rules-dialog-entity.component.html",
  styleUrls: ["./execute-rules-dialog-entity.component.scss"],
})
export class ExecuteRulesDialogEntityComponent implements OnInit {
  entityId: any;
  rulesList: any = [];
  selectedRulesList = [];
  showSpinner = false;
  dataSource: any[] = [];
  baseControlName = "eventSelect";
  displayedColumns: any[] = ["index", "checkbox", "ruleName", "event"];
  ruleEvents = ["Create", "Modify", "Delete"];
  rulesForm: FormGroup = new FormGroup({});
  getResp: any;
  noRecordFlag = false;
  name: any;
  JsonData: any;

  get ENTITY_RESOURCE() {
    return ConfigurationResources.Entity_Def;
  }

  constructor(
    public entityService: EntityService,
    private errorService: ErrorService,
    public fb: FormBuilder,
    public notificationMessage: ToasterService,
    private dialog: MatDialogRef<ExecuteRulesDialogEntityComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private data: any,
    private workflowEngineService: WorkflowEngineService
  ) {
    this.entityId = this.data.entityId;
    this.getRules(this.entityId);
    this.name = this.data.name;
  }

  ngOnInit(): void {}

  generateReactiveForm(data) {
    this.rulesForm = this.fb.group({});
    if (data && data.length != 0) {
      this.rulesForm = this.createGroup(data);
    }
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      const key = this.baseControlName + control.workflowName;
      group.addControl(key, this.fb.control(""));
    });
    return group;
  }

  getRules(entityId) {
    this.showSpinner = true;
    this.entityService.getRulesById(entityId).subscribe(
      (response) => {
        this.showSpinner = false;
        this.filterResp(response);
        this.dataSource = this.getResp;
        this.generateReactiveForm(this.getResp);

        if (this.dataSource.length == 0) {
          this.noRecordFlag = true;
        }
      },
      (error) => {
        this.showSpinner = false;
        this.noRecordFlag = true;
      }
    );
  }

  // to combine rules with multiple events
  filterResp(resp) {
    const filteredResp = resp.reduce((unique, o) => {
      if (!unique.some((obj) => obj.workflowName === o.workflowName)) {
        if (o?.event) {
          o.event = [o.event];
        }
        unique.push(o);
      } else {
        unique.find((rule) => {
          if (rule.workflowName == o.workflowName && rule.event) {
            if (typeof rule?.event === "string") {
              const event = [];
              event.push(rule.event);
              event.push(o.event);
              rule.event = event;
            } else {
              rule?.event.push(o.event);
            }
          }
        });
      }
      return unique;
    }, []);
    this.getResp = [...filteredResp];
  }

  closeDialog() {
    const data = {
      status: false,
    };
    this.dialog.close(data);
  }

  setRules() {
    this.ValidateRuleEvents(this.dataSource);
    this.rulesForm.markAllAsTouched();
    if (this.rulesForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    const entityWorkflowList = [];
    let sequence = 1;

    //splitting rules with multiple events for payload
    this.getResp.forEach((rule) => {
      const eventNum = rule?.event?.length;
      if (eventNum && eventNum > 1) {
        for (let index = 0; index < eventNum; index++) {
          this.createWorkflowList(rule, entityWorkflowList, sequence, index);
          sequence++;
        }
      } else {
        this.createWorkflowList(rule, entityWorkflowList, sequence, 0);
        sequence++;
      }
    });

    const payload = {
      id: null,
      workflowId: null,
      entityWorkflowList,
    };

    this.workflowEngineService
      .assignRulesForEntity(this.entityId, payload)
      .subscribe(
        (response) => {
          if (this.name == "extension") {
            this.notificationMessage.success(
              JsonData["label.success.Extension"]
            );
          } else {
            this.notificationMessage.success(JsonData["label.success.Entity"]);
          }
          this.dialog.close(entityWorkflowList);
        },
        (error) => {
          this.showSpinner = false;
          this.notificationMessage.error(
            "Failed to assign selected rules, Verify/Correct the rules in Rules Engine and try again"
          );
        }
      );
  }

  createWorkflowList(rule, workflowList, seq, i) {
    const ruleData = {
      id: null,
      dataModelId: rule.dataModelId,
      workflowId: rule.workflowId,
      workflowName: rule.workflowName,
      workflowVersion: rule.workflowVersion,
      isSelected: rule.isSelected,
      event: rule.event && rule.isSelected ? rule.event[i] : null,
      sequenceNumber: seq,
    };
    if (ruleData?.event) {
      workflowList.push(ruleData);
    }
  }

  ValidateRuleEvents(validationSource) {
    validationSource.forEach((control) => {
      const controlName = this.baseControlName + control.workflowName;
      this.rulesForm.controls[controlName].setValidators([]);
      this.rulesForm.controls[controlName].clearValidators();
      this.rulesForm.controls[controlName].updateValueAndValidity();

      if (control.isSelected) {
        this.rulesForm.controls[controlName].setValidators([
          Validators.required,
        ]);
        this.rulesForm.controls[controlName].updateValueAndValidity();
      }
    });
  }

  selectRule(event, row) {
    if (event.checked) {
      row.isSelected = true;
      this.ValidateRuleEvents([row]);
    } else {
      row.isSelected = false;
      row.event = "";
      this.ValidateRuleEvents([row]);
    }
  }

  getDefaultValues(row) {
    if (row.isSelected) return true;
    else false;
  }

  dropTable(event: CdkDragDrop<[]>) {
    const prevIndex = this.dataSource.findIndex((d) => d === event.item.data);
    moveItemInArray(this.dataSource, prevIndex, event.currentIndex);
    this.dataSource = this.dataSource.slice();
  }

  sortOrder(sort: Sort) {
    if (!sort.active || sort.direction === "") {
      this.dataSource = [...this.getResp];
      return;
    }
    const isAsc = sort.direction === "asc";

    this.dataSource.sort((r1, r2) => {
      return (
        (r1.isSelected === r2.isSelected ? 0 : r1.isSelected ? -1 : 1) *
        (isAsc ? 1 : -1)
      );
    });
    this.dataSource.sort((r1, r2) => {
      if (isAsc && r1.isSelected) {
        return r1.workflowId - r2.workflowId;
      } else if (!r1.isSelected) {
        return r1.workflowId - r2.workflowId;
      }
    });
    this.dataSource = [...this.dataSource];
  }
}
