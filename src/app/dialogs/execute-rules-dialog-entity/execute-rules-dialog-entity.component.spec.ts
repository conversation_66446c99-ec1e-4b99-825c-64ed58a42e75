import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ExecuteRulesDialogEntityComponent } from './execute-rules-dialog-entity.component';

describe('ExecuteRulesDialogEntityComponent', () => {
  let component: ExecuteRulesDialogEntityComponent;
  let fixture: ComponentFixture<ExecuteRulesDialogEntityComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ExecuteRulesDialogEntityComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ExecuteRulesDialogEntityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
