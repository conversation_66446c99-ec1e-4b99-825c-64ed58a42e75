<div *ngIf="!themeService.useNewTheme" class="oldUI">
  <mat-dialog-content>
    <div fxLayout="column" class="new-ui-confirmation-container">
      <div fxLayoutAlign="center center" class="new-ui-img">
        <img *ngIf="this.themeMode === 'light-mode'" width="350px" height="300px"
          src="../../../assets/imgs/New_UI.svg">
        <img *ngIf="this.themeMode === 'dark-mode'" width="350px" height="300px"
          src="../../../assets/imgs/New_UI_dark_mode.svg">
      </div>
      <div class="headline-main" fxLayoutAlign="center center">
        <span>Welcome to Our New Interface!</span>
      </div>
      <div fxLayoutAlign="center center" class="headline-main">
        <span>See how we've improved just for you.</span>
      </div>
      <div class="headline-support center m-t-10">
        <span><b class="headline-note">Notice: </b>The Classic UI will be discontinued soon to make
          a
          way for a more
          advance and seamless experience.</span>
      </div>
      <div class="headline-support center m-b-10">
        <span>We encourage you to explore the new design.</span>
      </div>
      <div fxLayoutAlign="center center" fxLayoutGap="10">
        <button class="outlined-button" mat-raised-button (click)="switchToOldTheme()">Continue with
          Classic</button>
        <button color="primary" mat-raised-button (click)="switchToNewTheme()">Explore New</button>
      </div>
    </div>
  </mat-dialog-content>
</div>

<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="column" class="new-ui-confirmation-container">
      <div fxLayoutAlign="center center" class="new-ui-img">
        <img *ngIf="this.themeMode === 'light-mode-new'" width="350px" height="300px"
          src="../../../assets/imgs/New_UI.svg">
        <img *ngIf="this.themeMode === 'dark-mode-new'" width="350px" height="300px"
          src="../../../assets/imgs/New_UI_dark_mode.svg">
      </div>
      <div class="headline-main" fxLayoutAlign="center center">
        <span>Welcome to Our New Interface!</span>
      </div>
      <div fxLayoutAlign="center center" class="headline-main">
        <span>See how we've improved just for you.</span>
      </div>
      <div class="headline-support center m-t-10">
        <span><b class="headline-note">Notice: </b>The Classic UI will be discontinued soon to make
          a
          way for a more
          advance and seamless experience.</span>
      </div>
      <div class="headline-support center m-b-10">
        <span>We encourage you to explore the new design.</span>
      </div>
      <div fxLayoutAlign="center center" fxLayoutGap="10">
        <button aria-label="continue-with-classic" class="outlined-button" mat-raised-button
          (click)="switchToOldTheme()">Continue with
          Classic</button>
        <button aria-label="explore-new" color="primary" mat-raised-button
          (click)="switchToNewTheme()">Explore New</button>
      </div>
    </div>

  </mat-dialog-content>
</div>
