import { Component } from "@angular/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-new-ui-confirmation-dialog",
  templateUrl: "./new-ui-confirmation-dialog.component.html",
  styleUrls: ["./new-ui-confirmation-dialog.component.scss"],
})
export class NewUiConfirmationDialogComponent {
  themeMode: any;

  constructor(
    public dialogRef: MatDialogRef<NewUiConfirmationDialogComponent>,
    public dialog: MatDialog,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.themeMode = localStorage.getItem("user-theme");
  }

  switchToNewTheme() {
    this.dialogRef.close(true);
  }

  switchToOldTheme() {
    this.dialogRef.close(false);
  }
}
