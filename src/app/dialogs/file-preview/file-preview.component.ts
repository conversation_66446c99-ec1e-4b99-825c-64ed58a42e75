import {
  Component,
  OnInit,
  Inject,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ToasterService } from "src/app/common/toaster.service";
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialog,
} from "@angular/material/dialog";
import { UntypedFormBuilder } from "@angular/forms";
import { DomSanitizer } from "@angular/platform-browser";
import { ActiveSectionService } from "src/app/application-summary/section-preview-dialog/active-section.service";

@Component({
  selector: "app-file-preview",
  templateUrl: "./file-preview.component.html",
  styleUrls: ["./file-preview.component.scss"],
})
export class FilePreviewComponent implements OnInit, OnDestroy {
  previewURLString: any = "";
  title: any = "";
  loading: boolean = true;
  // Full screen security issue with office viewer
  // viewerName = "google"
  viewerName = "office";
  private resizeObserver!: ResizeObserver;

  constructor(
    private validationErrorMessageService: ValidationErrorMessageService,
    public notificationMessage: ToasterService,
    public sanitizer: DomSanitizer,
    public dialogRef: MatDialogRef<FilePreviewComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private dialog: MatDialog,
    private fb: UntypedFormBuilder,
    public activeSectionService: ActiveSectionService,
    private el: ElementRef
  ) {
    this.previewURLString = this.data?.previewURLString;
    this.title = this.data?.fileName;

    if (this.title.split(".").pop() == "pdf"||
    this.title.split(".").pop() == "tiff") {
      this.viewerName = "google";
    }
    if (
      this.title.split(".").pop() == "jpeg" ||
      this.title.split(".").pop() == "jpg"||
      this.title.split(".").pop() == "png"
    ) {
      this.viewerName = "url";
    }
  }

  ngOnInit() {
    this.loading = true;

    const dialogContainer = this.el.nativeElement.closest(
      "mat-dialog-container"
    );

    this.resizeObserver = new ResizeObserver(() => {
      const width = dialogContainer.offsetWidth;
      const viewportWidth = window.innerWidth;
      const widthPercentage = (width / viewportWidth) * 100;
      this.activeSectionService.setFilePreviewWidth(widthPercentage);
    });

    this.resizeObserver.observe(dialogContainer);
  }

  ngOnDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  close() {
    this.dialog.closeAll();
  }

  onSave() {
    this.dialogRef.close();
  }
  onDocumentLoaded() {
    this.loading = false;
  }
}
