<div class="file-preview-container">

  <div *ngIf="!activeSectionService.previewActiveSection" class="file-preview-header">
    <h2>{{ title }}</h2>
    <button mat-icon-button (click)="close()">
      <span class="material-symbols-outlined">close</span>
    </button>
  </div>

  <div *ngIf="loading" class="table-spinner">
    <mat-spinner></mat-spinner>
  </div>

  <ngx-doc-viewer [class]="activeSectionService.previewActiveSection ? 'full-height' : 'height-90'"
    *ngIf="previewURLString" [url]="previewURLString" [viewer]="viewerName"
    (loaded)="onDocumentLoaded()">
  </ngx-doc-viewer>

</div>
