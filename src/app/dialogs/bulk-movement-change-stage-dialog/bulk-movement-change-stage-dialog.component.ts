import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DealService } from '../../shared-service/deal.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { FormControl, Validators } from '@angular/forms';

@Component({
  selector: 'app-bulk-movement-change-stage-dialog',
  templateUrl: './bulk-movement-change-stage-dialog.component.html',
  styleUrls: [ ]
})
export class BulkMovementChangeStageDialogComponent implements OnInit {
  remark:any;
  remarks = new FormControl('', [Validators.required]);
  constructor( public dialogRef: MatDialogRef<BulkMovementChangeStageDialogComponent>, @Inject(MAT_DIALOG_DATA) public data,
     private dealService: DealService,private notificationMessage: ToasterService) { }

  ngOnInit() {   

    
  }

  submit(){
    this.remarks.markAsTouched();
    if (this.remark) {
      let data ={
        isSubmit:true,
        remark: this.remark 
        }
      this.dialogRef.close(data)
    }else{
      this.notificationMessage.error('Please enter a value');
      return;
    }
  }

}
