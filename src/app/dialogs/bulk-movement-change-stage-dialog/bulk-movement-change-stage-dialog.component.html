

<mat-dialog-content class="mat-dialog-content-form-custom-css">
      <div class="create-asset-dialog">
        <div >
          <div fxLayout="row wrap" fxLayoutGap="4px" >
            <div fxFlex="90%" fxFlex.md="90%" fxFlex.xs="90%" fxFlex.sm="90%">
              <h2>{{data.title}}</h2>
            </div>
          </div>
        </div>
     
  
      <div fxLayout="column" fxLayoutGap="4px">
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="createDealInputs mb-8" >
            <mat-form-field class="width-100">
              <mat-label>Selected Stage</mat-label>
              <input type="text" matInput [(ngModel)]="data.name" disabled/>
            </mat-form-field>
            
        </div>
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="createDealInputs mb-8" >
            <mat-form-field  class="width-100">
              <mat-label>Remark</mat-label>
              <input type="text" matInput [(ngModel)]="remark" required maxlength="50"  [formControl]="remarks"/>
              <mat-hint class="noteForFile bulkMovementHint" >Note:Complete all Mandatory Details before moving stage</mat-hint>
            </mat-form-field>
            
        </div>
        
       <!--  <mat-form-field class="width-80">
          <mat-label>Remark</mat-label>
        <br>
          <textarea matInput [(ngModel)]="remark"></textarea>
        </mat-form-field> -->
  
        <mat-card-actions fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mt-5" align="center" >
          <button mat-raised-button  class="green" (click)="submit()"> CONTINUE </button>
          <button mat-raised-button class="red ml-3" mat-dialog-close  > CANCEL </button>
        </mat-card-actions>
  
      </div>
      
  
    </div>
      
  
    </mat-dialog-content>