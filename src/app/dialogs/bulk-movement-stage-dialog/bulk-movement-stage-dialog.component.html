


    <mat-dialog-content class="mat-dialog-content-form-custom-css">
      <div class="create-asset-dialog">
        <div >
          <div fxLayout="column" fxLayoutGap="4px">
            <h2 fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
              {{data.title}}
            </h2>
          </div>
        </div>
        
    <div fxLayout="column" fxLayoutGap="4px">

      <h3>
       {{data.message}}
      </h3>

      
     <!--  <mat-form-field class="width-80">
        <mat-label>Remark</mat-label>
      <br>
        <textarea matInput [(ngModel)]="remark"></textarea>
      </mat-form-field> -->

      <mat-card-actions fxLayout="row wrap" fxLayoutGap="4px" class="mt-5" align="center" >
        <button mat-raised-button  class="green" (click)="submit()"> CONTINUE </button>
        <button mat-raised-button class="red ml-3" mat-dialog-close  > CANCEL </button>
      </mat-card-actions>

    </div>
    


    
  </div>

    </mat-dialog-content>
    
   


