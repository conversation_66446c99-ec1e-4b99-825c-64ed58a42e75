import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DealService } from '../../shared-service/deal.service';
@Component({
  selector: 'app-bulk-movement-stage-dialog',
  templateUrl: './bulk-movement-stage-dialog.component.html',
  styleUrls: ['./bulk-movement-stage-dialog.component.scss']
})
export class BulkMovementStageDialogComponent implements OnInit {
  remark:any;

  constructor( public dialogRef: MatDialogRef<BulkMovementStageDialogComponent>, @Inject(MAT_DIALOG_DATA) public data,
     private dealService: DealService) { }

  ngOnInit() {   

    
  }

  submit(){
    let data ={
      isSubmit:true
      /* remark: this.remark */
    }
    this.dialogRef.close(data)
  }

}
