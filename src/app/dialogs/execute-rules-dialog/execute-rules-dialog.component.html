<mat-dialog-content class="mat-dialog-content-form-custom-css max-height-66"  fxLayout="row wrap"
    fxLayoutGap="4px">
    <div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

        <div fxLayout="row wrap" fxLayoutGap="4px">
            <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
                <h2>Select Rules to Execute</h2>
            </div>
            <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">
            <button mat-button (click)="closeDialog()">
                <mat-icon class="close-icon">close</mat-icon>
            </button>
            </div>
        </div>


        <div class="margin-left-40" fxLayout="row wrap" fxLayoutGap="4px" *ngIf="showSpinner">
            <mat-spinner></mat-spinner>
        </div>
        <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="!showSpinner">
            <ng-container *ngIf="rulesList.length == 0 ">
                No rules found.
            </ng-container>
            <ng-container *ngIf="rulesList.length != 0">
                <ng-container *ngFor="let rule of rulesList">

                    <div class="margin1" fxFlex="5%" fxFlex.md="5%" fxFlex.xs="5%" fxFlex.sm="5%">

                        <mat-checkbox color='primary' [checked]="getDefaultValue(rule.isChecked)" (change)="selectRule($event ,rule)">

                        </mat-checkbox>

                    </div>
                    <div class="margin1" fxFlex="90%" fxFlex.md="90%" fxFlex.xs="90%" fxFlex.sm="90%">

                       <p class="notifyClass"> {{rule.workflowName}} </p>

                    </div>
                </ng-container>
            </ng-container>
        </div>



    </div>
    <div *ngIf="!showSpinner" class="button-row centerd" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <button mat-raised-button class="green" type="submit" [disabled]="checkSelectedRules()" (click)="executeRules()">{{"label.button.executeRules"|literal}}
        </button>

    </div>
</mat-dialog-content>
