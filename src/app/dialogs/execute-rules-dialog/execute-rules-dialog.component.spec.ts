import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ExecuteRulesDialogComponent } from './execute-rules-dialog.component';

describe('ExecuteRulesDialogComponent', () => {
  let component: ExecuteRulesDialogComponent;
  let fixture: ComponentFixture<ExecuteRulesDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ExecuteRulesDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ExecuteRulesDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
