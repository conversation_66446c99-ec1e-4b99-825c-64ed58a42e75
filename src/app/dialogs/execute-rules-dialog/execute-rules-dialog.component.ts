import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToasterService } from 'src/app/common/toaster.service';
import { SetRulesComponent } from 'src/app/settings/businessProcess-configuration/stage/set-rules/set-rules.component';
import { DealService } from 'src/app/shared-service/deal.service';

@Component({
  selector: 'app-execute-rules-dialog',
  templateUrl: './execute-rules-dialog.component.html',
  styleUrls: ['./execute-rules-dialog.component.scss']
})
export class ExecuteRulesDialogComponent implements OnInit {
  rulesList: any = [];
  showSpinner = true;
  businessProcessId: any;
  currentStage: any;

  constructor(
    public dealService : DealService,
    public notificationMessage: ToasterService,
    private dialog: MatDialogRef<SetRulesComponent>, @Optional() @Inject(MAT_DIALOG_DATA) private data: any) {

    this.businessProcessId = this.data.businessProcessId;
    this.currentStage = this.data.currentStageName;
    this.getRules(this.businessProcessId);


  }


  ngOnInit() {
  }

  getRules(businessProcessId) {

    this.dealService.getAssignedRules(businessProcessId,this.currentStage).subscribe(response => {
      this.showSpinner = false;
      this.rulesList = response;
      this.rulesList = this.rulesList.filter(rule=> {
        if(rule.event == "On Request"){
          rule.isChecked = false;
          return 1;
        }
      })

    }, (err) => {
      this.showSpinner = false;
    })
  }

  closeDialog() {
    let data = {
      status: false,
    }
    this.dialog.close(data)
  }

  selectRule(event, rule) {
    rule.isChecked = event.checked;
  }

  getDefaultValue(isSelected){
    if(isSelected) return true;
    else return false
  }

  executeRules(){
    let businessProcessWorkflowList = [];

    this.rulesList.forEach(rule=>{
      if(rule.isChecked){
        businessProcessWorkflowList.push(rule)
      }
      delete rule.isChecked;
    })

    let payload = {
      "id":this.data.dealId,
      "businessProcessWorkflowList":businessProcessWorkflowList,
    }
    this.dealService.executeSelectedRules(payload).subscribe(response => {
    // this.notificationMessage.success("Selected Rules have been executed successfully, go to Rules management tab to see the Process results");
    // this.notificationMessage.success("Selected Rules have been executed successfully");
    this.dialog.close(response)
  
    }, (error) => {
      this.showSpinner = false;
      if (error.status == 400) {
        this.dialog.close(error)
      }else {
        this.notificationMessage.error(error.statusText);
      }
    })
  }

  checkSelectedRules(){
    let selectedRules = this.rulesList.find(rule => rule.isChecked)
    if(selectedRules){
      return false
    }
    else return true
  }

}
