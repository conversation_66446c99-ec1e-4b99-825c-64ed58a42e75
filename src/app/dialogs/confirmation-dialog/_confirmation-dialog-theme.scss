@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
    $color-config: mat.get-color-config($theme);

    $warn-palette: map.get($color-config, 'warn');


    .confirmation-container{
        .warning-icon, .delete-header{
            color: mat.get-color-from-palette($warn-palette, 200) !important;
        }
    }

 
}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .confirmation-container{
    .delete-header{
        @include mat.typography-level($typography-config, 'headline-4');
    }
    .label-title{
        @include mat.typography-level($typography-config, 'subtitle-1');
    }
    .info-label{
      @include mat.typography-level($typography-config, 'subtitle-1');
    }
    .sub-label{
        @include mat.typography-level($typography-config, 'subtitle-2');
    }
  }
}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}