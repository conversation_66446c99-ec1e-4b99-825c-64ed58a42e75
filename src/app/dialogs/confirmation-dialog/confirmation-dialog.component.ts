import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ThemeService } from 'src/app/theme.service';

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})
export class ConfirmationDialogComponent {
  message : any;
  additionalMessage = "";
  buttonList: any=[];
  icon :any;
  infoMessage:any;

  constructor(public dialogRef: MatDialogRef<ConfirmationDialogComponent>, 
    public dataSharingService: DataSharingService,
    @Inject(MAT_DIALOG_DATA)  public data, 
    public dialog: MatDialog,
    public themeService: ThemeService
  ) {
      this.message =  data?.message ? data?.message : 'Are you sure you want to delete?';
      this.additionalMessage = data?.additionalMessage ? data?.additionalMessage : '';
      this.infoMessage = data?.infoMessage ? data?.infoMessage : '';
      this.buttonList = data?.buttonList ? data?.buttonList:[{}];
      this.icon = data?.icon ? data?.icon : 'warning';
     }


  Onclick(value){
    if(value){
      this.submit();

    }
    else{
      this.close();
    }
    
  }
  
  submit() {
    this.dialogRef.close(true);
  }

  close() {
    this.dialogRef.close(false);
  }

  hasDeleteAction(): boolean {
    return this.buttonList.some(button => button.label === 'Yes,Delete');
  }

}
