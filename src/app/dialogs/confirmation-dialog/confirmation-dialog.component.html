<div *ngIf="!themeService.useNewTheme" class="oldUI">
<mat-dialog-content class="dialogContent">

  <div class="flex-grow-1">
    <h6 class="dialogContentMessage"> {{ this.message?.text ? this.message?.text : this.message }}</h6>
    <p>{{ this.additionalMessage }}</p>
  </div>

  <mat-icon *ngIf="this.message?.icon" class="messageIcon">{{ this.message?.icon }}</mat-icon>

</mat-dialog-content>
      
      <mat-dialog-actions align="center">
        <ng-container *ngFor="let type of buttonList">
        <button [disabled]="type.disableBtn" attr.aria-label="{{type.label}}-confirm-button" mat-button [class]="type.color" (click)="Onclick(type.value)">{{type.label}}</button>
        </ng-container>
      </mat-dialog-actions>
</div>

   

<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="column" class="confirmation-container">
      <div fxLayoutAlign="center center">
        <span class="material-symbols-outlined warning-icon">{{this.icon}}</span>
      </div>
      <div class="delete-header" fxLayoutAlign="center center" *ngIf="hasDeleteAction()">
        <span>Delete?</span>
      </div>
      <div fxLayoutAlign="center center" class="label-title m-v-10">
        <span>{{ this.message?.text ? this.message?.text : this.message}}</span>
      </div>
      <div class="sub-label center m-b-10">
        <span>{{ this.additionalMessage }}</span>
      </div>
      <div class="info-label center m-b-10">
        <span>{{ this.infoMessage }}</span>
      </div>
      <div fxLayoutAlign="center center" fxLayoutGap="10">
        <ng-container *ngFor="let type of buttonList">
          <button [disabled]="type.disableBtn" attr.aria-label="{{type.label}}-confirm-button" mat-raised-button [ngClass]="{ 'outlined-button': type.value === true }" 
          [color]="type.value === false ? 'primary' : ''" (click)="Onclick(type.value)">{{type.label}}</button>
          </ng-container>
      </div>

    </div>

  </mat-dialog-content>
</div>




