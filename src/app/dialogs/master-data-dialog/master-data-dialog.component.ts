import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-master-data-dialog',
  templateUrl: './master-data-dialog.component.html',
  styleUrls: ['./master-data-dialog.component.scss']
})
export class MasterDataDialogComponent implements OnInit {
  editorOptions = {theme: 'vs-dark', language: 'json', autoIndent: "full" ,readOnly: true};
  jsonRule :any;
  
  constructor(public dialogRef: MatDialogRef<MasterDataDialogComponent>,@Inject(MAT_DIALOG_DATA) public data: any
   ) { }

  ngOnInit() {
    if (this.data && this.data.jsonData) {
      this.jsonRule = JSON.stringify(this.data.jsonData, null, 2);
    }
  }
  closeDialog(event) {
    if(event == "close"){ this.dialogRef.close(false);}
    else if(event == "json"){ 
    let masterData;
    if(this.jsonRule == '' || this.jsonRule == undefined){
      masterData = null;
      this.dialogRef.close(masterData);
    }
    
   }
   
  }
}
