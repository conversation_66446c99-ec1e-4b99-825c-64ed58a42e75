
<mat-dialog-content class="mat-dialog-content-form-custom-css height-55"  fxLayout="row wrap"
fxLayoutGap="4px">
<div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
      <h2>View Json Data</h2>
    </div>
    <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="closeButton">

      <mat-icon (click)="closeDialog('close')"  class="pointer ShowLoader">close</mat-icon>

    </div>
  </div>
</div>
</mat-dialog-content>
<mat-card-footer>
<div class="editorOptions">
    <ngx-monaco-editor [options]="editorOptions" [(ngModel)]="jsonRule" class="masterDataDialog"></ngx-monaco-editor>
</div>

</mat-card-footer>