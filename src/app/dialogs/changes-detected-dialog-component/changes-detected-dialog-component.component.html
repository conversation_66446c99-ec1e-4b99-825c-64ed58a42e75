
<div *ngIf="!themeService.useNewTheme">
<mat-icon class="buttonPosition" (click)="close()">close</mat-icon>
<mat-dialog-content class="dialogContent">

    <div> 
      <h6 class="dialogContentMessage"> {{ this.message }}</h6>
    </div>
    
  </mat-dialog-content>
        
        <mat-dialog-actions align="center">
          <ng-container *ngFor="let type of buttonList">
          <button attr.aria-label="{{type.label}}-confirm-button" mat-button [class]="type.color" (click)="Onclick(type.value)">{{type.label}}</button>
          </ng-container>
        </mat-dialog-actions>
      </div>
  
  
      <div *ngIf="themeService.useNewTheme">
        <button mat-icon-button class="buttonPosition"  (click)="close()">
          <mat-icon>close</mat-icon>
          </button>
       <mat-dialog-content >
        <div> 
          <h4 class="dialogContentMessage"> {{ this.message }}</h4>
        </div>
        
      </mat-dialog-content>
            
            <mat-dialog-actions align="center">
              <ng-container *ngFor="let type of buttonList">
              <button attr.aria-label="{{type.label}}-confirm-button" mat-button [ngClass]="type.value === 'leave'?'warn-button':'outlined-button'" (click)="Onclick(type.value)">{{type.label}}</button>
              </ng-container>
            </mat-dialog-actions>
        </div>