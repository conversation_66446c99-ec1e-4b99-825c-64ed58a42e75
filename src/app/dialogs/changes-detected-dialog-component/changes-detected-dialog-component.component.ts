import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ThemeService } from 'src/app/theme.service';

@Component({
  selector: 'app-changes-detected-dialog-component',
  templateUrl: './changes-detected-dialog-component.component.html',
  styleUrls: ['./changes-detected-dialog-component.component.scss']
})
export class ChangesDetectedDialogComponentComponent implements OnInit {

  message : any;
  buttonList: any=[];

  constructor(public dialogRef: MatDialogRef<ChangesDetectedDialogComponentComponent>, public dataSharingService: DataSharingService,
    @Inject(MAT_DIALOG_DATA)  public data, public dialog: MatDialog,
    public themeService: ThemeService) {
      this.message =  data?.message ? data?.message : 'Are you sure you want to exit ?';
      this.buttonList = data?.buttonList ? data?.buttonList:[{}]
     }

  ngOnInit(): void {
    this.dataSharingService.loaderSavePrompt = true;
  }

  Onclick(value){
      this.dialogRef.close(value);
      this.dataSharingService.loaderSavePrompt = false;
  }

  close() {
    this.dialogRef.close(false);
  }

}
