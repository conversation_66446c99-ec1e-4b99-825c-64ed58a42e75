import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangesDetectedDialogComponentComponent } from './changes-detected-dialog-component.component';

describe('ChangesDetectedDialogComponentComponent', () => {
  let component: ChangesDetectedDialogComponentComponent;
  let fixture: ComponentFixture<ChangesDetectedDialogComponentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ChangesDetectedDialogComponentComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ChangesDetectedDialogComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
