<div *ngIf="!themeService.useNewTheme" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55" fxLayout="row wrap"
    fxLayoutGap="4px">
    <div class="create-asset-dialog" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
      fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>{{translationContext | literal }}</h2>

        </div>
        <div fxFlex="18%" fxFlex.md="18%" fxFlex.xs="16%" fxFlex.sm="17%" class="helpIconDiv">








          <button mat-button (click)="navigateToHelp()">
            <mat-icon class="helpIcon" [matTooltip]="'Rules Configuration Guide'">help</mat-icon>
          </button>
          <button mat-button (click)="closeDialog('close')">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-card-footer>
    <div>
      <ngx-monaco-editor class="editorOptions" (onInit)="registerJsonKeySuggestions()"
        [options]="editorOptions" [(ngModel)]="jsonRule"></ngx-monaco-editor>
    </div>
    <div class="addItemsubmitButton">
      <button mat-raised-button class="green" type="submit" (click)="closeDialog('json')"
        *ifHasPermission="BP_RESOURCE.Business_Process; scope:'CHANGE'">
        {{"label.button.saveRule" | literal}}
      </button>
    </div>
  </mat-card-footer>
</div>



<div *ngIf="themeService.useNewTheme" class="newUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css height-55" fxLayout="row wrap"
    fxLayoutGap="4px">
    <div fxLayout="row wrap" fxFlex="100%" fxLayoutAlign="space-between center">
      <div fxFlex="80%" fxLayoutAlign="start center">
        <h2>{{translationContext | literal }}</h2>
      </div>

      <div fxFlex="20%" fxLayoutAlign="end center">
        <mat-slide-toggle *ngIf="this.parentName == 'eventRules'" matTooltipClass="accent-tooltip"
          [checked]="false" matTooltip='Swicth to new Front-End Rules UI for a better experience'
          (change)="switchToNewConfiguration($event.checked)" color="primary">
        </mat-slide-toggle>
        <div *ngIf="showNewConfigTooltip" class="custom-tooltip  shape   m-l-25">
          <div class="tooltip-content ">
            <h4>{{'label.tooltip.rulesConfigurationNew' | literal}}</h4>
            <p>{{'label.tooltip.rulesConfigurationNewSub' | literal}}
            </p>
            <button (click)="showNewConfigTooltip = false">OK</button>
          </div>
        </div>
        <button mat-icon-button (click)="navigateToHelp()" matTooltipPosition="above"
          matTooltipClass="accent-tooltip" matTooltip="Rules Configuration Guide">
          <span class="material-symbols-outlined">help</span>
        </button>
        <button mat-icon-button (click)="closeDialog('close')">
          <span class="material-symbols-outlined">close</span>
        </button>
      </div>

    </div>

    <div fxLayout="row wrap" fxFlex="100%" fxLayoutAlign="center center">
      <ngx-monaco-editor (onInit)="registerJsonKeySuggestions()" class="full-width"
        [options]="editorOptions" [(ngModel)]="jsonRule"></ngx-monaco-editor>
    </div>

  </mat-dialog-content>
  <mat-card-footer>

    <div fxFlex="100%" fxLayout="row" fxLayoutAlign="center center">
      <button class="m-v-2" mat-raised-button color="primary" type="submit"
        (click)="closeDialog('json')"
        *ifHasPermission="BP_RESOURCE.Business_Process; scope:'CHANGE'">
        {{"label.button.saveRule" | literal}}
      </button>
    </div>
  </mat-card-footer>
</div>
