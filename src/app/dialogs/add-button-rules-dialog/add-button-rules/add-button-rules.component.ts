import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Optional } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { ConfigurationResources } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-add-button-rules",
  templateUrl: "./add-button-rules.component.html",
  styleUrls: ["./add-button-rules.component.scss"],
})
export class AddButtonRulesComponent implements OnInit, OnDestroy {
  isNewConfig = false;
  editorOptions = { theme: "vs-dark", language: "json", autoIndent: "full" };
  jsonRule: any;
  selectedUsers: any = [];
  translationContext: string;
  useNewThemeUI: any;
  customProvider: any;
  parentName: string = null;
  sideBarItemRuleKeys = [
    "__report__",
    "__topMenu__",
    "__subMenu__",
    "__dataModel__",
    "__businessProcess__",
    "__dashboard__",
    "__entityDefinition__",
    "__reports__",
    "__rolesActions__",
    "__themeToggle__",
    "__userGuide__",
    "__deal__",
    "__planner__",
    "__bulkStageMove__",
    "__personDetails__",
    "__companyDetails__",
  ];
  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }

  constructor(
    public dialogRef: MatDialogRef<AddButtonRulesComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private readonly data: any,
    public notificationMessage: ToasterService,
    public themeService: ThemeService
  ) {
    this.isNewConfig = data.isNewUI;
    this.parentName = data.parentName;
    if (this.parentName == "eventRules") {
      this.showOrHideTooltip(true);
    }
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;
    this.translationContext = this.data.translationContext;

    if (this.data.buttonRules != null) {
      this.jsonRule = JSON.stringify(this.data.buttonRules);
    }
  }

  ngOnDestroy(): void {
    this.customProvider?.dispose();
  }

  closeDialog(event) {
    if (event == "close") {
      this.dialogRef.close(undefined);
    } else if (event == "json") {
      let rules;
      if (this.jsonRule == "" || this.jsonRule == undefined) {
        rules = null;
        this.dialogRef.close(rules);
      } else {
        rules = JSON.parse(this.jsonRule);
        if (typeof rules == "string") {
          this.notificationMessage.error("Please Enter valid rules");
        } else if (Object.keys(rules).length === 0) {
          this.dialogRef.close(null);
        } else {
          let data = {
            rules: rules,
            eventName: "saveRule",
          };
          this.dialogRef.close(data);
        }
      }
    }
  }

  navigateToHelp() {
    window.open("/rules-configuration-guide", "_blank");
  }

  registerJsonKeySuggestions(): void {
    if (!window["monaco"] || !this.data?.enableSuggestions) return;
    this.customProvider = window[
      "monaco"
    ].languages.registerCompletionItemProvider("json", {
      provideCompletionItems: (model, position) => {
        const match = true;
        if (!match) {
          return { suggestions: [] };
        }
        const word = model.getWordUntilPosition(position);
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        };
        return {
          suggestions: createDependencyProposals(
            range,
            this.sideBarItemRuleKeys
          ),
        };
      },
    });

    function createDependencyProposals(range, assetItems) {
      const suggestions = [];
      assetItems.forEach((item) => {
        suggestions.push({
          label: item,
          kind: window["monaco"].languages.CompletionItemKind.Property,
          insertText: `"${item}": `,
          detail: "Rule Property",
          range: range,
        });
      });

      return [...suggestions];
    }
  }

  showNewConfigTooltip = false;
  switchToNewConfiguration(event) {
    let data = {
      eventName: "switchToNew",
    };
    this.dialogRef.close(data);
  }

  showOrHideTooltip(value): void {
    this.showNewConfigTooltip = value;
    setTimeout(() => {
      if (this.showNewConfigTooltip) {
        this.showNewConfigTooltip = !value;
      }
    }, 5000);
  }
}
