@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
    $color-config: mat.get-color-config($theme);
    $background-palette: map.get($color-config, 'background');

}

@mixin typography($theme) {
    $typography-config: mat.get-typography-config($theme);


    .add-btn-rules-dialog-container {
        .add-btn-rules-dialog-container-1 {
            .add-btn-rules-dialog-header {
                span {
                    @include mat.typography-level($typography-config, 'headline-5');
                }
            }
        }
    }
}

@mixin theme($theme) {
    $color-config: mat.get-color-config($theme);

    @if $color-config !=null {
        @include color($theme);
    }

    $typography-config: mat.get-typography-config($theme);

    @if $typography-config !=null {
        @include typography($theme);
    }
}
