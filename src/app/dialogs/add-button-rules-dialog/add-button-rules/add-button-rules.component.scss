//new UI
.editor-options {
  ngx-monaco-editor {
    height: 45vh !important;
  }
}





.mb-20 {
  margin-bottom: 20px;
}

//  new UI

.rule-section {
  overflow-y: auto;
  // height: 250px;
  width: 100%;
}

.editor-container {
  height: 50vh;
  width: 100%;
}

.mr0 {
  margin-right: 0 !important;
}

.mTitle {
  margin-top: -0.5%;
  margin-bottom: 1%;
}




.position-relative {
  position: relative;
}

//CSS for custom tooltip on userguide manual icon starts
.custom-tooltip {


  z-index: 1000;
  font-family: Arial, sans-serif;
}



.top-400 {
  top: 400px !important;
}

.top-330 {
  top: 330px !important;
}

.tooltip-arrow {
  position: absolute;
  top: -8%;
  /* Adjust for arrow alignment */
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid #fff;
  float: right;
  /* Match tooltip background */
}



.tooltip-content {
  padding: 16px;
}

.tooltip-content h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.tooltip-content p {
  margin: 8px 0;
  font-size: 14px;
}

.tooltip-content button {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
}

































div.shape {
  position: absolute;
  right: 16%;
  top: 8%;
  width: 300px;
  height: 180px;
  margin-top: 20px;
  border: 2px solid transparent;
  border-right: none;
  /* not required as the shape needs to be transparent */
  border-radius: 8px;
  /* not required as the right border is done through pseudo element */
  border-top-right-radius: 0px;
}

div.shape:before {
  position: absolute;
  content: '';
  top: -2px;
  /* equal to border top of parent - no need to change*/
  right: -2px;
  ;
  /* for positioning - no need to change*/
  height: 15%;
  /* should be changed depending on height of arrow */
  width: 10%;
  /* should be changed depending on width of arrow */
  border-top: 2px solid transparent;
  border-right: 3px solid transparent;
  /* thicker border because skew makes it thin */

  /* to achieve the arrow like shape */
  transform-origin: bottom right;
  -webkit-transform-origin: bottom right;
  -webkit-transform: skew(-45deg);
  -moz-transform: skew(-45deg);
  transform: skew(-45deg);
}
