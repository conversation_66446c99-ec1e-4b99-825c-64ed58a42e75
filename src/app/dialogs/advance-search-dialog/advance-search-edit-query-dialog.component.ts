import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DealService } from '../../shared-service/deal.service';
import { ThemeService } from 'src/app/theme.service';
@Component({
    selector: 'advance-search-edit-query-dialog',
    templateUrl: './advance-search-edit-query-dialog.component.html',
    styleUrls: ['./advance-search-edit-query-dialog.component.scss']
  })
  export class AdvanceSearchEditQueryDialogComponent implements OnInit{
    constructor( public dialogRef: MatDialogRef<AdvanceSearchEditQueryDialogComponent>,
       @Inject(MAT_DIALOG_DATA) public data,
    private dealService: DealService,
    public themeService: ThemeService
  ) { }

 ngOnInit() {   

   
 }

 submit(){
   this.dialogRef.close(true)
 }
  }