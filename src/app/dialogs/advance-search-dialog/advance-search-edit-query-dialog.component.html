
<div *ngIf="!themeService.useNewTheme" class="oldUI">
  
  <mat-dialog-content>

 
    <div fxLayout="row wrap" fxLayoutGap="4px" >
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

      <h1 class="editModalTxtColor">
        Edit search 
      </h1>
    </div>

    <div fxLayout="column" fxLayoutGap="4px" class="pb-3">

    <h3 class="editModalTxtColor">
     {{data.message}}
    </h3>

      <mat-card-actions fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mlt mt-5" align="center" >
        <button mat-raised-button  class="green" (click)="submit()"> CONTINUE </button>
        <button mat-raised-button class="red ml-3" mat-dialog-close  > CANCEL </button>
      </mat-card-actions>

    </div>
    </div>
</mat-dialog-content>
</div>


<div *ngIf="themeService.useNewTheme">

  <mat-dialog-content>

    <div fxLayout="column">
      <div>
        <h2>Edit search</h2>
      </div>

      <div>
        <h4>{{data.message}}</h4>
      </div>

      <div fxLayoutAlign="center center" fxLayoutGap="10">
          <button mat-raised-button class="warn-button" mat-dialog-close>CANCEL</button>
          <button mat-raised-button color="primary" (click)="submit()">CONTINUE</button>
      </div>
    </div>
</mat-dialog-content>
</div>


