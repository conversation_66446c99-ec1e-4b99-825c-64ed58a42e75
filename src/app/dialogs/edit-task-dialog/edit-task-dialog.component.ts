import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TasksServiceService } from 'src/app/task/tasks-service/tasks-service.service';
import { ValidationErrorMessageService } from '../../shared-service/validation-error-message.service';


@Component({
  selector: 'app-edit-task-dialog',
  templateUrl: './edit-task-dialog.component.html',
  styleUrls: ['./edit-task-dialog.component.scss']
})
export class EditTaskDialogComponent implements OnInit {

  taskData : any;

  constructor(@Inject(MAT_DIALOG_DATA) public data,
  public taskService: TasksServiceService,
  public matDialog: MatDialog,
  private errorMessageService: ValidationErrorMessageService,
  public dialogRef: MatDialogRef<EditTaskDialogComponent>) {
   
    this.taskData = data.data
   }

  ngOnInit() {
  }


  getColor(value){ 
    if(value.toLowerCase() != "investment"){
      return 'accent';
    }else{
      return "warn"
    }
  }
}
