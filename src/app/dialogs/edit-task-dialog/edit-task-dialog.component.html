<app-edit-task>
    <div generalDetails>
        <div fxLayout="row wrap" fxLayoutGap="4px" class="px-2" *ngIf="taskData.taskTagList && taskData.taskTagList.length != 0">
            <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%">
                <p class="labelFont">{{"label.relatedToeditTaskDialog"|literal}}</p>
            </div>
            <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="80%" fxFlex.sm="80%" class="padding-top-3" >

                <mat-chip-grid #chipList>
                    <ng-container *ngFor="let tag of taskData.taskTagList">
                        <mat-chip-row class="relatedToLabel pointer" [color]="getColor(tag.name)" selected>
                            {{tag.value}}</mat-chip-row>
                    </ng-container>

                    <input [matChipInputFor]="chipList">
                </mat-chip-grid>

            </div>
        </div>
    </div>
</app-edit-task>