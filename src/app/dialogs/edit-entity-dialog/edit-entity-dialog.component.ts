import { Component, Inject } from "@angular/core";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { EntityService } from "src/app/shared-service/entity.service";
import { ToasterService } from "src/app/common/toaster.service";
import JsonData from "src/assets/data.json";
import { ErrorService } from "src/app/shared-service/error.service";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { FormControl, Validators } from "@angular/forms";

@Component({
  selector: "app-edit-entity-dialog",
  templateUrl: "./edit-entity-dialog.component.html",
  styleUrls: ["./edit-entity-dialog.component.scss"],
})
export class EditEntityDialogComponent {
  entityNameInput: FormControl;
  customerId;
  JsonData: any;
  nameRegex: RegExp;
  For;

  constructor(
    public dialogRef: MatDialogRef<EditEntityDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private entityServiceObj: EntityService,
    public notificationservice: ToasterService,
    private errorService: ErrorService,
    public themeService: ThemeService,
    private validationService: ValidationErrorMessageService
  ) {
    this.customerId = data?.customerId;
    this.entityNameInput = new FormControl(data?.entityName, [
      Validators.required,
      Validators.pattern(this.validationService.nameRegex),
    ]);
  }

  closeDialog() {
    this.dialogRef.close();
  }

  saveEntityEdit() {
    if (this.entityNameInput.invalid) {
      this.notificationservice.error("Please fill in valid entity name.");
      return;
    }
    const para1 = this.customerId;
    const para2 = this.entityNameInput.value;

    this.entityServiceObj.editEntityNameServ(para1, "sync", para2).subscribe({
      next: (res) => {
        const result = {
          newEntityName: this.entityNameInput.value,
        };
        this.notificationservice.success(
          JsonData["label.success.UpdateDetails"]
        );
        this.dialogRef.close(result);
      },
    });
  }
}
