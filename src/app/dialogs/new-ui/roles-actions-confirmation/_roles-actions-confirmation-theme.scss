@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $accent-palette: map.get($color-config, 'accent');
  $primary-color: mat.get-color-from-palette($primary-palette, 400);
  $accent-color: mat.get-color-from-palette($accent-palette, 400);

.warning-dialog-header{
    color: $primary-color;
}
.dialog-accent-text{
    color: $accent-color;
}
}
@mixin typography($theme) {
 
}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}