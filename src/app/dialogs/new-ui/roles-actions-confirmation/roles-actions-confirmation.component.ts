import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-roles-actions-confirmation',
  templateUrl: './roles-actions-confirmation.component.html',
  styleUrls: ['./roles-actions-confirmation.component.scss'],
})
export class RolesActionsConfirmationComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public diaglogRef : MatDialogRef<RolesActionsConfirmationComponent>

  ){

  }
}
