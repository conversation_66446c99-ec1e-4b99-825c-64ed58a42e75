<div *ngIf="!themeService.useNewTheme" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div class="create-asset-dialog padding">
      <div>
        <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
          <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
            <h2>{{"label.title.cloneBusinessProcess"|literal}}</h2>
          </div>
          <div fxFlex="18%" fxFlex.md="10%" fxFlex.xs="30%" fxFlex.sm="30%">
            <button mat-button (click)="closeDialog()">
              <mat-icon class="close-icon">close</mat-icon>
            </button>
          </div>
        </div>
      </div>


      <form [formGroup]="cloneForm">
        <ng-container>
          <div fxLayout="row wrap" fxLayoutGap="4px">
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
              <span class="mr-3"> Clone Workflow Rules</span>
              <mat-slide-toggle color="primary" class="example-margin mr-3"
                (change)="ToggleSlide($event)">
              </mat-slide-toggle>

              <mat-form-field class="width-100">
                <mat-label>Business Process Name</mat-label>
                <input matInput required autocomplete="off" placeholder="Business Process Name"
                  [(ngModel)]="bPName" formControlName="businessProcessName">
                <mat-error *ngIf="cloneForm.controls.businessProcessName.errors?.required">
                  {{"label.materror.BP"|literal}}

                </mat-error>
              </mat-form-field>
              <br><br>

              <mat-form-field class="width-100">
                <mat-label>Alias Name</mat-label>
                <input matInput required autocomplete="off" placeholder="Alias Name"
                  [(ngModel)]="aliasName" formControlName="aliasName">
                <mat-error *ngIf="cloneForm.controls.aliasName.errors?.required">
                  {{"label.materror.Aliasname"|literal}}

                </mat-error>
              </mat-form-field>
              <br><br>

              <mat-form-field class="width-100">
                <mat-label>Record Name Options</mat-label>
                <mat-select disableRipple name="recordNameOptions" formControlName="dealIdentifier">
                  <mat-option *ngFor="let dropDown of dealIdentifierConfiguration"
                    [value]="dropDown">{{ dropDown }}</mat-option>
                </mat-select>

                <mat-error *ngIf="cloneForm.controls.aliasName.errors?.required">
                  {{"label.materror.Recordname"|literal}}
                </mat-error>
              </mat-form-field>
              <span class="warning_message"
                *ngIf="cloneForm.controls.dealIdentifier.value==='Custom'">Selecting 'Custom'
                requires creating and linking the <b>"&lt;Business Process Name&gt;_Case_Name"</b>
                workflow in the Business Process to auto-generate the application name.</span>
              <br><br>
              <mat-form-field class="width-100">
                <mat-label>Description</mat-label>
                <input matInput autocomplete="off" placeholder="Description"
                  [(ngModel)]="description" formControlName="description">
              </mat-form-field>

            </div>
          </div>
        </ng-container>
        <div class="button-row">
          <button mat-raised-button class="blue" type="submit" (click)="submit()">
            {{"label.button.cloneBusinessProcess"|literal}}
          </button>

        </div>
      </form>
    </div>
  </mat-dialog-content>
</div>

<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="row wrap" class="bp-clone-container">

      <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start center">
          <h2>{{"label.title.cloneBusinessProcess"|literal}}</h2>
        </div>
        <div fxLayoutAlign="end center">
          <span>
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon class="material-symbols-outlined">close</mat-icon>
            </button>
          </span>
        </div>
      </div>

      <form [formGroup]="cloneForm" class="full-width m-t-15">
        <div fxLayout="row wrap">
          <div fxFlex="100%" fxLayoutGap="10" class="m-l-10 m-b-20">
            <span>Clone Workflow Rules</span>
            <mat-slide-toggle color="primary" (change)="ToggleSlide($event)">
            </mat-slide-toggle>
          </div>

          <mat-form-field fxFlex="100%">
            <mat-label>Business Process Name</mat-label>
            <input matInput required autocomplete="off" placeholder="Business Process Name"
              [(ngModel)]="bPName" formControlName="businessProcessName">
            <mat-error *ngIf="cloneForm.controls.businessProcessName.errors?.required">
              {{"label.materror.BP"|literal}}
            </mat-error>
          </mat-form-field>

          <mat-form-field fxFlex="100%">
            <mat-label>Alias Name</mat-label>
            <input matInput required autocomplete="off" placeholder="Alias Name"
              [(ngModel)]="aliasName" formControlName="aliasName">
            <mat-error *ngIf="cloneForm.controls.aliasName.errors?.required">
              {{"label.materror.Aliasname"|literal}}
            </mat-error>
          </mat-form-field>

          <mat-form-field fxFlex="100%">
            <mat-label>Record Name Options</mat-label>
            <mat-select disableRipple name="recordNameOptions" formControlName="dealIdentifier">
              <mat-option *ngFor="let dropDown of  dealIdentifierConfiguration"
                [value]="dropDown">{{ dropDown }}</mat-option>
            </mat-select>
            <mat-error *ngIf="cloneForm.controls.aliasName.errors?.required">
              {{"label.materror.Recordname"|literal}}
            </mat-error>
          </mat-form-field>
          <span class="warning_message"
            *ngIf="cloneForm.controls.dealIdentifier.value==='Custom'">Selecting 'Custom' requires
            creating and linking the <b>"&lt;Business Process Name&gt;_Case_Name"</b> workflow in
            the Business Process to auto-generate the application name.</span>
          <mat-form-field fxFlex="100%">
            <mat-label>Description</mat-label>
            <input matInput autocomplete="off" placeholder="Description" [(ngModel)]="description"
              formControlName="description">
          </mat-form-field>
        </div>

        <div class="dialog-button" fxLayout="row wrap">
          <button color="primary" mat-raised-button type="submit" (click)="submit()">
            {{"label.button.cloneBusinessProcess"|literal}}
          </button>
        </div>
      </form>

    </div>
  </mat-dialog-content>
</div>
