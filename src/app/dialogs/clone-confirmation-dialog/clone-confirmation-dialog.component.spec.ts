import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CloneConfirmationDialogComponent } from './clone-confirmation-dialog.component';

describe('CloneConfirmationDialogComponent', () => {
  let component: CloneConfirmationDialogComponent;
  let fixture: ComponentFixture<CloneConfirmationDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CloneConfirmationDialogComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CloneConfirmationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
