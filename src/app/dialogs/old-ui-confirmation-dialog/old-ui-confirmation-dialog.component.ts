import { Component } from "@angular/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-old-ui-confirmation-dialog",
  templateUrl: "./old-ui-confirmation-dialog.component.html",
  styleUrls: ["./old-ui-confirmation-dialog.component.scss"],
})
export class OldUiConfirmationDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<OldUiConfirmationDialogComponent>,
    public dialog: MatDialog,
    public themeService: ThemeService
  ) {}

  switchToNewTheme() {
    this.dialogRef.close(true);
  }

  switchToOldTheme() {
    this.dialogRef.close(false);
  }
}
