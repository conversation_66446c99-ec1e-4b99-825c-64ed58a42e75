@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $warn-palette: map.get($color-config, 'warn');


  .old-ui-confirmation-container {
    .headline-main {
      color: mat.get-color-from-palette($warn-palette, 200) !important;
    }
  }


}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);

  .new-ui-confirmation-container {
    .headline-main {
      @include mat.typography-level($typography-config, 'headline-5');
    }

    .headline-support {
      @include mat.typography-level($typography-config, 'subtitle-1');
    }
  }
}



@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
