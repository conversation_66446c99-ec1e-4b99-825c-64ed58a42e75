import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AddItemTemplateComponent } from "../settings/assets/view-asset-detail/add-item-template/add-item-template.component";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { AddressConfigComponent } from "../settings/assets/view-asset-detail/address-config/address-config.component";
import { TableDataTypeConfigComponent } from "../settings/assets/view-asset-detail/table-data-type-config/table-data-type-config.component";
import { EditItemDialogComponent } from "../settings/assets/edit-item-dialog/edit-item-dialog.component";
import { AddItemComponent } from "../settings/entity/add-item/add-item.component";
import { EditItemComponent } from "../settings/entity/edit-item/edit-item.component";
import { AddDataModelItemsComponent } from "../settings/bussiness-process-config/steps/sub-steps/create-data-model/add-data-model-items/add-data-model-items.component";

@NgModule({
  declarations: [
    // AddItemTemplateComponent,
    // EditItemDialogComponent,
    // AddItemComponent,
    // EditItemComponent,
    AddressConfigComponent,
    TableDataTypeConfigComponent,
    // AddDataModelItemsComponent,
  ],
  imports: [CommonModule, SharedModuleModule],
  exports: [
    // AddItemTemplateComponent,
    // EditItemDialogComponent,
    // AddItemComponent,
    // EditItemComponent,
    AddressConfigComponent,
    TableDataTypeConfigComponent,
    // AddDataModelItemsComponent,
  ],
})
export class ConfigurationSharedModule {}
