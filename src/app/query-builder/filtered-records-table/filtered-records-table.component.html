<mat-card-content  class="mat-mdc-table-wrap-text" >

  <div fxLayout="row" fxLayoutAlign="end center">
    <button mat-icon-button (click)="bottomsheetRef.dismiss()">
      <mat-icon>close</mat-icon>
    </button>
  </div>
    <div fxLayout="row"  fxLayoutAlign="space-between center" class="p-percent-1 ">
        <div fxLayout="row"  fxLayoutAlign="start center" fxFlex="50" aria-label="header">
          <h2 class="no-m">Filtered Records</h2>
          <button  mat-icon-button [matTooltip]="searchQuery"  matTooltipPosition="above" matTooltipClass="accent-tooltip">
            <span class="material-symbols-outlined" >info</span>
          </button>
        </div>
        <div fxFlex="50">
          <div fxLayout="row" fxLayoutGap="10" fxLayoutAlign="end center">
            <mat-form-field appearance="outline" subscriptSizing="dynamic" class="full-width">
              <mat-icon matIconPrefix>search</mat-icon>
              <input aria-label="search-records-field" autocomplete="off" matInput  placeholder="Search" #searchInput (keyup)="applyFilter($event.target.value)" />
            </mat-form-field>
            <button mat-icon-button class="outlined-icon-button large-icon-button" [matMenuTriggerFor]="tableColSelectionMenu" matTooltip="Select columns" matTooltipClass="accent-tooltip">
              <mat-icon mat-icon-button >filter_list</mat-icon>
            </button>
            <button mat-icon-button class="outlined-icon-button large-icon-button"  matTooltip="Download Records" matTooltipClass="accent-tooltip" (click)="downloadedData()">
              <span class="material-symbols-outlined">get_app</span>
            </button>
          </div>
        </div>
  
    </div>
    <mat-divider class="relative"></mat-divider>
      <div class="data-table-container mat-mdc-table-wrap-text">
          <table mat-table [dataSource]="dataSource" matSort class="full-width"
          [matSortDirection]="sortDirection" (matSortChange)="sortData($event)">
            
            <ng-container *ngFor="let column of data.displayColumns;let i = index"
                      matColumnDef="{{ column.name}}">
  
                      <th mat-sort-header mat-header-cell *matHeaderCellDef>
                        {{column.displayName }}
                      </th> 
  
                      <td mat-cell *matCellDef="let element; let i = index" matTooltipShowDelay="1500" tooltip>
                        <span *ngIf="column.name === dealStaticColumn.name" 
                        (click)="navigateToSummaryPage(element, column.name)">
                          <p [matTooltip]="element?.dealIdentifier" class="hyperlinkColor link pointer">
                            {{element?.dealIdentifier}}</p>
                        </span>
                        <span *ngIf="column.name === entityStaticColumn.name" 
                        (click)="navigateToSummaryPage(element, column.name)">
                          <p [matTooltip]="element?.name" class="hyperlinkColor link pointer">
                            {{element?.name}}</p>
                        </span>
                        <ng-container *ngIf="!(column.name === dealStaticColumn.name || column.name === entityStaticColumn.name)">
                          {{element[column.name] === 'null' || element[column.name] === '' ? '-' :
                           element[column.name] }}
                        </ng-container>
                      
                      </td>
  
                    </ng-container>
            <tr mat-header-row *matHeaderRowDef="getDisplayCols(); sticky:true"></tr>
            <tr mat-row *matRowDef="let row; columns: getDisplayCols()"></tr>
          </table>
        <div class="no-records-found mt-1" *ngIf="dataSource.filteredData.length === 0 && !loader" ></div>
        <div class="mt-1" *ngIf="loader" >
          <mat-spinner></mat-spinner>
        </div>
        </div>
        <mat-paginator [length]="totalElements" [pageSize]="pageSize" [pageIndex]="pageIndex"
        [pageSizeOptions]="[8, 25,50, 100,500]" (page)="onPaginationChanged($event)"></mat-paginator>
</mat-card-content>


<mat-menu #tableColSelectionMenu="matMenu">
  <div class="table-column-menu">
    <div fxLayout="row" fxLayoutAlign="center center">
      <mat-form-field appearance="outline" class="dense width-90" subscriptSizing="dynamic"
       (click)="$event.stopPropagation()">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput #input placeholder="Search column" #columnSearchInput (input)="filterTableCols(columnSearchInput.value)">
        <mat-icon *ngIf="columnSearchInput.value" (click)="columnSearchInput.value='';filterTableCols(columnSearchInput.value)"
        class="material-symbols-outlined pointer" matSuffix>cancel</mat-icon>
      </mat-form-field>
    </div>


    <div class="column-list">
      <div *ngFor="let value of filteredTableCols">
        <mat-checkbox (click)="$event.stopPropagation();" [checked]="ischecked(value)"
        (change)="onSelectOfValue($event.checked,value)" [value]="value"
        class="mat-menu-item">{{value.displayName}}</mat-checkbox>
      </div>
      <ng-container *ngIf="filteredTableCols.length === 0">
        <div class="center" mat-menu-item>
          No matching column found.
        </div>
      </ng-container>
    </div>
  </div>
</mat-menu>
