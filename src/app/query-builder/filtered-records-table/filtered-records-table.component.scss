
//old UI css
mat-spinner {
    left:45% !important;
}


 td span{
    display: inline-flex;
 }
 th {
    width: 250px;
 }

.table-column-menu{
    max-height: 45vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .column-list{
        overflow-y: auto;
        flex-grow: 1; 
        padding: 1%;
    }
}


mat-card-content{
    display: flex;
    flex-direction: column;
    height: 98%;
}

.data-table-container{
    overflow: auto;
    flex-grow: 1;
}

.no-records-found{
    height: 25vh !important;
  }



