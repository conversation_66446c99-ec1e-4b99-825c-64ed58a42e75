import { Component, Inject, OnInit } from "@angular/core";
import { MatTableDataSource } from "@angular/material/table";
import { QueryBuilderService } from "../query-builder.service";
import { DealService } from "src/app/shared-service/deal.service";
import { DatePipe } from "@angular/common";
import * as xlsx from "xlsx";
import { Sort } from "@angular/material/sort";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { Router } from "@angular/router";
import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheetRef,
} from "@angular/material/bottom-sheet";
import { OverlayService } from "../overlay.service";

@Component({
  selector: "app-filtered-records-table",
  templateUrl: "./filtered-records-table.component.html",
  styleUrls: ["./filtered-records-table.component.scss"],
})
export class FilteredRecordsTableComponent implements OnInit {
  // @Input () displayColumns: Column[]

  searchResp = [];
  dataSource = new MatTableDataSource([]);
  pageSize = 25;
  pageIndex = 0;
  sortKey = "createdDate";
  sortDirection = "desc";
  searchQuery = "";
  totalElements = 0;
  dealStaticColumn: Column = {
    inputType: "text",
    name: "dealIdentifier",
    displayName: "Description",
    display: true,
  };

  entityStaticColumn: Column = {
    inputType: "text",
    name: "name",
    displayName: "Name",
    display: true,
  };

  nonAssetCols = [
    "dealIdentifier",
    "name",
    "businessProcessName",
    "currentStageName",
    "createdDate",
    "currentStatus",
  ];
  selectedQueryType = "dataModel";
  filteredTableCols: string[];
  loader: boolean;

  constructor(
    private queryBuilderSerivice: QueryBuilderService,
    private dealService: DealService,
    private datePipe: DatePipe,
    private dataSharingService: DataSharingService,
    private entityService: EntityService,
    private router: Router,
    @Inject(MAT_BOTTOM_SHEET_DATA) public data,
    public bottomsheetRef: MatBottomSheetRef<FilteredRecordsTableComponent>,
    private overlayService: OverlayService
  ) {
    this.dataSource = new MatTableDataSource(this.searchResp);
  }

  ngOnInit(): void {
    this.filteredTableCols = this.data.displayColumns;
    this.searchQuery = this.data.query;
    this.searchRecords();

    this.selectedQueryType = this.queryBuilderSerivice.selectedQueryType;
    if (this.selectedQueryType === "entity") {
      if (
        this.data.displayColumns.some(
          (col) => col.name === this.entityStaticColumn.name
        )
      )
        return;
      this.data.displayColumns.unshift(this.entityStaticColumn);
    } else {
      if (
        this.data.displayColumns.some(
          (col) => col.name === this.dealStaticColumn.name
        )
      )
        return;
      this.data.displayColumns.unshift(this.dealStaticColumn);
    }
  }

  getSearchResp(searchRespData) {
    this.searchResp = [];
    const accessNode =
      this.queryBuilderSerivice.selectedQueryType == "entity"
        ? "entityDetail"
        : "dealAsset";
    searchRespData.forEach((ele) => {
      const assetObj = ele[accessNode] ? ele[accessNode] : {};
      for (const key in assetObj) {
        if (key) {
          if (assetObj.hasOwnProperty(key)) {
            const value = assetObj[key];
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              assetObj[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = assetObj[key].name;
              assetObj[key] = newvalue;
            } else if (value == "null") {
              assetObj[key] = "";
            }
          }
        }
      }
      assetObj["id"] = ele.id;
      assetObj["dealIdentifier"] = ele.dealIdentifier;
      assetObj["businessProcessName"] = ele.businessProcessName;
      assetObj["currentStageName"] = ele.currentStageName;
      assetObj["createdDate"] = this.datePipe.transform(ele.createdDate);
      assetObj["currentStatus"] = ele.currentStatus;
      assetObj["name"] = ele?.name;
      assetObj["customerId"] = ele?.customerId;

      const entityObj = ele?.dealEntity ? ele.dealEntity : {};

      for (const key in entityObj) {
        if (entityObj?.[key]) {
          const newEntityKey = "entity" + key;
          entityObj[newEntityKey] = entityObj[key];
          delete entityObj[key];
        }
        if (entityObj.hasOwnProperty(key)) {
          const value = entityObj[key];
          if (value && Array.isArray(value)) {
            const valueArray = [];
            value?.forEach((ele) => valueArray.push(ele.name));
            entityObj[key] = valueArray;
          } else if (typeof value === "object" && value !== null) {
            const newvalue = entityObj[key].name;
            entityObj[key] = newvalue;
          }
        }
      }
      const obj = { ...assetObj, ...entityObj };
      this.searchResp.push(obj);
    });

    this.dataSource = new MatTableDataSource(this.searchResp);
  }

  onSelectOfValue(checked: boolean, column: Column) {
    column.display = checked;
  }

  ischecked(column: Column): boolean {
    return column?.display;
  }

  navigateToSummaryPage(data, header) {
    if (header == "dealIdentifier") {
      this.dataSharingService.selectedApplicationData = undefined;
      this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);
      this.router.navigate(["/application-summary/details/" + btoa(data.id)]);
      this.bottomsheetRef.dismiss();
      this.overlayService.close();
    } else if (header == "name") {
      this.dataSharingService.newSubPageNameValue(data.Name);
      this.dataSharingService.subPageEntityIdValue(data?.customerId);
      this.dataSharingService.companyIdOfPersonValue(data?.customerId);
      this.entityService
        .getCustomerBasicDetails(data.customerId)
        .subscribe((resp: any) => {
          this.entityService.customerDetails = null;
          const path =
            resp.entityType === "Person"
              ? "entity/viewperson/detail/"
              : "/entity/viewcompany/detail/";
          this.router.navigate([`${path}` + btoa(data.customerId)]);
          this.bottomsheetRef.dismiss();
          this.overlayService.close();
        });
    }
  }

  downloadedData() {
    const downloadGraphData = [];

    this.dataSource.data.forEach((record) => {
      const tempObj = {};
      this.data.displayColumns.forEach((col: Column) => {
        if (col.display) {
          tempObj[col.displayName] = record[col.name];
        }
      });
      downloadGraphData.push(tempObj);
    });
    if (downloadGraphData) {
      const excelList = downloadGraphData;
      const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
      const deal = xlsx.utils.json_to_sheet(excelList);
      deal["!cols"] = wscols;
      const sheetNames = ["Records"];
      const sheetobject = { Records: deal };
      const workbook: xlsx.WorkBook = {
        Sheets: sheetobject,
        SheetNames: sheetNames,
      };
      xlsx.writeFile(workbook, `${"Search_Records_" + this.getDateTime()}.csv`);
    }
  }

  getDateTime() {
    const currentdate = new Date();
    return (
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate() +
      "_" +
      currentdate.getHours() +
      ":" +
      currentdate.getMinutes() +
      ":" +
      currentdate.getSeconds()
    );
  }

  getDisplayCols() {
    const getSelectedCols = () => {
      return this.data.displayColumns.filter((col: Column) => col.display);
    };
    return getSelectedCols().map((col: Column) => col.name);
  }

  searchRecords() {
    this.loader = true;
    this.dataSource.data = [];
    const apiCall =
      this.queryBuilderSerivice.selectedQueryType === "entity"
        ? "queryFilterEntityWithPagination"
        : "queryFilterWithPagination";
    this.dealService[apiCall](
      this.searchQuery,
      this.pageIndex,
      this.pageSize,
      this.sortKey,
      this.sortDirection
    ).subscribe(
      (resp: SearchResponse) => {
        this.totalElements = resp.totalElements;
        this.getSearchResp(resp.content);
        this.loader = false;
      },
      (error) => {}
    );
  }

  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.searchRecords();
  }

  sortData(sortEvent: Sort) {
    const isAssetItem = (col) => {
      return !this.nonAssetCols.includes(col);
    };
    this.sortDirection = sortEvent.direction;
    this.sortKey = sortEvent.active;
    if (!sortEvent.direction) {
      this.sortDirection = "desc";
      this.sortKey = "createdDate";
    } else if (
      isAssetItem(sortEvent.active) &&
      this.selectedQueryType === "dataModel"
    ) {
      this.sortKey = "dealAsset." + sortEvent.active;
    } else if (
      isAssetItem(sortEvent.active) &&
      this.selectedQueryType === "entity"
    ) {
      this.sortKey = "entityDetail." + sortEvent.active;
    }
    this.searchRecords();
  }

  applyFilter(filterValue: string) {
    if (this.dataSource) {
      this.dataSource.filter = filterValue.trim().toLowerCase();
      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    }
  }

  filterTableCols = (searchKey: string) => {
    this.filteredTableCols = this.data.displayColumns.filter((col) =>
      col.displayName.toLowerCase().includes(searchKey)
    );
  };
}

interface Column {
  displayName: string;
  name: string;
  inputType: string;
  display: boolean;
}

interface SearchResponse {
  content: [];
  last: boolean;
  pageNo: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
}
