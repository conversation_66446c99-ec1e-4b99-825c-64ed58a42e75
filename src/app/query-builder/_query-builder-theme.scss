

@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);

  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');
  $background-palette: map.get($color-config, 'background');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $background-color: mat.get-color-from-palette($background-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, $primary-hue);
  
  .adjust-ui-container{
    mat-expansion-panel-header:hover{
      background-color: mat.get-color-from-palette($primary-palette,100) !important;
      box-shadow: none ;
      mat-form-field{

          .mat-mdc-text-field-wrapper{
            background-color: mat.get-color-from-palette($primary-palette,100);
          }
      
          .mat-mdc-select-value{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }
      
          .mat-mdc-select-arrow{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }

          .mat-mdc-floating-label{
            color: mat.get-contrast-color-from-palette($primary-palette, 100);
          }
        }
  }
}

.query-builder-field-container{
  mat-form-field{
    .mat-mdc-text-field-wrapper{
      border: 1px solid $primary-color;
    }
  }
}

}

@mixin typography($theme) {

}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  @if $color-config != null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);
  @if $typography-config != null {
    @include typography($theme);
  }
}