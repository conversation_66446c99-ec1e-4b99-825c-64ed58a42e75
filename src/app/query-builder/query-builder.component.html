
<div class="advance-search-overlay-conatiner">
  <button mat-icon-button class="outlined-icon-button fixed-close-button" *ngIf="false">
    <span class="material-symbols-outlined">close</span>
  </button>
  <div class="query-builder-container">
    <form name="queryBuilderForm">
      <div fxLayout="column">
               
         <div fxLayout="row" fxLayoutGap="5" fxLayoutAlign="center start" class="full-width">
      
          <div fxFlex="80" class="query-builder-field-container">
              <mat-form-field class="full-width" subscriptSizing="dynamic" #formField>
                <mat-icon matIconPrefix>search</mat-icon>
                  <input type="hidden" [formControl]="dateControl" [matDatepicker]="picker" >
                  <textarea type="text" (focus)="removeTruncate()" [cdkTextareaAutosize]="true" (paste)="updateSearchList($event)" (input)="triggerDisplayFun()" 
                    [readonly]="!selectedType || !selectedVal" #inputField placeholder="Type your conditions here" aria-label="conditional-search-field" matInput
                    [formControl]="myControl" [matAutocomplete]="auto" [spellcheck]="false">
                  </textarea>

                  <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn.bind(this)">
                    <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
                      {{option}}
                    </mat-option>
                  </mat-autocomplete>
         
                  <mat-datepicker-toggle *ngIf="getLastFieldInputType() === 'date'" matIconSuffix [for]="picker">
                  </mat-datepicker-toggle>
                  <mat-datepicker #picker>
                  </mat-datepicker>
    
                </mat-form-field>

           <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="5px" class="p-percent-1">
            <button mat-raised-button color="primary" (click)="triggerQuery()" matSuffix [disabled]="!selectedType || !selectedVal">Search</button>

            <button mat-raised-button class="outlined-button" (click)="applyTruncate(); adjustUIHandler.toggle()">
              <div fxLayout="row" fxLayoutAlign="center center"fxLayoutGap="5">
                <span> Adjust</span>
                <span *ngIf="!adjustUIHandler.isOpen" class="material-symbols-outlined">
                  tune
                </span>
                <span *ngIf="adjustUIHandler.isOpen" class="material-symbols-outlined">
                  close
                </span>
              </div>
            </button>

           </div>
          </div>
         </div>
      </div>
     </form>
  </div>

  <div *ngIf="adjustUIHandler.isOpen" fxLayout="row" fxLayoutAlign="center center" >
    <ng-container
    *ngTemplateOutlet="adjustConfigUI">
  </ng-container>
  </div>
  
</div>


<ng-template #adjustConfigUI>
  <mat-card appearance="outline" class="width-80">

  <div class="adjust-ui-container" fxLayout="column" fxLayoutAlign="center center">
    <mat-accordion  class="width-80" >
      <mat-expansion-panel [hideToggle]="true" [expanded]="false">
        <mat-expansion-panel-header class="no-p">
          <mat-form-field  class="full-width no-click-event" [subscriptSizing]="'dynamic'">
            <mat-label class="no-click-event">Search Type</mat-label>
            <mat-select [(value)]="selectedType" (selectionChange)="getList($event.value)">
              <mat-option value = "entity">Entity</mat-option>
              <mat-option value = "dataModel"> Data Model</mat-option>
            </mat-select>
          </mat-form-field>
        </mat-expansion-panel-header>
        <mat-selection-list [multiple]="false" (selectionChange)="getList($event)">
          <mat-list-option togglePosition="before" value = "entity" [selected]="selectedType === 'entity'">Entity</mat-list-option>
          <mat-list-option togglePosition="before" value = "dataModel" [selected]="selectedType=== 'dataModel'"> Data Model</mat-list-option>
        </mat-selection-list>

    </mat-expansion-panel>

    <mat-expansion-panel [hideToggle]="true" [expanded]="false">
      <mat-expansion-panel-header class="no-p">
        <mat-form-field  class="full-width no-click-event" [subscriptSizing]="'dynamic'">
          <mat-label class="no-click-event">Data Model/Entity</mat-label>
          <mat-select [(value)]="selectedVal">
            <mat-option  *ngFor="let list1 of list" [value]="selectedType === 'dataModel' ? list1.id : list1.entityName">
              {{selectedType === 'dataModel'? list1.assetTypeName : list1.entityName}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </mat-expansion-panel-header>

      <mat-form-field appearance="outline" class="dense full-width p-percent-1" >
        <mat-icon matPrefix>search</mat-icon>
        <input matInput #input placeholder="Search" #moduleSeachInput (input)="filterList(moduleSeachInput.value);" >
        <mat-icon *ngIf="moduleSeachInput.value" (click)="moduleSeachInput.value='';filterList(moduleSeachInput.value)" class="material-symbols-outlined pointer" matSuffix>cancel</mat-icon>
        </mat-form-field>

        <mat-selection-list [multiple]="false" (selectionChange)="setAssetItems($event.options[0].value)">
          <mat-list-option *ngFor="let list1 of filteredSelectVals" [value]="selectedType === 'dataModel' ? list1.id : list1.entityName" togglePosition="before" [selected]="selectedType === 'dataModel' ? list1.id === selectedVal : list1.entityName === selectedVal"
          >
            {{selectedType === 'dataModel'? list1.assetTypeName : list1.entityName}}
          </mat-list-option>
        </mat-selection-list>
        <div class="no-records-found" *ngIf="filteredSelectVals.length === 0" ></div>

    </mat-expansion-panel>
    </mat-accordion>
  </div>

  </mat-card>
</ng-template>
