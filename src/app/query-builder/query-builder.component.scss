
.mat-mdc-card-content {
  display: block;
  padding: 0 0px;
}

.fixed-close-button{
  position: fixed;
  top: 10px;
  right: 10px;
  cursor: pointer;
  z-index: 1000;
  background: var(--container-color);
}

.advance-search-overlay-conatiner{
  margin-top: 1.5%;
}

.query-builder-field-container{
  mat-form-field{
    box-shadow: 0px 4px 14.8px 0px rgba(0, 0, 0, 0.25);
  }   
  
  textarea {
    resize: none;
    min-height: 50px;
    max-height: 150px;
    
  }
  
  textarea.truncate {
    max-height: 56px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}


.adjust-ui-container{
  margin: 2% 4%;

  //-------old ui css------------
  // this is handled globally for new UI
  ::ng-deep .mdc-line-ripple::before {
    border: none !important;
    border-bottom-width: 0px !important;
  
  }
  
  ::ng-deep .mdc-line-ripple::after {
    border: none !important;
    border-bottom-width: 0px !important;
  
  }

  //-------old ui css-------------

  mat-expansion-panel{
    background: var(--mdc-filled-text-field-container-color);
    border-radius: 8px !important;
    mat-expansion-panel-header{
      height: 65px;
      border-radius: 8px;
      ::ng-deep .mat-content.mat-content-hide-toggle{
        margin: 0 !important;
      }

      mat-form-field{
        ::ng-deep .mdc-floating-label {
          pointer-events: none;
        }
      }
    }

    .no-records-found{
      height: 20vh !important;
    }

  }
    
  mat-expansion-panel:not(:first-of-type){
      margin-top: 3%;
  }
  
  mat-selection-list{
    max-height: 30vh;
    overflow-y: auto;
  }
}



