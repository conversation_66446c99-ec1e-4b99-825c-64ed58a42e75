import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { dataTypes } from '../settings/dashboard-configuration/dashboard-config/classes-and-interfaces/dashboard-interfaces';
import { DatePipe } from '@angular/common';
import { FormControl } from '@angular/forms';
import { Observable, startWith, map } from 'rxjs';
import { AssetServiceService } from '../shared-service/asset-service.service';
import { BusinessProcessService } from '../shared-service/businessProcess.service';
import { DealService } from '../shared-service/deal.service';
import { EntityService } from '../shared-service/entity.service';
import { QueryBuilderService } from './query-builder.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { FilteredRecordsTableComponent } from './filtered-records-table/filtered-records-table.component';
import { MatSelectionListChange } from '@angular/material/list';

@Component({
  selector: 'app-query-builder',
  templateUrl: './query-builder.component.html',
  styleUrls: ['./query-builder.component.scss']
})
export class QueryBuilderComponent implements OnInit {
  @ViewChild('inputField') inputField: ElementRef;

  selectedType = 'dataModel';
  selectedVal: number|string;
  list = [];
  allBps = [];
  displayColumns: displayColumn[] = [];

  adjustUIHandler = {
    isOpen : false,
    toggle : () =>{
      this.adjustUIHandler.isOpen  = !this.adjustUIHandler.isOpen
    }
  }
  filteredSelectVals: {
    id: number;
    entityName ?: string;
    assetTypeName ?: string;
  }[];



  constructor(
    private BPService:BusinessProcessService,
    private entityService:EntityService,
    private assetService:AssetServiceService,
    private dealService : DealService,
    private queryBuilderService :QueryBuilderService,
    private datePipe : DatePipe,
    private bottomSheet : MatBottomSheet) { }

  myControl = new FormControl({value:'',disabled:true});
  dateControl = new FormControl();
  operators = {
    text:['=' ,'!='],
    number:['=', '!=', '>','<','>=','<='],
    date:['=', '!=', '>','<','>=','<='],

  }

  private fieldList: SuggestionDetails = { Name: Action.Field, Value: [], Valid: ['string'] };
  private operatorList: SuggestionDetails = { Name: Action.Operator, Value: ['=', '!=', '>','<','>=','<='], Valid: ['string'] };
  private valueList: SuggestionDetails = { Name: Action.Value, Value: [], Valid: ['string'] };
  private expressionList: SuggestionDetails = { Name: Action.Expression, Value: ['AND', 'OR'], Valid: ['string'] };

  private operator: string[] = this.operatorList.Value;
  private value: string[] = this.valueList.Value;
  private expression: string[] = this.expressionList.Value;

  private get field(): string[] {
    return this.fieldList.Value;
  }

  filteredOptions: Observable<string[]>;
  private searchList: SelectedOption[] = [];

  private get selectionList(): SelectionDict[] {
    return [
      { Name: Action.Field, Value: this.field, NextSelection: Action.Operator },
      { Name: Action.Operator, Value: this.operator, NextSelection: Action.Value },
      { Name: Action.Value, Value: this.value, NextSelection: Action.Expression },
      { Name: Action.Expression, Value: this.expression, NextSelection: Action.Field }
    ];
  }

  private defaultSelection: string = Action.Field;
  private currentEvent: string;
  private response: ApiResponse[] = [];

  ngOnInit() {
    
    const defaultConfig = this.queryBuilderService.defaultConfig;

    if(defaultConfig && defaultConfig.searchType && defaultConfig.defaultId){
      this.selectedType = defaultConfig.searchType;
      this.selectedVal = defaultConfig.defaultId;
    }

      const dummySelectionEventObject  = {
        options: [
          {value: this.selectedType}
        ]
      } as MatSelectionListChange
      this.getList(dummySelectionEventObject,true);
    this.fieldList
    this.filteredOptions = this.myControl.valueChanges.pipe(
      startWith(''),
      map(value =>  this._filter(value))
    );

    this.dateControl.valueChanges.subscribe(newVal=>{
      this.myControl.setValue(this.datePipe.transform(newVal,'yyyy-MM-d'),{emitEvent:false})
    })

  }
  
  _filter(value: string): string[]{
  if(value == ''){
    this.searchList = [];
    this.currentEvent = Action.Field;
  }
  const optionListToBePopulated: string[] =  this.getOptionList();
  const searchText = this.getSearchText(value);
  return optionListToBePopulated.filter(option => option.toLowerCase().indexOf(searchText.toLowerCase().trim()) != -1);
  }

  displayFn(value: string): string {
    if (value)
     { const formattedVal = this.currentEvent === Action.Value ? '"'+value+'"':value;
      this.searchList.push(new SelectedOption(formattedVal, this.currentEvent, this.getNextEvent(this.currentEvent)));}
    const lastField = this.getlastField();
    if(lastField && lastField.Value == 'businessProcessName' && this.currentEvent === Action.Value){
      this.setDealEntityItems(value)
    }
    
    return this.searchList.length > 0 ? this.searchList.map(s => s.Value).join(' ') : '';
  }

  updateSearchList(event:ClipboardEvent){
    const clipboardData = event.clipboardData;
    const pastedText = clipboardData.getData('text');
    const pattern = /"[^"]*"|\S+/g;
    const splitText: string[] = pastedText.match(pattern) || [];
    const result: string[] = splitText.map(token => token.replace(/^"(.*)"$/, '$1'));

    result.forEach(ele=>{
      this.displayFn(ele);
      const currentList = this.selectionList.find(s => s.Name.toLowerCase() === this.currentEvent.toLowerCase());
      this.currentEvent = currentList ? currentList.NextSelection : this.defaultSelection
    })
    
  }

  triggerDisplayFun(){
   setTimeout(() => {
    const controlVal = this.myControl.value;
    const regex = /(?:^|AND|OR)\s*([^=]+)\s*([=:><]+)\s*"([^"]+)"/g;
    let match;
    let lastPairValue;

    while ((match = regex.exec(controlVal)) !== null) {
      lastPairValue = match[3];    
    }

    let lastPushedVal ;
    for(let i = this.searchList.length -1; i>=0 ; i--){
      if(this.searchList[i].PopulatedFrom === Action.Value){
        lastPushedVal = this.searchList[i].Value;
        break;
      }
    }
    
    if( lastPairValue && lastPushedVal !== '"'+lastPairValue+'"' ){
     this.displayFn(lastPairValue);
      this.myControl.updateValueAndValidity() //to dummy trigger of value changes
      }
    
   }, 100);
  }

  private  getOptionList():string[] {
    if (this.searchList == null || this.searchList == undefined || this.searchList.length === 0) {
      this.currentEvent = this.defaultSelection;
      return this.field;
    }

    const lastElement: SelectedOption = <SelectedOption>this.searchList.slice(-1).pop();
    const currentList = this.selectionList.find(s => s.Name.toLowerCase() === lastElement.Next.toLowerCase());
    this.currentEvent = currentList ? currentList.Name : this.defaultSelection;
    return currentList ? this.getValues(currentList) : this.field;
  }

  private  getValues(currentList: SelectionDict) {

    if (this.currentEvent.toLowerCase() == 'value') {
    const selectedField = this.getlastField();
    const selectedValue = selectedField ? selectedField.Value : ''
    const filteredResponse = this.response.find(r => r.DisplayName === selectedValue);
    return filteredResponse ? filteredResponse.AutoCompleteValues : [];
  } else if(this.currentEvent.toLowerCase() == 'operator'){
    const selectedField = this.getlastField();
  const selectedValue = selectedField ? selectedField.Value : ''
  const filteredResponse = this.response.find(r => r.DisplayName === selectedValue);
  return  filteredResponse ? this.operators[filteredResponse.InputType] : [];
  }else return currentList.Value;

  }

  private getSearchText(value: string): string {
    const oldText = this.searchList.map(s => s.Value).join(' ');
    this.handleBackspace(value);
    return value.trim().replace(oldText, '').replace('"','');
  }

  private handleBackspace(searchValue: string): void {
    const oldText = this.searchList.map(s => s.Value).join(' ');
    const previousListName = this.searchList.length != 0 ? this.searchList[this.searchList.length - 1].PopulatedFrom : '';
    const prevList = this.selectionList.find(s => s.Name.toLowerCase() === previousListName.toLowerCase());
    let prevListValue = prevList ? prevList.Value : [];


    if (previousListName == Action.Value) {
      const lastField = this.getlastField();
      const lastFieldValue = lastField ? lastField.Value : '';
      const filteredResponse = this.response.find(r => r.DisplayName === lastFieldValue);
      prevListValue = filteredResponse ? filteredResponse.AutoCompleteValues : [];
    }

    if ((prevListValue ? prevListValue.indexOf(searchValue) === -1 : false) && oldText.trim().length > searchValue.trim().length)
      this.searchList.pop();
  }


  private getNextEvent(currentEvent: string): string {
    const currentList = this.selectionList.find(s => s.Name.toLowerCase() === currentEvent.toLowerCase());
    return currentList ? currentList.NextSelection : this.defaultSelection;
  }


  private getlastField(): SelectedOption | undefined {
    if (this.searchList.length === 0) return undefined;
    let i: number = this.searchList.length - 1;
    for (i; i >= 0; i--) {
      if (this.searchList[i].PopulatedFrom == Action.Field)
        return this.searchList[i];
    }
    return undefined;
  }


  getLastFieldInputType(){
    const selectedField = this.getlastField();
    const selectedValue = selectedField ? selectedField.Value : ''
    const filteredResponse = this.response.find(r => r.DisplayName === selectedValue);
    return filteredResponse ? filteredResponse.InputType : '';
  }

  getList(event:MatSelectionListChange,defaultLoad?){
    const val = event.options[0].value;
    this.selectedType = val;
    !defaultLoad ? this.selectedVal = '' : ''
    this.searchList = [];
    this.myControl.setValue('',{emitEvent:false});
    this.list = [];
     if(val === 'dataModel'){
      this.assetService.getAssetListWithBasicDetails().subscribe((resp:[])=>{
        this.list = resp;
        this.filteredSelectVals = this.list;
        if (defaultLoad) this.setAssetItems(this.selectedVal);
      })
     }else if (val === 'entity'){
      this.entityService.getExtentionsList().subscribe((resp:[])=>{
        this.list = resp;
        this.filteredSelectVals = this.list;
        if(defaultLoad) this.setAssetItems(this.selectedVal);
        
      })
     }
    
  }

  setDealEntityItems(val:string){
  const selectedBpData =  this.allBps.find(bp => bp.name == val);
   this.dealService.getEntityList('Company','Company','Entity').subscribe((resp:{entityDetail})=>{
    const mappedData = resp.entityDetail.entityDetail.map(el=>({
      "DisplayName": Object.entries(el )[0][0]+'_entity',
      "InputType":this.setInputType(el[Object.entries(el)[0][0]].inputType),
      "SearchType": "Field",
      "AutoCompleteValues": this.setAutoCompleteValuesBasedOnInputType(el)
  }))
  this.response.push(...mappedData)
  this.fieldList.Value.push(...mappedData.map(el=>el.DisplayName));
   }

   )
  }
  setAssetItems(id) {
    this.response = [];
    !id ? id = this.filteredSelectVals[0]?.id : '';
    this.selectedVal = id;
    if(this.selectedType === 'dataModel'){

      this.BPService.getBpListByDataModelId(id).subscribe((resp:[])=>{
        this.searchList = [];
        this.allBps = resp;
        this.assetService.getAssetById(id).subscribe((resp)=>{
          this.fieldList.Value = [];
          const mappedData = resp.assetConfigurations.map(el=>({
            "DisplayName": Object.entries(el)[0][0] + '_asset',
            "InputType": this.setInputType(el[Object.entries(el)[0][0]].inputType),
            "ConvertValue": 'dealAsset.'+Object.entries(el)[0][0],
            "SearchType": "Field",
            "AutoCompleteValues": this.setAutoCompleteValuesBasedOnInputType(el)
        }))
  
  
      const createdDate = {
  
        "DisplayName": 'createdDate',
        "InputType": 'date',
        "ConvertValue":'Created Date',
        "SearchType": "Field",
        "AutoCompleteValues": ['__today()','__currentMonthStart()','__currentYearStart()']
      }
  
      const stages =
      {
        "DisplayName": 'currentStageName',
        "InputType": 'text',
        "ConvertValue":'Stage',
        "SearchType": "Field",
        "AutoCompleteValues": []
      }
  
      const  currentStatus =
      {
        "DisplayName": 'currentStatus',
        "InputType": 'text',
        "ConvertValue": 'Current Status',
        "SearchType": "Field",
        "AutoCompleteValues": ['In progress' ,'Approved', 'Rejected']
      }
  
      const Bp = {
        "DisplayName": 'businessProcessName',
        "InputType": 'text',
        "ConvertValue": 'Business Process',
        "SearchType": "Field",
        "AutoCompleteValues": this.allBps.map(el=>el.name)
      }
        const staticCols = [createdDate,stages,currentStatus,Bp].map(el => ({
          displayName : el.ConvertValue,
          name: el.DisplayName,
          inputType: el.InputType,
          display:true
        }))
        mappedData.unshift(createdDate,stages,currentStatus,Bp)
        this.response.push(...mappedData);
        
        this.displayColumns = resp.assetConfigurations.map(el => ({
          displayName : el[Object.entries(el)[0][0]].displayProperty.displayName,
          name: Object.entries(el)[0][0],
          inputType: el[Object.entries(el)[0][0]].inputType,
          display:false
        }))
        this.displayColumns = staticCols.concat(this.displayColumns);
        
        this.fieldList.Value.push(...mappedData.map(el=>el.DisplayName));
        this.myControl.setValue(''); // trigger the autocomplete to populate new values
  
        this.queryBuilderService.selectedQueryType = this.selectedType;
        this.queryBuilderService.selectedTypeValue = this.selectedVal;
        })
      })
    
     }else if (this.selectedType === 'entity'){
      this.searchList = [];
      this.fieldList.Value = [];
      this.response = [];
      const entityDetail = this.list.find(entity => entity.entityName == this.selectedVal)?.entityDetail.entityDetail;
      const mappedData = entityDetail.map(el=>({
          "DisplayName": Object.entries(el )[0][0]+'_entity',
          "InputType":this.setInputType(el[Object.entries(el)[0][0]].inputType),
          "SearchType": "Field",
          "AutoCompleteValues": this.setAutoCompleteValuesBasedOnInputType(el)
      }))

      const createdDate = {

        "DisplayName": 'createdDate',
        "InputType": 'date',
        "ConvertValue":'created Date',
        "SearchType": "Field",
        "AutoCompleteValues": ['__today()','__currentMonthStart()','__currentYearStart()']
      }

      const staticCols = [createdDate].map(el => ({
        displayName : el.ConvertValue,
        name: el.DisplayName,
        inputType: el.InputType,
        display:true
      }))

      this.displayColumns = entityDetail.map(el => ({
        displayName : el[Object.entries(el)[0][0]].displayProperty.displayName,
        name: Object.entries(el)[0][0],
        inputType: el[Object.entries(el)[0][0]].inputType,
        display:false
      }));

      this.displayColumns = staticCols.concat(this.displayColumns);

      mappedData.unshift(createdDate);
      this.response.push(...mappedData);
      this.fieldList.Value.push(...mappedData.map(el=>el.DisplayName));
      this.myControl.setValue(''); // trigger the autocomplete to populate new values
      this.queryBuilderService.selectedQueryType = this.selectedType;
      this.queryBuilderService.selectedTypeValue = this.selectedVal;
     }

     if(this.selectedType && this.selectedVal) this.myControl.enable();
   }

  setAutoCompleteValuesBasedOnInputType(ele){
    if(ele[Object.entries(ele)[0][0]].inputType == dataTypes.Picklist)
    return ele[Object.entries(ele)[0][0]].displayProperty.defaultValues.split(",");
    else if (ele[Object.entries(ele)[0][0]].inputType == dataTypes.Date){
      return ['__today()','__currentMonthStart()','__currentYearStart()']
    }else{
      return [];
    }
  }

  setInputType(inputType){
    return this.isText(inputType) ? 'text' : this.isNumber(inputType) ? 'number' : 'date';
  }

  isNumber(inputType:string){
    return [
        dataTypes.Number,
        dataTypes.Perentage,
        dataTypes.Currency, 
        dataTypes.Number_with_decimal].includes(<dataTypes>inputType)
  }
  isText(inputType:string){
    return [
      dataTypes.Text,
      dataTypes.Picklist,
      dataTypes.Multiple_Static_Picklist,
      dataTypes.Alphanumeric, 
      dataTypes.Email,
      dataTypes.Long_Text,
      dataTypes.Searchable_picklist,
      dataTypes.Multiple_picklist, 
      dataTypes.Document].includes(<dataTypes>inputType)
  }

  triggerQuery(){
    this.applyTruncate();
    this.adjustUIHandler.isOpen && this.adjustUIHandler.toggle();
    let filterQuery = this.searchList.map(s => {
      if(s.PopulatedFrom == Action.Field){
        if(s.Value.includes('_asset')){
          return 'dealAsset.' + s.Value.split('_')[0];
        }else if(s.Value.includes('_entity') && this.selectedType =='dataModel'){
          return 'dealEntity.' + s.Value.split('_')[0];
        }else if (s.Value.includes('_entity')){
          return 'entityDetail.' + s.Value.split('_')[0];
        }else{
          return s.Value;
        }
      }else if (s.PopulatedFrom === Action.Operator ){
        return s.Value === '=' ? ':' : s.Value === '!=' ? '!' : s.Value;
      }else return s.Value;
    }).join(' ');


    if(this.selectedType == 'entity') filterQuery = 'entityType:'+this.selectedVal + ' AND '+ filterQuery;

    this.queryBuilderService.selectedQueryType = this.selectedType;
    this.queryBuilderService.selectedTypeValue = this.selectedVal;
    this.bottomSheet.open(
      FilteredRecordsTableComponent,
      {
        data: {query:filterQuery,displayColumns:this.displayColumns},
        panelClass : 'conditional-search-sheet'
      }
    )
  }

  filterList(val:string){
    this.filteredSelectVals =  this.list.filter((item)=> 
      this.selectedType === 'dataModel'?
      item.assetTypeName.toLowerCase().includes(val.toLowerCase()) :
      item.entityName.toLowerCase().includes(val.toLowerCase()));
  }

  applyTruncate(): void {
    const textarea = document.querySelector('textarea');
    if (textarea) {
      setTimeout(() => {
        textarea.scrollTop = 0;
      }, 0);
      textarea.classList.add('truncate');
    }
  }

  removeTruncate(): void {
    if(this.adjustUIHandler.isOpen) this.adjustUIHandler.toggle(); 
    const textarea = document.querySelector('textarea');
    if (textarea) {
      textarea.classList.remove('truncate');
      setTimeout(() => {
        textarea.scrollTop = textarea.scrollHeight;
        textarea.selectionStart = textarea.selectionEnd = textarea.value.length;
        textarea.focus();
      }, 0);
    }
  }
}


class SelectedOption {
  public Value: string;
  public PopulatedFrom: string;
  public Next: string;

  constructor(value: string, populatedFrom: string, next: string,) {
    this.Value = value;
    this.PopulatedFrom = populatedFrom;
    this.Next = next;
  }
}

class SuggestionDetails {
  public Name: string;
  public Valid: string[];
  public Value: string[];
}

class SelectionDict {
  public Name: string;
  public Value: string[];
  public NextSelection: string;
}

// Server response
class ApiResponse {
  public DisplayName: string;
  public SearchType: string;
  public ConvertValue: string;
  public InputType: string;
  public AutoCompleteValues: string[];
}

enum Action {
  Field = 'Field',
  Operator = 'Operator',
  Value = 'Value',
  Expression = 'Expression'
}

type displayColumn = {
displayName : string,
name: string,
inputType: string,
display:boolean
}