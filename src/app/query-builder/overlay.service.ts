import { OverlayRef, Overlay, OverlayConfig } from '@angular/cdk/overlay';
import { ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class OverlayService {
  private overlayRef: OverlayRef | null = null;

  constructor(private overlay: Overlay) {}

  private createOverlay(): OverlayRef {
    const overlayConfig = this.getOverlayConfig();
    return this.overlay.create(overlayConfig);
  }

  private getOverlayConfig(): OverlayConfig {
    const positionStrategy = this.overlay.position()
      .global()
      .top()
      .centerHorizontally()

    return new OverlayConfig({
      width : "80%",
      hasBackdrop: true,
      backdropClass: 'custom-overlay-container',
      positionStrategy
    });
  }

  open(component:ComponentType<unknown>): void {
    if (!this.overlayRef) {
      this.overlayRef = this.createOverlay();
      const overlayPortal = new ComponentPortal(component);
      this.overlayRef.attach(overlayPortal);

      this.overlayRef.backdropClick().subscribe(() => this.close());
    }
  }

  close(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }
}
