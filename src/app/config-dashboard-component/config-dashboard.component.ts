import { Component, OnInit } from "@angular/core";
import { DataSharingService } from "../common/dataSharing.service";
import { DealService } from "../shared-service/deal.service";
import { DatePipe, CurrencyPipe } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { ErrorService } from "../shared-service/error.service";
import { ToasterService } from "../common/toaster.service";
import {
  Dashboard,
  EntityDashboard,
} from "../settings/dashboard-configuration/dashboard-config/dashboard-config.component";
import { DasboardConfigService } from "../settings/dashboard-configuration/dasboard-config.service";
import { ThemeService } from "../theme.service";
import { EntityService } from "../shared-service/entity.service";

@Component({
  selector: "app-config-dashboard",
  templateUrl: "./config-dashboard.component.html",
  styleUrls: ["./config-dashboard.component.scss"],
})
export class ConfigDashboardComponent implements OnInit {
  dashboard: Dashboard;
  loading: boolean;
  dashboards: any;
  nodashboard = true;
  selectedDashboardIndex = 0;
  queryType: any;
  initializationTriggered = true;

  constructor(
    public dataSharingService: DataSharingService,
    private dealService: DealService,
    public datepipe: DatePipe,
    public currencypipe: CurrencyPipe,
    private errorService: ErrorService,
    private notificationMessage: ToasterService,
    private dashboardService: DasboardConfigService,
    public router: Router,
    private activeRoute: ActivatedRoute,
    public themeService: ThemeService,
    public entityService: EntityService
  ) {}

  ngOnInit(): void {
    this.activeRoute.params.subscribe((params) => {
      this.selectedDashboardIndex = parseInt(atob(params["tabIndex"])) || 0;
      if (!this.dashboards) {
        this.dealService.getDashboardList().subscribe((res: any) => {
          this.loading = false;
          if (res.length > 0) {
            this.dashboards = res.sort(function (a, b) {
              return a.dashboardOrder - b.dashboardOrder;
            });
            this.loadDashboard();
            this.nodashboard = false;
          }
        });
      } else {
        this.loadDashboard();
      }
    });
    this.loading = true;
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  tabChange(tab) {
    this.router.navigate(["/dashboards", btoa(tab.selectedIndex)]);
  }

  loadDashboard() {
    this.dashboard = null;
    this.queryType =
      this.dashboards[this.selectedDashboardIndex]?.dashboardDetails?.queryType;

    const DashboardInstance =
      this.queryType == "Entity" ? EntityDashboard : Dashboard;
    this.dashboard = new DashboardInstance(
      this.dashboards[this.selectedDashboardIndex],
      this.dashboardService,
      this.router,
      this.errorService,
      this.notificationMessage,
      this.entityService
    );
    this.dashboard.setDashboardData();
    this.dashboard.setLinkageData();
  }

  getAllListConnections(index) {
    const connections = [];
    for (let i = 0; i < this.dashboards.length; i++) {
      if (i != index) {
        connections.push("list-" + i);
      }
    }
    return connections;
  }

  drop(event: CdkDragDrop<string[]>) {
    const previousIndex = parseInt(
      event.previousContainer.id.replace("list-", "")
    );
    const currentIndex = parseInt(event.container.id.replace("list-", ""));
    moveItemInArray(this.dashboards, previousIndex, currentIndex);
    this.updateOrder();

    const dashboards = this.dashboards;
    dashboards.dashboardOrder = currentIndex;

    const order = dashboards.map((item) => ({
      id: item.id,
      dashboardOrder: item.dashboardOrder,
      dashboardName: item.dashboardName,
    }));

    const payload = [...order];

    this.dealService.updateDashboardOrder(payload).subscribe((resp: any) => {
      resp = order;
      this.getDashboardList();
      this.updateOrder();
    });
  }

  updateOrder() {
    for (let index = 0; index < this.dashboards.length; index++) {
      this.dashboards[index].dashboardOrder = index + 1;
    }
  }
  getDashboardList() {
    this.loading = true;
    this.dealService.getDashboardList().subscribe(
      (resp: any) => {
        this.loading = false;
        this.dashboards = resp.sort(function (a, b) {
          return a.dashboardOrder - b.dashboardOrder;
        });
      },
      (err) => {
        this.loading = false;
      }
    );
  }
}
