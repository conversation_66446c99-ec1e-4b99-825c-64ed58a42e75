<div class="config-dashboard-container">
<mat-tab-group  *ngIf="!nodashboard" #tabgroup (selectedTabChange)="tabChange(tabgroup)" [selectedIndex]="selectedDashboardIndex">
  <mat-tab  *ngFor="let tab of dashboards ; let i = index" [label]="tab.dashboardName">
    <ng-template mat-tab-label>
      <div class="tab-div"
        [id]="'list-'+i"
        cdkDropList
        cdkDropListOrientation="horizontal"
        (cdkDropListDropped)="drop($event)"
        [cdkDropListConnectedTo]="getAllListConnections(i)"
      >
        <div cdkDrag>{{tab.dashboardName}}</div>
      </div>
    </ng-template>
    <div class=" margin-top-2 mb-5"  *ngIf="selectedDashboardIndex === i && dashboard?.fields.length>0">
      <ng-container class="z-index-100">
      </ng-container>
      <app-preview [preview]="false" [dashboard]="dashboard"></app-preview>
    </div>
  </mat-tab>

  </mat-tab-group>
  <div *ngIf="!loading">
  <div *ngIf="nodashboard" class="nodashboard">Configure your first Dashboard<a href="/dashboard-configuration"> here</a></div>
  </div>
  <div *ngIf="loading" [ngClass]="{'table-spinner': themeService.useNewTheme}">
    <mat-spinner></mat-spinner>
   </div>
</div>