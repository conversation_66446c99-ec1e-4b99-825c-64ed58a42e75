
.dc-table-head {
    width: 7% !important;
}
.dc-table-coloumn {
    width: 7% !important;
}

.dc-table-row td {
    text-align: center !important;
}

.numberChart {
    //background: orange;
    width: 200px;
    font-size: 55px;
    font-size: 55px;
    text-align: center;
    padding-top: 63px;
    padding-bottom: 63px;
    height: 74px;
    line-height: normal;
    cursor:pointer;
}


.btndash {
    color: #38a5d6 !important;
    border: 1px solid rgba(79, 195, 247, .0784313725) !important;
    padding: 8px;
    min-width: 0;
    flex-shrink: 0;
    line-height: 30px !important;
    border-radius: 30% !important;
    cursor: pointer !important;
    box-shadow: 0 3px 1px -2px #0003, 0 2px 2px #00000024, 0 1px 5px #0000001f !important;
    width: 40px;
    height: 40px;
}

.dataTable {
    text-align: left !important;
    display: table;
    border-collapse: separate;
    box-sizing: border-box !important;
    text-indent: initial;
    border-spacing: 2px;
    border-color: gray;
    width: auto !important;
    min-width: 150% !important; /* Adjust as needed */
    table-layout: fixed;
}


.dataTable ::ng-deep th, .dataTable ::ng-deep td {
        border-top: 1px solid #ddd !important;
    }

.dataTable ::ng-deep thead tr {
    height: 60px !important;
}

.dataTable ::ng-deep thead tr th{
    min-width: 128px;
    padding: 0px 11px;
}

.dataTable ::ng-deep tbody tr {
    height: 40px !important;
}

.dataTable ::ng-deep tbody tr td{
    min-width: 128px;
    padding: 0px 11px;
}

.data-table-container {
    width: 100%;
    overflow: auto;
     position: relative;
     max-height: 500px;
  }


  .graph-border-radius{
    border-radius: 12px;
    box-shadow: 0 2px 4px -1px #0003,0 4px 5px #00000024,0 1px 10px #0000001f!important;
  }

  .tab-div{
    cursor: move;
}

.z-index-100{
    z-index: 100 !important
}
.nodashboard{
    text-align: center;
    font-weight: bolder;
    font-size: 22px !important;
}
.table-spinner{
    margin-top:5%;
    display: flex;
    justify-content: center;
  }
  