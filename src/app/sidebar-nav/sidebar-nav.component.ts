import { Component, input } from "@angular/core";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import {
  DealResource,
  EntityResource,
} from "../settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";

import { DataSharingService } from "../common/dataSharing.service";
import { distinctUntilChanged, Subject, takeUntil } from "rxjs";
import { evalStringExpression, formStringExpression } from "../helpers/utils";
import { UtilitiesService } from "../settings/utilities/utilities.service";
import { IdentityService } from "../shared-service/identity.service";
import { DocumentService } from "../shared-service/document.service";
import { LoaderService } from "../shared-service/loader.service";
import { ThemeService } from "../theme.service";
import { OldUiConfirmationDialogComponent } from "../dialogs/old-ui-confirmation-dialog/old-ui-confirmation-dialog.component";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { CommonModule } from "@angular/common";
import { environment } from "src/environments/environment";
import { SidebarNavService } from "./sidebar-nav.service";
import {
  SIDEBAR_ITEMS,
  sidebarItemChildren,
  sidenavChildListConfig,
  sidenavListConfig,
} from "./sidebar-nav.model";

@Component({
  selector: "app-sidebar-nav",
  standalone: true,
  imports: [SharedModuleModule, CommonModule],
  templateUrl: "./sidebar-nav.component.html",
  styleUrl: "./sidebar-nav.component.scss",
})
export class SidebarNavComponent {
  readonly ENTITY_RESOURCE = EntityResource;
  readonly DEAL_RESOURCE = DealResource;
  readonly environment = environment;

  originalSidebarItems = input.required<[]>();

  closeUserToolTip = localStorage.getItem("closeUserToolTip");

  destroy$: Subject<void> = new Subject<void>();
  pageLoadedFromNav: string;
  showFiller = false;
  sidebarOriginalItems;
  dataModelConfigRules = true;
  businessProcessConfigRules = true;
  dashboardConfigRules = true;
  entityDefinitionConfigRules = true;
  reportConfigRules = true;
  personDetailsRules = true;
  companyDetailsRules = true;
  rolesAndActionsRule = true;
  themeToggleRules = true;
  userManualRules = true;
  allUserRoles: string[] = [];
  currentUserRoles: string[] = [];
  sidebarItems: any = [];
  breadcrumb: string;
  navigationurl: string;
  tenant: string;
  tenantIdentifier: string;

  //TODO: In future when items have children property, remove these hardcoded items and use the children property instead
  withChildren = [SIDEBAR_ITEMS.ENTITY, SIDEBAR_ITEMS.CONFIGURATION];

  // remove variables related to new UI toggle after new UI is permanent
  showTooltip = false;
  showThemeTooltip = false;

  constructor(
    private readonly dataSharingService: DataSharingService,
    private readonly utilitiesService: UtilitiesService,
    private readonly identityService: IdentityService,
    private readonly documentService: DocumentService,
    private readonly router: Router,
    private readonly matDialog: MatDialog,
    private readonly loader: LoaderService,
    public readonly sidebarNavService: SidebarNavService,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        this.allUserRoles = roles.map((role) => role.identifier);
      });

    const token = localStorage.getItem("accessToken");

    this.currentUserRoles = token
      ? JSON.parse(atob(token.split(".")[1])).realm_access.roles
      : [];

    this.dataSharingService
      .getSidebarItems()
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((config) => {
        this.mapSidebarItems(config);

        this.dashboardRules();
      });

    this.tenant = localStorage.getItem("tenantIdentifier");

    this.sideNavigationList();

    this.showTooltipAfterLogin();
  }

  // Add children to sidebar items if not present in BE
  mapSidebarItems(config) {
    this.sidebarItems = config.configDetails
      .map((item) => ({
        ...item,
        name:
          item.name.trim() === "Dashboard(Experimental)"
            ? SIDEBAR_ITEMS.DASHBOARD
            : item.name,
      }))
      .map((item) => {
        if (
          this.withChildren.includes(item.name) &&
          !item.hasOwnProperty("children")
        ) {
          return { ...item, children: sidebarItemChildren[item.name] };
        } else return item;
      });
  }

  getItemDetails(name, requiredDetail, parentName?: SIDEBAR_ITEMS) {
    const itemDetail = (
      parentName ? sidenavChildListConfig[parentName] : sidenavListConfig
    ).filter((ele) => ele.name == name);
    if (itemDetail && itemDetail?.length != 0) {
      if (requiredDetail == "icon") return itemDetail[0]?.icon;
      if (requiredDetail == "route") return itemDetail[0];
    }
  }

  sideNavigationList() {
    let configRules: any;
    this.dataSharingService
      .getSidebarItems()
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((items) => {
        this.sidebarOriginalItems = structuredClone(items);
        this.mapSidebarItems(items);
        this.personDetailsRules = true;
        this.companyDetailsRules = true;
        this.dataModelConfigRules = true;
        this.businessProcessConfigRules = true;
        this.dashboardConfigRules = true;
        this.entityDefinitionConfigRules = true;
        this.reportConfigRules = true;
        this.rolesAndActionsRule = true;
        this.themeToggleRules = true;
        this.userManualRules = true;

        this.sidebarItems.forEach((item: any) => {
          item.rule = true;
          if (item.rules !== null && item.rules !== undefined) {
            let itemName;
            if (item.name == "Dashboard") itemName = "__dashboard__";
            if (item.name == "Deal") itemName = "__deal__";
            if (item.name == "Planner") itemName = "__planner__";
            if (item.name == "Bulk Stage Move") itemName = "__bulkStageMove__";
            if (item.name == "Theme Toggle") itemName = "__themeToggle__";
            if (item.name == "User Guide") itemName = "__userGuide__";
            if (
              item.name != "Reporting" &&
              item.name != "Entity" &&
              item.name != "Configuration" &&
              item.name != "Theme Toggle" &&
              item.name != "User Guide"
            ) {
              const value = `${item.rules[itemName]}`;
              const exper = formStringExpression(value, [""]);
              if (evalStringExpression(exper, this, [""])) {
                item.rule = false;
              }
            } else {
              if (item.name == "Entity") {
                const keyArray = Object.keys(item.rules);
                keyArray.forEach((ele: any) => {
                  const value = `${item.rules[ele]}`;
                  const exper = formStringExpression(value, [""]);
                  if (evalStringExpression(exper, this, [""])) {
                    if (ele == "__personDetails__")
                      this.personDetailsRules = false;
                    if (ele == "__companyDetails__")
                      this.companyDetailsRules = false;
                  }
                });
              } else {
                configRules = item.rules;
                this.dashboardRules();
              }
            }
          }
        });
      });
  }

  goTo(data) {
    localStorage.setItem("activeListFormSideNavList", data.name);

    if (data.name != SIDEBAR_ITEMS.CONFIGURATION && SIDEBAR_ITEMS.ENTITY) {
      this.dataSharingService.newSubPageNameValue(null);
      this.dataSharingService.subPageEntityIdValue(null);
      if (data.url) {
        this.router.navigate([data.url]);
      }
    }
  }

  routeTo(parentName: SIDEBAR_ITEMS, childName: string) {
    const destinationPath = sidenavChildListConfig[parentName].find(
      (item) => item.name == childName
    )?.url;

    // TODO: Remove this code once new Utilities UI is permanent
    if (childName === SIDEBAR_ITEMS.UTILITIES) {
      this.router.navigate([
        this.utilitiesService.newUtilitesUI$.value ? "/utilities" : "/utility",
      ]);
      return;
    }

    this.router.navigate([destinationPath]);
    this.dataSharingService.newSubPageNameValue(null);
    this.dataSharingService.subPageEntityIdValue(null);
    return;
  }

  navigateToHelp() {
    if (
      this.utilitiesService.utilitiesConfig?.configDetails?.randomSerialNumber
    ) {
      this.loader.show();
      const randomSerialNumber =
        this.utilitiesService.utilitiesConfig?.configDetails.randomSerialNumber;
      this.documentService.openUserGuide(randomSerialNumber);
    } else window.open("../../../assets/usermanual.pdf", "_blank");
  }

  checkVisibility(itemName: string) {
    itemName = itemName === "Dashboard(Experimental)" ? "Dashboard" : itemName;

    const disabledItems = this.utilitiesService.hiddenSidebarItems;

    if (disabledItems.includes(itemName)) return false;

    const needsPermissions =
      this.sidebarOriginalItems?.configDetails.find(
        (item) => item.name == itemName
      )?.roles ?? this.allUserRoles;

    let hasPermission = false;

    needsPermissions?.forEach((permission) => {
      if (this.currentUserRoles.includes(permission)) hasPermission = true;
    });

    return hasPermission;
  }

  getResourceAsPerModule(moduelName) {
    const ModuleToResource = {
      Deal: this.DEAL_RESOURCE.Deal,
      Enitity: this.ENTITY_RESOURCE.Entity,
      "Dashboard(Experimental)": "",
    };
    return ModuleToResource[moduelName];
  }

  readonly mapSubmenuItemRules = {
    [SIDEBAR_ITEMS.DATA_MODELS]: "dataModelConfigRules",
    [SIDEBAR_ITEMS.BUSINESS_PROCESS]: "businessProcessConfigRules",
    [SIDEBAR_ITEMS.DASHBOARD]: "dashboardConfigRules",
    [SIDEBAR_ITEMS.ENTITY]: "entityDefinitionConfigRules",
    [SIDEBAR_ITEMS.REPORTS]: "reportConfigRules",
    [SIDEBAR_ITEMS.ROLES_AND_ACTIONS]: "rolesAndActionsRule",
  };

  dashboardRules() {
    this.sidebarItems.forEach((item: any) => {
      if (
        item.name == "Configuration" ||
        item.name == "Theme Toggle" ||
        item.name == "User Guide"
      ) {
        if (item.rules !== null && item.rules !== undefined) {
          const keyArray = Object.keys(item.rules);
          keyArray.forEach((ele: any) => {
            const value = `${item.rules[ele]}`;
            const exper = formStringExpression(value, [""]);
            if (evalStringExpression(exper, this, [""])) {
              switch (ele) {
                case "__dataModel__":
                  this.dataModelConfigRules = false;
                  break;
                case "__businessProcess__":
                  this.businessProcessConfigRules = false;
                  break;
                case "__dashboard__":
                  this.dashboardConfigRules = false;
                  break;
                case "__entityDefinition__":
                  this.entityDefinitionConfigRules = false;
                  break;
                case "__reports__":
                  this.reportConfigRules = false;
                  break;
                case "__rolesActions__":
                  this.rolesAndActionsRule = false;
                  break;
                case "__themeToggle__":
                  this.themeToggleRules = false;
                  break;
                case "__userGuide__":
                  this.userManualRules = false;
                  break;
              }
            }
          });
        }
      }
    });
  }

  showTooltipAfterLogin(): void {
    this.showTooltip = this.userManualRules; // show only if rule is truthy

    // Automatically hide the tooltip after 30 seconds
    setTimeout(() => {
      this.showTooltip = false;
    }, 30000); // Tooltip will be visible for 30 seconds
  }

  onThemeToggle(isNewThemeEnabled: boolean): void {
    if (!isNewThemeEnabled) {
      const matDialogRef = this.matDialog.open(
        OldUiConfirmationDialogComponent,
        {
          disableClose: true,
          width: "55%",
          hasBackdrop: true,
          backdropClass: "custom-overlay-container",
        }
      );

      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.themeService.useNewTheme = true;
        } else {
          this.themeService.useNewTheme = false;
        }
      });
    }
  }

  closeTooltip(): void {
    this.showTooltip = false;
    localStorage.setItem("closeUserToolTip", "false"); // Close the tooltip when "OK" is clicked
  }

  closeHoverTooltip(): void {
    this.showThemeTooltip = false;
  }

  hideTooltip(): void {
    this.showThemeTooltip = false;
  }

  showTooltipAfterHover(): void {
    this.showThemeTooltip = true;
    setTimeout(() => {
      if (this.showThemeTooltip) {
        this.showThemeTooltip = false;
      }
    }, 15000);
  }
}
