<button (click)="showFiller = !showFiller" mat-icon-button
  aria-label="Example icon button with a vertical three dot icon" aria-label="sidebar-top-btn">
  <mat-icon>
    {{showFiller ? "navigate_before" : "navigate_next"}}
  </mat-icon>
</button>

<div>
  <div [ngClass]="{'side-nav-bar': showFiller}">
    @for(data of sidebarItems; track data.displayName) {
    @if(checkVisibility(data.name)) {
    @if(data.name !== 'Theme Toggle' && data.name !== 'User Guide' && data.rule) {
    <p *ifHasPermission="getResourceAsPerModule(data.name) ; scope:'READ'">
      @if(showFiller) {
      <button [matTooltip]="data.displayName" matTooltipClass="accent-tooltip" mat-button
        [class.activeColorForSelectedlist]="(sidebarNavService.activeParentName$.value ) === (data.name === 'Dashboard(Experimental)' ? 'Dashboard' : data.name)"
        (click)="goTo(getItemDetails(data.name , 'route'))" aria-label="data.name"
        class="align-sidebar-buttons"
        [matMenuTriggerFor]="data.children?.length ? childrenMenu : null"
        [matMenuTriggerData]="{childrenItems:data.children, parentName:data.name}">
        <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
        <span class="sidebar-item-name">
          {{data.displayName}}
        </span>
      </button>
      } @else {
      <button [matTooltip]="data.displayName" matTooltipClass="accent-tooltip" mat-icon-button
        [class.activeColorForSelectedlist]="(sidebarNavService.activeParentName$.value ) === (data.name === 'Dashboard(Experimental)' ? 'Dashboard' : data.name)"
        (click)="goTo(getItemDetails(data.name , 'route'))" aria-label="data.name"
        [matMenuTriggerFor]="data.children?.length ? childrenMenu : null"
        [matMenuTriggerData]="{childrenItems:data.children, parentName:data.name}">
        <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
      </button>
      }
    </p>
    }
    }
    }
  </div>
  <div>
    @if(themeService.useNewTheme){
    <span class="custom-tooltip width-100" *ngIf="checkVisibility('User Guide')">
      <p class="userManualButtonContainer"
        [class.border-visible]="userManualRules && themeToggleRules">
        @if(showFiller) {
        <button mat-button *ngIf="userManualRules" (click)="navigateToHelp()"
          matTooltip="User Guide" aria-label="user-guide" matTooltipClass="accent-tooltip"
          class="align-sidebar-buttons width-100">
          <mat-icon>help_outline</mat-icon>
          <span class="sidebar-item-name">User Guide</span>
        </button>
        } @else {
        <button mat-icon-button *ngIf="userManualRules" (click)="navigateToHelp()"
          matTooltip="User Guide" aria-label="user-guide" matTooltipClass="accent-tooltip">
          <mat-icon>help_outline</mat-icon>
        </button>
        }
      </p>
      <!-- Added tooltip for user guide temp. -->
      <div *ngIf="showTooltip && closeUserToolTip==='true'" class="tooltip-content">
        <div class="tooltip-arrow"></div>
        <div>
          <h4>New UI User Guide</h4>
          <p>Need a hand? Check out our updated User Guide to get familiar with the new
            features.
          </p>
          <button (click)="closeTooltip()" aria-label="ok">OK</button>
        </div>
      </div>
      <!-- Added tooltip for user guide temp. -->
    </span>
    }
    <div *ngIf="checkVisibility('Theme Toggle')" class="custom-tooltip"
      (mouseover)="showTooltipAfterHover()" (mouseleave)="hideTooltip()">
      <div class="theme-toggle-container" [ngClass]="{'theme-toggle-container-icon': showFiller}">
        <div class="center">{{ themeService.useNewTheme ? 'New' : 'Classic' }}</div>
        <span class="p-h-6">
          <mat-slide-toggle color="primary" [(ngModel)]="themeService.useNewTheme"
            (ngModelChange)="onThemeToggle($event)">
          </mat-slide-toggle>
        </span>
      </div>

      <div *ngIf="showThemeTooltip && themeService.useNewTheme" class="tooltip-content m-t-10">
        <div class="tooltip-arrow"></div>
        <div>
          <h4>Switch To Classic</h4>
          <p>New Look,Same Excellence! Try The Updated Design And Switch Back Anytime If Needed.
          </p>
          <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
        </div>
      </div>

      <div *ngIf="showThemeTooltip && !themeService.useNewTheme"
        class="tooltip-content tooltip-content-old-ui m-t-10">
        <div class="tooltip-arrow tooltip-arrow-old-ui"></div>
        <div>
          <h4>New UI!</h4>
          <p>Discover A Fresh Design Built For A Better Experience.
          </p>
          <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
        </div>
      </div>

    </div>
  </div>
</div>

<mat-menu class="username-menu" #childrenMenu="matMenu">
  <ng-template matMenuContent let-childrenItems="childrenItems" let-parentName="parentName">
    @if(childrenItems?.length) {
    @for(child of childrenItems; track child.name) {
    <button attr.aria-label="{{child.name}}-configuration-button"
      *ngIf="(this[mapSubmenuItemRules[child.name]] ?? true) && environment.useKeycloakLogin"
      mat-menu-item (click)="routeTo(parentName, child.name)" attr.aria-label="{{child.name}}">
      <mat-icon matPrefix>{{getItemDetails(child.name , 'icon', parentName)}}</mat-icon>
      <span>{{child.displayName}}</span>
    </button>
    }
    }
  </ng-template>
</mat-menu>
