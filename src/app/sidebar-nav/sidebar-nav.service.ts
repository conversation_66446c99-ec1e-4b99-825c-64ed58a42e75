import { Injectable } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { BehaviorSubject, filter, Subject } from "rxjs";
import { activeSidebarParentPathList } from "./sidebar-nav.model";

@Injectable({
  providedIn: "root",
})
export class SidebarNavService {
  activeParentName$ = new BehaviorSubject<string>(undefined);

  constructor(private router: Router) {
    this.trackRoutesForSidebar();
  }

  trackRoutesForSidebar() {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        let found = false;

        // add / to prevent substring matches
        const currentUrl = event.urlAfterRedirects + "/";
        activeSidebarParentPathList.forEach((item) => {
          item.routes.forEach((route) => {
            if (currentUrl.includes(route)) {
              this.activeParentName$.next(item.parentName);
              found = true;
              return;
            }
          });
        });

        if (!found) {
          this.activeParentName$.next(undefined);
        }
      });
  }
}
