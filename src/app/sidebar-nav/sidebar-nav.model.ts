export type SidebarItem = {
  name: string;
  icon: string;
  url: string;
};

export const enum SIDEBAR_ITEMS {
  DASHBOARD = "Dashboard",
  WORKSPACE = "Workspace",
  CONFIGURATIONS = "Configuration",
  DEAL = "Deal",
  ENTITY = "Entity",
  REPORTING = "Reporting",
  PLANNER = "Planner",
  BULK_STAGE_MOVE = "Bulk Stage Move",
  CONFIGURATION = "Configuration",
  USER_GUIDE = "User Guide",
  THEME_TOGGLE = "Theme Toggle",
  COMPANIES = "Companies",
  PERSONS = "Persons",
  DATA_MODELS = "Data Models",
  BUSINESS_PROCESS = "Business Process",
  REPORTS = "Reports",
  UTILITIES = "Utilities",
  ROLES_AND_ACTIONS = "Roles and Actions",
  LABEL = "Label",
  TASK = "Task",
  WORKSPACE_CONFIGURATION = "Workspace Configuration",
}

// TODO: Refactor routes for components to follow parentItemName/childName pattern in path
// Keep track of parent item in sidebar for corresponding route
export const activeSidebarParentPathList = [
  {
    parentName: SIDEBAR_ITEMS.DASHBOARD,
    routes: ["/dashboards/"],
  },
  {
    parentName: SIDEBAR_ITEMS.WORKSPACE,
    routes: ["/workspace/"],
  },
  {
    parentName: SIDEBAR_ITEMS.DEAL,
    routes: ["/application/", "application-summary"],
  },
  {
    parentName: SIDEBAR_ITEMS.ENTITY,
    routes: ["/entity/"],
  },
  {
    parentName: SIDEBAR_ITEMS.TASK,
    routes: ["/task/"],
  },
  {
    parentName: SIDEBAR_ITEMS.CONFIGURATION,
    routes: [
      "/data-model/",
      "/entity-configuration/",
      "/business-process/",
      "/workspace-configuration/",
      "/dashboard-configuration/",
      "/reports/",
      "/utilities/",
      "/utility/",
      "/roles/",
      "/label/",
      "/view-report/",
      "/reports/",
      "/entity-details/",
      "/view-asset/",
      "/stage/",
      "/view-automated-report/",
    ],
  },
  {
    parentName: SIDEBAR_ITEMS.REPORTING,
    routes: ["/report/"],
  },
  {
    parentName: SIDEBAR_ITEMS.PLANNER,
    routes: ["/planner/"],
  },
  {
    parentName: SIDEBAR_ITEMS.BULK_STAGE_MOVE,
    routes: ["/home/"],
  },
];

export const sidenavListConfig: SidebarItem[] = [
  {
    name: "Dashboard(Experimental)",
    icon: "assessment",
    url: `/dashboards/${btoa("0")}`,
  }, //passing 0 to naviagte on first tab by default
  {
    name: SIDEBAR_ITEMS.DASHBOARD,
    icon: "assessment",
    url: `/dashboards/${btoa("0")}`,
  },
  {
    name: SIDEBAR_ITEMS.DEAL,
    icon: "work",
    url: "/application",
  },
  { name: SIDEBAR_ITEMS.ENTITY, icon: "group", url: "entity/companies" },
  { name: SIDEBAR_ITEMS.TASK, icon: "assignment_turned_in", url: "/task" },
  { name: SIDEBAR_ITEMS.PLANNER, icon: "event", url: "/planner" },
  { name: SIDEBAR_ITEMS.REPORTING, icon: "leaderboard", url: "/report" },
  { name: SIDEBAR_ITEMS.CONFIGURATION, icon: "settings", url: "/data-model" },
  { name: SIDEBAR_ITEMS.BULK_STAGE_MOVE, icon: "fast_forward", url: "/home" },
  { name: SIDEBAR_ITEMS.WORKSPACE, icon: "workspaces", url: "/workspace" },
  { name: SIDEBAR_ITEMS.USER_GUIDE, icon: "help_outline", url: "" },
  { name: SIDEBAR_ITEMS.THEME_TOGGLE, icon: "toggle_off", url: "/home" },
];

export const sidenavChildListConfig: {
  [key in SIDEBAR_ITEMS]?: SidebarItem[];
} = {
  [SIDEBAR_ITEMS.ENTITY]: [
    {
      name: SIDEBAR_ITEMS.COMPANIES,
      icon: "business",
      url: "entity/companies",
    },
    {
      name: SIDEBAR_ITEMS.PERSONS,
      icon: "person",
      url: "entity/persons",
    },
  ],
  [SIDEBAR_ITEMS.CONFIGURATION]: [
    {
      name: SIDEBAR_ITEMS.DATA_MODELS,
      icon: "dataset",
      url: "data-model",
    },
    {
      name: SIDEBAR_ITEMS.ENTITY,
      icon: "person",
      url: "entity-configuration",
    },
    {
      name: SIDEBAR_ITEMS.BUSINESS_PROCESS,
      icon: "account_tree",
      url: "business-process",
    },
    {
      name: SIDEBAR_ITEMS.WORKSPACE_CONFIGURATION,
      icon: "workspaces",
      url: "workspace-configuration",
    },
    {
      name: SIDEBAR_ITEMS.DASHBOARD,
      icon: "add_chart",
      url: "dashboard-configuration",
    },
    {
      name: SIDEBAR_ITEMS.REPORTS,
      icon: "summarize",
      url: "reports",
    },
    {
      name: SIDEBAR_ITEMS.UTILITIES,
      icon: "build",
      url: "utilities",
    },
    {
      name: SIDEBAR_ITEMS.ROLES_AND_ACTIONS,
      icon: "vpn_key",
      url: "roles",
    },
  ],
};

//TODO: Use values from BE in future
export const sidebarItemChildren = {
  Entity: [
    {
      name: SIDEBAR_ITEMS.COMPANIES,
      displayName: "Companies",
    },
    {
      name: SIDEBAR_ITEMS.PERSONS,
      displayName: "Persons",
    },
  ],
  Configuration: [
    {
      name: SIDEBAR_ITEMS.DATA_MODELS,
      displayName: "Data Models",
    },
    {
      name: SIDEBAR_ITEMS.ENTITY,
      displayName: "Entity",
    },
    {
      name: SIDEBAR_ITEMS.BUSINESS_PROCESS,
      displayName: "Business Process",
    },
    {
      name: SIDEBAR_ITEMS.WORKSPACE_CONFIGURATION,
      displayName: "Workspace Configuration",
    },
    {
      name: SIDEBAR_ITEMS.DASHBOARD,
      displayName: "Dashboard",
    },
    {
      name: SIDEBAR_ITEMS.REPORTS,
      displayName: "Reports",
    },
    {
      name: SIDEBAR_ITEMS.UTILITIES,
      displayName: "Utilities",
    },
    {
      name: SIDEBAR_ITEMS.ROLES_AND_ACTIONS,
      displayName: "Roles And Actions",
    },
  ],
};
