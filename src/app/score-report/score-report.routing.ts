import { Routes, RouterModule } from '@angular/router';
import { ScoreComponent } from '../application-summary/deal-details/score/score.component';
import { ScoreAnalysisComponent } from './score-analysis/score-analysis.component';
import { ScoreReportComponent } from './score-report.component';

const routes: Routes = [
  { path: '', component: ScoreReportComponent ,
  children : [  
    { path: '', redirectTo: 'score-analysis',pathMatch: 'full' },
    { path: 'score-analysis', component:  ScoreAnalysisComponent },
   
  ]
}
];

export const ScoreReportRoutes = RouterModule.forChild(routes);
