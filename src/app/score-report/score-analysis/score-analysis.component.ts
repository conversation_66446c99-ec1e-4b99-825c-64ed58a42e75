import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { ProgressSpinnerMode } from "@angular/material/progress-spinner";
import { Router, ActivatedRoute } from "@angular/router";
import { Subject } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { RichTextFullscreenComponent } from "src/app/dialogs/rich-text-fullscreen/rich-text-fullscreen.component";
import { DealService } from "src/app/shared-service/deal.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { BlurEvent, ChangeEvent } from "@ckeditor/ckeditor5-angular";
import JsonData from "src/assets/data.json";
//import { elementEventFullName } from '@angular/compiler/src/view_compiler/view_compiler';
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { ClassicEditor } from "ckeditor5";
import { RichTextConfig } from "src/app/zcp-data-types/rich-text-input/rich-text-config";
@Component({
  selector: "app-score-analysis",
  templateUrl: "./score-analysis.component.html",
  styleUrls: ["./score-analysis.component.scss"],
})
export class ScoreAnalysisComponent {
  private unsubscribe$ = new Subject();
  public Editor = ClassicEditor;
  selectedVersion = 0;
  config = RichTextConfig;
  content: any = "";
  profileScore = 0;
  marketScore = 0;
  businessScore = 0;
  revenueScore = 0;
  efficienceScore = 0;
  landscapeScore = 0;
  efficienceComment;
  revenueComment;
  businessComment;
  marketComment;
  profileComment;
  landscapeComment;
  avgScore: number = 0;
  mode: ProgressSpinnerMode = "determinate";
  usersList: any = [];
  teamEmailList: any = [];
  JsonData: any;

  // disable Action Button
  disableActionBtn;

  // TeamAverage
  teamAverage = 0;

  teamAverageValue = 0;
  // selected deal
  selectedApplicationsData: any;

  selectedBusinessProcessWithStagedetails: any;

  currentUserScore: any;

  currentUser;

  scores: any;
  showList: boolean = false;
  isContentEditorDisable;

  teamDealList: any = [];
  teamsData: any = [];
  stageselected: any;
  scoreselected: any = null;
  avgScores = [1, 2, 3, 4, 5];
  businessProcessList: any;
  selectedBusinessProcessDetails;
  scoreHistory: any = [];
  selectedScoreDetails: any = {};
  currentScore: any;
  formatLabel(value: number) {
    return value;
  }

  constructor(
    private dialog: MatDialog,
    public dataSharingService: DataSharingService,
    private notificationMessage: ToasterService,
    private dealService: DealService,
    private identityService: IdentityService,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private datePipe: DatePipe,
    private businessProcessService: BusinessProcessService
  ) {
    // // console.log(this.dataSharingService.selectedScoredData)
    this.selectedScoreDetails = this.dataSharingService.selectedScoredData;
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    // console.log(  this.selectedScoreDetails)
    this.getUserAnalysis();
    // // console.log(  this.selectedScoreDetails)
    dataSharingService.scoreChangeEmitted$.subscribe((data) => {
      if (data) {
        // console.log(data)
        this.selectedScoreDetails = this.dataSharingService.selectedScoredData;
        this.selectedApplicationsData =
          this.dataSharingService.selectedApplicationData;
        this.getUserAnalysis();
      }
    });
    // console.log(  this.selectedScoreDetails)
  }

  // disabledSaveBtn() {
  //   let currentUser = this.teamDealList.filter(item => item.teamName == localStorage.getItem('user'));
  //   if(this.showSaveBtn){
  //    if(currentUser && currentUser?.length != 0){
  //      return true;
  //    }else{
  //     return false
  //    }
  //   }else{
  //     return false
  //   }
  // }

  getUserAnalysis() {
    // let analysisData = null;
    // let teamLead =
    //           this.dataSharingService.selectedApplicationData?.dealTeamList.find(
    //             (data) => data.isTeamLead
    //           );
    // if(this.selectedScoreDetails && 'dealUserScoreHistoryList' in this.selectedScoreDetails){
    //   console.log(this.selectedScoreDetails.dealUserScoreHistoryList)
    //   analysisData = this.selectedScoreDetails.dealUserScoreHistoryList?.filter(ele =>
    //     ele.stageName == this.dataSharingService.stageSelectedInScore &&
    //     ele.createdBy == teamLead?.teamName )

    //   if(analysisData && analysisData.length != 0){
    //     if(analysisData[0]?.dealAnalysisDetails?.dealAnalysisDetails)
    //   {
    //     this.content = analysisData[0]?.dealAnalysisDetails?.dealAnalysisDetails;
    //     this.dataSharingService.dealAnalysisContentOfUser =  this.content ;
    //   }
    //   }
    // }else{
    //   // console.log(this.selectedScoreDetails.dealUserScore)
    //   // analysisData = this.selectedScoreDetails?.dealUserScore?.filter(ele =>
    //   //    ele.stageName == this.dataSharingService.stageSelectedInScore &&
    //   //    ele.createdBy == teamLead?.teamName)

    //   // if(analysisData && analysisData.length != 0){
    //   //  if(analysisData[0]?.dealAnalysisDetails?.dealAnalysisDetails)
    //     // {

    //   // }
    //   // }
    // }
    // console.log(this.dataSharingService.dealAnalysisContentOfUser )

    this.content =
      this.selectedScoreDetails?.dealAnalysisDetails?.dealAnalysisDetails;
    // console.log(this.content)
    if (!this.content) {
      this.content = "";
    }
    this.dataSharingService.dealAnalysisContentOfUser = this.content;
    if (this.dataSharingService.selectedScoredData?.isEditText) {
      this.isContentEditorDisable = false;
    } else {
      this.isContentEditorDisable = true;
    }
  }

  getTeamLead() {
    if (this.currentUser?.isTeamLead) {
      this.isContentEditorDisable = false;
    } else {
      if (!this.currentUser?.isTeamLead) {
        this.isContentEditorDisable = true;
      }
    }
  }

  getUpdatedAtScore() {
    if (this.selectedScoreDetails && this.selectedScoreDetails?.length != 0) {
      const updatedDate = this.selectedScoreDetails?.modifiedDate
        ? this.selectedScoreDetails?.modifiedDate
        : this.selectedScoreDetails?.createdDate;
      // return `Last scored on ${date}` ;
      if (updatedDate) {
        const date = this.datePipe.transform(new Date(updatedDate));
        return `Last scored on ${date}`;
      } else {
        const date = this.datePipe.transform(new Date());
        return `Last scored on ${date}`;
      }
    } else {
      return `Deal is yet to be scored.`;
    }
  }

  onFullscreen() {
    const matDialogRef = this.dialog.open(RichTextFullscreenComponent, {
      maxWidth: "95vw",
      maxHeight: "95%",
      height: "95%",
      width: "90%",
      data: {
        module: "Score Page",
        title: "Application Analysis",
        content: this.content,
        isDisable: this.isContentEditorDisable,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.content = result;
      }
    });
  }

  // CKEditor4.EventInfo
  change({ editor }: ChangeEvent) {
    //  console.log(editor)
    if (editor) {
      const EditorData = editor?.getData();
      this.dataSharingService.dealAnalysisContentOfUser = EditorData;
    }
    // console.log(EditorData)
    // console.log(this.dataSharingService.dealAnalysisContentOfUser)
  }

  // get Score details

  setAnalysisContentFromEditor({ editor }: BlurEvent) {
    this.dataSharingService.dealAnalysisContentOfUser = editor?.getData();
  }

  @ViewChild("editor", { static: false }) editor: ElementRef;

  public downloadAsPDF() {
    const selectedDiv = document.querySelector(".printformClass");
    // console.log(document.querySelector('.printformClass'))
    html2canvas(document.querySelector(".printformClass")).then(function (
      canvas
    ) {
      canvas.getContext("2d");

      const HTML_Width = canvas.width;
      const HTML_Height = canvas.height;
      const top_left_margin = 25;
      const PDF_Width: any = HTML_Width + top_left_margin * 2;
      const PDF_Height: any = PDF_Width * 1.5 + top_left_margin * 2;
      const canvas_image_width = HTML_Width;
      const canvas_image_height = HTML_Height;

      const totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
      // console.log(canvas.height+"  "+canvas.width);

      const imgData = canvas.toDataURL("image/jpeg", 1.0);
      const pdf = new jsPDF("p", "pt", [PDF_Width, PDF_Height]);
      pdf.addImage(
        imgData,
        "JPG",
        top_left_margin,
        top_left_margin,
        canvas_image_width,
        canvas_image_height
      );

      for (let i = 1; i <= totalPDFPages; i++) {
        pdf.addPage(PDF_Width, PDF_Height);
        let margin = -(PDF_Height * i) + top_left_margin * 4;
        if (i > 1) {
          margin = margin + i * 8;
        }
        // console.log(margin);
        // console.log(top_left_margin);
        // console.log(-(PDF_Height*i)+(top_left_margin*4));
        pdf.addImage(
          imgData,
          "JPG",
          top_left_margin,
          margin,
          canvas_image_width,
          canvas_image_height
        );
      }

      const fileName = "BIR" + new Date().toLocaleString();
      pdf.save(fileName);
    });
    this.notificationMessage.success(JsonData["label.success.DealAnalysis"]);
  }
}
