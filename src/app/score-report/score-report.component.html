<div class="app-full-viewport-height">
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="69%">
      <div class="text-align-end">
        <div>
          <button aria-label="view-score-btn"  mat-raised-button class="green score" (click)="openBottomSheet()">
            View score
          </button>
          
          <ng-container *ngIf="disabledSaveBtn()">
            <button aria-label="save-score-btn" *ngIf="this.showSaveButton"  mat-raised-button class="green score"
              (click)="onSave()">
              Save
            </button>
            <button aria-label="edit-score-btn" *ngIf="!this.showSaveButton"  mat-raised-button class="green score"
              (click)="onEdit()">
              Edit
            </button>
          </ng-container>
          <button aria-label="back-score-btn" mat-raised-button class="blue score" backButton>
            back
          </button>
        </div>
       
      </div>
    </div>
    <div class="mt-3"fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" >
      <nav mat-tab-nav-bar [tabPanel]="tabPanel">
        <ng-container *ngFor="let link of scoreReportTabs">
          <a attr.aria-label="{{link.label}}-tab" mat-tab-link [disabled]='link.isDisable' [routerLink]="getLink(link)" routerLinkActive
            #rla="routerLinkActive" [active]="rla.isActive" [routerLinkActiveOptions]="{exact: true}">
            {{link.label}}
          </a>
        </ng-container>
      </nav>
    </div>
  </div>
  <mat-tab-nav-panel #tabPanel>
    <router-outlet></router-outlet>
  </mat-tab-nav-panel>

  
</div>
