import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { DataSharingService } from "../common/dataSharing.service";
import { ToasterService } from "../common/toaster.service";
import { DealService } from "../shared-service/deal.service";
import { ErrorService } from "../shared-service/error.service";
import { scoreReportTabs } from "./reportData";
import { ScoreAnalysisComponent } from "./score-analysis/score-analysis.component";
import { UserScoreDetailsComponent } from "./user-score-details/user-score-details.component";
import JsonData from "src/assets/data.json";
@Component({
  selector: "app-score-report",
  templateUrl: "./score-report.component.html",
  styleUrls: ["./score-report.component.scss"],
  providers: [ScoreAnalysisComponent],
})
export class ScoreReportComponent implements OnInit {
  scoreReportTabs: any = scoreReportTabs;
  selectedScoreDetails: any = [];
  showSaveBtn: boolean = false;
  teamDealList: any = [];
  dealUsersScoreData: any = [];
  userDealTeamId: any;
  showSaveButton: boolean = false;
  JsonData: any;
  constructor(
    private errorService: ErrorService,
    public dataSharingService: DataSharingService,
    private _bottomSheet: MatBottomSheet,
    private dealService: DealService,
    public datePipe: DatePipe,

    private scoreAnalysisComponent: ScoreAnalysisComponent,
    public notificationMessage: ToasterService
  ) {
    this.selectedScoreDetails = this.dataSharingService.selectedScoredData;
    // console.log(this.dataSharingService.selectedScoredData);
    if (
      this.selectedScoreDetails &&
      "dealUserScoreHistoryList" in this.selectedScoreDetails
    ) {
      this.showSaveBtn = false;
    } else {
      this.showSaveBtn = true;
    }
    this.teamDealList = this.dataSharingService.selectedApplicationData
      ?.dealTeamList
      ? this.dataSharingService.selectedApplicationData?.dealTeamList
      : [];
    this.userDealTeamId = this.teamDealList.filter(
      (item) => item.teamName == localStorage.getItem("user")
    )[0]?.id;
  }

  ngOnInit() {}

  getLink(link) {
    return link.link;
  }

  disabledSaveBtn() {
    if (
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
    ) {
      return false;
    }
    let currentUser = this.teamDealList.filter(
      (item) => item.teamName == localStorage.getItem("user")
    );

    let stage =
      this.dataSharingService.selectedApplicationData?.currentStageName;
    let selectedStage = this.selectedScoreDetails?.stageName
      ? this.selectedScoreDetails?.stageName
      : this.dataSharingService?.stageSelectedInScore;

    if (this.selectedScoreDetails?.isScoreFreezed) {
      // console.log(this.selectedScoreDetails?.isScoreFreezed ,  "false one")
      return false;
    }
    if (this.showSaveBtn && selectedStage == stage) {
      // console.log(!(this.showSaveBtn , selectedStage == stage) ,  "false two")
      if (currentUser && currentUser?.length != 0) {
        // console.log(currentUser , currentUser?.length != 0,  "false three")
        return true;
      } else {
        // console.log(currentUser , currentUser?.length != 0,  "false four")
        return false;
      }
    } else {
      // console.log(!(this.showSaveBtn && selectedStage == stage) ,  "false five")
      return false;
    }
  }

  onSave() {
    // console.log(this.dataSharingService.selectedScoredData);

    if (
      this.selectedScoreDetails &&
      "dealUserScoreHistoryList" in this.selectedScoreDetails
    ) {
      this.dealUsersScoreData =
        this.selectedScoreDetails?.dealUserScoreHistoryList?.slice();
    } else {
      this.dealUsersScoreData =
        this.selectedScoreDetails?.dealUserScore?.slice();
    }
    // console.log(this.dealUsersScoreData , this.selectedScoreDetails);
    let loggedInUserId = this.teamDealList.filter(
      (item) => item.teamName == localStorage.getItem("user")
    )[0]?.id;
    this.onSaveDetails(loggedInUserId, localStorage.getItem("user"));
  }

  onSaveDetails(userId, userName) {
    let userSpecificDetail = [];

    userSpecificDetail = this.dealUsersScoreData?.filter(
      (ele) => ele.dealTeamId == userId
    );
    // console.log(userSpecificDetail , this.dealUsersScoreData)
    let payload = {};

    if (
      userSpecificDetail &&
      userSpecificDetail.length != 0 &&
      userSpecificDetail[0].id
    ) {
      payload = {
        dealAnalysisDetails: {
          dealAnalysisDetails:
            this.dataSharingService.dealAnalysisContentOfUser,
        },
        stageName: userSpecificDetail[0].stageName,
        userScoreAverage: userSpecificDetail[0].userScoreAverage,
        userScoreDetails: userSpecificDetail[0].userScoreDetails,
        dealId: userSpecificDetail[0].dealId,
        dealScoreId: userSpecificDetail[0].dealScoreId,
        dealTeamId: userSpecificDetail[0].dealTeamId,
      };
      this.dealService
        .updateScoreDetails(payload, userSpecificDetail[0].id)
        .subscribe((res) => {
          this.notificationMessage.success(JsonData["label.success.score"]);

          let index = this.dealUsersScoreData?.findIndex(
            (x) => x.dealTeamId == userName
          );

          // this.dealUsersScoreData[index].userScoreAverage = this.getAverageScore(userSpecificDetail[0]?.userScoreDetails?.userScoreDerails?.scores) ;
          this.dataSharingService.selectedScoredData;
          payload["modifiedDate"] = this.datePipe.transform(new Date());
          payload["modifiedBy"] = userName;

          this.getScoreDetails();
        });
    } else {
      let dealTeamId = this.teamDealList.filter(
        (item) => item.teamName == userName
      );

      payload = {
        dealAnalysisDetails: {
          dealAnalysisDetails:
            this.dataSharingService.dealAnalysisContentOfUser,
        },
        stageName:
          this.dataSharingService.selectedApplicationData?.currentStageName,
        dealId: this.dataSharingService.selectedApplicationData?.id,
        // userScoreAverage :  null,
        // userScoreDetails : { userScoreDerails : null} ,
        dealTeamId: dealTeamId[0].id,
      };

      this.dealService.sendScoreDetails(payload).subscribe((res) => {
        this.notificationMessage.success(JsonData["label.success.score"]);
        if (
          this.dealUsersScoreData &&
          this.dealUsersScoreData?.filter(
            (ele) => ele.dealTeamId == dealTeamId[0].id
          ).length != 0
        ) {
          let index = this.dealUsersScoreData?.findIndex(
            (x) => x.dealTeamId == dealTeamId[0].id
          );
          this.dealUsersScoreData[index] = payload;
        } else {
          if (this.dealUsersScoreData == undefined)
            this.dealUsersScoreData = [];
          payload["createdDate"] = this.datePipe.transform(new Date());
          payload["dealTeamId"] = userName;
          this.dealUsersScoreData.push(payload);
        }

        this.getScoreDetails();
      });
    }
  }

  getScoreDetails() {
    let id = this.dataSharingService.selectedApplicationData?.id;
    this.dealService.getScoreDetails(id).subscribe((res) => {
      let data: any = [];
      data = res;

      if (data) {
        let stageScore = [];
        stageScore = data.filter(
          (item) =>
            item.stageName == this.dataSharingService.stageSelectedInScore
        );
        this.selectedScoreDetails = stageScore[0];

        this.dataSharingService.selectedScoredData = stageScore[0];
        this.dataSharingService.selectedScoredData["isEditText"] = false;
        this.showSaveButton = false;
        this.dataSharingService.emitScoreChange(
          this.dataSharingService.selectedScoredData
        );
        // // console.log(this.dataSharingService.selectedScoredData);
        // // console.log(res);
        if (
          data.selectedScoreData &&
          "dealUserScoreHistoryList" in data.selectedScoreData
        ) {
          this.dealUsersScoreData = stageScore[0].dealUserScoreHistoryList;
        } else {
          this.dealUsersScoreData = stageScore[0].dealUserScore;
        }
        // // console.log(this.dealUsersScoreData)
      }
    });
  }

  openBottomSheet(): void {
    const matBottomSheetRef = this._bottomSheet.open(
      UserScoreDetailsComponent,
      {
        data: {
          selectedScoreData: this.dataSharingService.selectedScoredData,
          dealAnalysisDetails:
            this.dataSharingService.dealAnalysisContentOfUser,
        },
        panelClass: "large-bottom-sheet",
      }
    );
    matBottomSheetRef.afterDismissed().subscribe((result) => {
      // // console.log("bottomsheet was closed");
      // // console.log(result); //Deleted
      if (result) {
        this.getScoreDetails();
      }
    });
  }

  onEdit() {
    if (this.dataSharingService.selectedScoredData == undefined) {
      this.dataSharingService.selectedScoredData = {};
    }

    this.dataSharingService.selectedScoredData["isEditText"] = true;

    this.showSaveButton = true;
    this.dataSharingService.emitScoreChange(
      this.dataSharingService.selectedScoredData
    );
  }

  onDownload(): void {
    this.scoreAnalysisComponent.downloadAsPDF();
  }
}
