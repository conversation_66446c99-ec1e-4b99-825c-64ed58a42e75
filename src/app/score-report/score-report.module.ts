import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ScoreReportComponent } from './score-report.component';
import { ScoreReportRoutes } from './score-report.routing';
import { ScoreAnalysisComponent } from './score-analysis/score-analysis.component';
import { SharedModuleModule } from '../shared-module/shared-module.module';
import { UserScoreDetailsComponent } from './user-score-details/user-score-details.component';
@NgModule({
    imports: [
        CommonModule,
        ScoreReportRoutes,
        SharedModuleModule
    ],
    declarations: [ScoreReportComponent , ScoreAnalysisComponent , UserScoreDetailsComponent],
   
  
})
export class ScoreReportModule { }
