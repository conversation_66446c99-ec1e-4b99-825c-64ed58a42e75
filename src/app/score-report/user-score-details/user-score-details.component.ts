import { I } from "@angular/cdk/keycodes";
import { DatePipe } from "@angular/common";
import { Component, Inject, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MatBottomSheetRef,
  MAT_BOTTOM_SHEET_DATA,
} from "@angular/material/bottom-sheet";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
@Component({
  selector: "app-user-score-details",
  templateUrl: "./user-score-details.component.html",
  styleUrls: ["./user-score-details.component.scss"],
})
export class UserScoreDetailsComponent implements OnInit {
  teamDealList: any;
  dealUsersScoreData: any = [];
  isEditViewFor = "";
  showSaveBtn: boolean = false;
  DisableScore: boolean = false;
  JsonData: any;
  content: any = "";
  newScoreDetails = [
    { name: "Team profile and strength", score: 0, comment: "" },
    { name: " Market size attractiveness", score: 0, comment: "" },
    { name: " Business model robustness", score: 0, comment: "" },
    { name: " Revenue model and traction", score: 0, comment: "" },
    { name: "Capital efficiency", score: 0, comment: "" },
    { name: "Competitive Landscape", score: 0, comment: "" },
  ];
  formatLabel(value: number) {
    return value;
  }
  userScoreForm: UntypedFormGroup = new UntypedFormGroup({});
  constructor(
    private errorService: ErrorService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public data: any,
    private bottomSheet: MatBottomSheetRef<UserScoreDetailsComponent>,
    public dataSharingService: DataSharingService,
    public datePipe: DatePipe,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    public fb: UntypedFormBuilder
  ) {
    if (
      data.selectedScoreData &&
      "dealUserScoreHistoryList" in data.selectedScoreData
    ) {
      this.showSaveBtn = false;
      this.dealUsersScoreData =
        data.selectedScoreData?.dealUserScoreHistoryList?.slice();
    } else {
      this.showSaveBtn = true;
      this.dealUsersScoreData = data.selectedScoreData?.dealUserScore?.slice();
    }
    this.teamDealList =
      this.dataSharingService.selectedApplicationData.dealTeamList;

    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "BIR" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
    ) {
      this.DisableScore = true;
    }
  }

  ngOnInit() {
    let SelectedTab = this.teamDealList.findIndex(
      (tab) => tab.teamName === localStorage.getItem("user")
    );
    if (SelectedTab !== -1) {
      this.teamDealList.unshift(this.teamDealList.splice(SelectedTab, 1)[0]);
    }
  }

  onClose() {
    this.bottomSheet.dismiss(this.showSaveBtn);
    event.preventDefault();
  }

  getUpdatedAtScore(userId, dealTeamId) {
    let data = [];
    data = this.dealUsersScoreData?.filter(
      (ele) => ele.dealTeamId == dealTeamId
    );
    if (data && data.length != 0) {
      let updatedDate = data[0].modifiedDate
        ? data[0].modifiedDate
        : data[0].createdDate;
      if (updatedDate) {
        let date = this.datePipe.transform(new Date(updatedDate));
        return `Last scored on ${date}`;
      } else {
        let date = this.datePipe.transform(new Date());
        return `Last scored on ${date}`;
      }
    } else {
      return `${userId} has not scored yet.`;
    }
  }

  getUserScore(userName, fieldName, mode, dealTeamId) {
    let userSpecificDetail = [];
    userSpecificDetail = this.dealUsersScoreData?.filter(
      (ele) => ele.dealTeamId == dealTeamId
    );

    if (
      userName == this.isEditViewFor &&
      userSpecificDetail &&
      userSpecificDetail?.length != 0 &&
      !userSpecificDetail[0].userScoreDetails &&
      mode == "editMode"
    ) {
      return this.newScoreDetails;
    }

    if (
      mode == "editMode" &&
      (!userSpecificDetail || userSpecificDetail?.length == 0)
    ) {
      if (fieldName == "userScoreAverage") {
        return 0;
      }

      if (fieldName == "userScoreDetails") {
        return this.newScoreDetails;
      }
    }

    if (userSpecificDetail && userSpecificDetail?.length != 0) {
      if (fieldName == "userScoreAverage") {
        return userSpecificDetail[0]?.userScoreAverage;
      }
      if (fieldName == "userScoreDetails") {
        return userSpecificDetail[0].userScoreDetails?.userScoreDerails?.scores;
      }
    }
  }

  checkUserAccess(userName, dealTeamId) {
    let stage =
      this.dataSharingService.selectedApplicationData?.currentStageName;

    let selectedStage = this.data?.selectedScoreData?.stageName
      ? this.data?.selectedScoreData?.stageName
      : this.dataSharingService?.stageSelectedInScore;
    if (this.data?.selectedScoreData?.isScoreFreezed) {
      return false;
    }
    if (this.showSaveBtn && selectedStage == stage) {
      if (userName == localStorage.getItem("user")) return true;
      if (userName != localStorage.getItem("user")) return false;
    } else {
      return false;
    }
  }
  onEdit(userName, dealTeamId) {
    this.generateReactiveForm(this.newScoreDetails);
    this.isEditViewFor = userName;
  }

  showEditPart(userName, dealTeamId) {
    if (
      !this.isEditViewFor ||
      (this.isEditViewFor && userName != this.isEditViewFor)
    )
      return false;
    if (this.isEditViewFor && userName == this.isEditViewFor) return true;
  }

  onSave(userName, dealTeamId) {
    if (this.userScoreForm.invalid) {
      this.userScoreForm.markAllAsTouched();
      this.notificationMessage.error(
        "Some of the assessment items need inputs, please complete the assessment and save"
      );
      return;
    }

    this.onSaveDetails(userName, dealTeamId);
  }

  onSaveDetails(userName, dealTeamId) {
    let userSpecificDetail = [];
    userSpecificDetail = this.dealUsersScoreData?.filter(
      (ele) => ele.dealTeamId == dealTeamId
    );

    let payload = {};

    if (
      userSpecificDetail &&
      userSpecificDetail.length != 0 &&
      userSpecificDetail[0].id
    ) {
      if (
        userSpecificDetail &&
        userSpecificDetail?.length != 0 &&
        !userSpecificDetail[0].userScoreDetails
      ) {
        payload = {
          dealAnalysisDetails: {
            dealAnalysisDetails:
              this.dataSharingService.dealAnalysisContentOfUser,
          },
          stageName: userSpecificDetail[0].stageName,
          userScoreAverage: this.getAverageScore(this.newScoreDetails),
          userScoreDetails: {
            userScoreDerails: {
              avgScore: this.getAverageScore(this.newScoreDetails),
              scores: this.newScoreDetails,
            },
          },
          dealId: userSpecificDetail[0].dealId,
          dealScoreId: userSpecificDetail[0].dealScoreId,
          dealTeamId: userSpecificDetail[0].dealTeamId,
        };
      } else {
        payload = {
          dealAnalysisDetails: {
            dealAnalysisDetails:
              this.dataSharingService.dealAnalysisContentOfUser,
          },

          stageName: userSpecificDetail[0].stageName,
          userScoreAverage: this.getAverageScore(
            userSpecificDetail[0]?.userScoreDetails?.userScoreDerails?.scores
          ),
          userScoreDetails: {
            userScoreDerails: {
              avgScore: this.getAverageScore(
                userSpecificDetail[0]?.userScoreDetails?.userScoreDerails
                  ?.scores
              ),
              scores:
                userSpecificDetail[0]?.userScoreDetails?.userScoreDerails
                  ?.scores,
            },
          },
          dealId: userSpecificDetail[0].dealId,
          dealScoreId: userSpecificDetail[0].dealScoreId,
          dealTeamId: userSpecificDetail[0].dealTeamId,
        };
      }
      if (
        payload["userScoreAverage"] == 0 ||
        payload["userScoreAverage"] == null ||
        payload["userScoreAverage"] == undefined
      ) {
        this.notificationMessage.error("Average score can not be zero.");
        return;
      }
      //  console.log(payload)

      this.dealService
        .updateScoreDetails(payload, userSpecificDetail[0].id)
        .subscribe((res) => {
          this.notificationMessage.success(
            JsonData["label.success.UpdateAnalysis"]
          );

          let index = this.dealUsersScoreData?.findIndex(
            (x) => x.createdBy == userName
          );
          if (
            userSpecificDetail &&
            userSpecificDetail?.length != 0 &&
            !userSpecificDetail[0].userScoreDetails
          ) {
            this.dealUsersScoreData[index].userScoreAverage =
              this.getAverageScore(this.newScoreDetails);
          } else {
            this.dealUsersScoreData[index].userScoreAverage =
              this.getAverageScore(
                userSpecificDetail[0]?.userScoreDetails?.userScoreDerails
                  ?.scores
              );
          }
          payload["modifiedDate"] = this.datePipe.transform(new Date());
          payload["modifiedBy"] = userName;

          this.getScoreDetails();
        });
    } else {
      // let dealTeamId = this.teamDealList.filter(
      //   (item) => item.teamName == userName
      // );

      payload = {
        dealAnalysisDetails: {
          dealAnalysisDetails:
            this.dataSharingService.dealAnalysisContentOfUser,
        },
        stageName:
          this.dataSharingService.selectedApplicationData?.currentStageName,
        dealId: this.dataSharingService.selectedApplicationData?.id,
        userScoreAverage: this.getAverageScore(this.newScoreDetails),
        userScoreDetails: {
          userScoreDerails: {
            avgScore: this.getAverageScore(this.newScoreDetails),
            scores: this.newScoreDetails,
          },
        },
        dealTeamId: dealTeamId,
      };
      if (
        payload["userScoreAverage"] == 0 ||
        payload["userScoreAverage"] == null ||
        payload["userScoreAverage"] == undefined
      ) {
        this.notificationMessage.error("Average score can not be zero.");
        return;
      }
      this.dealService.sendScoreDetails(payload).subscribe((res) => {
        this.notificationMessage.success(
          JsonData["label.success.UpdateAnalysis"]
        );
        if (
          this.dealUsersScoreData &&
          this.dealUsersScoreData?.filter((ele) => ele.dealTeamId == dealTeamId)
            .length != 0
        ) {
          let index = this.dealUsersScoreData?.findIndex(
            (x) => x.dealTeamId == dealTeamId
          );
          this.dealUsersScoreData[index] = payload;
        } else {
          if (this.dealUsersScoreData == undefined)
            this.dealUsersScoreData = [];
          payload["createdDate"] = this.datePipe.transform(new Date());
          payload["createdBy"] = userName;
          this.dealUsersScoreData.push(payload);
        }

        this.getScoreDetails();
      });
    }
  }

  getScoreDetails() {
    let id = this.dataSharingService.selectedApplicationData?.id;
    this.dealService.getScoreDetails(id).subscribe((res) => {
      let data: any = [];
      data = res;
      if (data) {
        let stageScore = [];
        stageScore = data.filter(
          (item) =>
            item.stageName == this.dataSharingService.stageSelectedInScore
        );
        this.dataSharingService.selectedScoredData = stageScore[0];
        if (
          data.selectedScoreData &&
          "dealUserScoreHistoryList" in data.selectedScoreData
        ) {
          this.dealUsersScoreData = stageScore[0].dealUserScoreHistoryList;
        } else {
          this.dealUsersScoreData = stageScore[0].dealUserScore;
        }
        this.isEditViewFor = "";
      }
    });
  }

  getAverageScore(array) {
    let avgScore = array?.reduce((a, b) => a + (b["score"] || 0), 0) / 6;
    avgScore = Number(avgScore.toFixed(2));

    return avgScore;
  }

  generateReactiveForm(data) {
    if (data && data.length != 0) {
      this.userScoreForm = this.createGroup(data);
      // console.log(this.userScoreForm.value)

      this.getValidations(data);
    } else {
      // this.noFieldsMessage = "No data available"
    }
  }
  getFormControlName(name) {
    return name?.trim();
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) =>
      group.addControl(control?.name?.trim(), this.fb.control(""))
    );
    return group;
  }

  getValidations(data) {
    if (data && data.length != 0) {
      data.forEach((element) => {
        this.userScoreForm.controls[element?.name?.trim()].setValidators([
          Validators.required,
        ]);
        this.userScoreForm.controls[
          element?.name?.trim()
        ].updateValueAndValidity();
      });
    }
  }
}
