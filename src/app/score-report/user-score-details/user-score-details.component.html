<div fxLayout="row wrap" fxLayoutGap="4px">

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="closeButton">




    <mat-icon aria-label="score-back-btn"  (click)="onClose()" class="pointer">close</mat-icon>




  </div>
</div>





<div fxLayout="row wrap" fxLayoutGap="4px">

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">



    <mat-tab-group>
      <ng-container *ngFor="let tab of teamDealList; let i = index">
        <mat-tab [aria-label]="'team-score-' + tab.teamName" [label]="tab.teamName">
          <div class="userScoreContainer">
           
            <div fxLayout="row wrap" fxLayoutGap="4px" class="margin-bottom-2">

              <div fxFlex="40%" fxFlex.md="40%" fxFlex.xs="100%" fxFlex.sm="100%">
                <div fxLayout="row wrap" fxLayoutGap="4px">
                  <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="100%" fxFlex.sm="100%">
                    <h3 class="agendaTitle">{{tab.teamName}}</h3>
                    <h5>{{getUpdatedAtScore(tab.teamName , tab.id)}}</h5>
                  
                  </div>
                  <div fxFlex="49%" fxFlex.md="45%" fxFlex.xs="100%" fxFlex.sm="100%">
                    <h2  class="hyperlinkColor addIcon">{{getUserScore(tab.teamName ,
                      'userScoreAverage' , 'viewMode' , tab.id)}}</h2>
                 
                  </div>
                </div>
              </div>
            
              <div fxFlex="59%" fxFlex.md="59%" fxFlex.xs="100%" fxFlex.sm="100%" class="addIcon">
               <ng-container *ngIf="checkUserAccess(tab.teamName , tab.id)">
                <button aria-label="save-team-score-btn" *ngIf="isEditViewFor" mat-raised-button class="green" (click)="onSave(tab.teamName , tab.id)">Save</button>
                <button aria-label="edit-team-score-btn"  *ngIf="!isEditViewFor" mat-raised-button class="blue" [disabled]="DisableScore"  (click)="onEdit(tab.teamName , tab.id)">Edit</button>
               </ng-container>
               
              </div>
            </div>




            <div class="viewPart" *ngIf="!showEditPart(tab.teamName , tab.id)">
              <div fxLayout="row wrap" fxLayoutGap="4px"
                *ngFor="let userScore of getUserScore(tab.teamName , 'userScoreDetails' , 'viewMode' , tab.id)">
                <div fxFlex="41%" fxFlex.md="41%" fxFlex.xs="100%" fxFlex.sm="100%">


                  <div class="margin-5-0">
                    <p class="margin-0"><span  class="userScoreName">{{userScore.name}}</span> <span class="userScore">{{userScore.score}}</span></p>
                    <mat-slider  disabled thumbLabel [displayWith]="formatLabel" min="0"
                      max="5" step="1" class="width-100 userScoreSlider" name="revenueScore"><input matSliderThumb />
                      <input attr.aria-label="{{userScore.name}}-score"  matSliderThumb  [(ngModel)]="userScore.score" #slider>
                    </mat-slider>
                  </div>


                </div>
                <div class="pl-10" fxFlex="58%" fxFlex.md="58%" fxFlex.xs="100%" fxFlex.sm="100%">
                  <mat-form-field appearance="outline" class="width-100">
                    <mat-label class="scorecomment">Comment </mat-label>
                    <textarea aria-label="comment-textarea-input-field"   disabled matInput type="text" class="scorecomment" [(ngModel)]="userScore.comment"></textarea>
                  </mat-form-field>
                </div>
              </div>
            </div>




            <ng-container class="editPart" *ngIf="showEditPart(tab.teamName , tab.id)">
            <form autocomplete="off" [formGroup]="userScoreForm">
              <div fxLayout="row wrap" fxLayoutGap="4px"
              *ngFor="let userScore of getUserScore(tab.teamName , 'userScoreDetails' , 'editMode' , tab.id)">
              <div fxFlex="41%" fxFlex.md="41%" fxFlex.xs="100%" fxFlex.sm="100%">


                <div class="margin-5-0">
                  <p class="margin-0"><span class="userScoreName">{{userScore.name}}</span> <span class="userScore">{{userScore.score}}</span></p>
                  <mat-slider   thumbLabel [displayWith]="formatLabel" min="0"
                    max="5" step="1" class="width-100 userScoreSlider" name="revenueScore" ><input matSliderThumb />
                    <input attr.aria-label="{{userScore.name}}-score"  matSliderThumb [(ngModel)]="userScore.score" [ngModelOptions]="{standalone: true}" #slider>
                    </mat-slider>
                </div>


              </div>
              <div class="pl-10" fxFlex="58%" fxFlex.md="58%" fxFlex.xs="100%" fxFlex.sm="100%">
                <mat-form-field appearance="outline" class="width-100">
                  <mat-label class="scorecomment">Comment </mat-label>
                  <textarea aria-label="comment-textarea-input-field"    [formControlName]="userScore?.name?.trim()"class="scorecomment" matInput type="text" [(ngModel)]="userScore.comment"  ngDefaultControl  ></textarea>
                </mat-form-field>
              </div>
            </div>
            </form>
            </ng-container>
          </div>
        </mat-tab>
      </ng-container>


    </mat-tab-group>





  </div>
</div>