<mat-form-field class="full-width custom-mat-input-style">
  <mat-label *ngIf="floatingLabel" [matTooltip]="displayName" matTooltipClass="accent-tooltip"
    matTooltipShowDelay="1000">{{ displayName }}
  </mat-label>

  <input matInput [formControl]="control" [matAutocomplete]="auto"
    attr.aria-label="time-field-{{ displayName }}" [ngStyle]="{ color: getColor() }" type="text" />

  <button mat-icon-button matSuffix (click)="openPicker()" tabindex="-1">
    <mat-icon>access_time</mat-icon>
  </button>

  <mat-autocomplete #auto="matAutocomplete" autoActiveFirstOption #trigger="matAutocomplete">
    <mat-option *ngFor="let time of timeSlots" [value]="time">
      {{ time }}
    </mat-option>
  </mat-autocomplete>

  <mat-error *ngIf="control && control.invalid">
    {{ getErrorMessage(control) }}
  </mat-error>
</mat-form-field>
