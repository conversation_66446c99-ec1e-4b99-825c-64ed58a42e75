import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { SimpleCustomDataType } from "../data-types.model";
import { MatSelect } from "@angular/material/select";

@Component({
  selector: "app-time-input",
  templateUrl: "./time-input.component.html",
  styleUrl: "./time-input.component.scss",
})
export class TimeInputComponent extends SimpleCustomDataType implements OnInit {
  @ViewChild(MatSelect) matSelect: MatSelect;
  @Input() timeFormat12HR: string;
  timeSlots: string[] = [];
  is24HrFormat = true;

  ngOnInit() {
    this.generateTimeSlots(30);
  }

  generateTimeSlots(interval: number) {
    this.timeSlots = [];

    for (let h = 0; h < 24; h++) {
      for (let m = 0; m < 60; m += interval) {
        if (this.timeFormat12HR && this.timeFormat12HR == "Y") {
          // 12-hour format with AM/PM
          const hour12 = h % 12 === 0 ? 12 : h % 12;
          const period = h < 12 ? "AM" : "PM";
          const hh = hour12.toString().padStart(2, "0");
          const mm = m.toString().padStart(2, "0");
          this.timeSlots.push(`${hh}:${mm} ${period}`);
        } else {
          // 24-hour format
          const hh = h.toString().padStart(2, "0");
          const mm = m.toString().padStart(2, "0");
          this.timeSlots.push(`${hh}:${mm}`);
        }
      }
    }
  }
  openPicker() {
    this.matSelect?.open();
  }
}
