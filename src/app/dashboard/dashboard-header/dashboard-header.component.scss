.sidenav-container {
  width: 100%;
  height: 90vh;
}

.example-sidenav-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.sidenav-drawer {
  padding: 10px;
}

.sidenav-drawer-new {
  padding: 10px;
  width: 33%;
}

.notification-drawer {
  padding: 10px;
  width: 50%;
  position: fixed;
  border-radius: 10px !important;
}

.stages-drawer-new {
  width: 25%;
  margin-top: 175px;
  position: fixed;
}

.start {
  border-top-right-radius: 10px !important;
}

.end {
  border-top-left-radius: 10px !important;
}

.side-nav-icon {
  font-size: 21px;
}

.userNameCss {
  padding: 0;
  text-transform: uppercase;
  font-weight: 600;
}

.side-nav-bar {

  .mat-mdc-button,
  .mat-mdc-icon-button,
  .mat-mdc-outlined-button,
  .mat-mdc-unelevated-button {

    width: 100% !important;
    text-align: left !important;

  }
}


.notification {
  :host {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

}


.matIconSize {
  .mat-icon {
    font-size: 20px;
  }

  width:9%;
}

.matIconColor {
  color: var(--mdc-filled-text-field-disabled-input-text-color)
}

.customHeight {
  .mat-mdc-list-item {
    height: 90px !important;


  }

  ;

  .mat-mdc-card-subtitle {
    margin-bottom: 0 !important;
  }
}

.notif-card {
  margin-bottom: 1px;
  padding: 12px;
}

.notif-card:hover {

  box-shadow: 0 1px 8px rgb(0 0 0 / 40%) !important;

  .truncate {
    display: block;
  }

}

.truncate {
  display: -webkit-box;
  text-overflow: ellipsis !important;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}



.title {
  font-weight: bold;
  color: var(--mdc-filled-text-field-disabled-input-text-color)
}

.title:hover {
  text-decoration: underline !important;
}

.subtitle {
  color: var(--mdc-filled-text-field-disabled-input-text-color)
}

.buttonLink {
  cursor: pointer !important;
  text-decoration: underline !important;
}


.maxReportHeight {
  min-height: 400px !important;
  max-height: 400px !important;

}

.font-12 {
  font-size: 12px;
}

.mat-drawer-inner-container {
  overflow: hidden !important;
}

.nav-link {
  font-size: 16px;
  font-weight: 500;
}

.capitalize {
  text-transform: capitalize;
}

.header-navbar {
  height: 50px;
}

.menu {
  margin-left: 1%;
}

.report-tab {
  margin-left: 0.5vw;
}

.dividerOne {
  margin: 0 1% 0 0;
}

.headerThemeIcon {
  margin-top: 14%;
}


.themeOne {
  color: #3f51b5 !important
}

.themeTwo {
  color: #fda63a !important
}

.themeThree {
  color: #673ab7 !important
}

.width9 {
  width: 9% !important
}


.header-spacer {
  flex: 1 1 auto;
}

.leftNavSpacing {
  margin: 0 1vw 0 0.5vw;
}


#scroll-text {
  /* animation properties */
  -moz-transform: translateX(100%);
  -webkit-transform: translateX(100%);
  transform: translateX(100%);


  -moz-animation: my-animation 15s linear infinite;
  -webkit-animation: my-animation 15s linear infinite;
  animation: my-animation 15s linear infinite;
}

/* for Firefox */
@-moz-keyframes my-animation {
  from {
    -moz-transform: translateX(100%);
  }

  to {
    -moz-transform: translateX(-100%);
  }
}

/* for Chrome */
@-webkit-keyframes my-animation {
  from {
    -webkit-transform: translateX(100%);
  }

  to {
    -webkit-transform: translateX(-100%);
  }
}

@keyframes my-animation {
  from {
    -moz-transform: translateX(100%);
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }

  to {
    -moz-transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
}

.margin-top-1 {
  margin-top: 1% !important;
}

.margin-bottom-1 {
  margin-bottom: 1% !important;
}


//CSS for custom tooltip
.custom-tooltip {
  position: relative;
  display: block;
}

.custom-tooltip .tooltip-content {
  width: 250px;
  text-align: center;
  padding: 16px;
  border-radius: 6px;
  position: fixed;
  z-index: 1;
}

.tooltip-content-old-ui {
  background-color: var(--custom-primary-color);
  color: var(--custom-primary-contrast);
}

.tooltip-arrow {
  position: absolute;
  top: -10px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
}

.tooltip-arrow-old-ui {
  border-bottom: 10px solid var(--custom-primary-color);
}

.tooltip-content h4 {
  margin: 0;
  font-weight: bold;
}

.tooltip-content p {
  margin: 8px 0;
  font-size: 14px;
}

.tooltip-content button {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
}

.tooltip-content-old-ui button {
  background-color: var(--custom-primary-contrast);
  color: var(--custom-primary-color);
}


.alignSidebarbuttons {
  justify-content: left !important;
}

.userManualButtonContainer {
  /*  margin-top: 97px !important; */
}

.userManualButtonContainer.border-visible {
  border-bottom: 1px solid lightgray;
}

.fixed-height {
  height: 320px;
}

.text-button {
  color: var(--mdc-text-button-label-text-color) !important;
}
