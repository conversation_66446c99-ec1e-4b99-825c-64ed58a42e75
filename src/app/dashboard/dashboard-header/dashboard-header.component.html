<mat-toolbar class="header-navbar" color="primary">
  <a *ngIf="this.projectLogo === 'finnate' || this.projectLogo === null"
    (click)="navigateRoute('logo','/application')" class="margin-top-1 margin-bottom-1">
    <img *ngIf="this.themeMode === 'light-mode'" width="42px" height="42px"
      src="../../../assets/imgs/finnate_white_new.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'dark-mode'" width="45px" height="45px"
      src="../../../assets/imgs/finnate_black.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'light-mode-new'" width="42px" height="42px"
      src="../../../assets/imgs/finnate_white_new.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'dark-mode-new'" width="45px" height="45px"
      src="../../../assets/imgs/finnate_black.png" alt="Finnate-logo">
    <span class="header-title-font"></span>
  </a>
  <a *ngIf="this.projectLogo === 'metiz'" (click)="navigateRoute('logo','/application')"
    class="margin-top-1 margin-bottom-1">
    <img *ngIf="this.themeMode === 'light-mode'" width="60px" height="60px"
      src="../../../assets/imgs/metiz.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'dark-mode'" width="60px" height="60px"
      src="../../../assets/imgs/metiz_black.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'light-mode-new'" width="60px" height="60px"
      src="../../../assets/imgs/metiz.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'dark-mode-new'" width="60px" height="60px"
      src="../../../assets/imgs/metiz_black.png" alt="Finnate-logo" class="margin-top-10">
    <span class="header-title-font"></span>
  </a>
  <span class="header-margin-name">{{projectTitle}}</span>
  <div class="flex leftNavSpacing">
    <div class="menu" *ngFor="let menu of menus">
      <div *ngIf="menu.rule">
        <button mat-button [matMenuTriggerFor]="belowMenu" #menuTrigger="matMenuTrigger"
          (click)="openMyMenu(menuTrigger)">{{menu.menu}}</button>
        <mat-menu #belowMenu="matMenu" yPosition="below" [overlapTrigger]="false">
          <span (mouseenter)="menuenter()">
            <div *ngFor="let subMenu of menu.subMenus;">
              <button mat-menu-item *ngIf="subMenu.rule"
                (click)="subMenu.query?navigateRoute('subMenu',subMenu):''">{{subMenu.subMenu}}</button>
            </div>
          </span>
        </mat-menu>

      </div>
    </div>
  </div>
  <div>
    <div class="report-tab"
      *ngIf="this.reportConfigDetails !== undefined && this.reports?.length > 0">
      <button mat-button [matMenuTriggerFor]="reportMenu" #menuTrigger="matMenuTrigger"
        (click)="openReportMenu(menuTrigger)">Reports</button>
      <mat-menu #reportMenu="matMenu" yPosition="below" [overlapTrigger]="false">
        <span (mouseenter)="menuenter()">
          <div class="maxReportHeight">
            <div *ngFor="let report of reports">
              <button mat-menu-item (click)="navigateRoute('report',report)"
                *ngIf="report.rule">{{report.reportName}}</button>
            </div>
          </div>
        </span>
      </mat-menu>
    </div>
  </div>

  <span class="header-spacer font-100"></span>

  <a [style.display]="'flex'" *ngIf="investfactRedirectionDetails?.value"
    [disabled]="disableActionButton" class="nav-link" mat-button
    [href]="investfactRedirectionDetails?.applicationUrl" target="_blank">
    <span class="text-button">
      {{investfactRedirectionDetails.component}}
    </span></a>

  <span *ngIf="investfactRedirectionDetails?.value" class="dividerOne">
    |
  </span>

  <a matTooltipClass="accent-tooltip" matTooltip="Workflow Engine"
    [style.display]="usedKeyclockLogin ? 'flex' : 'none'" *ngIf="wfeRedirectionDetails?.value"
    [disabled]="disableActionButton" mat-icon-button [href]="workflowEngineUrl" target="_blank">
    <span class="material-symbols-outlined text-button">
      rebase
    </span>
  </a>
  <a matTooltipClass="accent-tooltip" matTooltip="Workflow Engine" class="text-button"
    [style.display]="!usedKeyclockLogin ? 'flex' : 'none'" *ngIf="wfeRedirectionDetails?.value"
    [disabled]="disableActionButton" mat-icon-button
    [href]="workflowEngineUrl+'/login?user='+user+'&accessToken='+accessToken+'&tenant='+tenant"
    target="_blank">
    <span class="material-symbols-outlined">
      rebase
    </span>
  </a>

  <a *ngIf="hasCopyConfigurationAccess" matTooltipClass="accent-tooltip"
    matTooltip="Replicate Configuration" [style.display]="'flex'" [disabled]="disableActionButton"
    mat-icon-button target="_blank" (click)="navigateRoute('routeTo','copy-configuration')">
    <span class="material-symbols-outlined text-button">
      settings_input_component
    </span>
  </a>

  <span class="dividerOne">
    |
  </span>


  <span *ngIf="!themeService.useNewTheme">
    <ng-container *ngIf="breadcrumb !== 'Deal'">
      <span class="font-16 capitalize pointer nav-link"
        (click)="navigateRoute('routeTo',navigationurl)">
        {{getSidebarItembyName(breadcrumb) ? getSidebarItembyName(breadcrumb) : breadcrumb}}
      </span>

      <span class="font-16 capitalize pointer nav-link" *ngIf="subPageLoaded"
        (click)="openSubPage()"> &nbsp; / {{subPageLoaded}}</span>
    </ng-container>

    <ng-container *ngIf="breadcrumb === 'Deal'">
      <span class="font-16 capitalize pointer nav-link"
        (click)="navigateRoute('goTo',{name: 'Deal', icon: 'work', url: '/application'})">
        {{getSidebarItembyName(breadcrumb) ? getSidebarItembyName(breadcrumb) : breadcrumb}}
      </span>
      <span class="font-16 capitalize pointer nav-link" *ngIf="subPageLoaded"
        (click)="openSubPage()"> &nbsp; / {{subPageLoaded}}</span>
    </ng-container>
  </span>

  <button aria-label="notifications-button" class="margin-left-1 notification" mat-icon-button
    (click)="dataSharingService.notificationDrawerToggle.next(true)">
    <mat-icon matBadgeColor="warn"
      [matBadge]="this.notificationService.unreadNotificationsCount > 0 ? this.notificationService.unreadNotificationsCount : ''">notifications</mat-icon>
  </button>

  <button aria-label="user-name-button" class="font-16 userNameCss" [matMenuTriggerFor]="username"
    mat-button #menuTriggerAcc="matMenuTrigger" (click)="openMyMenu(menuTriggerAcc)"
    aria-label="user">
    <span>
      {{ user}}
    </span>
  </button>
  <div [matMenuTriggerFor]="theme" #menuTriggerTheme="matMenuTrigger"
    (click)="openMyMenu(menuTriggerTheme)">
    <button mat-button aria-label="theme-button" class="font-16 userNameCss" aria-label="theme">
      <span>
        <mat-icon class="headerThemeIcon"> format_color_fill</mat-icon>
      </span>
    </button>
  </div>
</mat-toolbar>



<mat-drawer-container class="sidenav-container" [hasBackdrop]="false" autosize>
  <mat-drawer #drawer class="sidenav-drawer" mode="side" opened="true">
    <app-sidebar-nav [originalSidebarItems]="sidebarItems"></app-sidebar-nav>
  </mat-drawer>

  <mat-drawer #historyDrawer class="sidenav-drawer-new" mode="over" position="end"
    autoFocus="false">
    <app-history-record></app-history-record>
  </mat-drawer>

  <mat-drawer-content class="drawer-content">
    <div class="full-height">
      <router-outlet></router-outlet>
    </div>
    <!-- <div *ngIf="isLoading | async" class="spinner">
      <app-loader></app-loader>
  </div> -->
  </mat-drawer-content>
</mat-drawer-container>

<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">
  <mat-drawer #notificationDrawer class="notification-drawer" mode="over" position="end"
    autoFocus="false">
    <app-notification-record *ngIf="notificationDrawer.opened"></app-notification-record>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>


<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">

  <mat-drawer #previousStagesDrawer class="stages-drawer-new start" mode="over" position="start"
    autoFocus="false">
    <app-upcomnig-stages [status]="'completed'"
      *ngIf="previousStagesDrawer.opened"></app-upcomnig-stages>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>

<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">

  <mat-drawer #upcomingStagesDrawer class="stages-drawer-new end" mode="over" position="end"
    autoFocus="false">
    <app-upcomnig-stages [status]="'upcoming'"
      *ngIf="upcomingStagesDrawer.opened"></app-upcomnig-stages>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>


<mat-menu class="username-menu" #username="matMenu">
  <span (mouseenter)="menuenter()">

    <button aria-label="logout-icon-button" mat-menu-item (click)="navigateRoute('logout',null)"
      aria-label="logout">
      <mat-icon>exit_to_app</mat-icon>
      <span>{{"label.logout"|literal}}</span>
    </button>
  </span>
</mat-menu>

<mat-menu class="username-menu" #theme="matMenu">
  <span (mouseenter)="menuenter()">
    <button mat-menu-item (click)="switchTheme('light-mode')" aria-label="theme-change"><mat-icon
        class="themeOne">cloud_circle</mat-icon>{{'Blue'}}</button>
    <button mat-menu-item (click)="switchTheme('dark-mode')" aria-label="theme-change"> <mat-icon
        class="themeTwo">wb_sunny</mat-icon>{{'Amber'}}</button>
    <div [hidden]="true">
      <mat-chip-listbox aria-label="new-theme-selection">
        <mat-chip-option [selected]="themeService.useNewTheme" color="accent"
          (click)="$event.stopPropagation()" (selectionChange)="switchToNewTheme($event.selected)">
          New UI
          <span matChipTrailingIcon class="material-symbols-outlined">
            experiment
          </span>
        </mat-chip-option>
      </mat-chip-listbox>
    </div>
  </span>
</mat-menu>
