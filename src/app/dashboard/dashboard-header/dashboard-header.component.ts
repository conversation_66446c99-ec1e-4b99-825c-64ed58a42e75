import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from "@angular/core";
import { NavigationStart, Router } from "@angular/router";
import { DataSharingService } from "../../common/dataSharing.service";
import { ChangePasswordDialogComponent } from "src/app/dialogs/change-password-dialog/change-password-dialog.component";
import { MatDialog } from "@angular/material/dialog";
import { ThemeService } from "src/app/theme.service";
import { distinctUntilChanged, Subject, Subscription, takeUntil } from "rxjs";
import { LoaderService } from "src/app/shared-service/loader.service";
import { NotificationService } from "src/app/common/notification.service";
import { MatMenuTrigger } from "@angular/material/menu";
import { ErrorService } from "src/app/shared-service/error.service";
import { ToasterService } from "src/app/common/toaster.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { TokenHelperService } from "src/app/shared-service/token-helper.service";
import { MatSidenav } from "@angular/material/sidenav";
import { environment } from "src/environments/environment";
import { KeycloakService } from "keycloak-angular";
import {
  evalStringExpression,
  formStringExpression,
} from "src/app/helpers/utils";
import { QueryBuilderService } from "src/app/query-builder/query-builder.service";
import {
  ConfigurationResources,
  CopyConfigurationResource,
  DealResource,
  EntityResource,
} from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { OldUiConfirmationDialogComponent } from "src/app/dialogs/old-ui-confirmation-dialog/old-ui-confirmation-dialog.component";
import { CustomKeycloakService } from "src/app/keycloak-login/custom-keycloak.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { DateFormattingService } from "src/app/common/date/date-formatting.service";
import { CopyConfigurationService } from "src/app/shared-service/copy-configuration.service";
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";
import { DocumentService } from "src/app/shared-service/document.service";
import { UtilitiesService } from "src/app/settings/utilities/utilities.service";
import { sidenavListConfig } from "src/app/sidebar-nav/sidebar-nav.model";
import { Title } from "@angular/platform-browser";

@Component({
  selector: "app-dashboard-header",
  templateUrl: "./dashboard-header.component.html",
  styleUrls: ["./dashboard-header.component.scss"],
})
export class DashboardHeaderComponent implements OnInit, OnDestroy {
  @ViewChild("historyDrawer", { static: true })
  public historyDrawer: MatSidenav;
  @ViewChild("sideNavDrawer", { static: true })
  public sideNavDrawer: MatSidenav;
  @ViewChild("upcomingStagesDrawer", { static: true })
  public upcomingStagesDrawer: MatSidenav;
  @ViewChild("previousStagesDrawer", { static: true })
  public previousStagesDrawer: MatSidenav;
  @ViewChild("notificationDrawer", { static: true })
  public notificationDrawer: MatSidenav;

  usedKeyclockLogin = environment.useKeycloakLogin;
  sidenavList: { name: string; icon: string; url?: string }[];
  pageLoaded: string;
  loggedInUser: any = "";
  subPageEntityId: any;
  companyIdOfPerson: any;
  accessToken: any;
  user: any;
  tenant: any;
  monitorUrl: string;
  workflowEngineUrl: string;
  isLoading: Subject<boolean> = this.loader.isLoading;
  notifications: any = [];
  @ViewChild("menuTrigger") trigger: MatMenuTrigger;
  totalUnreadCount = 0;
  applicationConfigdetailsIndex: any;
  componentLabel: any;
  appConfigdetailsIndex: any;
  component: any;
  refresh: any;
  workflowHide = false;
  monitorHide = false;
  applicationConfigdetails: any;
  projectTitle: any = "Originate";
  menubarConfigDetails: any;
  menus: any;
  belowMenu: any;
  notificationTimer: NodeJS.Timeout;
  menuBar: any;
  tenantConf: any;
  reportConfigDetails: any;
  reports: any = [];
  documentConf: any;
  disableActionButton = false;
  loading = false;
  dataModelConfigRules = true;
  businessProcessConfigRules = true;
  dashboardConfigRules = true;
  entityDefinitionConfigRules = true;
  reportConfigRules = true;
  personDetailsRules = true;
  companyDetailsRules = true;
  rolesAndActionsRule = true;
  themeToggleRules = true;
  userManualRules = true;
  sidebarOriginalItems;

  pageLoadedFromNav: string;
  breadcrumb: any;
  showTestenv = false;
  showtest: any;
  status = "completed";
  navigationurl: any;
  isDarkTheme: any;
  userRole = localStorage.getItem("userRole");

  showTooltip = false;
  closeUserToolTip = localStorage.getItem("closeUserToolTip");
  showThemeTooltip = false;
  investfactRedirectionDetails: any;
  wfeRedirectionDetails: any;
  projectLogo = "finnate";

  allUserRoles: string[] = [];
  currentUserRoles: string[] = [];
  filteredUserRoles: string[] = [];
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: Router,
    public loader: LoaderService,
    public themeService: ThemeService,
    public dataSharingService: DataSharingService,
    private readonly matDialog: MatDialog,
    public readonly notificationService: NotificationService,
    private readonly errorService: ErrorService,
    private readonly notificationMessage: ToasterService,
    public pageLayoutService: PageLayoutService,
    public tokenHelperService: TokenHelperService,
    private readonly keyCloak: KeycloakService,
    private readonly customeKeycloakService: CustomKeycloakService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly querybuilderSerice: QueryBuilderService,
    private readonly identityService: IdentityService,
    private readonly dateFormatService: DateFormattingService,
    private copyConfigurationService: CopyConfigurationService,
    private accessControlService: AccessControlService,
    private readonly utilitiesService: UtilitiesService,
    private readonly documentService: DocumentService,
    private readonly titleProject: Title
  ) {
    this.loggedInUser = this.dataSharingService.currentUserName
      ? this.dataSharingService.currentUserName
      : localStorage.getItem("user");
    this.setTheme();
    this.getSidebarHighlight();
    this.getNotifications();
    this.tokenHelperService.events$.forEach((item: any) => {
      if (item == "timeout") {
        clearTimeout(this.notificationTimer);
      }
    });
    this.dataSharingService.historyDrawerToggle.subscribe(() => {
      this.historyDrawer.toggle();
    });

    this.dataSharingService.stagesDrawerToggle.subscribe(() => {
      this.upcomingStagesDrawer.toggle();
    });

    this.dataSharingService.previousstagesDrawerToggle.subscribe(() => {
      this.previousStagesDrawer.toggle();
    });

    this.dataSharingService.notificationDrawerToggle.subscribe(() => {
      this.notificationDrawer.toggle();
    });

    this.getConfigurationDetailsByIdentifier("SIDE_BAR");

    this.routerSubscription = this.route.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.closeAllDrawers();
      }
    });
  }
  showFiller = false;
  subPageLoaded: string;
  themeMode: any;
  routerz = false;
  isDarkMode: boolean;
  sidebarItems: any = [];
  enteredButton = false;
  isMatMenuOpen = false;
  tenantIdentifier = localStorage.getItem("tenantIdentifier");

  get environement() {
    return environment;
  }

  get DEAL_RESOURCE() {
    return DealResource;
  }

  get ENTITY_RESOURCE() {
    return EntityResource;
  }

  get CONFIGURATION_RESOURCE() {
    return ConfigurationResources;
  }

  get COPY_CONFIGURATION_RESOURCE() {
    return CopyConfigurationResource;
  }

  private readonly routerSubscription: Subscription;

  // Function to close all drawers
  closeAllDrawers() {
    if (this.previousStagesDrawer && this.previousStagesDrawer.opened) {
      this.previousStagesDrawer.close();
    }
    if (this.upcomingStagesDrawer && this.upcomingStagesDrawer.opened) {
      this.upcomingStagesDrawer.close();
    }
    if (this.historyDrawer && this.historyDrawer.opened) {
      this.historyDrawer.close();
    }
    if (this.notificationDrawer && this.notificationDrawer.opened) {
      this.notificationDrawer.close();
    }
  }

  ngOnDestroy() {
    clearTimeout(this.notificationTimer);
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        this.allUserRoles = roles.map((role) => role.identifier);
        this.filteredUserRoles = this.allUserRoles;
      });

    this.handleSessionExpiry();
    this.themeMode = localStorage.getItem("user-theme");
    this.accessToken = localStorage.getItem("accessToken");
    this.user = localStorage.getItem("user");
    this.tenant = localStorage.getItem("tenantIdentifier");
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );
    this.dataSharingService.subPageNameSubject.subscribe((data) => {
      this.subPageLoaded = data;
    });

    this.dataSharingService.subPageEntityIdSubject.subscribe((data) => {
      this.subPageEntityId = data;
    });

    this.dataSharingService.companyIdOfPersonSubject.subscribe((data) => {
      this.companyIdOfPerson = data;
    });

    this.dataSharingService.getProjectTitle().subscribe((items) => {
      this.projectTitle = localStorage.getItem("projectTitle");
    });

    this.dataSharingService.getProjectLogo().subscribe((items) => {
      this.projectLogo = localStorage.getItem("projectLogo");
    });

    this.sidenavList = sidenavListConfig;

    this.pageLayoutService
      .getAllConfigurationDetails()
      .subscribe((res: any) => {
        this.menuBar = res.find((item) => item.configIdentifier == "MENU_BAR");
        this.tenantConf = res.find(
          (item) => item.configIdentifier == "TENANT_CONFIGURATION"
        );
        this.utilitiesService.utilitiesConfig =
          res.find((item) => item.configIdentifier == "USER_GUIDE") ?? null;
        this.documentConf = res.find(
          (item) => item.configIdentifier == "DEFAULT_DOCUMENT_TYPE"
        );
        this.querybuilderSerice.defaultConfig = res
          .find((item) => item.configIdentifier == "PROJECT_CONFIG")
          ?.configDetails.find(
            (config) => config.configName == "QUERY_BUILDER"
          );

        // Set Title in topbar
        const { displayName: projectTitle, logo: businessLogo } = res
          .find((item) => item.configIdentifier == "PROJECT_CONFIG")
          ?.configDetails.find(
            (config) => config.configName == "BUSINESS_TITLE"
          );
        localStorage.setItem("projectTitle", projectTitle);
        localStorage.setItem("projectLogo", businessLogo);

        this.titleProject.setTitle(projectTitle);
        this.dataSharingService.setProjectTitle(projectTitle);
        this.dataSharingService.setProjectLogo(businessLogo);

        this.dataSharingService.setdoclist(this.documentConf);
        localStorage.setItem("Documentlist", JSON.stringify(this.documentConf));
        this.reportConfigDetails = res.find(
          (item) => item.configIdentifier == "REPORT_CONFIG"
        );

        if (this.reportConfigDetails !== undefined) {
          this.reports = this.reportConfigDetails.configDetails;
          this.reports.forEach((item: any) => {
            item.rule = true;
            if (item.rules !== null && item.rules !== undefined) {
              const value = `${item.rules["__report__"]}`;
              const exper = formStringExpression(value, [""]);
              if (evalStringExpression(exper, this, [""])) {
                item.rule = false;
              }
            }
          });
        }
        this.dataSharingService
          .getMenubarItems()
          .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
          .subscribe((items) => {
            if (items) {
              this.menus = items?.configDetails;
              this.menus.forEach((menu: any) => {
                menu.rule = true;
                if (menu.rules !== null && menu.rules !== undefined) {
                  const value = `${menu.rules["__topMenu__"]}`;
                  const exper = formStringExpression(value, [""]);
                  if (evalStringExpression(exper, this, [""])) {
                    menu.rule = false;
                  }
                }
                menu.subMenus.forEach((item: any) => {
                  item.rule = true;
                  if (item.rules !== null && item.rules !== undefined) {
                    const value = `${item.rules["__subMenu__"]}`;
                    const exper = formStringExpression(value, [""]);
                    if (evalStringExpression(exper, this, [""])) {
                      item.rule = false;
                    }
                  }
                });
              });
            } else {
              if (this.menuBar != undefined) {
                this.getConfigurationDetailsByIdentifier("MENU_BAR");
              }
            }
          });
        this.dataSharingService
          .getDashboardItems()
          .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
          .subscribe((items) => {
            if (items) {
              const applicationconfiguration = items?.configDetails.findIndex(
                (item) =>
                  this.getPropertyName(item) == "applicationconfiguration"
              );

              this.investfactRedirectionDetails = items.configDetails[
                applicationconfiguration
              ]?.applicationconfiguration.find(
                (item) => item.component == "Investfact"
              );

              this.wfeRedirectionDetails = items.configDetails[
                applicationconfiguration
              ]?.applicationconfiguration.find(
                (item) => item.component == "Workflow Engine"
              );

              const dateFormatConfig =
                items.configDetails[
                  applicationconfiguration
                ]?.applicationconfiguration.find(
                  (item) => item.component == "DateFormat"
                )?.value || this.dateFormatService.defaultDateFormatConfig;

              this.dateFormatService.setDateTimeFormat(dateFormatConfig);

              this.getWorkflowEngineUrl();
              // Needs to remove monitor rediction  related code
              const monitorLabel = items.configDetails[
                applicationconfiguration
              ]?.applicationconfiguration.find(
                (item) => item.component == "Monitor"
              );
              if (monitorLabel?.value == true) {
                this.monitorHide = true;
              } else {
                this.monitorHide = false;
              }
              const TestLabel = items.configDetails[
                applicationconfiguration
              ]?.applicationconfiguration.find(
                (item) => item.component == "TestEnv"
              );
              this.showtest = TestLabel?.Name;
              if (TestLabel?.value == true) {
                this.showTestenv = true;
              } else {
                this.showTestenv = false;
              }
            } else {
              if (this.tenantConf != undefined) {
                this.getConfigurationDetailsByIdentifier(
                  "TENANT_CONFIGURATION"
                );
              }
            }
          });

        const envProperties = res.find(
          (config) => config.configIdentifier == "ENV_PROPERTIES"
        );

        if (envProperties && envProperties.configDetails) {
          this.copyConfigurationService.setEnvConfigDetails(envProperties);
        }
      });

    this.setUserList();
    this.getMonitorUrl();
  }

  get hasCopyConfigurationAccess(): boolean {
    return (
      this.accessControlService.havePermission(
        this.COPY_CONFIGURATION_RESOURCE.Create_Configuration,
        "CHANGE"
      ) ||
      this.accessControlService.havePermission(
        this.COPY_CONFIGURATION_RESOURCE.Update_Configuration,
        "CHANGE"
      ) ||
      this.accessControlService.havePermission(
        this.COPY_CONFIGURATION_RESOURCE.Rule_Configuration,
        "CHANGE"
      )
    );
  }

  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }

  setTheme() {
    const useNewThemeFromConfig = JSON.parse(
      localStorage.getItem("useNewThemeFromConfig")
    );

    const useNewTheme =
      useNewThemeFromConfig?.useNewTheme !== undefined
        ? useNewThemeFromConfig?.useNewTheme
        : environment.useNewTheme;

    const theme = useNewTheme
      ? localStorage.getItem("user-theme") || "light-mode-new"
      : localStorage.getItem("user-theme") || "light-mode";

    this.themeService.useNewTheme =
      localStorage.getItem("user-theme") === "light-mode-new" ||
      localStorage.getItem("user-theme") === "dark-mode-new";
    this.toggleTODarkMode(theme);
  }

  toggleTODarkMode(theme: string) {
    switch (theme) {
      case "dark-mode":
      case "light-mode":
        this.themeService.update(theme);
        break;

      case "light-mode-new":
        this.themeService.updatetheme(theme);
        this.themeService.useNewTheme = true;
        break;

      default:
        this.themeService.initTheme();
        this.isDarkTheme = this.themeService.isDarkMode();
        return { value: this.isDarkTheme ? "dark" : "light" };
    }
  }

  navigateRoute(funcName: string, data: any) {
    if (funcName == "goTo") this.goTo(data);
    if (funcName == "routeTo") this.routeTo(data);
    if (funcName == "report") this.openReports(data);
    if (funcName == "subMenu") this.viewDetails(data);
    if (funcName == "notify") this.navigateToDetails(data);
    if (funcName == "logo") this.route.navigate([data]);
    if (funcName == "logout") this.logout();
  }

  openReports(report) {
    this.dataSharingService.reportDetails = report;
    localStorage.setItem("report", JSON.stringify(report));
    this.route.navigate([`/report-details/${btoa(report.reportName)}`], {
      state: { data: { report: report } },
    });
  }

  openReportMenu(menuTrigger: MatMenuTrigger) {
    this.pageLayoutService
      .getAllConfigurationDetails()
      .subscribe((res: any) => {
        this.reportConfigDetails = res.find(
          (item) => item.configIdentifier == "REPORT_CONFIG"
        );
        this.dataSharingService.reportConfigDetails = this.reportConfigDetails;
        this.reports = this.reportConfigDetails.configDetails;
        this.reports.forEach((item: any) => {
          item.rule = true;
          if (item.rules !== null && item.rules !== undefined) {
            const value = `${item.rules["__report__"]}`;
            const exper = formStringExpression(value, [""]);
            if (evalStringExpression(exper, this, [""])) {
              item.rule = false;
            }
          }
        });
      });

    setTimeout(() => {
      menuTrigger.openMenu();
    });
  }

  getItemDetails(name, requiredDetail) {
    const itemDetail = this.sidenavList.filter((ele) => ele.name == name);
    if (itemDetail && itemDetail?.length != 0) {
      if (requiredDetail == "icon") return itemDetail[0]?.icon;
      if (requiredDetail == "route") return itemDetail[0];
    }
  }
  getWorkflowEngineUrl() {
    // Needs to refactor
    let url = "";
    const hostName = "https://" + document.location.hostname;
    url = hostName.replace("originate", "workflow");

    this.workflowEngineUrl = this.wfeRedirectionDetails?.applicationUrl
      ? this.wfeRedirectionDetails?.applicationUrl
      : url;
    this.dataSharingService.wfeUrl = this.wfeRedirectionDetails?.applicationUrl
      ? this.wfeRedirectionDetails?.applicationUrl
      : url;
  }

  getMonitorUrl() {
    let url = "";

    if (document.location.hostname == "localhost") {
      url =
        document.location.protocol +
        "//" +
        document.location.hostname +
        ":8400";
    }

    if (
      document.location.hostname == "stage-originate.centelon.com" ||
      document.location.hostname ==
        this.tenantIdentifier + ".stage.originate.aif.centelon.com"
    ) {
      url = "https://" + this.tenantIdentifier + ".stage.monitor.centelon.com";
    }

    if (
      document.location.hostname ==
      this.tenantIdentifier + ".dev.originate.centelon.com"
    ) {
      url = "https://" + this.tenantIdentifier + ".dev.monitor.centelon.com";
    }
    if (
      document.location.hostname ==
      this.tenantIdentifier + ".qa.originate.centelon.com"
    ) {
      url = "https://" + this.tenantIdentifier + ".qa.monitor.centelon.com";
    }
    if (document.location.hostname == "originate.finnate.app") {
      url = "https://monitor.finnate.app";
    }

    if (
      document.location.hostname ==
        this.tenantIdentifier + ".pre.originate.finnate.app" ||
      document.location.hostname ==
        this.tenantIdentifier + ".pre.originate.aif.finnate.app"
    ) {
      url = "https://" + this.tenantIdentifier + ".pre.monitor.finnate.app";
    }

    if (
      document.location.hostname == "originate.finnate.app" ||
      document.location.hostname == "originate-aif.finnate.app"
    ) {
      url = "https://monitor.finnate.app";
    }

    this.monitorUrl = url;
  }

  goTo(data) {
    localStorage.setItem("activeListFormSideNavList", data.name);

    if (data.name != "Configuration" && data.name != "Entity") {
      this.dataSharingService.newSubPageNameValue(null);
      this.dataSharingService.subPageEntityIdValue(null);
      if (data.url) {
        this.route.navigate([data.url]);
        this.getSidebarHighlight(data.url);
      }
    }
  }

  openSubPage() {
    if (this.pageLoadedFromNav == "companies") {
      this.route.navigate(
        [`entity/viewcompany/detail/${btoa(this.subPageEntityId)}`],
        { state: { data: { customerId: this.subPageEntityId, edit: true } } }
      );
    }
    if (this.pageLoadedFromNav == "persons") {
      this.route.navigate(
        [`entity/viewperson/detail/${btoa(this.subPageEntityId)}`],
        {
          state: {
            data: {
              customerId: this.subPageEntityId,
              edit: true,
              companyId: this.companyIdOfPerson,
            },
          },
        }
      );
    }
    if (this.pageLoadedFromNav == "application") {
      this.dataSharingService.selectedApplicationData =
        this.dataSharingService.dealDataBackup;
      this.route.navigate(["/application-summary"]);
    }
  }

  routeTo(type) {
    if (type != "Notification Record") {
      this.dataSharingService.newSubPageNameValue(null);
      this.dataSharingService.subPageEntityIdValue(null);
    }
    switch (type) {
      case "company": {
        this.route.navigate(["entity/companies"]);
        this.getSidebarHighlight("/entity/companies");
        break;
      }
      case "person": {
        this.route.navigate(["entity/persons"]);
        this.getSidebarHighlight("/entity/persons");
        break;
      }
      case "Business process": {
        this.route.navigate(["../business-process"]);
        this.getSidebarHighlight("../business-process");
        break;
      }
      case "data-model": {
        this.route.navigate(["../data-model"]);
        this.getSidebarHighlight("../data-model");
        break;
      }
      case "entity": {
        this.route.navigate(["../entity-configuration"]);
        this.getSidebarHighlight("../entity-configuration");
        break;
      }
      case "label": {
        this.route.navigate(["../application-labels"]);
        this.getSidebarHighlight("../application-labels");
        break;
      }
      case "Template": {
        this.route.navigate(["/document-template"]);
        this.getSidebarHighlight("/document-template");
        break;
      }

      case "Utilities": {
        this.route.navigate([
          this.utilitiesService.newUtilitesUI$.value
            ? "/utilities"
            : "/utility",
        ]);
        this.getSidebarHighlight(
          // this.utilitiesService.newUtilitesUI$.value ? "/utilities" : "/utility"
          "/utility"
        );
        break;
      }

      case "Dashboard": {
        this.route.navigate(["/dashboard-configuration"]);
        this.getSidebarHighlight("/dashboard-configuration");
        break;
      }

      case "Auto Search": {
        this.route.navigate(["/auto-complete"]);
        this.getSidebarHighlight();
        break;
      }

      case "Notification Record": {
        this.route.navigate(["/planner/notifications"]);
        this.getSidebarHighlight();
        break;
      }

      case "reports": {
        this.route.navigate(["/reports"]);
        this.getSidebarHighlight("/reports");

        break;
      }

      case "Roles And Actions": {
        this.route.navigate(["/roles"]);
        this.getSidebarHighlight("/roles");

        break;
      }
      case "menu-details": {
        this.route.navigate(["/menu-details"]);
        this.getSidebarHighlight("/menu-details");

        break;
      }
      case "report-details": {
        this.route.navigate(["/report-details"]);
        this.getSidebarHighlight("/report-details");
        break;
      }
      case "copy-configuration": {
        this.route.navigate(["/configurations"]);
        break;
      }
      case "workspace": {
        this.route.navigate(["/workspace"]);
        this.getSidebarHighlight("/workspace");
        break;
      }
      case "workspace-configuration": {
        this.route.navigate(["/workspace-configuration"]);
        this.getSidebarHighlight("/workspace-configuration");
        break;
      }
    }
  }

  logout() {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("accessTokenExpiration");
    localStorage.removeItem("user");
    localStorage.removeItem("tenantIdentifier");
    localStorage.removeItem("activeListFormSideNavList");
    localStorage.removeItem("fileMaxSizeCache");
    localStorage.removeItem("closeUserToolTip");
    clearTimeout(this.notificationTimer);
    if (environment.useKeycloakLogin) {
      this.keyCloak.logout();
    } else {
      this.route.navigate(["/login"]);
    }
  }

  openChangePassword() {
    window.scroll(0, 0);
    const dialogRef = this.matDialog.open(ChangePasswordDialogComponent, {
      width: "40%",
    });
    dialogRef.afterClosed().subscribe((result) => {
      //nothing to add
    });
  }
  toggleDarkMode(event) {
    if (event == "dark-mode") {
      this.isDarkMode;
      this.themeService.update("dark-mode");
      this.themeMode = "dark-mode";
      localStorage.setItem("user-theme", "dark-mode");
    }
    if (event == "light-mode") {
      !this.isDarkMode;
      this.themeService.update("light-mode");
      this.themeMode = "light-mode";
      localStorage.setItem("user-theme", "light-mode");
    }
  }

  switchTheme(themeClass: string) {
    this.themeService.useNewTheme && themeClass == "light-mode"
      ? (themeClass = "light-mode-new")
      : this.themeService.useNewTheme && themeClass == "dark-mode"
      ? (themeClass = "dark-mode-new")
      : "";
    if (themeClass == "dark-mode") {
      this.isDarkMode;
      this.themeService.newThemeSwitch.next(false);
      this.themeService.updatetheme("dark-mode");
      this.themeMode = "dark-mode";
      localStorage.setItem("user-theme", "dark-mode");
    }
    if (themeClass == "light-mode") {
      !this.isDarkMode;
      this.themeService.newThemeSwitch.next(false);
      this.themeService.updatetheme("light-mode");
      this.themeMode = "light-mode";
      localStorage.setItem("user-theme", "light-mode");
    }
    if (themeClass == "light-purple-mode") {
      !this.isDarkMode;
      this.themeService.newThemeSwitch.next(false);
      this.themeService.updatetheme("light-purple-mode");
      this.themeMode = "light-purple-mode";
      localStorage.setItem("user-theme", "light-purple-mode");
    }
    if (themeClass == "light-mode-new") {
      !this.isDarkMode;
      this.themeService.newThemeSwitch.next(true);
      this.themeService.updatetheme("light-mode-new");
      this.themeMode = "light-mode-new";
      localStorage.setItem("user-theme", "light-mode-new");
    }
    if (themeClass == "dark-mode-new") {
      this.isDarkMode;
      this.themeService.newThemeSwitch.next(true);
      this.themeService.updatetheme("dark-mode-new");
      this.themeMode = "dark-mode-new";
      localStorage.setItem("user-theme", "dark-mode-new");
    }
  }

  switchToNewTheme(checked) {
    const oldToNewThemeMap = {
      "light-mode": "light-mode-new",
      "dark-mode": "dark-mode-new",
    };
    const newToOldThemeMap = {
      "light-mode-new": "light-mode",
      "dark-mode-new": "dark-mode",
    };
    checked
      ? (this.themeService.useNewTheme = true)
      : (this.themeService.useNewTheme = false);
    const currentTheme = localStorage.getItem("user-theme");
    const newTheme = checked
      ? oldToNewThemeMap[currentTheme]
      : newToOldThemeMap[currentTheme];
    this.switchTheme(newTheme);
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  startNotificationTimer() {
    this.notificationTimer = setTimeout(() => {
      this.getNotifications();
    }, 2 * 60 * 1000);
  }

  handleSessionExpiry() {
    this.customeKeycloakService.startRefreshTimer();
  }

  navigateToDetails(notif) {
    this.trigger.closeMenu();
    notif.readFlag = true;
    this.notificationService.notificationRead([notif]).subscribe(
      (resp) => {
        this.getNotifications();
        this.route.navigate([this.getRoute(notif)]);
      },
      (error) => {
        this.getNotifications();
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
      }
    );
  }

  getRoute(notification) {
    this.dataSharingService.selectedApplicationData = null;
    switch (notification.moduleName) {
      case "Document":
        return (
          "/application-summary/documents/" + btoa(notification?.moduleEntityId)
        );
      case "Deal":
      case "STAGE_SHARE":
      case "Comment":
      case "Deal Notifications":
        return (
          "/application-summary/details/" + btoa(notification?.moduleEntityId)
        );
      case "Score":
        return (
          "/application-summary/dealAnalysis/" +
          btoa(notification?.moduleEntityId)
        );
      case "Communication":
        return "planner/tasks";
      case "Business Process":
        return "/stage/" + btoa(notification?.moduleEntityId);
    }
  }

  getNotifications() {
    const readFlag = false; //to get unread notifications;
    const pageSize = 20;
    this.notificationService.getNotifications(readFlag, pageSize).subscribe(
      (resp: any) => {
        if (resp?.content) this.notifications = resp.content;
        this.totalUnreadCount = resp?.totalElements;
        this.notificationService.unreadNotificationsCount =
          this.totalUnreadCount;
        this.startNotificationTimer();
      },
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
      }
    );
  }

  closeMenu(trigger) {
    trigger.closeMenu();
  }
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  getConfigurationDetailsByIdentifier(identifier) {
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .subscribe((res: any) => {
        const originalRes = structuredClone(res);
        if (res?.configIdentifier == "TENANT_CONFIGURATION") {
          this.dataSharingService.setDashboardItems(res);
          this.dataSharingService.tenantConfigurationresponse = res;
          const applicationconfiguration = res.configDetails.findIndex(
            (item) => this.getPropertyName(item) == "applicationconfiguration"
          );
          this.investfactRedirectionDetails = res.configDetails[
            applicationconfiguration
          ]?.applicationconfiguration.find(
            (item) => item.component == "Investfact"
          );

          this.wfeRedirectionDetails = res.configDetails[
            applicationconfiguration
          ]?.applicationconfiguration.find(
            (item) => item.component == "Workflow Engine"
          );
          const monitorLabel = res.configDetails[
            applicationconfiguration
          ]?.applicationconfiguration.find(
            (item) => item.component == "Monitor"
          );
          if (monitorLabel?.value == true) {
            this.monitorHide = true;
          } else {
            this.monitorHide = false;
          }
          const TestLabel = res.configDetails[
            applicationconfiguration
          ]?.applicationconfiguration.find(
            (item) => item.component == "TestEnv"
          );
          this.showtest = TestLabel?.Name;
          if (TestLabel?.value == true) {
            this.showTestenv = true;
          } else {
            this.showTestenv = false;
          }
        } else if (res?.configIdentifier == "MENU_BAR") {
          this.menubarConfigDetails = Object.assign({}, res);
          this.menus = this.menubarConfigDetails?.configDetails?.slice();
          this.menus.forEach((menu: any) => {
            menu.rule = true;
            if (menu.rules !== null && menu.rules !== undefined) {
              const value = `${menu.rules["__topMenu__"]}`;
              const exper = formStringExpression(value, [""]);
              if (evalStringExpression(exper, this, [""])) {
                menu.rule = false;
              }
            }
            menu.subMenus.forEach((item: any) => {
              item.rule = true;
              if (item.rules !== null && item.rules !== undefined) {
                const value = `${item.rules["__subMenu__"]}`;
                const exper = formStringExpression(value, [""]);
                if (evalStringExpression(exper, this, [""])) {
                  item.rule = false;
                }
              }
            });
          });
        } else if (res?.configIdentifier == "REPORT_CONFIG") {
          this.reports = this.reportConfigDetails.configDetails;
          this.reports.forEach((item: any) => {
            item.rule = true;
            if (item.rules !== null && item.rules !== undefined) {
              const value = `${item.rules["__report__"]}`;
              const exper = formStringExpression(value, [""]);
              if (evalStringExpression(exper, this, [""])) {
                item.rule = false;
              }
            }
          });
        } else if (res?.configIdentifier == "SIDE_BAR") {
          this.dataSharingService.setSidebarItems(originalRes);
        }
      });
  }

  viewDetails(subMenu) {
    localStorage.setItem("subMenu", JSON.stringify(subMenu));
    this.route.navigate([`/menu-details/${btoa(subMenu.subMenu)}`], {
      state: { data: { subMenu: subMenu } },
    });
  }

  openMyMenu(menuTrigger: MatMenuTrigger) {
    setTimeout(() => {
      menuTrigger.openMenu();
    });
  }

  menuenter() {
    this.isMatMenuOpen = true;
  }

  getSidebarHighlight(url?) {
    const currentUrl = url ? url : this.route.url;
    const parts = currentUrl.split("/");
    const paramOne = parts[1];
    const paramTwo = parts[2];

    switch (paramOne) {
      case "dashboards": {
        this.pageLoadedFromNav = "Dashboard(Experimental)";
        this.breadcrumb = "dashboard";
        this.navigationurl = "dashboard";
        break;
      }
      case "application": {
        this.pageLoadedFromNav = "Deal";
        this.breadcrumb = "Deal";
        break;
      }
      case "application-summary": {
        this.pageLoadedFromNav = "Deal";
        this.breadcrumb = "Deal";
        break;
      }
      case "entity": {
        this.pageLoadedFromNav = "Entity";
        if (paramTwo == "companies") {
          this.pageLoadedFromNav = "Entity";
          this.breadcrumb = "Entity/Company";
          this.navigationurl = "company";
        } else if (paramTwo == "viewcompany") {
          this.pageLoadedFromNav = "Entity";
          this.breadcrumb = "Entity/Company";
          this.navigationurl = "company";
        } else {
          this.pageLoadedFromNav = "Entity";
          this.breadcrumb = "Entity/Person";
          this.navigationurl = "person";
        }
        break;
      }
      case "planner": {
        if (paramTwo == "notifications") {
          this.breadcrumb = "Planner";
          this.navigationurl = "Planner";
        } else {
          this.pageLoadedFromNav = "Planner";
          this.breadcrumb = "Planner";
          this.navigationurl = "Planner";
        }
        break;
      }
      case "home": {
        this.pageLoadedFromNav = "Bulk Stage Move";
        this.breadcrumb = "Bulk Stage Move";
        this.navigationurl = "Bulk Stage Move";
        break;
      }
      case "workspace": {
        this.pageLoadedFromNav = "Workspace";
        this.breadcrumb = "Workspace";
        this.navigationurl = "workspace";
        break;
      }
      case "data-model": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/data-model";
        this.navigationurl = "data-model";
        break;
      }
      case "entity-configuration": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Entity";
        this.navigationurl = "entity";
        break;
      }
      case "business-process": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Business Process";
        this.navigationurl = "Business process";
        break;
      }
      case "dashboard-configuration": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Dashboard Configuration";
        this.navigationurl = "Dashboard";
        break;
      }
      case "workspace-configuration": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Workspace Configuration";
        this.navigationurl = "workspace-configuration";
        break;
      }
      case "roles": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Roles And Actions";
        this.navigationurl = "Roles And Actions";
        break;
      }
      case "reports": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Reports";
        this.navigationurl = "reports";
        break;
      }
      case "utilities":
      case "utility": {
        this.pageLoadedFromNav = "Configuration";
        this.breadcrumb = "Configuration/Utililty";
        this.navigationurl = "Utilities";
        break;
      }
      case "menu-details": {
        this.breadcrumb = "Menu Details";
        this.navigationurl = "menu-details";
        break;
      }
      case "report-details": {
        this.breadcrumb = "Report Details";
        this.navigationurl = "report-details";
        break;
      }
    }
  }

  getResourceAsPerModule(moduelName) {
    const ModuleToResource = {
      Deal: this.DEAL_RESOURCE.Deal,
      Enitity: this.ENTITY_RESOURCE.Entity,
      "Dashboard(Experimental)": "",
    };
    return ModuleToResource[moduelName];
  }

  setUserList() {
    this.identityService.getAllUser().subscribe((res) => {
      const usersList = res;
      localStorage.setItem("userList", usersList);
      this.dataSharingService.setUserList(usersList);
      this.currentUserRoles = usersList.find(
        (item) => item.identifier == this.user
      ).roleMappings;
    });
  }
}
