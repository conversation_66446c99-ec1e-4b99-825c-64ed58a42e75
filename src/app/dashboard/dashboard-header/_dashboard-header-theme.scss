@use 'sass:map';
@use '@angular/material' as mat;

@mixin color($theme) {
  $color-config: mat.get-color-config($theme);
  $primary-palette: map.get($color-config, 'primary');
  $primary-hue : 400;
  $primary-color: mat.get-color-from-palette($primary-palette, $primary-hue);
  $primary-contrast: mat.get-contrast-color-from-palette($primary-palette, 400);

  .activeColorForSelectedlist {
    //update class name later
    background: mat.get-color-from-palette($primary-palette, 400);
    color: $primary-contrast !important;
  }

  .tooltip-content {
    background-color: mat.get-color-from-palette($primary-palette, 400);
    color: $primary-contrast;
  }

  .tooltip-content button {
    background-color: $primary-contrast;
    color: $primary-color;
  }

  .tooltip-arrow {
    border-bottom: 10px solid $primary-color;
  }

  .help-icon {
    color: var(--mat-icon-default-color);
  }
}

@mixin typography($theme) {
  $typography-config: mat.get-typography-config($theme);
}

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);

  @if $color-config !=null {
    @include color($theme);
  }

  $typography-config: mat.get-typography-config($theme);

  @if $typography-config !=null {
    @include typography($theme);
  }
}
