import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { UploadDocumentDialogComponent } from 'src/app/application-summary/dialogs/upload-document-dialog/upload-document-dialog.component';
import { DataSharingService } from 'src/app/common/dataSharing.service';

@Component({
  selector: 'app-save-search',
  templateUrl: './save-search.component.html',
  styleUrls: ['./save-search.component.scss']
})
export class SaveSearchComponent implements OnInit {
  saveas:UntypedFormGroup;

  
  constructor(@Inject(MAT_DIALOG_DATA) public dialogData,
   public dialogRef: MatDialogRef<SaveSearchComponent>,
   private dataSharingService: DataSharingService,) { }

  ngOnInit(): void {
    this.saveas = new UntypedFormGroup({
      searchname: new UntypedFormControl("", Validators.required,)

    });
  }

  cancel() {
    this.dialogRef.close(false)
  }
  submit(){
    
    this.saveas.markAllAsTouched()
    if (this.saveas.invalid) {
      return
    }

    
  }


}
