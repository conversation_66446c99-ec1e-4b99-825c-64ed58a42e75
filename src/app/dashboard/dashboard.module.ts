import { DashboardHeaderComponent } from "./dashboard-header/dashboard-header.component";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DashboardComponent } from "./dashboard.component";
import { DashboardRoutes } from "./dashboard.routing";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { HomeComponent } from "../home/<USER>";
import { ReportComponent } from "../report/report.component";
import { ApplicationModule } from "../application/application.module";
import { CustomSearchComponent } from "./custom-search/custom-search.component";
import { BusinessProcessConfigurationModule } from "../settings/businessProcess-configuration/businessProcess-configuration.module";
import { AssetModule } from "../settings/assets/assets.module";
import { ConfigDashboardComponent } from "../config-dashboard-component/config-dashboard.component";
import { HistoryRecordComponent } from "../history-record/asset-history/history-record.component";
import { MenuDetailsComponent } from "./menu-details/menu-details.component";
import { ReportDetailsComponent } from "./report-details/report-details.component";
import { UpcomnigStagesComponent } from "../application-summary/upcomnig-stages/upcomnig-stages.component";
import { BussinessProcessConfigModule } from "../settings/bussiness-process-config/bussiness-process-config.module";
import { NotificationRecordComponent } from "../notification-record/notification-record.component";
import { SidebarNavComponent } from "../sidebar-nav/sidebar-nav.component";

@NgModule({
  imports: [
    CommonModule,
    DashboardRoutes,
    SharedModuleModule,
    ApplicationModule,
    BusinessProcessConfigurationModule,
    BussinessProcessConfigModule,
    AssetModule,
    SidebarNavComponent,
  ],
  declarations: [
    DashboardComponent,
    DashboardHeaderComponent,
    HomeComponent,
    ReportComponent,
    ConfigDashboardComponent,
    CustomSearchComponent,
    HistoryRecordComponent,
    UpcomnigStagesComponent,
    MenuDetailsComponent,
    ReportDetailsComponent,
    NotificationRecordComponent,
  ],
})
export class DashboardModule {}
