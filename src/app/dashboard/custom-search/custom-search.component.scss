.number-width {
   width: 30%;
}

.filter-button{

   min-width: 30px !important;

}

.width25{

   width: 25%;

}

.pointer{

   cursor: pointer !important;
}

.expansion-width-divider{
   width: 97%;
   margin-left: 17px;
}

.columnNameCss {
   text-transform: capitalize;
 }
 .CustomMargin{
   margin-top: 1px !important;
 }
 .bulkMovementList{
   max-height: 120px !important;
   overflow: auto;
 }
 .customSearchName{
   margin-bottom: -2% !important; 
   width: 23.5% !important;
 }
 .eligibleRecords{
   font-size: 15px !important;
    margin-top: 5px !important;
 }
 .ml-20{
   margin-left: 20px !important;
 }
 .search-mat-card-actions{
   margin-left: 0.1% !important;
   display: flex;
   justify-content: flex-end;
 }
 .customshowTableSpinner{
   max-height: 900px;
   overflow-y: auto; 
   margin-top: -2% !important;
 }
 
 .noData{
   text-align: center;
   box-shadow: unset;
 }

 .wd25{
  margin-top: 2vh;
 }
 .mb2{
  margin-bottom: 1.5% !important;
 }
 .ml1{
  margin-left: 1.5% !important;
 }

 .bttnCenter{
  margin: auto;
 }