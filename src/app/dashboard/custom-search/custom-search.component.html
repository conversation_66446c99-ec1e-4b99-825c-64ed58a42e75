<div class="dashboard-wrapper">

 
  <div class="CustomMargin">
    <mat-accordion>

      <mat-expansion-panel class="expansion-width CustomMargin" [(expanded)]="panelOpenState" >
        <mat-expansion-panel-header>
          <mat-panel-title class="bold">
            Search Details
          </mat-panel-title>
        </mat-expansion-panel-header>



        <div>
          <div fxLayout="row wrap" >

            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb2">
              <mat-form-field appearance="outline" class="customSearchName">
                <mat-label>Search Name</mat-label>
                <input matInput [formControl]="searchName">
                <mat-error *ngIf="searchName.errors?.required">
                  {{"label.materror.Searchname"|literal}}
             
          </mat-error>
              </mat-form-field>
            </div>


            <div fxFlex="24%" fxFlex.md="24%" fxFlex.xs="25%" fxFlex.sm="25%" class="wd25">
              <mat-form-field appearance="outline" class="width-100">
                <mat-label>Select Field</mat-label>
                <mat-select [(ngModel)]="queryField" (selectionChange)="querySelected($event.value)"
                  [formControl]="queryFieldForm" [disabled]="editQueryOption">
                  <mat-option>
                    <ngx-mat-select-search  placeholderLabel="Search Data Model Type" 
                  noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterAsset($event)" [ngModelOptions]="{standalone: true}"
                  ></ngx-mat-select-search>
                </mat-option>
                  <mat-option *ngFor="let item of getAssetList(finalAssetItems)" [value]="item" >
                    {{item.displayProperty.displayName}} <span *ngIf="item.type === 'assetItem'" > (deal) </span>
                    <span *ngIf="item.type === 'entity'" > (entity - {{item.entityNameTitle}}) </span>
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="queryFieldForm.errors?.required">
                  {{"label.materror.Searchfield"|literal}}
          </mat-error>
              </mat-form-field>
            </div>

            <div fxFlex="30%" fxFlex.md="24%" fxFlex.xs="25%" fxFlex.sm="20%"
              *ngIf="selectedParamArr?.length >0 && (isDateField || isNumberField || isTextField || isStageSelected) ">
              <mat-form-field appearance="outline" class="width-100">
                <mat-label>Operator</mat-label>
                <mat-select [(ngModel)]="selectedOperator" [formControl]="selectedOperatorForm">
                  <mat-option *ngFor="let data of operatorList" [value]="data">{{data}}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div fxFlex="55%" fxFlex.md="55%" fxFlex.xs="50%" fxFlex.sm="50%" *ngIf="isTextField">

              <mat-form-field appearance="outline" class="number-width">
                <mat-label>Options</mat-label>
                <mat-select [(ngModel)]="selectedOptions">
                  <mat-option *ngFor="let data of optionsList" [value]="data">{{data}}</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="number-width top-bar-buttons" >
                <mat-label>Text</mat-label>
                <input matInput [(ngModel)]="stringValue" placeholder="text" [formControl]="stringValueForm">
              </mat-form-field>

          


            </div>

            <div fxFlex="24%" fxFlex.md="35%" fxFlex.xs="35%" fxFlex.sm="25%" *ngIf="isStageSelected" class="ml1 wd25">

              <mat-form-field appearance="outline">
                <mat-label>Stages</mat-label>
                <mat-select [(ngModel)]="stageValue" [formControl]="stageValueForm">
                  <mat-option *ngFor="let item of  stageList" [value]="item">
                    {{item}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxFlex="45%" fxFlex.md="60%" fxFlex.xs="60%" fxFlex.sm="60%" *ngIf="isStageSelected" class="wd25">
              <mat-form-field appearance="outline" *ngIf="stageValue">
                <mat-label>Status</mat-label>
                <mat-select [(ngModel)]="loanAccValue" >
                    <mat-option *ngFor="let status of  currentStatus" [value]="status" >
                      {{status}}
                    </mat-option>
                </mat-select>
              </mat-form-field>


            </div>


            <div fxFlex="55%" fxFlex.md="55%" fxFlex.xs="50%" fxFlex.sm="50%" *ngIf="isNumberField">

              <mat-form-field appearance="outline" class="number-width">
                <mat-label>Lesser than</mat-label>
                <input matInput type="number" [(ngModel)]="lesserThanValue" [formControl]="lesserThanValueForm">
              </mat-form-field>

              <mat-form-field appearance="outline"  class="number-width top-bar-buttons">
                <mat-label>Greater than</mat-label>
                <input matInput type="number" [(ngModel)]="greaterThanValue" [formControl]="greaterThanValueForm">
              </mat-form-field>

              <mat-form-field appearance="outline"  class="number-width top-bar-buttons">
                <mat-label>Equal to </mat-label>
                <input matInput type="number" [(ngModel)]="equalToValue" [formControl]="equalToValueForm">
              </mat-form-field>


            </div>

            <div fxFlex="55%" fxFlex.md="55%" fxFlex.xs="50%" fxFlex.sm="50%" *ngIf="isDateField">

              <mat-form-field appearance="outline">
                <mat-label>From</mat-label>
                <input matInput [matDatepicker]="datePickerFrom" [(ngModel)]="fromDate" [formControl]="fromDateForm">
                <mat-datepicker-toggle matSuffix [for]="datePickerFrom"></mat-datepicker-toggle>
                <mat-datepicker #datePickerFrom></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline" class="top-bar-buttons">
                <mat-label>To</mat-label>
                <input matInput [matDatepicker]="datePickerTo" [(ngModel)]="toDate" [formControl]="toDateForm">
                <mat-datepicker-toggle matSuffix [for]="datePickerTo"></mat-datepicker-toggle>
                <mat-datepicker #datePickerTo></mat-datepicker>
              </mat-form-field>

            </div>
          </div>
          <mat-card-actions align="end" class="margin-top-1" *ifHasPermission="DASHBOARD_RESOURCE.Dashboard; scope:'CHANGE'">
            <button mat-button (click)='addFilter()'>ADD</button>
            <button mat-button (click)='resetSearchFilter()'>CLEAR</button>
          </mat-card-actions>

          <mat-card appearance="outlined" *ngIf="selectedParamArr?.length > 0" class="mat-card-top-border margin-top-1" >

            <mat-chip-grid #chipList *ngIf="!queryTextFeild">
              <span *ngFor="let data of selectedParamArr">
                <span *ngIf="data.value !== 'AND' && data.value !== 'OR' && data.value !== '(' && data.value !== ')' ">
                  <span >
                    <mat-chip-row [removable]="removeTag" (removed)="remove(data)" >
                      {{data.value}}
                      <mat-icon matChipRemove>cancel</mat-icon>
                    </mat-chip-row>
                  </span>
                </span>
                <span *ngIf="data.value === 'AND' || data.value === 'OR' || data.value === '(' || data.value === ')' ">
                  <mat-chip-row>
                    {{data.value}}
                  </mat-chip-row>
                </span>
               
              </span>
              <input disabled [matChipInputFor]="chipList">
            </mat-chip-grid>
            <mat-form-field class="width-80" *ngIf="queryTextFeild">
              <textarea matInput [(ngModel)]="queryText"></textarea>
            </mat-form-field>
         
            <mat-card-actions class="bttnCenter" *ngIf="selectedParamArr?.length >0">
              <button mat-button (click)="previewFilter('queryfilter')">PREVIEW</button>
              <button mat-button (click)='saveSearch()' [disabled]="searchName.invalid">SAVE</button>
              <button mat-button (click)='reset()'>RESET</button>
              <button mat-button  *ngIf="!queryTextFeild" (click)="editQueryDialog()" >Edit</button>

            </mat-card-actions>
          </mat-card>

        </div>

      </mat-expansion-panel>

      <mat-expansion-panel class="expansion-width"   *ngIf="savedQueries?.length>0">
        <mat-expansion-panel-header>
          <mat-panel-title class="bold">
            Saved Searches
          </mat-panel-title>
        </mat-expansion-panel-header>

        <mat-card appearance="outlined" class="bulkMovementList">

          <mat-card-content>

            <mat-chip-grid #chipList>
              <span *ngFor="let data of savedQueries">
                <mat-chip-row class="pointer" (click)="filter(data)" (removed)="removeSavedQuery(data)">
                    <div matLine>{{data.name}}</div>
                      <mat-icon matChipRemove *ifHasPermission="DASHBOARD_RESOURCE.Dashboard; scope:'DELETE'">cancel</mat-icon>
                </mat-chip-row>
              </span>
            </mat-chip-grid>

          </mat-card-content>
       

        </mat-card>
     

        <mat-card appearance="outlined" *ngIf="queryView" class="mat-card-top-border margin-top-2 button-row" >
        
         <mat-card-content>
         <span class="bold"> 
          {{queryView.name}} 
         </span>
         <br> Selected Query :{{queryView.query}}
          </mat-card-content>
         
        
        </mat-card>

      </mat-expansion-panel>

      <mat-expansion-panel class="expansion-width"  *ngIf="bulkMovementList?.length>0">
        <mat-expansion-panel-header>
          <mat-panel-title class="bold">
            Moved Stages
          </mat-panel-title>
        </mat-expansion-panel-header>

        <mat-card appearance="outlined" class="bulkMovementList">

          <mat-card-content>

            <mat-chip-grid #chipList>
              <span *ngFor="let data of bulkMovementList">
                <mat-chip-row class="pointer" (click)="filterBulkmovementList(data)">
                  {{data.dashboardQuery.name}}
                </mat-chip-row>
              </span>
            </mat-chip-grid>
            
          </mat-card-content>
       

        </mat-card>
       
        <mat-card appearance="outlined" *ngIf="bulkView" class="mat-card-top-border margin-top-2 button-row " >
          
          <div *ngIf="showCountSpinner" class="mt-5">
            <mat-spinner [diameter]=35  mode="indeterminate" ></mat-spinner>
          </div>
  
         <mat-card-content *ngIf="!showCountSpinner">

          <div class="bold"> 
            {{bulkView.name}} 
           </div>
           
          <mat-chip-row class="eligibleRecords" >Eligible Records : {{bulkView.eligible}}, Success : {{bulkView.success}}, Failed : {{bulkView.failed}}, In Progress : {{bulkView.inprogress?bulkView.inprogress:0 }}</mat-chip-row>
        
       <br>
       <div class="mt-10">

        <button class="green"  mat-raised-button *ngIf="!showQuery" (click)="showQuery = !showQuery" >SHOW QUERY</button>
        <button class="green" mat-raised-button *ngIf="showQuery" (click)="showQuery = !showQuery" >HIDE QUERY</button>
        <button class="green ml-20"  mat-raised-button (click)="showBulkMovementDetails(bulkView)" *ngIf="bulkView.eligible !== 0" >SHOW DETAILS</button>
        <button  mat-raised-button (click)="onPageRefresh(bulkView)" type="button" class="blue ml-5">
          <mat-icon>refresh</mat-icon>
        </button>

       </div>

         <br> 
         <div *ngIf="showQuery"  class="mt-10">
          Selected query : {{bulkView.query}}
         </div>
         
        
         </mat-card-content>
         
        
        </mat-card>

      </mat-expansion-panel>
  </mat-accordion>
  </div>



  <div fxLayout="row wrap" class="margin-top-2" >

  
    <mat-card-actions fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"  class="search-mat-card-actions mb2"  >
     
      <span *ngIf="count>1"> 
<span *ngIf="!noData">
        <mat-chip-row *ngIf="!viewStageStatus" class="mt-5px">Eligible Records : {{totalCountOflength}}</mat-chip-row>
      </span>
        <mat-chip-row *ngIf="viewStageStatus" class="mt-5px">Eligible Records : {{totalCountOflength}}, Success : {{successCount}}, Failed : {{failedCount}}</mat-chip-row>
        
      </span>

      

      <span *ifHasPermission="DASHBOARD_RESOURCE.Bulk_Stage_Movement; scope:'CHANGE'">
        <button *ngIf="queryData?.length>0 && count>1 && stagename !== 'Loan Agreement'&& stagename !== ''"  mat-raised-button class="green" (click)="changeStage()">
          MOVE STAGE
        </button>
      </span>

        <button *ngIf="queryData?.length>0"   mat-icon-button class="pointer green margin-right-5" (click)="exportXls()">
          <mat-icon>download</mat-icon>
        </button>
    </mat-card-actions>

 

   <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"  class="customshowTableSpinner"  >
    <div class="dataTableCss mat-table-wrap-text" [style.overflow-x]="showTableSpinner  ? 'auto': 'scroll !important'" >
      <div  [style.width]=" lengthOfcols >= 5 ? 'max-content' : ''" >
  <table  class="width-100"  mat-table [dataSource]="dataSource " matSort (matSortChange)="sortData($event)"
    [matSortDirection]="sortDirection" >


    <ng-container matColumnDef="dealIdentifier" sticky>
      <th mat-sort-header mat-header-cell *matHeaderCellDef class="  w-22 width-250" > Description </th>
      <td mat-cell class="pointer " *matCellDef="let row">
        <span (click)="navigateToSummaryPage(row)" class="customDescription">
          <p [matTooltip]="row?.dealIdentifier" class="hyperlinkColor">
            {{stringWithEllipsis(row?.dealIdentifier)}}</p>

        </span>
      </td>
    </ng-container>
     <ng-container matColumnDef="currentStageName">
      <th mat-header-cell *matHeaderCellDef class="  w-22 width-250"> Stage </th>
      <td mat-cell *matCellDef="let row">
        {{row?.currentStageName}}
      </td>
    </ng-container>

    <ng-container *ngFor="let col of tableColumn; let i = index" [matColumnDef]="col[getPropertyName(col)].name" >
      <th mat-header-cell  *matHeaderCellDef  class="columnNameCss"  [style.width] = "col[getPropertyName(col)].inputType === 'Action' ? '80px' : lengthOfcols >= 5 ? '250px' : (100/lengthOfcols) +'%' "  >
        <span
          *ngIf="col[getPropertyName(col)].inputType !== 'Action'">
          <ng-container *ngIf="col[getPropertyName(col)].inputType !== 'Currency'">{{ col[getPropertyName(col)].displayProperty.displayName }}</ng-container>
          <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Currency'">{{ col[getPropertyName(col)].displayProperty.displayName }} in {{selectedCurrency}}</ng-container>
        </span>
       
      </th>


      <td mat-cell *matCellDef="let element; let i = index" [matTooltip]="getTooltip(element,col)" 
      matTooltipShowDelay="1500" tooltip>
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Text'">
          {{ getValue(element,getPropertyName(col))}}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Long Text'">
          {{stringWithEllipsis(getValue(element,getPropertyName(col))) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container> 
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Number'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Email'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Boolean'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Number with decimal'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Picklist'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Alphanumeric'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Document'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Date'">
          <span *ngIf="getValue(element,getPropertyName(col))!==''">
           {{ getValue(element,getPropertyName(col))| date }}
           <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
         </span>
         <span *ngIf="getValue(element,getPropertyName(col))===''">
           {{ getValue(element,getPropertyName(col)) }}
           <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
         </span>
         </ng-container>
       
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Searchable picklist'">
          {{ (getValue(element,getPropertyName(col))).name }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Multiple picklist'">
          {{ getList(getValue(element,getPropertyName(col)))}}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Multiple Static Picklist'">
          {{ getValue(element,getPropertyName(col)) }}
          <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>
        <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Currency'">
          <span *ngIf="getValue(element,getPropertyName(col))">
            {{this.getCurrencySymbol(col[getPropertyName(col)].displayProperty.defaultValues)}}</span>&nbsp; {{ getValue(element,getPropertyName(col))| currency : col[getPropertyName(col)].displayProperty.defaultValues : '' }}
            <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
        </ng-container>

      </td>
    </ng-container>
    <ng-container matColumnDef="status" stickyEnd>
      <th  mat-header-cell *matHeaderCellDef class="  w-22 width-160" > Status </th>
      <td mat-cell class="pointer " *matCellDef="let row">
            {{row?.currentStatus}}
      </td>
    </ng-container>
    <ng-container matColumnDef="createdDate" stickyEnd>
      <th  mat-header-cell *matHeaderCellDef class="  w-22 width-160" > Created Date </th>
      <td mat-cell class="pointer " *matCellDef="let row">
            {{row?.createdDate | date}}
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>


  </table>
      </div>
      <div  class="no-records-found mt-1 mb-2" *ngIf="noData && queryData">
        <!-- <mat-card class="noData"> No data available</mat-card> -->
      </div>
</div>
   </div>  
  </div> 

  <div *ngIf="showTableSpinner" class="mt-5">
    <mat-spinner [diameter]=65  mode="indeterminate" ></mat-spinner>
  </div>

 


  <mat-paginator *ngIf="!noData && queryData && !showTableSpinner" [length]="totalCountOflength" class="" [pageSize]="pageSize" showFirstLastButtons="true" [pageIndex]="pageIndex"
  [pageSizeOptions]="[10, 25,50, 100, 500, 1000]" (page)="onPaginationChanged($event)"></mat-paginator>

</div>