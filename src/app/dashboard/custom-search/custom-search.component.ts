import { Params, Router } from "@angular/router";
import { DealService } from "src/app/shared-service/deal.service";
//import { SaveSearchComponent } from './../save-search/save-search.component';
import { filter, takeUntil, map, sample } from "rxjs/operators";
import {
  Component,
  Input,
  OnInit,
  ViewChild,
  AfterViewInit,
} from "@angular/core";
import {
  FormGroup,
  UntypedFormControl,
  UntypedFormBuilder,
  UntypedFormArray,
  Validators,
} from "@angular/forms";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { Subject } from "rxjs/internal/Subject";
import { MatDialog } from "@angular/material/dialog";
import { MatTableDataSource } from "@angular/material/table";
import { ToasterService } from "src/app/common/toaster.service";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort, Sort } from "@angular/material/sort";
import { LiveAnnouncer } from "@angular/cdk/a11y";
import * as xlsx from "xlsx";
import { ExcelService } from "../../shared-service/excel-service.service";
import { BulkMovementStageDialogComponent } from "../../dialogs/bulk-movement-stage-dialog/bulk-movement-stage-dialog.component";
import { BulkMovementDetailsDialogComponent } from "../../dialogs/bulk-movement-details-dialog/bulk-movement-details-dialog.component";
import { AdvanceSearchEditQueryDialogComponent } from "../../dialogs/advance-search-dialog/advance-search-edit-query-dialog.component";
import { BulkMovementChangeStageDialogComponent } from "../../dialogs/bulk-movement-change-stage-dialog/bulk-movement-change-stage-dialog.component";
import { MatSlideToggleChange } from "@angular/material/slide-toggle";
import { CurrencyUnitService } from "../../shared-service/currency-unit.service";
import { DatePipe } from "@angular/common";
import { SelectorContext } from "@angular/compiler";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { ConfigurationResources } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ThemeService } from "src/app/theme.service";
import { EntityService } from "src/app/shared-service/entity.service";

@Component({
  selector: "app-custom-search",
  templateUrl: "./custom-search.component.html",
  styleUrls: ["./custom-search.component.scss"],
})
export class CustomSearchComponent implements OnInit {
  @Input() dealListFromParent: any;
  businessProcessList: any = [];
  businessProcessStagesWithDeals: any[] = [];
  data: any = [];
  ProgramPoint = 0;
  disable = 0;
  rural = 0;
  data6: any;
  selectedFilter = "";
  searchKey: any = "";
  chipListBox: any = true;
  searchArr: any = [];
  isAssetKey: any = false;
  dealsAsPerBusinessProcess: any = [];
  selectedBusinessProcess = "";
  selectedDealList = "searchResult.length";
  panelOpenState = true;
  totalDealsNumber: any = 0;
  form = this._fb.group({
    lessons: this._fb.array([]),
  });

  selectedBusinessProcessDetails: any;

  assestitems: any = [];
  private unsubscribe$ = new Subject();
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  dataSource: any;
  listOfDeals: any[];
  stageList: any[];
  uniqueInputTypeList: any;
  operatorList: any[] = ["AND", "OR"];
  optionsList: any[] = ["Equal to", "Contains"];
  selectedOptions: any;
  isTextField: boolean;
  isDateField: boolean;
  isNumberField: boolean;
  selectedOperator: any;
  filteredQuery: any;
  stringValue: any;
  fromDate: any;
  toDate: any;
  lesserThanValue: any;
  greaterThanValue: any;
  equalToValue: any;
  selectedParamArr: any;
  queryField: any;

  fromDateForm = new UntypedFormControl();
  toDateForm = new UntypedFormControl();
  lesserThanValueForm = new UntypedFormControl();
  greaterThanValueForm = new UntypedFormControl();
  equalToValueForm = new UntypedFormControl();
  queryFieldForm = new UntypedFormControl("", [Validators.required]);
  stringValueForm = new UntypedFormControl();
  searchName = new UntypedFormControl("", [Validators.required]);
  stageValueForm = new UntypedFormControl();
  removeTag = true;
  isStageSelected: boolean;
  stageValue: any;
  isEntitySelected: any;
  entityValue: any;
  equalDateForm = new UntypedFormControl();
  equalDate: any;
  queryDate: any;
  queryData: any[];
  entityList: any[];
  savedQueriesState = false;
  queryDataSource: any;
  queryView: any;
  downloadData: any[];
  sortingData: any[];
  excelList: any = [];
  showTableSpinner: boolean;
  noData: boolean;
  totalCountOflength: any = 0;
  count: any = 0;
  pageIndex: any = 0;
  pageSize: any = 25;
  filterQuery: any;
  bulkMovementButtonView: boolean;
  selectedOperatorForm = new UntypedFormControl();
  finalAssetItems: any;
  bulkMovementId: any;
  successCount: any;
  failedCount: any;
  viewStageStatus: boolean;
  bulkMovementList: any[];
  bulkView: any;
  showQuery: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  savedQueries: any;
  defaultPagination: {};
  totalData: any;
  sortingDataSource: any;
  queryTextFeild: boolean;
  queryText: any;
  editQueryOption: boolean;
  stagename: any = "";
  sortBy: any = "desc";
  sortingKey: any = "dealIdentifier";
  showCountSpinner: boolean;
  sortquery: any;
  currentStageName: any;
  query: any;
  loanAccValue: any;
  currentStatus: any[] = ["In progress", "Rejected"];
  selectedCurrency: string;
  selectedBusinessProcessId: any;
  lastStage: any;
  disableLastStage = false;
  DealAssest: any;
  tableColumn: any;
  lengthOfcols: number;
  isShared: boolean;
  displayedColumns: string[] = [];
  searchedAsset: any;
  JsonData: any;

  get DASHBOARD_RESOURCE() {
    return ConfigurationResources.Dashboard_Def;
  }

  constructor(
    private businessProcessService: BusinessProcessService,
    private errorService: ErrorService,
    private entityService: EntityService,
    private dialog: MatDialog,
    public router: Router,
    private route: Router,
    private notificationMessage: ToasterService,
    private dataSharingService: DataSharingService,
    private _fb: UntypedFormBuilder,
    private currencyFormatService: CurrencyFormatService,
    private dealService: DealService,
    private _liveAnnouncer: LiveAnnouncer,
    private currencyUnitService: CurrencyUnitService,
    public datepipe: DatePipe,
    protected themeService: ThemeService
  ) {}

  get lessons() {
    return this.form.controls["lessons"] as UntypedFormArray;
  }

  addlesson(event: any) {
    const lessonForm = this._fb.group({
      unit: ["", Validators.required],
      FieldName: [event, Validators.required],
      action: ["", Validators.required],
      operator: ["", Validators.required],
      value: ["", Validators.required],
    });

    this.lessons.push(lessonForm);
    this.searchArr = [...this.lessons.value];
  }

  navigateToSummaryPage(data) {
    this.dataSharingService.selectedApplicationData = undefined;
    this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);

    this.router.navigate(["/application-summary/details/" + btoa(data?.id)]);
  }

  ngOnInit(): void {
    this.selectedParamArr = [];
    this.pageSize = this.dataSharingService.customSearchPageSize;
    this.pageIndex = this.dataSharingService.customSearchPageIndex;
    this.count = 0;
    this.selectedCurrency = localStorage.getItem("currency");
    this.dataSharingService.wokflowIdEmmision.subscribe((response: any) => {
      this.count = 0;
      this.selectedCurrency = localStorage.getItem("currency");
      this.getBusinessProcessDetails();
      this.fetchQueries();
      this.fetchBulkMovement();
    });

    this.lessons.valueChanges.subscribe((value) => {
      this.searchArr = [...this.lessons.value];
    });

    this.getBusinessProcessDetails();
    this.selectedOperator = "AND";

    this.fetchQueries();
    this.fetchBulkMovement();
  }

  async getBusinessProcessDetails() {
    this.clearAllField();

    this.showTableSpinner = true;
    this.searchName.setValue("");
    this.selectedParamArr = [];
    this.selectedParamArr.push({
      value:
        "businessProcessId:" +
        this.dataSharingService.selectedBusinessProcessIdCustom,
    });
    await this.previewFilter(null);
    this.businessProcessService
      .getBusinessProcessById(
        this.dataSharingService.selectedBusinessProcessIdCustom
      )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (data) => {
          this.selectedBusinessProcessDetails = data;
          this.displayedColumns = [];
          this.DealAssest = data.assetItems;
          this.tableColumn = this.DealAssest.filter(
            (item) =>
              item[this.getPropertyName(item)].displayProperty?.isForListView
          );
          this.displayedColumns.push("dealIdentifier");
          this.displayedColumns.push("currentStageName");

          this.displayedColumns = this.displayedColumns.concat(
            this.tableColumn.map((item) => this.getPropertyName(item))
          );
          const index = this.displayedColumns.indexOf("Team Lead");
          if (index !== -1) {
            this.displayedColumns[index] = "teamLead";
          }
          this.displayedColumns.push("status");
          this.displayedColumns.push("createdDate");
          if (this.displayedColumns) {
            this.lengthOfcols = this.displayedColumns?.length - 1;
          }
          this.lastStage =
            data.businessProcessStageList[
              data.businessProcessStageList?.length - 1
            ].name;
          const assest = data.assetItems;
          const newArr = [];
          assest.forEach((element) => {
            const [key, value] = Object.entries(element);
            let assetItmesData: any;
            assetItmesData = key[1];
            assetItmesData.name = key[0];
            assetItmesData.type = "assetItem";
            if (assetItmesData.inputType !== "Table") {
              newArr.push(key[1]);
            }
          });
          this.assestitems = newArr;
          this.generateStageList(this.assestitems);
          this.getEntityListApi();

          if (this.stageList?.length > 0) {
            this.resetfilter();
            this.clearAllField();
          }
          let createdDate = {};
          createdDate = {
            displayProperty: {
              displayName: "Created Date",
              name: "createdDate",
            },
            inputType: "Date",
            type: "Date",
            name: "createdDate",
          };
          this.assestitems.push(createdDate);
          let stageAvgScore = {};
          stageAvgScore = {
            displayProperty: {
              displayName: "Average Score",
              name: "stageAverageScore",
            },
            inputType: "Number",
            type: "Number",
            name: "stageAverageScore",
          };
          this.assestitems.push(stageAvgScore);
          this.finalAssetItems = [
            {
              displayProperty: {
                displayName: "Stage name",
                name: "stageName",
              },
              inputType: "stage",
              type: "stage",
            },
          ];
          this.uniqueInputTypeList = [
            ...new Set(this.assestitems.map((item) => item.inputType)),
          ];
        },
        (error) => {
          this.showTableSpinner = false;
        }
      );
  }

  generateStageList(data) {
    this.stageList = [];
    data.forEach((ele) => {
      ele.stages.forEach((ele1) => {
        if (ele1.stageName !== "QDE") {
          const queryName = this.stageList.push({ stageName: ele1.stageName });
        }
      });
    });
    this.stageList = [...new Set(this.stageList.map((item) => item.stageName))];
  }

  saveSearch() {
    this.queryView = null;
    let filterQuery = "";
    if (this.queryText) {
      filterQuery = this.queryText;
    } else {
      for (let i = 0; i < this.selectedParamArr.length; i++) {
        filterQuery += this.selectedParamArr[i].value + " ";
      }
    }
    const payload = {
      name: this.searchName.value,
    };
    this.dealService.saveQuery(filterQuery, payload).subscribe((res: any) => {
      this.fetchQueries();
      this.notificationMessage.success(JsonData["label.success.SaveQuery"]);
      this.selectedParamArr = [];
      this.queryData = [];
      this.queryView = null;
      this.resetfilter();
      this.searchName.markAsUntouched();
      this.queryFieldForm.markAsUntouched();
      this.showTableSpinner = false;
      this.noData = true;
    });
  }

  resetfilter() {
    this.selectedParamArr = [];
    const stageData = [
      {
        displayProperty: {
          displayName: "Stage name",
          name: "stageName",
        },
        inputType: "stage",
        type: "stage",
      },
    ];
    this.finalAssetItems = [];
    this.selectedParamArr.length > 3
      ? (this.finalAssetItems = this.assestitems)
      : (this.finalAssetItems = stageData);
    this.queryTextFeild = false;
    this.queryText = "";
    this.editQueryOption = false;
    this.disableLastStage = false;
    this.loanAccValue = "";
  }

  reset() {
    this.selectedParamArr = [];
    const stageData = [
      {
        displayProperty: {
          displayName: "Stage name",
          name: "stageName",
        },
        inputType: "stage",
        type: "stage",
      },
    ];
    this.finalAssetItems = [];
    this.selectedParamArr.length > 3
      ? (this.finalAssetItems = this.assestitems)
      : (this.finalAssetItems = stageData);
    this.queryTextFeild = false;
    this.queryText = "";
    this.editQueryOption = false;
    this.queryData = [];
    this.showTableSpinner = false;
    this.noData = true;
    this.disableLastStage = false;
    this.dataSource = [];
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 50);
  }

  getAllDeals(reloadApi) {
    const data = {
      businessProcessId:
        this.dataSharingService.selectedBusinessProcessIdCustom,
      stageName: this.selectedFilter ? this.selectedFilter : "all",
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "desc",
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      numberOfRecords: this.pageSize,
      isAssetKey: this.isAssetKey,
    };
    this.listOfDeals = [];
    this.dataSource = new MatTableDataSource([]);

    this.dealService.getAllDealsLength(data, this.searchKey).subscribe(
      (response: any) => {
        if (response) {
          this.totalDealsNumber = response["count"];
          this.dealService.getDealList(data, this.searchKey).subscribe(
            (res: any) => {
              if (res) {
                this.dataSharingService.dealList = res;
                this.listOfDeals = this.dataSharingService.dealList;
                this.dealsAsPerBusinessProcess = this.listOfDeals;
                this.refreshDataTable(this.dealsAsPerBusinessProcess);
              }
            },
            (err) => {
              this.refreshDataTable([]);
              this.showTableSpinner = false;
            }
          );
        }
      },
      (error) => {
        this.refreshDataTable([]);
        this.showTableSpinner = false;
      }
    );
  }
  getDataFromDealAssets(rowData, fieldName) {
    const data = rowData?.dealAsset?.dealAssetItem?.filter(
      (ele: any) => this.getPropertyName(ele) == fieldName
    );
    if (data.length != 0) {
      return data[this.getPropertyName(fieldName)][fieldName].value;
    } else {
      return "-";
    }
  }
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  refreshDataTable(filterdData) {
    let data = [];
    data = filterdData;

    if (data && data.length == 0) {
      this.dataSource = new MatTableDataSource(data);
    }
    if (data && data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
    }
  }

  setStagesSelectedBusinessProcess(dealData) {
    dealData = dealData.filter(
      (deal) => deal.workFlowDetail.name == this.selectedBusinessProcess
    );
    const selectedBusinessProcessDetails = this.businessProcessList.filter(
      (item) =>
        item.name.toLowerCase() === this.selectedBusinessProcess.toLowerCase()
    )[0];
    if (
      selectedBusinessProcessDetails &&
      selectedBusinessProcessDetails.stageList.length != 0
    ) {
      const numberOfDeals = 0;
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order: selectedBusinessProcessDetails.stageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !selectedBusinessProcessDetails.stageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        selectedBusinessProcessDetails.stageList.push(rejectionObj);
      }
      selectedBusinessProcessDetails.businessProcessStageList =
        selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );

      const finalData = selectedBusinessProcessDetails.stageList.sort(function (
        a,
        b
      ) {
        return a.order - b.order;
      });
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;
      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
      this.businessProcessStagesWithDeals = finalData;
      if (
        this.businessProcessStagesWithDeals &&
        this.businessProcessStagesWithDeals.length != 0
      ) {
      }
    }
  }

  querySelected(data) {
    this.clearAllField();
    if (
      data.inputType == "Text" ||
      data.inputType == "Picklist" ||
      data.inputType == "Alphanumeric" ||
      data.inputType == "Email" ||
      data.inputType == "Long Text" ||
      data.inputType == "Searchable picklist" ||
      data.inputType == "Multiple picklist" ||
      data.inputType == "Document"
    ) {
      this.isTextField = true;
      this.selectedOptions = "Equal to";
    } else if (data.inputType == "Date") {
      this.isDateField = true;
    } else if (
      data.inputType == "Number" ||
      data.inputType == "Currency" ||
      data.inputType == "Number with decimal"
    ) {
      this.isNumberField = true;
    } else if (data.inputType == "stage") {
      this.isStageSelected = true;
    }
  }

  editFilter(val) {
    const index = this.selectedParamArr.indexOf(val);
  }

  remove(val: string): void {
    const docType = this.selectedParamArr as string[];
    this.removeFirst(docType, val);
    if (this.selectedParamArr.length == 3) {
      this.selectedParamArr.splice(1, 2);
    }
    if (this.selectedParamArr.length < 2) {
      this.resetfilter();
      this.clearAllField();
    }
  }

  private removeFirst<T>(array: T[], toRemove: T): void {
    const index = array.indexOf(toRemove);
    if (index !== -1) {
      if (index == 0) {
        array.splice(index, 2);
      } else if (index == 3) {
        array.splice(index, 1);
        array.splice(index, 1);
      } else {
        array.splice(index - 1, 2);
      }
    }
  }

  clearAllField() {
    this.isDateField = false;
    this.isNumberField = false;
    this.isTextField = false;
    this.isStageSelected = false;
    this.selectedOperator = "AND";
  }

  resetSearchFilter() {
    this.selectedOperator = "AND";
    this.greaterThanValue = "";
    this.lesserThanValue = "";
    this.equalToValue = "";
    this.stringValue = "";
    this.fromDate = "";
    this.toDate = "";
    this.stageValue = "";
    this.searchName.markAsUntouched();
    this.queryFieldForm.markAsUntouched();
    this.editQueryOption = false;
    this.isTextField = false;
    this.isDateField = false;
    this.isNumberField = false;
    this.queryField = {};
  }

  addFilter() {
    this.searchName.markAsTouched();
    this.queryFieldForm.markAsTouched();
    if (
      !this.isDateField &&
      !this.isNumberField &&
      !this.isTextField &&
      !this.isStageSelected
    ) {
      this.notificationMessage.error("Please enter a value");
      return;
    }
    if (this.isDateField && !this.fromDate && !this.toDate) {
      this.notificationMessage.error("Please enter a value");
      return;
    }
    if (
      this.isNumberField &&
      !this.lesserThanValue &&
      !this.greaterThanValue &&
      !this.equalToValue
    ) {
      this.notificationMessage.error("Please enter a value");
      return;
    }
    if (this.isTextField && !this.stringValue) {
      this.notificationMessage.error("Please enter a value");
      return;
    }
    if (this.isStageSelected && !this.stageValue) {
      this.notificationMessage.error("Please enter a value");
      return;
    }

    if (this.selectedParamArr.length == 0) {
      this.selectedParamArr.push({
        value:
          "businessProcessId:" +
          this.dataSharingService.selectedBusinessProcessIdCustom,
      });
    }

    if (this.selectedParamArr.length > 0) {
      this.selectedParamArr.push({ value: this.selectedOperator });
    }
    if (this.selectedParamArr.length == 2) {
      this.selectedParamArr.push({ value: "(" });
    }
    let selectedType;
    if (this.queryField.type == "entity") {
      selectedType = "dealEntity";
    } else if (this.queryField.type == "assetItem") {
      selectedType = "dealAsset";
    }

    if (this.isDateField) {
      if (this.queryField.type == "Date") {
        if (this.selectedParamArr.length !== 3) {
          this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
        }

        if (this.fromDate && this.toDate && this.equalDate) {
          this.selectedParamArr.push({
            value:
              "(" +
              this.queryField.name +
              ":" +
              this.dateFormatted(this.equalDate, "equal") +
              ")" +
              "OR" +
              "(" +
              this.queryField.name +
              ">" +
              this.dateFormatted(this.fromDate, "from") +
              " AND " +
              this.queryField.name +
              "<" +
              this.dateFormatted(this.toDate, "to") +
              ")",
          });
        } else if (this.fromDate && this.toDate) {
          this.selectedParamArr.push({
            value:
              "(" +
              this.queryField.name +
              ">" +
              this.dateFormatted(this.fromDate, "from") +
              " AND " +
              this.queryField.name +
              "<" +
              this.dateFormatted(this.toDate, "to") +
              ")",
          });
        } else {
          if (this.equalDate) {
            this.selectedParamArr.push({
              value:
                this.queryField.name +
                ">" +
                this.dateFormatted(this.equalDate, "equal"),
            });
          }
          if (this.fromDate) {
            this.selectedParamArr.push({
              value:
                this.queryField.name +
                ">" +
                this.dateFormatted(this.fromDate, "from"),
            });
          }
          if (this.toDate) {
            this.selectedParamArr.push({
              value:
                this.queryField.name +
                "<" +
                this.dateFormatted(this.toDate, "to"),
            });
          }
        }
      } else {
        if (this.selectedParamArr.length !== 3) {
          this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
        }

        if (this.fromDate && this.toDate && this.equalDate) {
          this.selectedParamArr.push({
            value:
              "(" +
              selectedType +
              "." +
              this.queryField.name +
              ":" +
              this.dateFormatted(this.equalDate, "equal") +
              ")" +
              "OR" +
              "(" +
              selectedType +
              "." +
              this.queryField.name +
              ">" +
              this.dateFormatted(this.fromDate, "from") +
              " AND " +
              selectedType +
              "." +
              this.queryField.name +
              "<" +
              this.dateFormatted(this.toDate, "to") +
              ")",
          });
        } else if (this.fromDate && this.toDate) {
          this.selectedParamArr.push({
            value:
              "(" +
              selectedType +
              "." +
              this.queryField.name +
              ">" +
              this.dateFormatted(this.fromDate, "from") +
              " AND " +
              selectedType +
              "." +
              this.queryField.name +
              "<" +
              this.dateFormatted(this.toDate, "to") +
              ")",
          });
        } else {
          if (this.equalDate) {
            this.selectedParamArr.push({
              value:
                selectedType +
                "." +
                this.queryField.name +
                ">" +
                this.dateFormatted(this.equalDate, "equal"),
            });
          }
          if (this.fromDate) {
            this.selectedParamArr.push({
              value:
                selectedType +
                "." +
                this.queryField.name +
                ">" +
                this.dateFormatted(this.fromDate, "from"),
            });
          }
          if (this.toDate) {
            this.selectedParamArr.push({
              value:
                selectedType +
                "." +
                this.queryField.name +
                "<" +
                this.dateFormatted(this.toDate, "to"),
            });
          }
        }
      }
    } else if (this.isNumberField) {
      if (this.selectedParamArr.length !== 3) {
        this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
      }
      if (this.greaterThanValue && this.lesserThanValue && this.equalToValue) {
        this.selectedParamArr.push({
          value:
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ":" +
            this.equalToValue +
            " OR " +
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ">" +
            this.greaterThanValue +
            " AND " +
            selectedType +
            "." +
            this.queryField.name +
            "<" +
            this.lesserThanValue +
            ") )",
        });
      } else if (this.equalToValue && this.greaterThanValue) {
        this.selectedParamArr.push({
          value:
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ":" +
            this.equalToValue +
            " OR " +
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ">" +
            this.greaterThanValue +
            ") )",
        });
      } else if (this.equalToValue && this.lesserThanValue) {
        this.selectedParamArr.push({
          value:
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ":" +
            this.equalToValue +
            " OR " +
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            "<" +
            this.lesserThanValue +
            ") )",
        });
      } else if (this.greaterThanValue && this.lesserThanValue) {
        this.selectedParamArr.push({
          value:
            "(" +
            selectedType +
            "." +
            this.queryField.name +
            ">" +
            this.greaterThanValue +
            " AND " +
            selectedType +
            "." +
            this.queryField.name +
            "<" +
            this.lesserThanValue +
            ")",
        });
      } else {
        if (this.equalToValue) {
          this.selectedParamArr.push({
            value:
              selectedType +
              "." +
              this.queryField.name +
              ":" +
              this.equalToValue,
          });
        }
        if (this.greaterThanValue) {
          this.selectedParamArr.push({
            value:
              selectedType +
              "." +
              this.queryField.name +
              ">" +
              this.greaterThanValue,
          });
        }
        if (this.lesserThanValue) {
          this.selectedParamArr.push({
            value:
              selectedType +
              "." +
              this.queryField.name +
              "<" +
              this.lesserThanValue,
          });
        }
      }
    } else if (this.isTextField) {
      if (this.selectedParamArr.length !== 3) {
        this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
      }

      if (this.selectedOptions == "Equal to") {
        if (this.stringValue == "Economics MU") {
          this.stringValue = "Economics  MU";
        }

        if (this.stringValue) {
          this.selectedParamArr.push({
            value:
              selectedType +
              "." +
              this.queryField.name +
              ":" +
              '"' +
              this.stringValue +
              '"',
          });
        }
      } else if (this.selectedOptions == "Contains") {
        if (this.stringValue) {
          this.selectedParamArr.push({
            value:
              selectedType +
              "." +
              this.queryField.name +
              ":" +
              '"' +
              "*" +
              this.stringValue +
              '*"',
          });
        }
      }
    } else if (this.isStageSelected) {
      if (this.selectedParamArr.length !== 3) {
        this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
      }

      this.selectedParamArr.forEach((ele) => {
        const duplicate = ele.value.indexOf(this.stageValue);
        if (duplicate > -1) {
          this.notificationMessage.error("Stage name is already exists");
          return;
        }
      });

      if (this.stageValue) {
        this.selectedParamArr.push({
          value: "currentStageName" + ":" + '"' + this.stageValue + '"',
        });
      }
      if (this.loanAccValue) {
        this.selectedParamArr.push({ value: this.selectedOperator });
      }
      if (this.loanAccValue) {
        this.selectedParamArr.push({
          value: "currentStatus" + ":" + '"' + this.loanAccValue + '"',
        });
      }
    } else if (this.isEntitySelected) {
      if (this.selectedParamArr.length !== 3) {
        this.selectedParamArr.splice(this.selectedParamArr.length - 2, 1);
      }
      if (this.entityValue) {
        this.selectedParamArr.push({
          value:
            selectedType +
            "." +
            this.queryField.name +
            ":" +
            '"' +
            this.entityValue +
            '"',
        });
      }
    }
    this.selectedParamArr.push({ value: ")" });
    const stageData = [
      {
        displayProperty: {
          displayName: "Stage name",
          name: "stageName",
        },
        inputType: "stage",
        type: "stage",
      },
    ];
    this.finalAssetItems = [];
    this.selectedParamArr.length > 2
      ? (this.finalAssetItems = this.assestitems)
      : (this.finalAssetItems = stageData);
    if (this.stageValue) {
      this.stagename = this.stageValue;
    }
    this.clearAllField();
    this.resetSearchFilter();
    this.queryField = "";
  }

  clearFilter() {
    this.selectedOperator = "";
    this.stringValue = "";
    this.greaterThanValue = "";
    this.lesserThanValue = "";
    this.equalToValue = "";
    this.fromDate = "";
    this.toDate = "";
    this.equalDate = "";
  }

  dateFormatted(newdate, str) {
    let date;
    date = new Date(newdate);
    if (str == "from") {
      date = new Date(date.setDate(date.getDate() - 1));
    } else {
      date = new Date(date.setDate(date.getDate() + 1));
    }
    if (date != null) {
      return this.dataSharingService.getDateFormatInPayload(date);
    } else {
      return null;
    }
  }

  filter(data) {
    this.queryView = {};
    let query = data.queryString;
    this.query = query.substring(7);
    this.sortquery = query.substring(7);
    query = query.substring(7);
    this.queryView = {
      name: data.name,
      query: query,
    };
    const pattern = /currentStatus:"([^"]+)"/;
    const match = pattern.exec(query);
    if (match) {
      const currentStatus = match[1];
      this.loanAccValue = currentStatus;
    }

    this.dataSharingService.customSearchPageSize = 25;
    this.dataSharingService.customSearchPageIndex = 0;
    this.previewFilter(query);
  }

  previewFilterWithPagination() {
    this.viewStageStatus = false;
    this.showTableSpinner = true;
    this.queryData = [];
    this.noData = false;
    this.queryView = null;
    for (let i = 0; i < this.selectedParamArr.length; i++) {
      this.filterQuery += this.selectedParamArr[i].value + " ";
    }
    this.dealService
      .queryFilterWithPagination(
        this.filterQuery,
        this.pageIndex,
        this.pageSize,
        this.sortAsPerKeyName,
        this.sortDirection
      )
      .subscribe(
        (res: any) => {
          if (res.content.length > 0) {
            this.queryData = res.content;
            this.totalCountOflength = res.totalElements;
            this.generateDataForDownload();
            this.generateDataForSort();
            this.dataList();
            this.refreshDataTable(this.queryData);
          } else {
            this.noData = true;
          }
          this.filterQuery = "";
          this.showTableSpinner = false;
        },
        (err) => {
          this.showTableSpinner = false;
        }
      );
  }

  previewFilter(query) {
    this.viewStageStatus = false;
    this.count++;
    if (query == "queryfilter") {
      this.pageSize = 25;
      this.pageIndex = 0;
    } else {
      this.pageSize = this.dataSharingService.customSearchPageSize;
      this.pageIndex = this.dataSharingService.customSearchPageIndex;
    }
    this.noData = false;
    this.showTableSpinner = true;
    this.queryData = [];
    this.filterQuery = "";
    if (query && query != "queryfilter") {
      this.filterQuery = query.replace(/&/g, "%26");

      this.dealService
        .queryFilterWithPagination(
          this.filterQuery,
          this.pageIndex,
          this.pageSize,
          this.sortAsPerKeyName,
          this.sortDirection
        )
        .subscribe(
          (res: any) => {
            if (res.content.length > 0) {
              this.currentStageName = res.content[0].currentStageName;
              this.stagename = res.content[0].currentStageName;
              this.queryData = res.content;
              this.totalCountOflength = res.totalElements;
              this.generateDataForDownload();
              this.dataList();
              this.refreshDataTable(this.queryData);
              if (this.lastStage == this.currentStageName) {
                this.disableLastStage = true;
              } else {
                this.disableLastStage = false;
              }
            } else {
              this.refreshDataTable([]);
              this.noData = true;
            }
            this.showTableSpinner = false;
          },
          (err) => {
            this.showTableSpinner = false;
          }
        );
    } else {
      this.queryView = null;
      if (this.queryText) {
        this.filterQuery = this.queryText.replace(/&/g, "%26");
      } else {
        for (let i = 0; i < this.selectedParamArr.length; i++) {
          this.filterQuery += this.selectedParamArr[i].value + " ";
        }
        this.filterQuery = this.filterQuery.replace(/&/g, "%26");
      }
      this.dealService
        .queryFilterWithPagination(
          this.filterQuery,
          this.pageIndex,
          this.pageSize,
          this.sortAsPerKeyName,
          this.sortDirection
        )
        .subscribe(
          (res: any) => {
            if (res.content.length > 0) {
              this.sortquery = "";
              this.queryData = res.content;
              this.currentStageName = res.content[0].currentStageName;
              this.totalCountOflength = res.totalElements;
              this.generateDataForDownload();
              this.dataList();
              this.refreshDataTable(this.queryData);
              if (this.lastStage == this.currentStageName) {
                this.disableLastStage = true;
              } else {
                this.disableLastStage = false;
              }
            } else {
              this.refreshDataTable([]);
              this.noData = true;
            }
            this.showTableSpinner = false;
          },
          (err) => {
            this.showTableSpinner = false;
          }
        );
    }

    this.panelOpenState = false;
  }

  generateDataForDownload() {
    this.downloadData = [];
    //Logic is for multiple and searchable picklist start
    this.queryData.forEach((ele) => {
      const obj1 = ele.dealAsset;
      for (const key in obj1) {
        if (obj1.hasOwnProperty(key)) {
          const value = obj1[key];
          if (value && Array.isArray(value)) {
            const valueArray = [];
            value?.forEach((ele) => valueArray.push(ele?.name));
            obj1[key] = valueArray;
          } else if (typeof value === "object" && value !== null) {
            const newvalue = obj1[key].name;
            obj1[key] = newvalue;
          }
        }
      }
      const obj2 = ele.dealEntity;
      for (const key in obj2) {
        if (obj2.hasOwnProperty(key)) {
          const value = obj2[key];
          if (value && Array.isArray(value)) {
            const valueArray = [];
            value?.forEach((ele) => valueArray.push(ele?.name));
            obj2[key] = valueArray;
          } else if (typeof value === "object" && value !== null) {
            const newvalue = obj2[key].name;
            obj2[key] = newvalue;
          }
        }
      }
      //Logic is for multiple and searchable picklist end

      const obj = { ...obj1, ...obj2 };
      const newArray = {};
      newArray["Description"] = ele.dealIdentifier.toString();
      this.displayedColumns.forEach((key) => {
        if (obj.hasOwnProperty(key)) {
          const titleCaseKey = this.titleCase(key);
          newArray[titleCaseKey] = obj[key];
        }
      });
      newArray["Stage"] = ele.currentStageName.toString();
      newArray["Status"] = ele.currentStatus.toString();
      newArray["Created Date"] = ele.createdDate.toString();
      this.downloadData.push(newArray);
    });
  }

  exportXls() {
    this.generateDataForDownload();
    if (this.downloadData) {
      this.excelList = this.downloadData;
      const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
      let deal;
      deal = xlsx.utils.json_to_sheet(this.excelList);
      deal["!cols"] = wscols;
      let sheetNames;
      let sheetobject;
      if (this.selectedBusinessProcessId) {
        sheetNames = ["Deals"];
        sheetobject = { Deals: deal };
      } else {
        sheetNames = ["Entities"];
        sheetobject = { Entities: deal };
      }

      const workbook: xlsx.WorkBook = {
        Sheets: sheetobject,
        SheetNames: sheetNames,
      };
      xlsx.writeFile(
        workbook,
        `${"Eligible student list" + "_" + this.getDateTime()}.csv`
      );
    }
  }

  getDateTime() {
    const currentdate = new Date();
    let dateTime;
    return (dateTime =
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate() +
      "_" +
      currentdate.getHours() +
      ":" +
      currentdate.getMinutes() +
      ":" +
      currentdate.getSeconds());
  }

  generateDataForSort() {
    this.sortingData = [];
    this.queryData.forEach((ele) => {
      const obj = {
        id: ele.id,
        dealIdentifier: ele.dealIdentifier,
        ask:
          ele.dealAsset.ask != "null" && ele.dealAsset.ask
            ? this.getCurrencyInShorterFormat(
                ele.dealAsset.ask,
                this.selectedCurrency
              )
            : "-",
        currentStageName:
          ele.currentStageName != "null" && ele.currentStageName
            ? ele.currentStageName
            : "-",
        avgScore:
          ele.stageAverageScore != "null" && ele.stageAverageScore
            ? ele.stageAverageScore.toFixed(2)
            : "-",
        createdDate:
          ele.createdDate != "null" && ele.createdDate ? ele.createdDate : "-",
      };
      this.sortingData.push(obj);
    });
  }

  dataList() {
    this.dataSource = new MatTableDataSource(this.queryData);
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  fetchQueries() {
    this.searchName.setValue("");
    this.queryField = "";
    this.savedQueries = [];
    this.dealService.fetchQueries().subscribe(
      (res: any) => {
        res.forEach((ele) => {
          const user = localStorage.getItem("user");
          const valueCheck = ele.queryString.includes(
            "businessProcessId:" +
              this.dataSharingService.selectedBusinessProcessIdCustom
          );

          this.savedQueries.push(ele);
        });
      },
      (err) => {
        this.showTableSpinner = false;
      }
    );
  }

  removeSavedQuery(query) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (this.queryView && this.queryView.name == query.name) {
          this.queryView = null;
        }
        this.dealService.deleteSavedQuery(query.id).subscribe(
          (res) => {
            this.notificationMessage.success(
              JsonData["label.success.DeleteQuery"]
            );
            this.fetchQueries();
          },
          (error) => {
            this.showTableSpinner = false;
          }
        );
      }
    });
  }

  getEntityListApi() {
    this.entityService
      .getPrimaryEntityDetailsUsingBPId(this.selectedBusinessProcessDetails.id)
      .subscribe(
        (res: any) => {
          if (res) {
            const newArr = [];

            const d = res?.flatMap((item) =>
              item.entityDefinition.entityDetail.entityDetail.map((detail) => ({
                ...detail,
                entityNameTitle: item.entityName,
              }))
            );
            const entityData = d;
            entityData.forEach((element) => {
              const [key, value] = Object.entries(element);
              let data: any;
              data = key[1];
              data.type = "entity";
              data.entityNameTitle = element.entityNameTitle;
              data.displayProperty.displayName = data.displayName
                ? data.displayName
                : data.displayProperty.displayName;
              data.name = key[0];
              if (data.displayProperty.displayName != "Grant available")
                newArr.push(key[1]);
            });
            this.entityList = newArr;
            if (this.entityList) {
              this.assestitems = this.assestitems.concat(this.entityList);
            }
          }
        },
        (err) => {
          this.showTableSpinner = false;
        }
      );
  }

  onPaginationChanged(event) {
    this.dataSharingService.isAdvanceSearch = true;
    this.dataSharingService.advancedSearchView = true;
    this.dataSharingService.customSearchPageSize = event.pageSize;
    this.dataSharingService.customSearchPageIndex = event.pageIndex;
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    if (this.sortquery) {
      this.previewFilter(this.sortquery);
    } else if (this.selectedParamArr.length > 2) {
      if (this.stagename == "") {
        this.count = 0;
      }
      this.previewFilter(null);
    } else {
      if (this.stagename == "") {
        this.count = 0;
      }
      this.selectedParamArr.push({
        value:
          "businessProcessId:" +
          this.dataSharingService.selectedBusinessProcessIdCustom,
      });
      this.previewFilter(null);
      this.resetfilter();
    }
  }

  changeStage() {
    this.bulkMovementId = null;
    let changeStageName;
    if (this.stageValue) {
      changeStageName = this.stageValue;
    } else {
      changeStageName = this.currentStageName;
    }
    if (this.queryView !== null) {
      const matDialogRef = this.dialog.open(
        BulkMovementChangeStageDialogComponent,
        {
          autoFocus: false,
          width: "45%",
          disableClose: true,
          data: {
            title: "Change Stage",
            name: changeStageName,
          },
        }
      );
      matDialogRef.afterClosed().subscribe((result) => {
        if (result.isSubmit) {
          this.bulkMovement(result.remark);
        }
      });
    } else {
      this.notificationMessage.success(JsonData["label.success.SaveSearch"]);
    }
  }

  bulkMovement(remark) {
    this.bulkMovementId = null;
    const d = new Date();
    const date =
      d.getDate() +
      "-" +
      (d.getMonth() + 1) +
      "-" +
      d.getFullYear() +
      " " +
      d.getHours() +
      ":" +
      d.getMinutes() +
      ":" +
      d.getSeconds();
    if (this.queryView !== null) {
      const payload = {
        dashboardQuery: {
          name: this.queryView.name,
        },
        remarks:
          "Remark:" +
          remark +
          " Automated Movement Query:[" +
          this.queryView.query +
          "] Date Time: [ " +
          date +
          " ]",
      };
      const matDialogRef = this.dialog.open(BulkMovementStageDialogComponent, {
        autoFocus: false,
        width: "45%",
        disableClose: true,
        data: {
          title: "Move Stage",
          message:
            "This action moves " +
            this.totalCountOflength +
            " deals to next stage,do you want to continue?",
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result.isSubmit) {
          this.dealService
            .bulkMovement(this.filterQuery, payload)
            .subscribe((res: any) => {
              this.clearAllField();
              this.searchName.setValue("");
              this.selectedParamArr = [];

              this.bulkMovementId = res.eventId;
              this.fetchBulkMovement();
              this.selectedParamArr = [];
              this.queryData = [];
              this.queryView = null;
              this.resetfilter();
              this.searchName.markAsUntouched();
              this.queryFieldForm.markAsUntouched();
              this.showTableSpinner = false;
              this.noData = true;
              this.dataSource = [];
            });
        }
      });
    } else {
      this.notificationMessage.success(JsonData["label.success.SaveSearch"]);
    }
  }

  fetchBulkMovement() {
    this.bulkView = null;
    this.bulkMovementList = [];
    this.dealService.fetchBulkMovement().subscribe(
      (res: any) => {
        res.content.forEach((ele) => {
          const valueCheck = ele.dashboardQuery?.queryString?.includes(
            "businessProcessId:" +
              this.dataSharingService.selectedBusinessProcessIdCustom
          );
          const user = localStorage.getItem("user");
          if (valueCheck && ele.createdBy == user) {
            this.bulkMovementList.push(ele);
          }
        });
      },
      (err) => {
        this.showTableSpinner = false;
      }
    );
  }

  filterBulkmovementList(data) {
    this.bulkView = {};
    this.showQuery = false;
    let query = data.dashboardQuery.queryString;
    query = query.substring(7);
    this.showCountSpinner = true;
    this.dealService
      .fetchBulkMovementById(
        data.id,
        this.pageIndex,
        this.pageSize,
        this.sortBy,
        this.sortingKey
      )
      .subscribe(
        (res: any) => {
          this.bulkView = {
            name: res.dashboardQuery.name,
            query: res.dashboardQuery.queryString,
            eligible: res.recordSize,
            success: res.success,
            failed: res.failed,
            inprogress: res.inProgress,
            id: res.id,
          };
          this.showCountSpinner = false;
        },
        (err) => {
          this.showCountSpinner = false;
        }
      );

    this.noData = true;
    this.queryData = [];
    this.dataSource = [];
  }

  showBulkMovementDetails(data) {
    const matDialogRef = this.dialog.open(BulkMovementDetailsDialogComponent, {
      width: "80%",
      height: "90%",
      disableClose: true,
      data: {
        bulkMovementDetails: data,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {});
  }

  announceSortChange(sortState: Sort) {
    // This example uses English messages. If your application supports
    // multiple language, you would internationalize these strings.
    // Furthermore, you can customize the message to add additional
    // details about the values being sorted.
    if (sortState.direction) {
      this._liveAnnouncer.announce(`Sorted ${sortState.direction}ending`);
    } else {
      this._liveAnnouncer.announce("Sorting cleared");
    }
  }
  sortData(event) {
    if (event?.active == "dealIdentifier") {
      this.sortAsPerKeyName = "dealIdentifier";
      this.dataSharingService.selectedSortKeyForDashboard = "dealIdentifier";
      this.isAssetKey = false;
    }
    if (event?.active == "nRC") {
      this.sortAsPerKeyName = "nRC";
      this.dataSharingService.selectedSortKeyForDashboard = "nRC";
      this.isAssetKey = false;
    }
    if (event?.active == "institution") {
      this.dataSharingService.selectedSortKeyForDashboard = "institution";
      this.sortAsPerKeyName = "institution";
      this.isAssetKey = false;
    }
    if (event?.active == "program") {
      this.dataSharingService.selectedSortKeyForDashboard = "program";
      this.sortAsPerKeyName = "program";
      this.isAssetKey = false;
    }
    if (event?.active == "points") {
      this.dataSharingService.selectedSortKeyForDashboard = "points";
      this.sortAsPerKeyName = "points";
      this.isAssetKey = false;
    }
    if (event?.active == "loanSupportCategory") {
      this.dataSharingService.selectedSortKeyForDashboard =
        "loanSupportCategory";
      this.sortAsPerKeyName = "loanSupportCategory";
      this.isAssetKey = false;
    }
    this.dataSharingService.selectedSortDirection = event.direction;

    this.sortDirection = event.direction;
    if (!this.sortDirection) {
      this.dataSharingService.selectedSortKeyForDashboard = "createdDate";
      this.sortAsPerKeyName = "createdDate";
      this.isAssetKey = false;
    }
    if (this.sortquery) {
      if (this.stagename == "") {
        this.count = 0;
      }
      this.previewFilter(this.sortquery);
    } else if (this.selectedParamArr?.length > 2) {
      if (this.stagename == "") {
        this.count = 0;
      }
      this.previewFilter(null);
    } else {
      if (this.stagename == "") {
        this.count = 0;
      }
      this.selectedParamArr.push({
        value:
          "businessProcessId:" +
          this.dataSharingService.selectedBusinessProcessIdCustom,
      });
      this.previewFilter(null);
      this.resetfilter();
    }
  }

  editQueryDialog() {
    this.queryText = "";
    const matDialogRef = this.dialog.open(
      AdvanceSearchEditQueryDialogComponent,
      {
        disableClose: true,
        data: {
          message:
            "This action edits the current search, no new details can be added, do you want to proceed?",
        },
      }
    );
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.queryTextFeild = true;
        for (let i = 0; i < this.selectedParamArr.length; i++) {
          this.queryText += this.selectedParamArr[i].value + " ";
        }
        this.editQueryOption = true;
      }
    });
  }

  cancelEdit() {
    this.queryTextFeild = false;
  }

  titleCase(str) {
    const splitStr = str.toLowerCase().split(" ");
    for (let i = 0; i < splitStr.length; i++) {
      // You do not need to check if i is larger than splitStr length, as your for does that for you
      // Assign it back to the array
      if (splitStr[i] != "of") {
        splitStr[i] =
          splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
      }
      splitStr[i] =
        splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    // Directly return the joined string
    return splitStr.join(" ");
  }

  onPageRefresh(data) {
    this.showCountSpinner = true;
    this.dealService
      .fetchBulkMovementById(
        data.id,
        this.pageIndex,
        this.pageSize,
        this.sortBy,
        this.sortingKey
      )
      .subscribe(
        (res: any) => {
          this.bulkView = {
            name: res.dashboardQuery.name,
            query: res.dashboardQuery.queryString,
            eligible: res.recordSize,
            success: res.success,
            failed: res.failed,
            inprogress: res.inProgress,
            id: res.id,
          };
          this.showCountSpinner = false;
        },
        (err) => {
          this.showCountSpinner = false;
        }
      );
  }

  getCurrencyInShorterFormat(amount, currency) {
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }

  getDate(element) {
    const newmonth =
      element.month.substring(0, 3)[0].toUpperCase() +
      element.month.substring(0, 3).slice(1);
    const date = newmonth + " " + element.dayOfMonth + ", " + element.year;
    return date;
  }

  getValue(row, nodeName) {
    const item = row.dealAsset?.[nodeName];
    if (item) {
      return item;
    }
    return "";
  }

  getList(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }
  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  getTooltip(ele, col) {
    if (col[this.getPropertyName(col)].inputType == "Multiple picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col))?.map(
            (e) => " " + e.name
          )
        : "";
    else if (col[this.getPropertyName(col)].inputType == "Searchable picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).name
        : "";
    else return this.getValue(ele, this.getPropertyName(col));
  }

  getAssetList(list) {
    if (this.searchedAsset) {
      return this.finalAssetItems
        .slice()
        .filter((list) =>
          list.displayProperty?.displayName
            ?.toLowerCase()
            .includes(this.searchedAsset.toLowerCase())
        );
    } else {
      return this.finalAssetItems;
    }
  }

  filterAsset($event) {
    this.searchedAsset = $event;
  }
}
