<div fxLayout="row wrap" fxLayoutGap="4px" class="mtb">

  <div fxFlex="15%" fxFlex.md="20%" fxFlex.xs="30%" fxFlex.sm="30%">
    <mat-form-field class="serachItems width-97 formfield">
      <mat-label>{{"label.field.searchItems" | literal}}</mat-label>
      <input class="text-align-left" matInput (keyup.enter)="applyFilter($event)"
        placeholder="Item Name" autocomplete="off" #input />
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>




  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" fxFlex.lg="100%"
    class="height-auto">

    <mat-card appearance="outlined" class="mat-card-top-border mb-5 padding0">

      <mat-card-content>
        <div>
          <button matTooltip="Download" class="downloadButton" mat-icon-button
            (click)="exportXls()">
            <mat-icon class="pointer icon-white mt-20">
              get_app</mat-icon>
          </button>
          <h2 class="heading" mat-icon-button>
            {{this.data.subMenu}}
          </h2>
        </div>
        <div class=" dealTable mat-elevation-z0
        mat-table-width mt7 " [style.overflow-x]="showTableSpinner  ? 'auto': 'scroll !important'">

          <div class="dataTableCss mat-table-wrap-text">
            <div [style.width]=" lengthOfcols >= 5 ? 'max-content' : ''">
              <table class="width-100" mat-table [dataSource]="dataSource " matSort
                (matSortChange)="sortData($event)" [matSortDirection]="sortDirection">

                <ng-container matColumnDef="name" sticky *ngIf="!isBP">
                  <th mat-sort-header class="  w-22 width-250" mat-header-cell *matHeaderCellDef>
                    Name </th>
                  <td mat-cell class="pointer " *matCellDef="let element">
                    <span (click)="viewCompany(element)" class="customDescription">
                      <p [matTooltip]="element?.name" class="hyperlinkColor">
                        {{element.name}}</p>
                    </span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="dealIdentifier" sticky *ngIf="isBP">
                  <th mat-sort-header mat-header-cell *matHeaderCellDef class="  w-22 width-250">
                    Description
                  </th>
                  <td mat-cell class="pointer " *matCellDef="let row">
                    <span (click)="navigateToSummaryPage(row)" class="customDescription">
                      <p [matTooltip]="row?.dealIdentifier" class="hyperlinkColor">
                        {{stringWithEllipsis(row?.dealIdentifier)}}</p>
                    </span>
                  </td>
                </ng-container>

                <ng-container *ngFor="let col of tableColumn; let i = index"
                  [matColumnDef]="getPropertyName(col)">
                  <th mat-header-cell *matHeaderCellDef class="columnNameCss"
                    [style.width]="col[getPropertyName(col)].inputType === 'Action' ? '80px' : lengthOfcols >= 5 ? '250px' : (100/lengthOfcols) +'%' ">
                    <span *ngIf="col[getPropertyName(col)].inputType !== 'Action'">
                      <ng-container *ngIf="col[getPropertyName(col)].inputType !== 'Currency'">{{
                        col[getPropertyName(col)].displayProperty.displayName }}</ng-container>
                      <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Currency'">{{
                        col[getPropertyName(col)].displayProperty.displayName }} in
                        {{selectedCurrency}}</ng-container>
                    </span>

                  </th>

                  <td mat-cell *matCellDef="let element; let i = index" matTooltipShowDelay="1500"
                    tooltip>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Text'">
                      {{ getValue(element,getPropertyName(col))}}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Long Text'">
                      {{stringWithEllipsis(getValue(element,getPropertyName(col))) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Number'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Email'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Boolean'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container
                      *ngIf="col[getPropertyName(col)].inputType === 'Number with decimal'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Picklist'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Alphanumeric'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Document'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Configuration'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Date'">
                      <span
                        *ngIf="getValue(element,getPropertyName(col))!=='' && getValue(element,getPropertyName(col))!=='null'">
                        {{ getValue(element,getPropertyName(col))| date }}
                      </span>
                      <span *ngIf="getValue(element,getPropertyName(col))===''">
                        {{ getValue(element,getPropertyName(col)) }}
                        <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                      </span>
                    </ng-container>

                    <ng-container
                      *ngIf="element[getPropertyName(element)].inputType === 'Date And Time'">
                      <span *ngIf="getValue(element,getPropertyName(element))!==''">
                        {{ getValue(element,getPropertyName(element)) + "Z"| dateTime }}
                      </span>
                      <span *ngIf="getValue(element,getPropertyName(element))===''">
                        {{ getValue(element,getPropertyName(element)) }}
                        <span *ngIf="!getValue(element,getPropertyName(element))">-</span>
                      </span>
                    </ng-container>

                    <ng-container
                      *ngIf="col[getPropertyName(col)].inputType === 'Searchable picklist'">
                      {{ (getValue(element,getPropertyName(col))).name }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container
                      *ngIf="col[getPropertyName(col)].inputType === 'Multiple picklist'">
                      {{ getList(getValue(element,getPropertyName(col)))}}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container
                      *ngIf="col[getPropertyName(col)].inputType === 'Multiple Static Picklist'">
                      {{ getValue(element,getPropertyName(col)) }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                    <ng-container *ngIf="col[getPropertyName(col)].inputType === 'Currency'">
                      <span *ngIf="getValue(element,getPropertyName(col))">
                        {{this.getCurrencySymbol(col[getPropertyName(col)].displayProperty.defaultValues)}}</span>&nbsp;
                      {{
                      getValue(element,getPropertyName(col))| currency :
                      col[getPropertyName(col)].displayProperty.defaultValues : '' }}
                      <span *ngIf="!getValue(element,getPropertyName(col))">-</span>
                    </ng-container>

                  </td>
                </ng-container>

                <ng-container matColumnDef="createdDate" stickyEnd>
                  <th mat-header-cell *matHeaderCellDef class="  w-22 width-160"> Created Date </th>
                  <td mat-cell class="pointer " *matCellDef="let row">
                    {{row?.createdDate | date}}
                  </td>
                </ng-container>
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              </table>

            </div>
          </div>
          <div *ngIf="queryData?.length===0 && !showTableSpinner">
            No data available
          </div>

          <div *ngIf="showTableSpinner" class="mt-5">
            <mat-spinner class="m-5"> </mat-spinner>
          </div>
        </div>

      </mat-card-content>
      <div>
        <mat-paginator class="formly-single-align-100" [length]="totalCountOflength"
          [pageSize]="pageSize" showFirstLastButtons="true" [pageIndex]="pageIndex"
          [pageSizeOptions]="[10, 25,50, 100, 500, 1000]"
          (page)="onPaginationChanged($event)"></mat-paginator>
      </div>

    </mat-card>
  </div>
</div>
