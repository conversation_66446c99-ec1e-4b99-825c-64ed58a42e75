import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { Subject } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { LoaderService } from "../shared-service/loader.service";
import { sidenavListConfig } from "../sidebar-nav/sidebar-nav.model";

@Component({
  selector: "app-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
})
export class DashboardComponent implements OnInit {
  sidenavList: { name: string; icon: string; url?: string }[];
  isLoading: Subject<boolean> = this.loader.isLoading;
  constructor(
    public loader: LoaderService,
    private route: Router,
    public dataSharingService: DataSharingService
  ) {}
  showFiller = false;
  ngOnInit() {
    this.sidenavList = sidenavListConfig;
  }

  goTo(url) {}

  logout() {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("accessTokenExpiration");
    localStorage.removeItem("user");
    localStorage.removeItem("tenantIdentifier");
    localStorage.removeItem("fileMaxSizeCache");
    localStorage.removeItem("workspaceRecentItems");

    this.route.navigate(["/login"]);
  }
}
