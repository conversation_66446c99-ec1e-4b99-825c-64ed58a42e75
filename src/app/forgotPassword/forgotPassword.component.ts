import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { ToasterService } from "../common/toaster.service";
@Component({
  selector: "app-forgotPassword",
  templateUrl: "./forgotPassword.component.html",
  styleUrls: ["./forgotPassword.component.scss"],
})
export class ForgotPasswordComponent implements OnInit {
  constructor(private route: Router, private toaster: ToasterService) {}

  ngOnInit() {}

  goBack() {
    this.route.navigate(["/login"]);
  }

  submit() {
    this.openSnackBar();
    this.goBack();
  }

  openSnackBar() {
    this.toaster.success("Password reset link has been sent to your email");
  }
}
