import { Component } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { staticRuleGuide } from './guide-data';
import { ToasterService } from '../common/toaster.service';
@Component({
    selector: 'app-help-page',
    templateUrl: './help-page.component.html',
    styleUrls: ['./help-page.component.scss']
})
export class HelpPageComponent {
activeLink: any = 'general';
guideData = staticRuleGuide;
tableColumns = ['rule','format','example','description'];
dataSource:any = new MatTableDataSource([]);
toggleView = true;
constructor(private notification:ToasterService){

}
triggerCopySuccess(){
    this.notification.success('Copied to clipboard')
}
}
