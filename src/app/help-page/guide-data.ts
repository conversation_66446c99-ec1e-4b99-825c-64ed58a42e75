export const staticRuleGuide  = {
    valueRule:[
        {
            usecase:'To set value in the field',
            format:'"value"',
            description:'The value mentioned in double quotes will be set for the field',
            example:'"India"'
        },
        {
            usecase:'To set values in uppercase format',
            format:'controls.fieldname.value.toUpperCase()   ',
            description:'The characters mentioned in field like PAN will be set in uppercase only by using this expression.',
            example:'controls.pan.value.toUpperCase()'
        },
        {
            usecase:'To set values in lowercase format',
            format:'controls.fieldname.value.toLowerCase()   ',
            description:'The characters mentioned in field like PAN will be set in lowercase only by using this expression.',
            example:'controls.pan.value.toLowerCase() '
        },
        {
            usecase:'To set the value of field cheking other field value',
            format:'controls.isSameAsFieldOne.value ? controls.FieldTwo.value : ""',
            description:'Consider, we inserted the values in the field of current address and we want to use/fetch that same values in the field of correspondence address that time we can use this expression otherwise we can insert new value in double quotes.',
            example:'controls.isSameAsCurrentAddress.value ? controls.addressLineOne.value : ""'
        },
        {
            usecase:'To Concatenate values of two fields',
            format:'asset.fieldname1.value+" "+asset.fieldname2.value',
            description:'As we can see field names are firstName and lastName so by using this rule we are able to concatenate first name and last name of person.',
            example:'asset.firstName.value+" "+asset.lastName.value'
        },
        {
            usecase:'To Add or Substarct Days/Week/Month/Year from another Date',
            format:"(controls.FieldOne.value && controls.FieldOne.value > 4) ? this.addTimeToDate(this.today, '-' + (controls.FieldOne.value) + 'M'):'';",
            description:'Consider the field one value is greater than 4 then I want to add/Substarct the respective Days/Weeks/Months/Years into the current date. Annotation for Days/Weeks/Months/Years are D/W/M/Y respectively',
            example:"(controls.FieldOne.value && controls.FieldOne.value < 24)? this.addTimeToDate(this.today, '-' + (controls.FieldOne.value/4) + 'M'):'';"
        },
    ],
    disableRule:[
        {
            usecase:'To check field value',
            format:'asset.isFrom Application Name.value ? true : false',
            description:'Suppose this rule is inserted in read only column for the field "First Name", Then the field "First Name" will be read only if the value of that filed is coming from DOB (digital Onboarding) Application, Else it will be  editable.',
            example:'asset.isFromDob.value ? true : false'
        },
        {
            usecase:'Enable/disable field based on value of other field.',
            format:'controls.fieldname1.value=="value"?true:false',
            description:'This rule is applied on a field when, whether that field will be ediatble or read only depends on the value of other field. ',
            example:'controls.previouslyAppliedForSponsorship.value=="No"?true:false'
        },
        {
            usecase:'Comparing values of two fields',
            format:'controls.fieldname.value.!=this.user?true:false',
            description:' Here if the user and value in mentioned fieldname is same then only approve field get enable',
            example:'controls.approver1.value.id !=this.user?true:false'
        }
    ],
    hideRule:[
        {
            usecase:'To hide the field',
            format:'true',
            description:'When we insert "true" in hide rule column for any field then that field is hidden',
            example:'true'
        },
        {
            usecase:'To hide the field dependent on other field value present or not',
            format:'controls.sameAsfieldname.value ? true: false',
            description:'If value of this "sameAsCurrentAddress" field is present, then only state field will be in hide state or else it will be visible. ',
            example:'controls.sameAsCurrentAddress.value ? true: false'
        },
        {
            usecase:'To hide the field dependent on other field value matching to specified value',
            format:'controls.fieldname.value == "value" ? false : true',
            description:'If the value in field like "marital status" that is "Married" is false, then the field like "Spouse Name" should be hide. ',
            example:'controls.maritalStatus.value == "Married" ? false : true'
        }
    ],

    validateRule:[
        {
            usecase:'For the mandatory field',
            format:'[{required: true/false}]',
            description:'This will make field mandatory',
            example:'[{required: true}]'
        },
        {
            usecase:"Mandatory field with condition and then set value",
            format:"[{required: controls.fieldname.value == 'value' ? true:false}]",
            description:"Suppose a field name like 'Marital Status', if the value in this field is 'Married' then the 'Spouse Name' field should be mandatory.",
            example:"[{required: controls.maritalStatus.value == 'Married' ? true:false}]"
        },
        {
            usecase:'Maximum length',
            format:"[{ 'maxLength' : length }]",
            description:'Consider a field like "Mobile Number", for this field we need a maximum length of 10 numbers that time we can add this rule.',
            example:"[{'maxLength' : 10 }]"
        },
        {
            usecase:'Minimum length',
            format:"[{'minLength' : length}]",
            description:'Consider a field like "Password", if we need minimum length of 8 characters/numbers in that field that time we can add this rule.',
            example:"[{'maxLength' : 8 }]"
        },
        {
            usecase:'To insert integer values only',
            format:"[{pattern:regex}]",
            description:'We can use this pattern for the fields like "Bank Number" where  only numbers are allowed in field.',
            example:"[{pattern:'^[0-9]+$'}]"
        },
        {
            usecase:"To insert only uppercase alpha charaters",
            format:"[{pattern: regex}]",
            description:"This pattern allows to insert only uppercase alpha characters",
            example:"[{pattern:'^[A-Z]+$'}]"
        },
        
        {
            usecase:"Date with dd-mm-yyyy format.",
            format:"[{pattern:regex}]",
            description:"This pattern is used for date with dd-mm-yyyy format.(0[1-9]|[12][0-9]|3[01]) is the first group of the pattern and it lets you specify the date only between 01 and 31, (0[1-9]|1[1,2]) represents the month and it lets you specify it between 01 and 12, (19|20)\d{2} represents the year which can be between 1900 and any 2000 year",
            example:"[{pattern:'^{(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[1,2])-(19|20)\d{2}}$'}]"
        },
        {
            usecase:"Age above 18",
            format:"[{'max' : date}]",
            description:"This pattern will ensure that the age is above 18 ,considering the entered date of birth. new Date() returns a date object with the current date and time The setFullYear() method of Date instances changes the year, month, and/or day of month for this date. The getFullYear() method of Date instances returns the year for this date                                                                                                 As we need age above 18, we are substracting 18 from current year provided by the user",
            example:"[{'max' : new Date().setFullYear(new Date().getFullYear()-18)}]"
        },
        {
            usecase:"To apply custom condition with error message",
            format:"[{'customVal':'if(condition){ return { customVal: 'Message'  }  }  else {  return   } '}]",
            description:"Here we used this rule for 12th grade completion year. Year should be between 2000 to 9999 otherwise it will display error message.",
            example:"[{'customVal':' if(parseFloat(control?.value) > 9999 || parseFloat(control?.value) < 2000){ return { customVal: 'Value should be between 2000 and 9999'  }  }  else {  return null } '}] "
        },
        
    ],
    defaultValueRule:[
        {
            usecase:"To set the default values to the picklist(Rule can be use only for Picklist)",
            format:"'controls.fieldName.value=='some value'? 'comma seperated string includes deafult values':'defaultValue'",
            description:"This rule is only to set the default values(picklist options) in the picklist. This is visible only for picklist data type. By this rule we can able to add the options(should be part of default values sets in data model) to the picklist which depends on the value of other field",
            example:"'controls.foodType.value=='Fruits'?:'Mango,Apple,Banana':'defaultValue'"
        },
    ],

    eventRule:[
        {
            usecase:"Button Rule based on login user",
            format:"'__buttonname__':'this.user == 'loginuser' ? true : false'",
            description:"Here button name is 'reject' and the username is 'USER1'. If the user is USER1 then that reject button is hidden  ",
            example:"{'__reject__':'this.user == 'USER1' ? true : false'}"
        },
        {
            usecase:"Button Rule based on Deal Asset Field Name & value",
            format:"'__buttonname__':'asset.DealAssetFieldName.value.trim() == 'RequiredDealAssetFieldValue'? true : false'",
            description:" Here, button name is 'approve', 'FirstName' is field name from deal asset and the required value is 'Snehal' ,if the condition is true then only that approve button is hidden else it is visible.",
            example:"{'__approve__':'asset.FirstName.value.trim() == 'John'? true : false'}"
        },
        {
            usecase:"Button rule with logical AND operator",
            format:"'__buttonname__':'(this.user == 'loginuser') && asset.DealAssetFieldName.value <= DealAssetFieldValue? true : false'",
            description:"If the user is 'USER1' and value of 'num' field is less than 100 then then the 'More' button is hidden.",
            example:"{'__more__':'(this.user == 'USER1') && asset.num.value <= 100? true : false'}"
        },
        {
            usecase:"Button rule with logical OR operator",
            format:"'__buttonname__':'(this.user == 'loginuser' ) || (this.user == 'loginuser')? true : false'",
            description:"If the users are USER1 or USER2 then that Reopen button is hidden.",
            example:"{'__reOpen__':'(this.user == 'USER1' ) || (this.user == 'USER2')? true : false'}"
        },
        {
            usecase:"Button rule with logical operator, loginuser,deal asset field name and value",
            format:"{'__buttonname__':'((this.user != 'loggedInUser') && (asset.DealAssetFieldName.value.trim() == 'DealAssetFieldValue' && asset.DealAssetFieldName.value.trim() == 'DealAssetFieldValue'))? false : true'}",
            description:"For the changeStage button user should not be 'USER1' and value in field name 'approval3' and 'approval5' should be 'Yes'.If this condition is false then 'changestage' button should be visible",
            example:"{'__changeStage__':'((this.user != 'USER1') && (asset.approval3.value.trim() == 'Yes' && asset.approval5.value.trim() == 'Yes'))? false : true'} "
        }
    ]
}