<mat-drawer-container class="height-100vh">
	<mat-drawer-content class="padding-20">
		<div fxLayout="row wrap" fxLayoutGap="4px">
			<div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
				<div fxLayout="row wrap" fxLayoutGap="4px">
					<div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
						<h1> Rules Configuration Guide </h1>
					</div>
				</div>
			</div>
		</div>
	  <hr>
	
		<main class="help-container">
			<main class="main">
				<mat-card appearance="outlined" class="menu mat-card-top-border">
					<h1 class="menu-title">Quick Links</h1>
						<nav class="menu-links">
							<span (click)="activeLink = 'general';toggleView=true" [class]="activeLink == 'general' ? 'activeLink' :'inActiveLink'" class="menu-link">General</span>
							<span (click)="activeLink = 'valueRule';" [class]="activeLink == 'valueRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Value Rule</span>
							<span (click)="activeLink = 'hideRule'" [class]="activeLink == 'hideRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Hide/subsection Rule</span>
							<span (click)="activeLink = 'disableRule'" [class]="activeLink == 'disableRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Read-Only/Disable Rule</span>
							<span (click)="activeLink = 'validateRule'" [class]="activeLink == 'validateRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Validate Rule</span>
							<span (click)="activeLink = 'defaultValueRule'" [class]="activeLink == 'defaultValueRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Default Value Rule</span>
							<span (click)="activeLink = 'eventRule'" [class]="activeLink == 'eventRule' ? 'activeLink' :'inActiveLink'"  class="menu-link">Event Rule</span>
							
						</nav>
				</mat-card>
	
				<mat-card appearance="outlined" class="content mat-card-top-border mb-20">
					<div class="toggleAlign" *ngIf="activeLink != 'general'">
						<button mat-icon-button class="pointer toggle" (click)="toggleView = !toggleView" [matTooltip]="toggleView ? 'Table view' : 'List view'" >
							<mat-icon>{{toggleView ? ' table_chart' : 'list'}}</mat-icon>
						</button>

					</div>
					<div class="mainContent">
						<!-- List view -->
						<div *ngIf="toggleView">
							<div class="content-item" *ngIf="activeLink == 'general'">
								<h3 class="content-title">Overview</h3>
								<p>User friendly and effective forms is one of the best feature one can implement in frontend.</p>
								<p>In ZCP aka. Zero Code Platform we are using dynamic reactive forms. To add additional behavioural change to these forms we have implemented rules using configuration.</p>
								<p>Examples of Additional behaviour can be</p>
								<ul>
								<li> Hiding an input box</li>
								<li> Disabling an input box</li>
								<li> Validating input value</li>
								<li> Predefined/calculated value binding to input which is dependent on another input value.</li>
								</ul>
								<p>Rules have been implemented at Business process level using the “Manage item” screen except for datatypes like Table, Repetitive section and Formly.</p>
								<p>&nbsp;</p>
								<p>The Dynamic reactive form group class has one variable called 'Controls'. Inside 'Controls' all the defined items are present.</p><p>Using these controls, we have implemented rules.</p>
								<p>We have four kinds of rules</p>
								<ul>
								<li>Hide rule</li>
								<li>Read only rule</li>
								<li>Validate rule</li>
								<li>Value rule</li>
								</ul>
								<p>These rules are useful in many scenarios like if any field validation/value/behaviour is dependent on other field or to any field needs to be assigned with default validation/value or any field's behaviour is dependent on logged in user</p>
							</div>

							<div class="content-item" *ngIf="activeLink != 'general'">

								<ng-container *ngFor="let item of guideData[activeLink]" class="copyIcon">
									<h4 class="boldfont">{{item.usecase}}</h4>
									<p><u>Format</u>{{' - '+item.format}}</p>
									<p><u>Example</u>{{' - '}} <span  class="flex bold">{{item.example}}</span>
										<span class="flex">  
										<button mat-icon-button class="blue copyIcon" [cdkCopyToClipboard]="item.example" (click)="triggerCopySuccess()">
											<mat-icon class="font-size-15">content_copy</mat-icon>
										</button>
									</span>
									</p>
									<p><u>Description</u>{{' - '+item.description}}</p>
									<p>&nbsp;</p><hr><p>&nbsp;</p>
								</ng-container>
							</div>
						</div>

						<!-- Table View -->
						<div *ngIf="!toggleView">
							<table mat-table [dataSource]="guideData[activeLink]" class="mat-elevation-z8">
								<ng-container matColumnDef="rule">
									<th mat-header-cell *matHeaderCellDef> Rule </th>
									<td mat-cell *matCellDef="let element"> {{element.usecase}} </td>
								</ng-container>

								<ng-container matColumnDef="format">
									<th mat-header-cell *matHeaderCellDef> Format </th>
									<td mat-cell *matCellDef="let element"> {{element.format}} </td>
								</ng-container>

								<ng-container matColumnDef="example">
									<th mat-header-cell *matHeaderCellDef> Example </th>
									<td mat-cell *matCellDef="let element"> {{element.example}} </td>
								</ng-container>

								<ng-container matColumnDef="description">
									<th mat-header-cell *matHeaderCellDef> Description </th>
									<td mat-cell *matCellDef="let element"> {{element.description}} </td>
								</ng-container>

								<tr mat-header-row *matHeaderRowDef="tableColumns"></tr>
								<tr mat-row *matRowDef="let row; columns: tableColumns;"></tr>
								</table>
						</div>
					</div>

				</mat-card>
			</main>
		</main>
	</mat-drawer-content>
</mat-drawer-container>	