.help-container {
	margin: {
		right: auto;
		left: auto;
	}
}

.main {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	margin-top: 35px;
	margin-bottom: 35px;
}

 .mainContent{
	max-height: 550px;
	overflow-y: auto;
	width: -webkit-fill-available ;
 }

.menu {
	width: 280px;
	padding: 30px;
	margin-right: 30px;
	border-radius: 10px;
	
	&-title {
		font-weight: 700;
		font-size: 18px;
	}
	
	&-links {
		margin-top: 20px;
		padding-bottom: 25px;
		border-bottom: 2px solid #141428;
	} 
	
	&-link {
		display: block;
		padding: {
			top: 7px;
			bottom: 7px;
		}
		font-size: 16px;
		text-decoration: none;
		
		&:hover {
			cursor: pointer;
		}
	}
}


.content {
	width: 100%;
	border-radius: 10px;
	
	&-item {
		position: relative;
		padding: 30px;
		margin-top: 10px;
		
		&:first-child {
			margin-top: 0;
		}
		
	}
	
	&-title {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		font-size: 18px;
		font-weight: 700;
		line-height: 24px;
		
		.badge {
			margin-left: 15px;
		}
	}
	
	&-description {
		margin-top: 20px;
		line-height: 22px;
		font-size: 16px;
		color: #bbbbd7;
	}
}


.mat-mdc-cell {
	white-space: normal !important;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-right: 10px;
  }

.toggleAlign{
	margin-bottom: 1%;
	justify-content: flex-end;
	display: flex;
}

::ng-deep .mat-mdc-icon-button.copyIcon {
	width: 30px !important;
	height: 30px  !important;
	line-height: 30px !important;
display: flex;
align-items: center;
}

::ng-deep  .mat-mdc-icon-button.mat-mdc-button-base{
	width: 48px !important;
    height: 48px !important;
    padding: 12px;
}
.flex{
	display: inline-flex;
}
.toggle{
	box-shadow: 0 3px 1px -2px #0003,0 2px 2px #00000024,0 1px 5px #0000001f!important;
}
.boldfont{
	font-weight: bold
}
.height-100vh{
	height: 100vh !important
}
.padding-20{
	padding: 20px !important
}

.inActiveLink{
    color: #747585;
  }
  
  .inActiveLink:hover{
    color: blue;
    transition: color 250ms;
  }

  .activeLink{
    color: blue;
    text-decoration: underline !important;
  }