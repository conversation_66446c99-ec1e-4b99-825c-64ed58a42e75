<div fxLayout="column" class="workplace-container" fxFlexFill>

  <!-- Header -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h1>{{"label.workspace" | literal}}</h1>
  </div>
  <mat-divider class="header"></mat-divider>

  <!-- Search -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <mat-form-field appearance="outline" class="search-bar">
      <mat-icon matPrefix>search</mat-icon>
      <input matInput placeholder="Search Work Groups"
        (input)="onSearchTextChange($event.target.value)" />
    </mat-form-field>
  </div>

  <mat-divider class="header"></mat-divider>

  @if(searchTerm$.value.length === 0) {
  <app-workspace-recent-items></app-workspace-recent-items>
  }

  <div class="accordion-container">
    <mat-accordion class="expansion-panels-container">
      @for(category of filteredCategoryItems; track category.cardName; let idx = $index) {
      @if(checkVisibility(category.roles)) {
      <app-category-item [categoryItem]="category" [expanded]="idx===0"
        [user]="USER_TYPE.USERS"></app-category-item>
      }
      }
      <app-uncategorized-items [hasCategories]="filteredCategoryItems.length > 0"
        [uncategorizedItems]="uncategorizedItems" [isUncategorizedLoading]="isUncategorizedLoading"
        [user]="USER_TYPE.USERS"></app-uncategorized-items>
    </mat-accordion>
  </div>


</div>
