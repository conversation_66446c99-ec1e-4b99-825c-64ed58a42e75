import { Component } from "@angular/core";
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  finalize,
  Subject,
  takeUntil,
} from "rxjs";
import { CardViewApiService } from "src/app/settings/card-view/card-view.api.service";
import {
  CategoriesItem,
  USER_TYPE,
} from "src/app/settings/card-view/card-view.model";
import { CategoryItemComponent } from "src/app/settings/card-view/category-item/category-item.component";
import { UncategorizedItemsComponent } from "src/app/settings/card-view/uncategorized-items/uncategorized-items.component";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { LoaderService } from "src/app/shared-service/loader.service";
import { WorkspaceRecentItemsComponent } from "./workspace-recent-items/workspace-recent-items.component";

@Component({
  selector: "app-workspace-main-container",
  standalone: true,
  imports: [
    SharedModuleModule,
    CategoryItemComponent,
    UncategorizedItemsComponent,
    WorkspaceRecentItemsComponent,
  ],
  templateUrl: "./workspace-main-container.component.html",
  styleUrl: "./workspace-main-container.component.scss",
})
export class WorkspaceMainContainerComponent {
  readonly USER_TYPE = USER_TYPE;

  searchTerm$: BehaviorSubject<string> = new BehaviorSubject("");
  categoryItems: CategoriesItem[] = [];
  filteredCategoryItems: CategoriesItem[] = [];
  destroy$: Subject<void> = new Subject();
  isUncategorizedLoading = false;
  currentUserRoles: string[];
  uncategorizedItems;

  constructor(
    private readonly loader: LoaderService,
    private readonly cardViewApiService: CardViewApiService
  ) {}

  ngOnInit() {
    this.getAllCards();

    const token = localStorage.getItem("accessToken");
    this.currentUserRoles = token
      ? JSON.parse(atob(token.split(".")[1])).realm_access.roles
      : [];

    this.searchTerm$
      .pipe(takeUntil(this.destroy$), distinctUntilChanged(), debounceTime(100))
      .subscribe((searchText) => {
        if (searchText.trim().length) {
          this.filteredCategoryItems = this.categoryItems.filter((item) =>
            item.cardName.toLowerCase().includes(searchText.toLowerCase())
          );
        } else {
          this.filteredCategoryItems = structuredClone(this.categoryItems);
        }
      });
  }

  checkVisibility(requiredRoles: string[]) {
    let hasPermission = false;

    requiredRoles?.forEach((role) => {
      if (this.currentUserRoles.includes(role)) hasPermission = true;
    });

    return hasPermission;
  }

  getAllCards() {
    this.loader.show();
    this.cardViewApiService
      .getCardConfig()
      .pipe(finalize(() => this.loader.hide()))
      .subscribe((config) => {
        this.categoryItems = config.content;
        this.filteredCategoryItems = structuredClone(this.categoryItems);
      });
    this.getAllUncategorizedItems();
  }

  getAllUncategorizedItems() {
    this.isUncategorizedLoading = true;

    this.cardViewApiService
      .getUncategorisedCardConfig()
      .pipe(finalize(() => (this.isUncategorizedLoading = false)))
      .subscribe((config) => {
        const response = config;
        this.uncategorizedItems = {
          ...response,
          cardDetails: {
            businessProcesses: response?.cardDetails?.businessProcesses ?? [],
            entityDefinitions: response?.cardDetails?.entityDefinitions.length
              ? response?.cardDetails?.entityDefinitions.map((item) => ({
                  name: item.entityName,
                  type: item.entityType,
                  subType: item.subType,
                }))
              : [],
          },
        };
      });
  }

  onSearchTextChange(searchText: string) {
    this.searchTerm$.next(searchText);
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
