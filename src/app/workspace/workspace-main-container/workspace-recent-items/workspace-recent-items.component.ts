import { Component } from "@angular/core";
import {
  USER_TYPE,
  WORKSPACE_ITEM_TYPE,
} from "src/app/settings/card-view/card-view.model";
import { CardItemComponent } from "src/app/settings/card-view/category-item/card-item/card-item.component";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { WorkspaceService } from "../../workspace.service";
import { Subject, takeUntil } from "rxjs";
import { WorkspaceRecentItem } from "../../workspace.model";

@Component({
  selector: "app-workspace-recent-items",
  standalone: true,
  imports: [SharedModuleModule, CardItemComponent],
  templateUrl: "./workspace-recent-items.component.html",
  styleUrl: "./workspace-recent-items.component.scss",
})
export class WorkspaceRecentItemsComponent {
  readonly USER_TYPE = USER_TYPE;
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;
  recentItems: WorkspaceRecentItem[];
  destroy$: Subject<void> = new Subject();

  constructor(private readonly workspaceService: WorkspaceService) {}

  ngOnInit() {
    this.getRecentItems();

    this.workspaceService.recentItemsUpdated$
      .pipe(takeUntil(this.destroy$))
      .subscribe((updated) => {
        if (updated) {
          this.getRecentItems();
          this.workspaceService.recentItemsUpdated$.next(false);
        }
      });
  }

  getRecentItems() {
    this.recentItems = localStorage.getItem("workspaceRecentItems")
      ? JSON.parse(localStorage.getItem("workspaceRecentItems"))
      : [];
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
