import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { EntityCategoryItem } from "../settings/card-view/card-view.model";

@Injectable({
  providedIn: "root",
})
export class WorkspaceService {
  recentItemsUpdated$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  // track modified/ clicked items from workspace in existing components
  createdEntityId: string;
  clickedItem: string | EntityCategoryItem;

  constructor() {}
}
