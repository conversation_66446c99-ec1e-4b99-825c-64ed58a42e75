/* eslint-disable @typescript-eslint/no-unused-vars */

import { TestBed, async } from '@angular/core/testing';
import { LiteralPipePipe } from './literal-pipe.pipe';
import { LiteralsCollectService } from '../shared-service/literals-collect.service';

let literalService:LiteralsCollectService
describe('Pipe: LiteralPipee', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers:[
        {provide:LiteralsCollectService, useValue:literalService},

      ] 
    });

  });
  
  it('create an instance', () => {
    let pipe = new LiteralPipePipe(literalService);
    expect(pipe).toBeTruthy();
  });
});
