import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'FilterArrayWithNamePipe'
})
export class FilterArrayWithNamePipe implements PipeTransform {
  transform(items: any[], filter: String, parentName): any {

    if (!items || !filter) {
      return items;
    }
    if (parentName == 'deal') {
      return items.filter(item => {
        let index = item.dealCustomerList.findIndex(ele => ele.coApplicantFlag == false)

        return item ?.dealCustomerList[index != -1 ? index : 0] ?.customerName ?.toLowerCase().indexOf(filter.toLowerCase()) !== -1
        }
      );
    }
    if (parentName == 'entity') {
      return items.filter(item => {
        // let index = item.dealCustomerList.findIndex(ele => ele.coApplicantFlag == false)

        return item?.name ?.toLowerCase().indexOf(filter.toLowerCase()) !== -1
        }
      );
    }

  }

}
