import { Pipe, PipeTransform } from '@angular/core';
import { LiteralsCollectService } from '../shared-service/literals-collect.service';

@Pipe({
  name: 'literal-pipe',
  pure:false
})
export class LiteralPipePipe implements PipeTransform {
constructor(private literalsCollectService : LiteralsCollectService)
{

}
  transform(key:any): any {
    return this.literalsCollectService.data[key] || key;
  }

}
