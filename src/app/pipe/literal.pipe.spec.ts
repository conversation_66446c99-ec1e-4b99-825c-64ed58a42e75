/* eslint-disable @typescript-eslint/no-unused-vars */

import { TestBed, async } from '@angular/core/testing';
import { LiteralPipe } from './literal.pipe';
import { LiteralsCollectService } from '../shared-service/literals-collect.service';

let literalService:LiteralsCollectService
describe('Pipe: Literale', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers:[
        {provide:LiteralsCollectService, useValue:literalService},

      ] 
    });

  });

  it('create an instance', () => {
    let pipe = new LiteralPipe(literalService);
    expect(pipe).toBeTruthy();
  });
});
