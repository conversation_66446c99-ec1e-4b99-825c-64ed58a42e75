import { keyframes } from '@angular/animations';
import { Pipe, PipeTransform } from '@angular/core';
import { LiteralsCollectService } from '../shared-service/literals-collect.service';

@Pipe({
  name: 'literal',
  pure: false
})
export class LiteralPipe implements PipeTransform {
constructor(private literalsCollectService : LiteralsCollectService){}
  transform(key : any): any {
    return this.literalsCollectService.data[key] || key;
  }

}
