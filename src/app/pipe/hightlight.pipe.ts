import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'hightlight'
})
export class HightlightPipe implements PipeTransform {

  transform(text: string, searchTerms: string[]): string {
    if (!searchTerms || !text) {
      return text;
    }

    const regex = new RegExp(searchTerms.join('|'), 'gi');
    return text.replace(regex, match => `<span class="tag">${match}</span>`);
  }

}
