import { Injectable } from '@angular/core';
import { DataSharingService } from '../common/dataSharing.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  accessToken : string;
  user  : string;
  tenant : string;
  redirectUrl:string;

  constructor(
    public dataSharingService : DataSharingService ,
    public router: Router,
    ) {

  }

  /**
   * 
   * @param token JWT token
   * @returns boolean of token validity
   */
  isTokenExpired(token: string): boolean {
    const tokenPayload = JSON.parse(atob(token.split('.')[1]));
    return Date.now() >= tokenPayload.exp * 1000;
  }



/**
 * Check whether user is logged In or not.
 *
 * @return {*}  {boolean}
 * @memberof AuthService
 */
isAuthenticated(): boolean {
  if (window.location.search.includes("accessToken")) {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);

    this.accessToken = urlParams.get("accessToken");
    this.tenant = urlParams.get("tenant");
    this.user = urlParams.get("user");

    if (this.accessToken && this.user) {
      localStorage.setItem("user", this.user);
      localStorage.setItem("accessToken", this.accessToken);
      localStorage.setItem("tenantIdentifier", this.tenant);
      this.dataSharingService.currentUserName = this.user;
    }
  }

  return !!(
    localStorage.getItem("user") &&
    localStorage.getItem("accessToken") &&
    !this.isTokenExpired(localStorage.getItem("accessToken"))
  );
}
}
