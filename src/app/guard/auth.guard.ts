import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from "@angular/router";
import { AuthService } from "./auth.service";

@Injectable({
  providedIn: "root",
})
export class AuthGuard {
  constructor(
    private router: Router,
    private authenticationService: AuthService
  ) {}
  /** Restrict to enter in application througth url without login */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const url = state.url;

    /** Check user is logged in or not*/
    if (this.authenticationService.isAuthenticated()) {
      return true;
    }

    /** Only for deal details page for now */
    if (url) {
      // Store the attempted URL for redirecting
      this.authenticationService.redirectUrl = url;
    }

    /** Redirect to login if user hasnot logged In.*/
    this.router.navigate(["/login"], { replaceUrl: true });
    return false;
  }
}
