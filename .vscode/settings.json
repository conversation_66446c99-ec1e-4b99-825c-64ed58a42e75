{
    "editor.formatOnSave": true,

    // HTML formatting
    "[html]": {
        "editor.defaultFormatter": "vscode.html-language-features"
    },
    "html.format.wrapLineLength": 100,
    "html.format.indentInnerHtml": true,

    // TypeScript formatting
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // SCSS formatting
    "[scss]": {
        "editor.defaultFormatter": "vscode.css-language-features"
    },
    "scss.format.spaceAroundSelectorSeparator": true,

    // JSON formatting
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "json.format.keepLines": true,

    // Additional formatting
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.trimAutoWhitespace": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true
}
