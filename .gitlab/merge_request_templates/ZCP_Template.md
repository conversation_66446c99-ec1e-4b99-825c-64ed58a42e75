## Description

(Summarize the bug encountered concisely)

## Related Issues

(Reference any related issues or tasks from your issue tracker (e.g., GitLab issues, Jira tasks) that are addressed by this merge request. Use the “#” symbol followed by the issue number to automatically link them.)

## Changes Made

(List and describe the changes made in bullet points or a numbered list. Be specific about the files modified, added, or deleted, and the changes made within them.)

## Screenshots

(Include screenshots or GIFs if applicable, to showcase the changes visually, especially for UI-related changes.)

## Testing Done

(Explain the testing you’ve conducted to ensure the changes work as expected. This may include unit tests, integration tests, manual testing, etc.)

## Testing Impact

- [ ] OLD UI
- [ ] New UI
- [ ] Extensions
- [ ] Global Styles

## Deployment Impact

(Describe any potential impact these changes may have on the production environment or other existing features. If necessary, provide information on how to mitigate any risks.)

## Acceptance Criteria

- [x] I have tested these changes thoroughly.

## Additional Notes

 (Add any other relevant information or context that could be helpful for the reviewers.)

## Closing Note

(End your merge request comment with a closing note of appreciation or any other relevant message to acknowledge the effort of the reviewers and to encourage collaboration.)

/cc @username1 @username2
